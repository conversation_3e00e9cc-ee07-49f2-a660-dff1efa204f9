# POS收银模块重构设计文档

## 概述

本设计文档基于POS收银模块重构需求，提供了一个全面的技术架构设计方案。重构的核心目标是将当前单体化的POS模块（主组件840行、API文件1192行、状态管理883行）拆分为符合ERP模块标准的模块化架构，提升代码的可维护性、复用性和性能表现。

设计遵循Vue 3 Composition API最佳实践，采用分层架构和模块化设计，确保重构后的代码结构清晰、职责明确、易于测试和维护。

## 架构

### 整体架构设计

```mermaid
graph TB
    A[POS主页面 index.vue] --> B[业务逻辑层 Composables]
    A --> C[组件层 Components]
    A --> D[API层 API Services]
    
    B --> B1[useCart 购物车逻辑]
    B --> B2[usePayment 支付逻辑]
    B --> B3[useMember 会员逻辑]
    B --> B4[useOrder 订单逻辑]
    
    C --> C1[购物车组件组]
    C --> C2[支付组件组]
    C --> C3[商品组件组]
    C --> C4[会员组件组]
    
    D --> D1[cart.js 购物车API]
    D --> D2[payment.js 支付API]
    D --> D3[member.js 会员API]
    D --> D4[product.js 商品API]
    
    E[工具函数层 Utils] --> E1[calculator.js 计算工具]
    E --> E2[formatter.js 格式化工具]
    E --> E3[validator.js 验证工具]
    
    F[状态管理 Pinia Store] --> F1[购物车状态]
    F --> F2[支付状态]
    F --> F3[会员状态]
    F --> F4[系统状态]
```

### 分层架构说明

1. **表现层（Presentation Layer）**
   - 主页面：`index.vue` - 负责整体布局和组件协调
   - 组件层：按功能域分组的UI组件

2. **业务逻辑层（Business Logic Layer）**
   - Composables：封装可复用的业务逻辑
   - 工具函数：提供通用的计算、格式化、验证功能

3. **数据访问层（Data Access Layer）**
   - API Services：按业务功能分类的接口调用
   - 状态管理：集中管理应用状态

4. **基础设施层（Infrastructure Layer）**
   - 错误处理：统一的错误处理机制
   - 性能监控：性能监控和优化工具

## 组件和接口

### 目录结构设计

```
src/views/erp/pos/
├── index.vue                    # 主页面入口（<200行）
├── api/                         # API接口层
│   ├── index.js                 # 统一导出
│   ├── cart.js                  # 购物车API（~200行）
│   ├── payment.js               # 支付API（~300行）
│   ├── member.js                # 会员API（~200行）
│   ├── product.js               # 商品API（~300行）
│   └── order.js                 # 订单API（~200行）
├── components/                  # 组件层
│   ├── index.js                 # 统一导出
│   ├── cart/                    # 购物车组件组
│   │   ├── ShoppingCart.vue     # 购物车主组件（<300行）
│   │   ├── CartItem.vue         # 购物车项组件
│   │   └── CartSummary.vue      # 购物车汇总组件
│   ├── payment/                 # 支付组件组
│   │   ├── PaymentPanel.vue     # 支付面板主组件（<300行）
│   │   ├── PaymentMethod.vue    # 支付方式选择
│   │   └── PaymentResult.vue    # 支付结果显示
│   ├── product/                 # 商品组件组
│   │   ├── ProductDisplayArea.vue # 商品展示区域（<300行）
│   │   ├── ProductGrid.vue      # 商品网格
│   │   └── ProductSearch.vue    # 商品搜索
│   ├── member/                  # 会员组件组
│   │   ├── MemberPanel.vue      # 会员面板（<200行）
│   │   └── MemberSelector.vue   # 会员选择器
│   └── common/                  # 通用组件
│       ├── ToolbarPanel.vue     # 工具栏面板
│       └── OrderSuspend.vue     # 挂单管理
├── composables/                 # 组合式函数
│   ├── index.js                 # 统一导出
│   ├── useCart.js               # 购物车逻辑（~200行）
│   ├── usePayment.js            # 支付逻辑（~200行）
│   ├── useMember.js             # 会员逻辑（~150行）
│   ├── useOrder.js              # 订单逻辑（~200行）
│   └── useKeyboard.js           # 键盘快捷键逻辑
├── utils/                       # 工具函数
│   ├── index.js                 # 统一导出
│   ├── calculator.js            # 计算工具（~150行）
│   ├── formatter.js             # 格式化工具（~100行）
│   ├── validator.js             # 验证工具（~100行）
│   └── constants.js             # 常量定义
└── styles/                      # 样式文件（保持现有结构）
    ├── index.js
    ├── common.css
    ├── member.css
    ├── payment.css
    ├── product-display.css
    ├── shopping-cart.css
    └── toolbar.css
```

### 核心组件接口设计

#### 1. 主页面组件 (index.vue)

```vue
<template>
  <div class="pos-main" :class="{ 'fullscreen': isFullscreen }">
    <ToolbarPanel 
      :current-user="currentUser"
      :is-fullscreen="isFullscreen"
      @toggle-fullscreen="toggleFullscreen"
      @reset-all="resetAll"
    />
    
    <div class="pos-content">
      <div class="pos-left">
        <ProductDisplayArea 
          :categories="categories"
          :products="products"
          @add-to-cart="handleAddToCart"
        />
      </div>
      
      <div class="pos-right">
        <MemberPanel 
          :member="currentMember"
          @member-change="handleMemberChange"
        />
        
        <ShoppingCart 
          :items="cartItems"
          :total="cartTotal"
          @item-change="handleCartItemChange"
        />
        
        <PaymentPanel 
          :total="finalAmount"
          :member="currentMember"
          @payment="handlePayment"
        />
      </div>
    </div>
    
    <OrderSuspend 
      v-if="showSuspendDialog"
      :orders="suspendedOrders"
      @close="showSuspendDialog = false"
      @restore="handleRestoreOrder"
    />
  </div>
</template>

<script setup>
import { usePos } from './composables/usePos'
import { useKeyboard } from './composables/useKeyboard'

// 使用组合式函数管理业务逻辑
const {
  // 状态
  currentUser,
  isFullscreen,
  categories,
  products,
  currentMember,
  cartItems,
  cartTotal,
  finalAmount,
  suspendedOrders,
  showSuspendDialog,
  
  // 方法
  toggleFullscreen,
  resetAll,
  handleAddToCart,
  handleMemberChange,
  handleCartItemChange,
  handlePayment,
  handleRestoreOrder
} = usePos()

// 键盘快捷键支持
useKeyboard()
</script>
```

#### 2. 购物车组合式函数 (composables/useCart.js)

```javascript
import { ref, computed } from 'vue'
import { usePosStore } from '@/stores/pos'
import { CartCalculator } from '../utils/calculator'
import { CartValidator } from '../utils/validator'

export function useCart() {
  const store = usePosStore()
  
  // 购物车状态
  const items = computed(() => store.cartItems)
  const totalAmount = computed(() => store.totalAmount)
  const discountAmount = computed(() => store.discountAmount)
  const finalAmount = computed(() => store.finalAmount)
  
  // 添加商品到购物车
  const addItem = async (product, quantity = 1) => {
    try {
      // 验证商品和数量
      CartValidator.validateProduct(product)
      CartValidator.validateQuantity(quantity)
      
      // 检查库存
      await checkInventory(product.id, quantity)
      
      // 添加到购物车
      store.addCartItem({
        ...product,
        quantity,
        subtotal: CartCalculator.calculateSubtotal(product.price, quantity)
      })
      
      // 重新计算总金额
      recalculateTotal()
      
    } catch (error) {
      throw new Error(`添加商品失败: ${error.message}`)
    }
  }
  
  // 更新商品数量
  const updateQuantity = (itemId, quantity) => {
    try {
      CartValidator.validateQuantity(quantity)
      
      if (quantity <= 0) {
        removeItem(itemId)
      } else {
        store.updateCartItemQuantity(itemId, quantity)
        recalculateTotal()
      }
    } catch (error) {
      throw new Error(`更新数量失败: ${error.message}`)
    }
  }
  
  // 移除商品
  const removeItem = (itemId) => {
    store.removeCartItem(itemId)
    recalculateTotal()
  }
  
  // 清空购物车
  const clearCart = () => {
    store.clearCart()
  }
  
  // 重新计算总金额
  const recalculateTotal = () => {
    const total = CartCalculator.calculateTotal(items.value)
    const discount = CartCalculator.calculateDiscount(total, store.memberDiscountRate)
    const final = total - discount
    
    store.updateCartTotals({
      totalAmount: total,
      discountAmount: discount,
      finalAmount: final
    })
  }
  
  // 检查库存
  const checkInventory = async (productId, quantity) => {
    // 实现库存检查逻辑
  }
  
  return {
    // 状态
    items,
    totalAmount,
    discountAmount,
    finalAmount,
    
    // 方法
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    recalculateTotal
  }
}
```

#### 3. API接口设计 (api/cart.js)

```javascript
import Request from '@/utils/request/request-util'
import { PosErrorHandler } from '@/utils/erp/pos-error-handler'

/**
 * 购物车相关API接口
 */
export class CartApi {
  
  /**
   * 验证商品库存
   * @param {string} productId - 商品ID
   * @param {number} quantity - 需要数量
   * @returns {Promise<Object>} 库存信息
   */
  static async checkInventory(productId, quantity) {
    const apiCall = () => Request.post('/api/pos/cart/check-inventory', {
      productId,
      quantity
    })
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '检查商品库存',
      showMessage: true
    })
  }
  
  /**
   * 获取商品详细信息
   * @param {string} productId - 商品ID
   * @returns {Promise<Object>} 商品信息
   */
  static async getProductDetail(productId) {
    const apiCall = () => Request.get(`/api/pos/products/${productId}`)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '获取商品详情',
      showMessage: false
    })
  }
  
  /**
   * 批量获取商品信息
   * @param {Array<string>} productIds - 商品ID列表
   * @returns {Promise<Array>} 商品信息列表
   */
  static async getProductsBatch(productIds) {
    const apiCall = () => Request.post('/api/pos/products/batch', {
      productIds
    })
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '批量获取商品信息',
      showMessage: false
    })
  }
  
  /**
   * 保存购物车状态（用于挂单）
   * @param {Object} cartData - 购物车数据
   * @returns {Promise<Object>} 保存结果
   */
  static async saveCartState(cartData) {
    const apiCall = () => Request.post('/api/pos/cart/save', cartData)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '保存购物车状态',
      showMessage: true
    })
  }
  
  /**
   * 恢复购物车状态
   * @param {string} cartId - 购物车ID
   * @returns {Promise<Object>} 购物车数据
   */
  static async restoreCartState(cartId) {
    const apiCall = () => Request.get(`/api/pos/cart/restore/${cartId}`)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '恢复购物车状态',
      showMessage: true
    })
  }
}
```

## 数据模型

### 核心数据结构

#### 1. 购物车项模型

```typescript
interface CartItem {
  id: string                    // 商品ID
  name: string                  // 商品名称
  barcode?: string             // 商品条码
  price: number                // 单价
  quantity: number             // 数量
  subtotal: number             // 小计
  discount?: number            // 折扣金额
  categoryId: string           // 分类ID
  categoryName: string         // 分类名称
  unit: string                 // 单位
  image?: string               // 商品图片
  specifications?: string      // 规格说明
  createdAt: Date             // 添加时间
}
```

#### 2. 会员信息模型

```typescript
interface Member {
  id: string                   // 会员ID
  cardNo: string              // 会员卡号
  name: string                // 会员姓名
  phone: string               // 手机号码
  level: string               // 会员等级
  discountRate: number        // 折扣率
  points: number              // 积分余额
  balance: number             // 账户余额
  status: 'active' | 'inactive' | 'suspended'  // 状态
  createdAt: Date             // 注册时间
  lastVisit?: Date            // 最后消费时间
}
```

#### 3. 订单模型

```typescript
interface Order {
  id: string                   // 订单ID
  orderNo: string             // 订单号
  items: CartItem[]           // 订单商品
  member?: Member             // 会员信息
  totalAmount: number         // 总金额
  discountAmount: number      // 折扣金额
  finalAmount: number         // 实付金额
  paymentMethod: string       // 支付方式
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded'  // 支付状态
  cashierId: string          // 收银员ID
  cashierName: string        // 收银员姓名
  createdAt: Date            // 创建时间
  paidAt?: Date              // 支付时间
  remark?: string            // 备注
}
```

#### 4. 支付信息模型

```typescript
interface Payment {
  id: string                   // 支付ID
  orderId: string             // 订单ID
  method: 'cash' | 'alipay' | 'wechat' | 'card' | 'points'  // 支付方式
  amount: number              // 支付金额
  receivedAmount?: number     // 实收金额（现金支付）
  changeAmount?: number       // 找零金额
  transactionId?: string      // 交易流水号
  status: 'pending' | 'success' | 'failed'  // 支付状态
  createdAt: Date            // 创建时间
  completedAt?: Date         // 完成时间
}
```

### 状态管理数据结构

```typescript
interface PosState {
  // 购物车状态
  cart: {
    items: CartItem[]
    totalAmount: number
    discountAmount: number
    finalAmount: number
  }
  
  // 会员状态
  member: {
    current: Member | null
    discountRate: number
    pointsDeduction: number
  }
  
  // 支付状态
  payment: {
    status: 'idle' | 'processing' | 'success' | 'failed'
    method: string
    receivedAmount: number
    changeAmount: number
  }
  
  // 系统状态
  system: {
    isFullscreen: boolean
    currentUser: User
    currentTime: string
    suspendedOrders: Order[]
  }
  
  // 商品状态
  products: {
    categories: Category[]
    currentCategory: string
    searchKeyword: string
    productList: Product[]
  }
}
```

## 错误处理

### 错误处理策略

#### 1. 分层错误处理

```mermaid
graph TD
    A[用户操作] --> B[组件层]
    B --> C[Composables层]
    C --> D[API层]
    D --> E[后端服务]
    
    E --> F[API错误处理]
    F --> G[Composables错误处理]
    G --> H[组件错误处理]
    H --> I[用户错误提示]
    
    F --> F1[网络错误]
    F --> F2[业务错误]
    F --> F3[系统错误]
    
    G --> G1[业务逻辑错误]
    G --> G2[数据验证错误]
    
    H --> H1[用户友好提示]
    H --> H2[错误恢复操作]
```

#### 2. 错误类型定义

```javascript
// utils/error-types.js
export const PosErrorTypes = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 业务错误
  INSUFFICIENT_INVENTORY: 'INSUFFICIENT_INVENTORY',
  INVALID_MEMBER: 'INVALID_MEMBER',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  
  // 验证错误
  INVALID_QUANTITY: 'INVALID_QUANTITY',
  INVALID_PRICE: 'INVALID_PRICE',
  EMPTY_CART: 'EMPTY_CART',
  
  // 系统错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  SYSTEM_MAINTENANCE: 'SYSTEM_MAINTENANCE'
}

export const ErrorMessages = {
  [PosErrorTypes.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [PosErrorTypes.INSUFFICIENT_INVENTORY]: '商品库存不足',
  [PosErrorTypes.INVALID_MEMBER]: '会员信息无效',
  [PosErrorTypes.PAYMENT_FAILED]: '支付失败，请重试',
  [PosErrorTypes.INVALID_QUANTITY]: '商品数量必须大于0',
  [PosErrorTypes.EMPTY_CART]: '购物车为空，无法结算'
}
```

#### 3. 统一错误处理器

```javascript
// utils/error-handler.js
import { message, notification } from 'ant-design-vue'
import { PosErrorTypes, ErrorMessages } from './error-types'

export class PosErrorHandler {
  
  /**
   * 包装API调用，提供统一的错误处理
   */
  static wrapApiCall(apiFunction, options = {}) {
    return async (...args) => {
      try {
        const result = await apiFunction(...args)
        return result
      } catch (error) {
        this.handleError(error, options)
        throw error
      }
    }
  }
  
  /**
   * 处理错误
   */
  static handleError(error, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      context = '操作'
    } = options
    
    const errorType = this.getErrorType(error)
    const errorMessage = this.getErrorMessage(errorType, error)
    
    if (showMessage) {
      message.error(errorMessage)
    }
    
    if (showNotification) {
      notification.error({
        message: `${context}失败`,
        description: errorMessage,
        duration: 5
      })
    }
    
    // 记录错误日志
    this.logError(error, context)
  }
  
  /**
   * 获取错误类型
   */
  static getErrorType(error) {
    if (error.code) {
      return error.code
    }
    
    if (error.message?.includes('网络')) {
      return PosErrorTypes.NETWORK_ERROR
    }
    
    if (error.message?.includes('库存')) {
      return PosErrorTypes.INSUFFICIENT_INVENTORY
    }
    
    return 'UNKNOWN_ERROR'
  }
  
  /**
   * 获取错误消息
   */
  static getErrorMessage(errorType, error) {
    return ErrorMessages[errorType] || error.message || '操作失败，请重试'
  }
  
  /**
   * 记录错误日志
   */
  static logError(error, context) {
    console.error(`[POS Error] ${context}:`, {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    })
  }
}
```

### 错误恢复机制

#### 1. 自动重试机制

```javascript
// utils/retry-handler.js
export class RetryHandler {
  
  /**
   * 带重试的API调用
   */
  static async withRetry(apiFunction, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      retryCondition = (error) => this.shouldRetry(error)
    } = options
    
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries || !retryCondition(error)) {
          throw error
        }
        
        await this.delay(retryDelay * Math.pow(2, attempt))
      }
    }
    
    throw lastError
  }
  
  /**
   * 判断是否应该重试
   */
  static shouldRetry(error) {
    // 网络错误可以重试
    if (error.code === 'NETWORK_ERROR') return true
    
    // 超时错误可以重试
    if (error.code === 'TIMEOUT_ERROR') return true
    
    // 服务器错误可以重试
    if (error.status >= 500) return true
    
    return false
  }
  
  /**
   * 延迟函数
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
```

#### 2. 数据恢复机制

```javascript
// composables/useDataRecovery.js
export function useDataRecovery() {
  
  /**
   * 保存购物车状态到本地存储
   */
  const saveCartState = (cartData) => {
    try {
      const stateData = {
        ...cartData,
        timestamp: Date.now()
      }
      localStorage.setItem('pos_cart_backup', JSON.stringify(stateData))
    } catch (error) {
      console.warn('保存购物车状态失败:', error)
    }
  }
  
  /**
   * 从本地存储恢复购物车状态
   */
  const restoreCartState = () => {
    try {
      const savedData = localStorage.getItem('pos_cart_backup')
      if (savedData) {
        const stateData = JSON.parse(savedData)
        
        // 检查数据是否过期（24小时）
        const isExpired = Date.now() - stateData.timestamp > 24 * 60 * 60 * 1000
        
        if (!isExpired) {
          return stateData
        }
      }
    } catch (error) {
      console.warn('恢复购物车状态失败:', error)
    }
    
    return null
  }
  
  /**
   * 清除备份数据
   */
  const clearBackup = () => {
    localStorage.removeItem('pos_cart_backup')
  }
  
  return {
    saveCartState,
    restoreCartState,
    clearBackup
  }
}
```

## 测试策略

### 测试架构

#### 1. 测试分层策略

```mermaid
graph TB
    A[E2E测试] --> B[集成测试]
    B --> C[组件测试]
    C --> D[单元测试]
    
    A --> A1[用户场景测试]
    A --> A2[业务流程测试]
    
    B --> B1[API集成测试]
    B --> B2[状态管理测试]
    
    C --> C1[组件渲染测试]
    C --> C2[组件交互测试]
    
    D --> D1[工具函数测试]
    D --> D2[Composables测试]
```

#### 2. 单元测试示例

```javascript
// __tests__/composables/useCart.test.js
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useCart } from '@/views/erp/pos/composables/useCart'
import { createPinia, setActivePinia } from 'pinia'

describe('useCart', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  
  describe('addItem', () => {
    it('应该成功添加商品到购物车', async () => {
      const { addItem, items } = useCart()
      
      const product = {
        id: '1',
        name: '测试商品',
        price: 10.00
      }
      
      await addItem(product, 2)
      
      expect(items.value).toHaveLength(1)
      expect(items.value[0]).toMatchObject({
        id: '1',
        name: '测试商品',
        price: 10.00,
        quantity: 2,
        subtotal: 20.00
      })
    })
    
    it('应该在商品无效时抛出错误', async () => {
      const { addItem } = useCart()
      
      await expect(addItem(null)).rejects.toThrow('添加商品失败')
    })
    
    it('应该在数量无效时抛出错误', async () => {
      const { addItem } = useCart()
      
      const product = { id: '1', name: '测试商品', price: 10.00 }
      
      await expect(addItem(product, 0)).rejects.toThrow('添加商品失败')
      await expect(addItem(product, -1)).rejects.toThrow('添加商品失败')
    })
  })
  
  describe('updateQuantity', () => {
    it('应该成功更新商品数量', async () => {
      const { addItem, updateQuantity, items } = useCart()
      
      const product = { id: '1', name: '测试商品', price: 10.00 }
      await addItem(product, 2)
      
      updateQuantity('1', 5)
      
      expect(items.value[0].quantity).toBe(5)
      expect(items.value[0].subtotal).toBe(50.00)
    })
    
    it('应该在数量为0时移除商品', async () => {
      const { addItem, updateQuantity, items } = useCart()
      
      const product = { id: '1', name: '测试商品', price: 10.00 }
      await addItem(product, 2)
      
      updateQuantity('1', 0)
      
      expect(items.value).toHaveLength(0)
    })
  })
})
```

#### 3. 组件测试示例

```javascript
// __tests__/components/ShoppingCart.test.js
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ShoppingCart from '@/views/erp/pos/components/cart/ShoppingCart.vue'

describe('ShoppingCart', () => {
  const createWrapper = (props = {}) => {
    return mount(ShoppingCart, {
      props: {
        items: [],
        total: 0,
        ...props
      },
      global: {
        plugins: [createPinia()]
      }
    })
  }
  
  it('应该正确渲染购物车', () => {
    const items = [
      { id: '1', name: '商品1', price: 10, quantity: 2, subtotal: 20 }
    ]
    
    const wrapper = createWrapper({ items, total: 20 })
    
    expect(wrapper.find('.shopping-cart').exists()).toBe(true)
    expect(wrapper.find('.cart-item').exists()).toBe(true)
    expect(wrapper.text()).toContain('商品1')
    expect(wrapper.text()).toContain('¥20.00')
  })
  
  it('应该在购物车为空时显示空状态', () => {
    const wrapper = createWrapper()
    
    expect(wrapper.find('.empty-cart').exists()).toBe(true)
    expect(wrapper.text()).toContain('购物车为空')
  })
  
  it('应该在点击删除按钮时触发删除事件', async () => {
    const items = [
      { id: '1', name: '商品1', price: 10, quantity: 2, subtotal: 20 }
    ]
    
    const wrapper = createWrapper({ items })
    
    await wrapper.find('.delete-btn').trigger('click')
    
    expect(wrapper.emitted('item-change')).toBeTruthy()
    expect(wrapper.emitted('item-change')[0]).toEqual([{
      type: 'remove',
      itemId: '1'
    }])
  })
})
```

#### 4. API测试示例

```javascript
// __tests__/api/cart.test.js
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { CartApi } from '@/views/erp/pos/api/cart'
import Request from '@/utils/request/request-util'

vi.mock('@/utils/request/request-util')

describe('CartApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  describe('checkInventory', () => {
    it('应该成功检查库存', async () => {
      const mockResponse = { available: true, stock: 100 }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.checkInventory('product1', 5)
      
      expect(Request.post).toHaveBeenCalledWith('/api/pos/cart/check-inventory', {
        productId: 'product1',
        quantity: 5
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理库存不足的情况', async () => {
      const mockError = new Error('库存不足')
      Request.post.mockRejectedValue(mockError)
      
      await expect(CartApi.checkInventory('product1', 100)).rejects.toThrow('库存不足')
    })
  })
  
  describe('getProductDetail', () => {
    it('应该成功获取商品详情', async () => {
      const mockProduct = { id: '1', name: '测试商品', price: 10 }
      Request.get.mockResolvedValue(mockProduct)
      
      const result = await CartApi.getProductDetail('1')
      
      expect(Request.get).toHaveBeenCalledWith('/api/pos/products/1')
      expect(result).toEqual(mockProduct)
    })
  })
})
```

### 测试覆盖率要求

- **单元测试覆盖率**: ≥ 90%
- **组件测试覆盖率**: ≥ 85%
- **API测试覆盖率**: ≥ 95%
- **集成测试覆盖率**: ≥ 80%

### 性能测试

#### 1. 组件性能测试

```javascript
// __tests__/performance/component-performance.test.js
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { performance } from 'perf_hooks'

describe('组件性能测试', () => {
  it('购物车组件渲染性能应该在可接受范围内', () => {
    const largeItemList = Array.from({ length: 100 }, (_, i) => ({
      id: `item-${i}`,
      name: `商品${i}`,
      price: Math.random() * 100,
      quantity: Math.floor(Math.random() * 10) + 1
    }))
    
    const startTime = performance.now()
    
    const wrapper = mount(ShoppingCart, {
      props: { items: largeItemList }
    })
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // 渲染时间应该小于100ms
    expect(renderTime).toBeLessThan(100)
    expect(wrapper.findAll('.cart-item')).toHaveLength(100)
  })
})
```

## 性能优化

### 优化策略

#### 1. 代码分割和懒加载

```javascript
// router/pos.js
export const posRoutes = [
  {
    path: '/erp/pos',
    name: 'POS',
    component: () => import('@/views/erp/pos/index.vue'),
    meta: {
      title: 'POS收银系统',
      keepAlive: true
    }
  }
]

// 组件懒加载
// components/index.js
export const ShoppingCart = defineAsyncComponent({
  loader: () => import('./cart/ShoppingCart.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

#### 2. 虚拟滚动优化

```vue
<!-- components/product/ProductGrid.vue -->
<template>
  <div class="product-grid">
    <VirtualList
      :items="products"
      :item-height="120"
      :container-height="600"
      v-slot="{ item }"
    >
      <ProductCard
        :key="item.id"
        :product="item"
        @add-to-cart="$emit('add-to-cart', item)"
      />
    </VirtualList>
  </div>
</template>

<script setup>
import VirtualList from '@/components/common/VirtualList.vue'
import ProductCard from './ProductCard.vue'

defineProps({
  products: Array
})

defineEmits(['add-to-cart'])
</script>
```

#### 3. 状态管理优化

```javascript
// stores/pos.js - 优化版本
import { defineStore } from 'pinia'
import { ref, computed, shallowRef } from 'vue'

export const usePosStore = defineStore('pos', () => {
  // 使用 shallowRef 优化大数组的响应性
  const cartItems = shallowRef([])
  const products = shallowRef([])
  
  // 使用 computed 缓存计算结果
  const cartTotal = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.subtotal, 0)
  })
  
  // 批量更新方法，减少响应式更新次数
  const batchUpdateCart = (updates) => {
    cartItems.value = [...cartItems.value]
    updates.forEach(update => {
      const index = cartItems.value.findIndex(item => item.id === update.id)
      if (index !== -1) {
        Object.assign(cartItems.value[index], update)
      }
    })
  }
  
  return {
    cartItems,
    products,
    cartTotal,
    batchUpdateCart
  }
})
```

#### 4. 防抖和节流优化

```javascript
// composables/useOptimization.js
import { debounce, throttle } from 'lodash-es'

export function useOptimization() {
  
  // 搜索防抖
  const debouncedSearch = debounce((keyword) => {
    // 执行搜索逻辑
    searchProducts(keyword)
  }, 300)
  
  // 滚动节流
  const throttledScroll = throttle((event) => {
    // 处理滚动事件
    handleScroll(event)
  }, 100)
  
  // 批量操作优化
  const batchOperation = (() => {
    let operations = []
    let timer = null
    
    return (operation) => {
      operations.push(operation)
      
      if (timer) clearTimeout(timer)
      
      timer = setTimeout(() => {
        // 批量执行操作
        executeBatchOperations(operations)
        operations = []
      }, 50)
    }
  })()
  
  return {
    debouncedSearch,
    throttledScroll,
    batchOperation
  }
}
```

### 性能监控

#### 1. 性能指标监控

```javascript
// utils/performance-monitor.js
export class PerformanceMonitor {
  
  static measureComponentRender(componentName, renderFunction) {
    const startTime = performance.now()
    
    const result = renderFunction()
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // 记录性能数据
    this.recordMetric('component_render', {
      component: componentName,
      renderTime,
      timestamp: Date.now()
    })
    
    // 如果渲染时间过长，发出警告
    if (renderTime > 100) {
      console.warn(`组件 ${componentName} 渲染时间过长: ${renderTime}ms`)
    }
    
    return result
  }
  
  static measureApiCall(apiName, apiFunction) {
    return async (...args) => {
      const startTime = performance.now()
      
      try {
        const result = await apiFunction(...args)
        
        const endTime = performance.now()
        const apiTime = endTime - startTime
        
        this.recordMetric('api_call', {
          api: apiName,
          duration: apiTime,
          status: 'success',
          timestamp: Date.now()
        })
        
        return result
      } catch (error) {
        const endTime = performance.now()
        const apiTime = endTime - startTime
        
        this.recordMetric('api_call', {
          api: apiName,
          duration: apiTime,
          status: 'error',
          error: error.message,
          timestamp: Date.now()
        })
        
        throw error
      }
    }
  }
  
  static recordMetric(type, data) {
    // 发送性能数据到监控系统
    if (window.posMetrics) {
      window.posMetrics.push({ type, ...data })
    }
  }
  
  static getMetrics() {
    return window.posMetrics || []
  }
  
  static clearMetrics() {
    window.posMetrics = []
  }
}
```

#### 2. 内存使用监控

```javascript
// composables/useMemoryMonitor.js
export function useMemoryMonitor() {
  
  const checkMemoryUsage = () => {
    if (performance.memory) {
      const memInfo = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }
      
      const usagePercent = (memInfo.used / memInfo.limit) * 100
      
      if (usagePercent > 80) {
        console.warn('内存使用率过高:', usagePercent.toFixed(2) + '%')
        
        // 触发垃圾回收建议
        if (window.gc) {
          window.gc()
        }
      }
      
      return memInfo
    }
    
    return null
  }
  
  // 定期检查内存使用情况
  const startMemoryMonitoring = () => {
    setInterval(checkMemoryUsage, 30000) // 每30秒检查一次
  }
  
  return {
    checkMemoryUsage,
    startMemoryMonitoring
  }
}
```

这个设计文档提供了POS收银模块重构的完整技术架构方案，包括模块化设计、组件拆分、API优化、错误处理、测试策略和性能优化等各个方面。设计遵循了Vue 3最佳实践和项目的ERP模块标准，确保重构后的代码具有良好的可维护性、可测试性和性能表现。