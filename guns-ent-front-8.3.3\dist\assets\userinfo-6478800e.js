import{_ as I,b2 as b,b3 as y,r as n,o as x,a as d,c as C,b as e,d as c,I as k,t as o,w,g as B,f as N,bI as O,h as S,bJ as U,bK as V,B as L}from"./index-18a1ea24.js";const z={class:"user-info-card"},A={class:"user-info-card-header"},D={class:"user-info-card-body"},F={class:"user-info-profile-photo"},P={class:"user-info-profile"},j={class:"user-info-profile-info"},E={class:"title"},J={class:"title"},K={class:"title"},M={class:"title"},R={class:"title"},T={__name:"userinfo",setup(W){const _=b(),p=y(),t=n({}),l=n({}),a=n(!1),u=n(""),f=()=>{a.value=!0},v=()=>{U.getUserInfo().then(i=>{t.value=i})},h=()=>{_.push({path:"/index/personal"})};return x(()=>{v();let s=p.info.userOrgInfoList.filter(r=>r.currentSelectFlag);s.length===1&&(l.value=s[0],u.value=l.value.orgId)}),(i,s)=>{const r=V,m=L;return d(),C("div",z,[e("div",A,[s[1]||(s[1]=e("div",{class:"user-info-card-header-title"},[e("span",null,"\u4E2A\u4EBA\u4FE1\u606F")],-1)),e("div",{class:"user-info-card-header-icon",onClick:h},[c(k,{"font-size":"34px",color:"#2a82e4","icon-class":"icon-opt-bianji",title:"\u7F16\u8F91","font-weight":"bold"})])]),e("div",D,[e("div",F,[c(r,{size:110,src:t.value.avatarWrapper},null,8,["src"])]),e("div",P,[e("div",j,[e("div",E,[e("span",null,"\u59D3\u540D\uFF1A"+o(t.value.realName),1)]),e("div",J,[e("span",null,"\u8D26\u53F7\uFF1A"+o(t.value.account),1)]),e("div",K,[e("span",null,"\u516C\u53F8\uFF1A"+o(l.value.companyName),1),c(m,{type:"link",onClick:f},{default:w(()=>s[2]||(s[2]=[B("\u5207\u6362\u5F53\u524D\u516C\u53F8")])),_:1,__:[2]})]),e("div",M,[e("span",null,"\u90AE\u7BB1\uFF1A"+o(t.value.email),1)]),e("div",R,[e("span",null,"\u7535\u8BDD\uFF1A"+o(t.value.phone),1)])])])]),a.value?(d(),N(O,{key:0,visible:a.value,"onUpdate:visible":s[0]||(s[0]=g=>a.value=g),selectOrgId:u.value},null,8,["visible","selectOrgId"])):S("",!0)])}}},q=I(T,[["__scopeId","data-v-724041dd"]]);export{q as default};
