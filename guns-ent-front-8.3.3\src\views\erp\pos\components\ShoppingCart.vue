<template>
  <div class="shopping-cart">
    <!-- 购物车商品数量显示 -->
    <div class="cart-header" v-if="hasCartItems">
      <div class="cart-info">
        <div class="cart-count">
          <span class="item-count">
            <span class="count-number">{{ cartItemCount }}</span>
            <span class="count-text">件商品</span>
          </span>
        </div>
        <a-button
          size="small"
          danger
          @click="clearAllItems"
          class="clear-all-btn"
        >
          <template #icon>
            <delete-outlined />
          </template>
          清空
        </a-button>
      </div>
    </div>

    <!-- 购物车列表 -->
    <div class="cart-content">
      <!-- 商品列表 -->
      <div class="cart-items" v-if="hasCartItems">
        <div
          v-for="item in cartItems"
          :key="item.productId"
          class="cart-item"
        >
          <!-- 第一行：商品名称 + 删除按钮 -->
          <div class="item-row item-row-1">
            <div class="item-name" :title="item.productName">
              {{ item.productName }}
            </div>
            <a-button
              type="primary"
              size="small"
              danger
              @click="removeItem(item)"
              class="remove-btn"
              title="删除商品"
            >
              <template #icon>
                <delete-outlined />
              </template>
            </a-button>
          </div>

          <!-- 第二行：单价 + 数量控制 -->
          <div class="item-row item-row-2">
            <div class="item-unit-price">
              ￥{{ formatPrice(getItemPrice(item)) }}/{{ item.unit || '件' }}
            </div>
            <div class="quantity-control">
              <a-button
                size="small"
                @click="decreaseQuantity(item)"
                :disabled="item.quantity <= 1"
                class="quantity-btn"
              >
                <template #icon>
                  <minus-outlined />
                </template>
              </a-button>

              <a-input-number
                v-model:value="item.quantity"
                :min="1"
                v-bind="getQuantityConfig(item)"
                size="small"
                class="quantity-input"
                @change="(value) => updateQuantity(item, value)"
              />

              <a-button
                size="small"
                @click="increaseQuantity(item)"
                :disabled="!canIncreaseQuantity(item)"
                class="quantity-btn"
              >
                <template #icon>
                  <plus-outlined />
                </template>
              </a-button>
            </div>
          </div>

          <!-- 第三行：商品编码 + 小计金额 -->
          <div class="item-row item-row-3">
            <div class="item-code" v-if="item.productCode">
              编码: {{ item.productCode }}
            </div>
            <div class="item-total">
              ￥{{ formatPrice(item.totalPrice) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 空购物车状态 -->
      <div v-else class="empty-cart">
        <a-empty 
          description="购物车为空"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        >
          <p class="empty-tip">请选择商品添加到购物车</p>
        </a-empty>
      </div>
    </div>

    <!-- 购物车底部汇总 -->
    <div class="cart-footer" v-if="hasCartItems">
      <!-- 会员信息 -->
      <div class="member-info" v-if="currentMember">
        <div class="member-card">
          <icon-font iconClass="icon-member" />
          <span class="member-name">{{ currentMember.memberName }}</span>
          <span class="member-level">{{ currentMember.levelName }}</span>
        </div>
        <div class="member-discount" v-if="memberDiscountRate > 0">
          会员折扣: {{ (memberDiscountRate * 100).toFixed(1) }}%
        </div>
      </div>

      <!-- 金额汇总 -->
      <div class="amount-summary">
        <div class="summary-row">
          <span class="label">商品总额:</span>
          <span class="value">￥{{ formatPrice(totalAmount) }}</span>
        </div>
        <div class="summary-row" v-if="discountAmount > 0">
          <span class="label">优惠金额:</span>
          <span class="value discount">-￥{{ formatPrice(discountAmount) }}</span>
        </div>
        <div class="summary-row" v-if="pointsDeductionAmount > 0">
          <span class="label">积分抵扣:</span>
          <span class="value discount">-￥{{ formatPrice(pointsDeductionAmount) }}</span>
        </div>
        <div class="summary-row total">
          <span class="label">应付金额:</span>
          <span class="value">￥{{ formatPrice(finalAmount) }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="cart-actions">
        <!-- 挂单按钮 -->
        <a-button
          size="large"
          @click="suspendOrder"
          :disabled="!hasCartItems"
          class="action-btn suspend-btn"
        >
          挂单
        </a-button>

        <!-- 立即结算按钮 -->
        <a-button
          type="primary"
          size="large"
          @click="checkout"
          :disabled="!canCheckout"
          class="action-btn checkout-btn"
        >
          立即结算
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { message, Modal, Empty } from 'ant-design-vue'
import {
  DeleteOutlined,
  MinusOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import { usePosStore } from '@/stores/pos'

// 导入样式文件
import '../styles/common.css'
import '../styles/shopping-cart.css'

// 定义组件名称
defineOptions({
  name: 'ShoppingCart'
})

// 定义事件
const emit = defineEmits(['checkout', 'itemChange'])

// 使用POS状态管理
const posStore = usePosStore()

// 计算属性
const cartItems = computed(() => posStore.cartItems)
const cartItemCount = computed(() => posStore.cartItemCount)
const hasCartItems = computed(() => posStore.hasCartItems)
const canCheckout = computed(() => posStore.canCheckout)
const totalAmount = computed(() => posStore.totalAmount)
const discountAmount = computed(() => posStore.discountAmount)
const finalAmount = computed(() => posStore.finalAmount)
const currentMember = computed(() => posStore.currentMember)
const memberDiscountRate = computed(() => posStore.memberDiscountRate)
const pointsDeductionAmount = computed(() => posStore.pointsDeductionAmount)

/**
 * 获取商品价格 - 根据计价类型返回正确的价格
 */
const getItemPrice = (item) => {
  // 优先使用购物车中的实际单价
  if (item.unitPrice != null && item.unitPrice !== 0) {
    return item.unitPrice
  }

  // 根据计价类型获取价格
  const priceMap = {
    NORMAL: item.retailPrice,
    WEIGHT: item.unitPrice,
    PIECE: item.piecePrice,
    VARIABLE: item.referencePrice
  }

  return priceMap[item.pricingType] || item.retailPrice || item.unitPrice || 0
}

/**
 * 格式化价格显示
 */
const formatPrice = (price) => {
  return (price == null || isNaN(price)) ? '0.00' : Number(price).toFixed(2)
}

/**
 * 商品数量相关工具函数
 */
const getQuantityConfig = (item) => {
  const isWeight = item.pricingType === 'WEIGHT'
  return {
    max: item.stockQuantity ?? 999,
    step: isWeight ? 0.001 : 1,
    precision: isWeight ? 3 : 0
  }
}

/**
 * 检查是否可以增加数量
 */
const canIncreaseQuantity = (item) => {
  if (item.stockQuantity == null) return true
  return item.quantity < item.stockQuantity
}

/**
 * 增加商品数量
 */
const increaseQuantity = (item) => {
  if (canIncreaseQuantity(item)) {
    const { step } = getQuantityConfig(item)
    const newQuantity = item.quantity + step
    updateQuantity(item, newQuantity)
  }
}

/**
 * 减少商品数量
 */
const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    const { step } = getQuantityConfig(item)
    const newQuantity = Math.max(1, item.quantity - step)
    updateQuantity(item, newQuantity)
  }
}

/**
 * 更新商品数量
 */
const updateQuantity = (item, quantity) => {
  if (quantity <= 0) {
    removeItem(item)
    return
  }

  // 检查库存限制
  const { max } = getQuantityConfig(item)
  if (max < 999 && quantity > max) {
    message.warning(`库存不足，最多只能添加 ${max} ${item.unit || '件'}`)
    return
  }

  posStore.updateQuantity(item.productId, quantity)
  emit('itemChange', { action: 'update', item, quantity })
}

/**
 * 移除商品
 */
const removeItem = (item) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要从购物车中移除 "${item.productName}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      posStore.removeFromCart(item.productId)
      emit('itemChange', { action: 'remove', item })
    }
  })
}

/**
 * 清空购物车
 */
const clearCart = (showMessage = false) => {
  Modal.confirm({
    title: '确认清空',
    content: '确定要清空购物车中的所有商品吗？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      posStore.clearCart()
      if (showMessage) {
        message.success('购物车已清空')
      }
      emit('itemChange', { action: 'clear' })
    }
  })
}

// 清空全部商品（顶部按钮）
const clearAllItems = () => clearCart(true)

/**
 * 挂单功能
 */
const suspendOrder = () => {
  if (!hasCartItems.value) {
    message.warning('购物车为空，无法挂单')
    return
  }

  Modal.confirm({
    title: '确认挂单',
    content: '确定要将当前订单挂起吗？挂起后可以在挂单列表中恢复。',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      try {
        // 调用POS store的挂单方法
        posStore.suspendCurrentOrder()
        message.success('订单已挂起')
        emit('itemChange', { action: 'suspend' })
      } catch (error) {
        console.error('挂单失败:', error)
        message.error('挂单失败，请重试')
      }
    }
  })
}



/**
 * 结算
 */
const checkout = () => {
  if (!canCheckout.value) {
    message.warning('购物车为空，无法结算')
    return
  }
  
  emit('checkout')
}

// 暴露方法给父组件
defineExpose({
  clearCart,
  checkout
})
</script>