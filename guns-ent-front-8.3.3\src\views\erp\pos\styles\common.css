/* POS系统通用样式 */

/* 解决guns-admin-body容器的自适应布局问题 */
:deep(.guns-admin-body) {
  height: 100vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

:deep(.guns-admin-content) {
  flex: 1 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

:deep(.guns-admin-content-view) {
  flex: 1 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* POS主容器样式 */
.pos-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.pos-main.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

/* 工具栏样式 */
.pos-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.pos-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.logo-text {
  font-size: 16px;
}

.cashier-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

/* 内容区域样式 */
.pos-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
  min-height: 0;
}

.pos-left {
  flex: 3;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.pos-center {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.cart-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.pos-right {
  width: 120px;
  flex-shrink: 0;
}

.pos-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 状态栏样式 */
.pos-statusbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 按钮样式 */
.toolbar-btn {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.toolbar-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.logout-btn {
  border-radius: 6px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pos-content {
    gap: 12px;
    padding: 12px;
  }
  
  .pos-right {
    width: 100px;
  }
}

@media (max-width: 768px) {
  .pos-content {
    flex-direction: column;
  }
  
  .pos-left,
  .pos-center,
  .pos-right {
    flex: none;
    width: 100%;
  }
  
  .pos-right {
    height: 80px;
  }
}

/* 动画效果 */
@keyframes pos-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pos-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 工具类 */
.pos-fade-in {
  animation: pos-fade-in 0.3s ease-out;
}

.pos-slide-in {
  animation: pos-slide-in-right 0.3s ease-out;
}

/* 滚动条样式优化 */
.pos-content::-webkit-scrollbar,
.cart-content::-webkit-scrollbar {
  width: 6px;
}

.pos-content::-webkit-scrollbar-track,
.cart-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.pos-content::-webkit-scrollbar-thumb,
.cart-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.pos-content::-webkit-scrollbar-thumb:hover,
.cart-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
