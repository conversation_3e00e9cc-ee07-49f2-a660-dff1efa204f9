import{_ as I,r as u,o as O,X as R,a as c,f as d,w as a,b as n,d as o,g as z,t as i,aR as h,c as y,F as w,e as C,aS as k,bK as A,u as E,v as G,G as H,H as M,ch as P}from"./index-18a1ea24.js";import{T as U}from"./TenantApi-e23e3174.js";const X={class:"top"},$={class:"username"},q={class:"content"},J={class:"content-item"},Q={class:"content-item"},W={__name:"tenant-detail",props:{visible:Boolean,data:Object},emits:["update:visible","done"],setup(x,{emit:T}){const m=x,D=T,_=u("1"),B=u([{key:"1",name:"\u57FA\u7840\u4FE1\u606F",icon:"icon-tab-baseinfo"},{key:"2",name:"\u7EDF\u8BA1\u4FE1\u606F",icon:"icon-menu-zuzhijiagou"}]),t=u({}),K=u([{name:"\u79DF\u6237\u540D\u79F0",value:"tenantName"},{name:"\u79DF\u6237\u7F16\u7801",value:"tenantCode"},{name:"\u6CE8\u518C\u90AE\u7BB1",value:"email"},{name:"\u6CE8\u518C\u7535\u8BDD",value:"safePhone"},{name:"\u6027\u522B",value:"sex"},{name:"\u79DF\u6237\u5F00\u901A\u65F6\u95F4",value:"activeDate"},{name:"\u79DF\u6237\u5230\u671F\u65F6\u95F4",value:"expireDate"},{name:"\u521B\u5EFA\u65F6\u95F4",value:"createTime"},{name:"\u4E0A\u6B21\u66F4\u65B0\u65F6\u95F4",value:"updateTime"}]),L=u([{name:"\u79DF\u6237\u7528\u6237\u6570",value:"tenantUserCount"},{name:"\u79DF\u6237\u4E0B\u516C\u53F8\u6570\u91CF",value:"tenantOrgCount"}]);O(()=>{v()}),R(()=>m.data,e=>{e&&v()},{deep:!0});const v=()=>{U.detail({tenantId:m.data.tenantId}).then(e=>{t.value=Object.assign({},e)})},N=e=>{D("update:visible",e)},S=e=>{_.value=e},V=e=>{let l=e;return e&&e.length>1&&(l=e.substr(0,1)),l};return(e,l)=>{const j=A,p=E,f=G,b=H,g=M,F=P;return c(),d(F,{width:800,visible:m.visible,title:"\u79DF\u6237\u4FE1\u606F",onClose:l[0]||(l[0]=s=>N(!1)),isShowTab:!0,activeKey:_.value,tabList:B.value,onTabChange:S},{top:a(()=>[n("div",X,[o(j,{style:{"background-color":"#6f9ae7"}},{default:a(()=>[z(i(V(t.value.tenantName)),1)]),_:1}),n("span",$,i(t.value.tenantName),1)])]),default:a(()=>[n("div",q,[h(n("div",J,[o(g,{ref:"formRef",model:t.value,"label-col":{span:8}},{default:a(()=>[o(b,{gutter:16},{default:a(()=>[(c(!0),y(w,null,C(K.value,(s,r)=>(c(),d(f,{span:12,key:r},{default:a(()=>[o(p,{label:s.name},{default:a(()=>[n("span",null,i(t.value[s.value]),1)]),_:2},1032,["label"])]),_:2},1024))),128))]),_:1})]),_:1},8,["model"])],512),[[k,_.value=="1"]]),h(n("div",Q,[o(g,{ref:"formRef",model:t.value,"label-col":{span:8}},{default:a(()=>[o(b,{gutter:16},{default:a(()=>[(c(!0),y(w,null,C(L.value,(s,r)=>(c(),d(f,{span:12,key:r},{default:a(()=>[o(p,{label:s.name},{default:a(()=>[n("span",null,i(t.value[s.value]),1)]),_:2},1032,["label"])]),_:2},1024))),128))]),_:1})]),_:1},8,["model"])],512),[[k,_.value=="2"]])])]),_:1},8,["visible","activeKey","tabList"])}}},ee=I(W,[["__scopeId","data-v-a75d6508"]]);export{ee as default};
