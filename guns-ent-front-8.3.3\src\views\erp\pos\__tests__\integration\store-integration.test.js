/**
 * 状态管理集成测试
 * 
 * 测试Pinia状态管理与组件和API的集成
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { usePosStore } from '@/stores/pos'
import { useCart } from '../../composables/useCart'
import { useMember } from '../../composables/useMember'
import { usePayment } from '../../composables/usePayment'
import { useOrder } from '../../composables/useOrder'

// Mock APIs
vi.mock('../../api/cart', () => ({
  CartApi: {
    checkInventory: vi.fn().mockResolvedValue({ available: true, stock: 100 }),
    validateCart: vi.fn().mockResolvedValue({ valid: true, errors: [] })
  }
}))

vi.mock('../../api/member', () => ({
  MemberApi: {
    searchMember: vi.fn(),
    updateMemberPoints: vi.fn()
  }
}))

vi.mock('../../api/payment', () => ({
  PaymentApi: {
    processCashPayment: vi.fn(),
    processQrCodePayment: vi.fn()
  }
}))

vi.mock('../../api/order', () => ({
  OrderApi: {
    createOrder: vi.fn(),
    generateOrderNo: vi.fn().mockResolvedValue({ orderNo: 'POS20250102001' })
  }
}))

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('状态管理集成测试', () => {
  let pinia
  let store
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = usePosStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('购物车状态集成', () => {
    it('应该正确管理购物车状态变化', async () => {
      const { addItem, updateQuantity, removeItem, clearCart } = useCart()
      
      // 初始状态
      expect(store.cartItems).toHaveLength(0)
      expect(store.totalAmount).toBe(0)
      
      // 添加商品
      const product1 = {
        id: 'P001',
        name: '红苹果',
        price: 5.5,
        unit: '斤'
      }
      
      await addItem(product1, 2)
      
      expect(store.cartItems).toHaveLength(1)
      expect(store.cartItems[0]).toMatchObject({
        id: 'P001',
        name: '红苹果',
        price: 5.5,
        quantity: 2,
        subtotal: 11
      })
      expect(store.totalAmount).toBe(11)
      
      // 添加另一个商品
      const product2 = {
        id: 'P002',
        name: '香蕉',
        price: 3.0,
        unit: '斤'
      }
      
      await addItem(product2, 1)
      
      expect(store.cartItems).toHaveLength(2)
      expect(store.totalAmount).toBe(14)
      
      // 更新商品数量
      await updateQuantity('P001', 3)
      
      expect(store.cartItems[0].quantity).toBe(3)
      expect(store.cartItems[0].subtotal).toBe(16.5)
      expect(store.totalAmount).toBe(19.5)
      
      // 移除商品
      await removeItem('P002')
      
      expect(store.cartItems).toHaveLength(1)
      expect(store.totalAmount).toBe(16.5)
      
      // 清空购物车
      await clearCart()
      
      expect(store.cartItems).toHaveLength(0)
      expect(store.totalAmount).toBe(0)
    })
    
    it('应该正确处理相同商品的合并', async () => {
      const { addItem } = useCart()
      
      const product = {
        id: 'P001',
        name: '红苹果',
        price: 5.5
      }
      
      // 第一次添加
      await addItem(product, 2)
      expect(store.cartItems).toHaveLength(1)
      expect(store.cartItems[0].quantity).toBe(2)
      
      // 再次添加相同商品
      await addItem(product, 3)
      expect(store.cartItems).toHaveLength(1)
      expect(store.cartItems[0].quantity).toBe(5)
      expect(store.cartItems[0].subtotal).toBe(27.5)
    })
  })
  
  describe('会员状态集成', () => {
    it('应该正确管理会员状态和折扣计算', async () => {
      const { searchMember, selectMember, clearMember, calculateMemberDiscount } = useMember()
      const { addItem } = useCart()
      
      // 先添加商品
      const product = {
        id: 'P001',
        name: '红苹果',
        price: 10
      }
      
      await addItem(product, 2)
      expect(store.totalAmount).toBe(20)
      expect(store.discountAmount).toBe(0)
      expect(store.finalAmount).toBe(20)
      
      // 选择会员
      const member = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        discountRate: 0.1,
        points: 1000,
        balance: 500
      }
      
      await selectMember(member)
      
      expect(store.currentMember).toEqual(member)
      
      // 计算会员折扣
      const discountResult = await calculateMemberDiscount(20)
      
      expect(discountResult.discountAmount).toBe(2)
      expect(discountResult.finalAmount).toBe(18)
      
      // 验证store状态更新
      expect(store.discountAmount).toBe(2)
      expect(store.finalAmount).toBe(18)
      
      // 清除会员
      await clearMember()
      
      expect(store.currentMember).toBe(null)
      expect(store.discountAmount).toBe(0)
      expect(store.finalAmount).toBe(20)
    })
    
    it('应该正确处理会员积分抵扣', async () => {
      const { selectMember, calculatePointsDeduction } = useMember()
      const { addItem } = useCart()
      
      // 添加商品
      await addItem({
        id: 'P001',
        name: '商品',
        price: 10
      }, 1)
      
      // 选择会员
      const member = {
        id: 'M001',
        points: 500,
        allowPointsPayment: true
      }
      
      await selectMember(member)
      
      // 使用积分抵扣
      const pointsResult = await calculatePointsDeduction(200, 10) // 使用200积分，订单10元
      
      expect(pointsResult.pointsUsed).toBe(200)
      expect(pointsResult.deductionAmount).toBe(2) // 200积分=2元
      expect(pointsResult.remainingAmount).toBe(8) // 10-2=8
      
      // 验证store状态
      expect(store.pointsDeduction).toBe(2)
      expect(store.finalAmount).toBe(8)
    })
  })
  
  describe('支付状态集成', () => {
    it('应该正确管理支付流程状态', async () => {
      const { processCashPayment, setPaymentMethod, setReceivedAmount } = usePayment()
      const { addItem } = useCart()
      const { createOrder } = useOrder()
      
      // 添加商品
      await addItem({
        id: 'P001',
        name: '商品',
        price: 10
      }, 1)
      
      // 创建订单
      const { OrderApi } = await import('../../api/order')
      OrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER001',
        orderNo: 'POS20250102001'
      })
      
      const orderResult = await createOrder()
      expect(store.currentOrder).toMatchObject({
        orderId: 'ORDER001',
        orderNo: 'POS20250102001'
      })
      
      // 设置支付方式
      setPaymentMethod('CASH')
      expect(store.selectedPaymentMethod).toBe('CASH')
      
      // 设置实收金额
      setReceivedAmount(15)
      expect(store.receivedAmount).toBe(15)
      expect(store.changeAmount).toBe(5) // 15-10=5
      
      // 处理支付
      const { PaymentApi } = await import('../../api/payment')
      PaymentApi.processCashPayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY001',
        status: 'SUCCESS'
      })
      
      const paymentResult = await processCashPayment({
        orderId: 'ORDER001',
        paymentAmount: 10,
        receivedAmount: 15
      })
      
      expect(paymentResult.success).toBe(true)
      expect(store.paymentStatus).toBe('success')
      
      // 支付完成后，购物车应该被清空
      expect(store.cartItems).toHaveLength(0)
      expect(store.totalAmount).toBe(0)
    })
    
    it('应该正确处理扫码支付状态轮询', async () => {
      const { processQrCodePayment, queryPaymentStatus } = usePayment()
      
      // 发起扫码支付
      const { PaymentApi } = await import('../../api/payment')
      PaymentApi.processQrCodePayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY002',
        qrCodeUrl: 'https://qr.weixin.com/test',
        status: 'PENDING'
      })
      
      const paymentResult = await processQrCodePayment({
        orderId: 'ORDER002',
        paymentAmount: 50,
        paymentMethod: 'WECHAT'
      })
      
      expect(store.paymentStatus).toBe('pending')
      expect(store.qrCodeUrl).toBe('https://qr.weixin.com/test')
      
      // 查询支付状态 - 仍在等待
      PaymentApi.queryQrCodePaymentStatus = vi.fn().mockResolvedValue({
        paymentId: 'PAY002',
        status: 'PENDING'
      })
      
      let statusResult = await queryPaymentStatus({ paymentId: 'PAY002' })
      expect(statusResult.status).toBe('PENDING')
      expect(store.paymentStatus).toBe('pending')
      
      // 查询支付状态 - 支付成功
      PaymentApi.queryQrCodePaymentStatus.mockResolvedValue({
        paymentId: 'PAY002',
        status: 'SUCCESS',
        transactionId: 'WX_TXN_001'
      })
      
      statusResult = await queryPaymentStatus({ paymentId: 'PAY002' })
      expect(statusResult.status).toBe('SUCCESS')
      expect(store.paymentStatus).toBe('success')
    })
  })
  
  describe('订单状态集成', () => {
    it('应该正确管理订单创建和状态更新', async () => {
      const { createOrder, updateOrderStatus } = useOrder()
      const { addItem } = useCart()
      const { selectMember } = useMember()
      
      // 准备数据
      await addItem({
        id: 'P001',
        name: '商品',
        price: 15
      }, 2)
      
      await selectMember({
        id: 'M001',
        discountRate: 0.1
      })
      
      // 创建订单
      const { OrderApi } = await import('../../api/order')
      OrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER003',
        orderNo: 'POS20250102003',
        status: 'PENDING'
      })
      
      const orderResult = await createOrder({ remark: '测试订单' })
      
      expect(orderResult.success).toBe(true)
      expect(store.currentOrder).toMatchObject({
        orderId: 'ORDER003',
        orderNo: 'POS20250102003',
        status: 'PENDING'
      })
      
      // 更新订单状态
      OrderApi.updateOrderStatus = vi.fn().mockResolvedValue({
        success: true,
        orderId: 'ORDER003',
        status: 'PAID',
        updatedAt: '2025-01-02T10:30:00Z'
      })
      
      const updateResult = await updateOrderStatus({
        orderId: 'ORDER003',
        status: 'PAID',
        remark: '支付完成'
      })
      
      expect(updateResult.success).toBe(true)
      expect(store.currentOrder.status).toBe('PAID')
    })
  })
  
  describe('复合状态变化', () => {
    it('应该正确处理会员变更对购物车金额的影响', async () => {
      const { addItem } = useCart()
      const { selectMember, clearMember } = useMember()
      
      // 添加商品
      await addItem({
        id: 'P001',
        name: '商品',
        price: 20
      }, 1)
      
      expect(store.totalAmount).toBe(20)
      expect(store.finalAmount).toBe(20)
      
      // 选择会员 - 9折
      await selectMember({
        id: 'M001',
        discountRate: 0.1
      })
      
      expect(store.discountAmount).toBe(2)
      expect(store.finalAmount).toBe(18)
      
      // 更换会员 - 8折
      await selectMember({
        id: 'M002',
        discountRate: 0.2
      })
      
      expect(store.discountAmount).toBe(4)
      expect(store.finalAmount).toBe(16)
      
      // 清除会员
      await clearMember()
      
      expect(store.discountAmount).toBe(0)
      expect(store.finalAmount).toBe(20)
    })
    
    it('应该正确处理购物车变更对支付金额的影响', async () => {
      const { addItem, updateQuantity } = useCart()
      const { setReceivedAmount } = usePayment()
      
      // 添加商品
      await addItem({
        id: 'P001',
        name: '商品',
        price: 10
      }, 1)
      
      // 设置实收金额
      setReceivedAmount(15)
      expect(store.changeAmount).toBe(5) // 15-10=5
      
      // 增加商品数量
      await updateQuantity('P001', 2)
      expect(store.totalAmount).toBe(20)
      expect(store.changeAmount).toBe(-5) // 15-20=-5 (不足)
      
      // 调整实收金额
      setReceivedAmount(25)
      expect(store.changeAmount).toBe(5) // 25-20=5
    })
  })
  
  describe('状态持久化', () => {
    it('应该正确保存和恢复购物车状态', async () => {
      const { addItem } = useCart()
      
      // 添加商品
      await addItem({
        id: 'P001',
        name: '商品',
        price: 10
      }, 2)
      
      // 保存状态
      const savedState = store.$state
      expect(savedState.cartItems).toHaveLength(1)
      expect(savedState.totalAmount).toBe(20)
      
      // 模拟页面刷新，重新创建store
      const newPinia = createPinia()
      setActivePinia(newPinia)
      const newStore = usePosStore()
      
      // 恢复状态
      newStore.$patch(savedState)
      
      expect(newStore.cartItems).toHaveLength(1)
      expect(newStore.totalAmount).toBe(20)
      expect(newStore.cartItems[0]).toMatchObject({
        id: 'P001',
        name: '商品',
        price: 10,
        quantity: 2
      })
    })
    
    it('应该正确处理状态重置', async () => {
      const { addItem } = useCart()
      const { selectMember } = useMember()
      
      // 设置一些状态
      await addItem({
        id: 'P001',
        name: '商品',
        price: 10
      }, 1)
      
      await selectMember({
        id: 'M001',
        discountRate: 0.1
      })
      
      expect(store.cartItems).toHaveLength(1)
      expect(store.currentMember).not.toBe(null)
      expect(store.totalAmount).toBe(10)
      
      // 重置状态
      store.$reset()
      
      expect(store.cartItems).toHaveLength(0)
      expect(store.currentMember).toBe(null)
      expect(store.totalAmount).toBe(0)
      expect(store.discountAmount).toBe(0)
      expect(store.finalAmount).toBe(0)
    })
  })
  
  describe('状态计算属性', () => {
    it('应该正确计算派生状态', async () => {
      const { addItem } = useCart()
      const { selectMember } = useMember()
      
      // 初始状态
      expect(store.isEmpty).toBe(true)
      expect(store.itemCount).toBe(0)
      expect(store.canCheckout).toBe(false)
      
      // 添加商品
      await addItem({
        id: 'P001',
        name: '商品A',
        price: 10
      }, 2)
      
      await addItem({
        id: 'P002',
        name: '商品B',
        price: 5
      }, 3)
      
      expect(store.isEmpty).toBe(false)
      expect(store.itemCount).toBe(2) // 2种商品
      expect(store.totalQuantity).toBe(5) // 总数量2+3=5
      expect(store.canCheckout).toBe(true)
      
      // 选择会员
      await selectMember({
        id: 'M001',
        name: '张三',
        discountRate: 0.1
      })
      
      expect(store.hasMember).toBe(true)
      expect(store.memberName).toBe('张三')
      expect(store.memberDiscountRate).toBe(0.1)
      
      // 验证金额计算
      expect(store.totalAmount).toBe(35) // 10*2 + 5*3 = 35
      expect(store.discountAmount).toBe(3.5) // 35 * 0.1 = 3.5
      expect(store.finalAmount).toBe(31.5) // 35 - 3.5 = 31.5
    })
  })
})