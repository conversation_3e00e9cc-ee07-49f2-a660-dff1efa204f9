/* 产品展示区域样式 */

/* 产品展示主容器 */
.product-display-area {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 分类导航区域 */
.category-section {
  height: 60px;
  flex-shrink: 0;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 16px 12px 16px;
  overflow: visible;
}

.category-tabs {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.pos-category-tabs {
  height: 100%;
  width: 100%;
}

.pos-category-tabs .ant-tabs-content-holder {
  display: none;
}

.pos-category-tabs .ant-tabs-nav {
  margin-bottom: 0;
  padding-bottom: 12px;
  width: 100%;
}

.pos-category-tabs .ant-tabs-nav-wrap {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
  padding-bottom: 4px;
  width: 100%;
}

.pos-category-tabs .ant-tabs-nav-list {
  display: flex;
  flex-wrap: nowrap;
  min-width: max-content;
}

/* 自定义滚动条样式 */
.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar {
  height: 6px;
}

.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
  margin: 0 8px;
}

.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  border: 1px solid #f5f5f5;
}

.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.pos-category-tabs .ant-tabs-tab {
  padding: 8px 16px !important;
  margin: 0 4px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  border: 1px solid #e8e8e8 !important;
  background: #fff !important;
  color: #595959 !important;
  min-width: 80px !important;
  text-align: center !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.pos-category-tabs .ant-tabs-tab:hover {
  background: #f0f9ff !important;
  color: #1890ff !important;
  border-color: #91d5ff !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.15) !important;
}

.pos-category-tabs .ant-tabs-tab-active {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: #fff !important;
  border-color: #1890ff !important;
  font-weight: 600 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(24, 144, 255, 0.3) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.pos-category-tabs .ant-tabs-tab-active:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%) !important;
  color: #fff !important;
  border-color: #40a9ff !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4) !important;
}

/* 确保选中状态的文字始终可见 */
.pos-category-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff !important;
  font-weight: 600 !important;
}

.pos-category-tabs .ant-tabs-tab-active .ant-tabs-tab-btn:hover {
  color: #fff !important;
}

/* 搜索过滤区域 */
.search-filter-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.search-filter-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-input-wrapper {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-select {
  min-width: 120px;
}

/* 重置按钮样式优化 */
.reset-btn {
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #595959;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 60px;
  justify-content: center;
}

.reset-btn:hover {
  background: #f5f5f5;
  border-color: #40a9ff;
  color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(64, 169, 255, 0.1);
}

.reset-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(64, 169, 255, 0.1);
}

.reset-btn .anticon {
  font-size: 12px;
}

.view-toggle {
  display: flex;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
}

.view-toggle-btn {
  padding: 6px 12px;
  border: none;
  background: #fff;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-toggle-btn:hover {
  background: #f5f5f5;
  color: #1890ff;
}

.view-toggle-btn.active {
  background: #1890ff;
  color: #fff;
}

/* 产品网格区域 */
.product-grid-section {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding-bottom: 20px;
}

/* 产品卡片 */
.product-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.product-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.product-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card.disabled:hover {
  border-color: #f0f0f0;
  box-shadow: none;
  transform: none;
}

.product-status {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  color: #fff;
  background: #ff4d4f;
}

.product-image {
  width: 100%;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #bfbfbf;
  font-size: 24px;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-code {
  font-size: 11px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: 2px;
  margin-top: 4px;
}

.price-symbol {
  font-size: 12px;
  color: #ff4d4f;
  font-weight: 500;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
  font-family: 'Courier New', monospace;
}

.price-unit {
  font-size: 11px;
  color: #8c8c8c;
  margin-left: 2px;
}

.product-stock {
  font-size: 11px;
  color: #666;
  margin-top: 4px;
}

.stock-low {
  color: #ff4d4f;
  font-weight: 500;
}

.stock-out {
  color: #ff4d4f;
  font-weight: 600;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #8c8c8c;
}

/* 空状态 */
.empty-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #8c8c8c;
}

.empty-products .ant-empty-description {
  color: #bfbfbf;
  font-size: 14px;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 1024px) {
  .pos-category-tabs .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 13px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }

  .product-image {
    height: 100px;
  }
}

@media (max-width: 768px) {
  .category-section {
    padding: 6px 12px 8px 12px;
  }

  .search-filter-section {
    padding: 8px 12px;
  }

  .search-filter-row {
    gap: 8px;
  }

  .filter-controls {
    gap: 6px;
  }

  .product-grid-section {
    padding: 12px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  .product-card {
    padding: 8px;
  }

  .product-image {
    height: 80px;
    margin-bottom: 6px;
  }

  .product-name {
    font-size: 13px;
  }

  .price-value {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .category-section {
    height: auto;
    min-height: 40px;
  }

  .pos-category-tabs .ant-tabs-tab {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 60px;
  }

  .pos-category-tabs .ant-tabs-nav {
    margin-bottom: 0;
  }

  .search-filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input-wrapper {
    min-width: auto;
  }

  .filter-controls {
    justify-content: space-between;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 6px;
  }

  .product-card {
    padding: 6px;
  }

  .product-image {
    height: 60px;
    margin-bottom: 4px;
  }

  .product-name {
    font-size: 12px;
  }

  .product-code {
    font-size: 10px;
  }

  .price-value {
    font-size: 13px;
  }

  .price-symbol,
  .price-unit {
    font-size: 10px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .product-card:hover {
    border-color: #f0f0f0;
    box-shadow: none;
    transform: none;
  }

  .view-toggle-btn:hover {
    background: #fff;
    color: #666;
  }

  .view-toggle-btn.active:hover {
    background: #1890ff;
    color: #fff;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .product-card {
    border: 1px solid #d9d9d9;
  }

  .search-filter-section {
    border-bottom: 1px solid #d9d9d9;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .product-card,
  .view-toggle-btn,
  .pos-category-tabs .ant-tabs-tab {
    transition: none;
  }

  .product-card:hover {
    transform: none;
  }
}
