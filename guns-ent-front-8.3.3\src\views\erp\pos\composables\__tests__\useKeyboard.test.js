/**
 * 键盘快捷键组合式函数单元测试
 * 
 * 测试键盘快捷键功能的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useKeyboard } from '../useKeyboard'

// Mock Vue
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    onMounted: vi.fn(fn => fn()),
    onUnmounted: vi.fn()
  }
})

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    info: vi.fn(),
    success: vi.fn()
  }
}))

describe('useKeyboard', () => {
  let mockElement
  
  beforeEach(() => {
    // Mock DOM element
    mockElement = {
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      focus: vi.fn(),
      blur: vi.fn()
    }
    
    // Mock document
    Object.defineProperty(global, 'document', {
      value: {
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        getElementById: vi.fn(() => mockElement),
        querySelector: vi.fn(() => mockElement),
        body: mockElement
      },
      writable: true
    })
    
    // Mock window
    Object.defineProperty(global, 'window', {
      value: {
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      },
      writable: true
    })
    
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('基础功能', () => {
    it('应该返回所有必要的状态和方法', () => {
      const keyboard = useKeyboard()
      
      expect(keyboard.shortcuts).toBeDefined()
      expect(keyboard.isEnabled).toBeDefined()
      expect(keyboard.currentModifiers).toBeDefined()
      expect(keyboard.pressedKeys).toBeDefined()
      
      expect(keyboard.registerShortcut).toBeDefined()
      expect(keyboard.unregisterShortcut).toBeDefined()
      expect(keyboard.enableShortcuts).toBeDefined()
      expect(keyboard.disableShortcuts).toBeDefined()
      expect(keyboard.triggerShortcut).toBeDefined()
      expect(keyboard.getShortcutInfo).toBeDefined()
      expect(keyboard.resetShortcuts).toBeDefined()
    })
    
    it('应该正确初始化状态', () => {
      const { isEnabled, shortcuts, currentModifiers, pressedKeys } = useKeyboard()
      
      expect(isEnabled.value).toBe(true)
      expect(shortcuts.value).toEqual({})
      expect(currentModifiers.value).toEqual({
        ctrl: false,
        alt: false,
        shift: false,
        meta: false
      })
      expect(pressedKeys.value).toEqual(new Set())
    })
  })
  
  describe('registerShortcut', () => {
    it('应该成功注册快捷键', () => {
      const { registerShortcut, shortcuts } = useKeyboard()
      
      const callback = vi.fn()
      const result = registerShortcut('F1', callback, { description: '帮助' })
      
      expect(result).toBe(true)
      expect(shortcuts.value['F1']).toEqual({
        key: 'F1',
        callback,
        description: '帮助',
        modifiers: {
          ctrl: false,
          alt: false,
          shift: false,
          meta: false
        },
        enabled: true,
        preventDefault: true,
        stopPropagation: true
      })
    })
    
    it('应该支持组合键注册', () => {
      const { registerShortcut, shortcuts } = useKeyboard()
      
      const callback = vi.fn()
      const result = registerShortcut('Ctrl+S', callback, { description: '保存' })
      
      expect(result).toBe(true)
      expect(shortcuts.value['Ctrl+S'].modifiers.ctrl).toBe(true)
    })
    
    it('应该处理重复注册', () => {
      const { registerShortcut } = useKeyboard()
      
      const callback1 = vi.fn()
      const callback2 = vi.fn()
      
      registerShortcut('F1', callback1)
      const result = registerShortcut('F1', callback2, { override: false })
      
      expect(result).toBe(false)
    })
    
    it('应该支持覆盖注册', () => {
      const { registerShortcut, shortcuts } = useKeyboard()
      
      const callback1 = vi.fn()
      const callback2 = vi.fn()
      
      registerShortcut('F1', callback1)
      const result = registerShortcut('F1', callback2, { override: true })
      
      expect(result).toBe(true)
      expect(shortcuts.value['F1'].callback).toBe(callback2)
    })
  })
  
  describe('unregisterShortcut', () => {
    it('应该成功注销快捷键', () => {
      const { registerShortcut, unregisterShortcut, shortcuts } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback)
      
      const result = unregisterShortcut('F1')
      
      expect(result).toBe(true)
      expect(shortcuts.value['F1']).toBeUndefined()
    })
    
    it('应该处理不存在的快捷键', () => {
      const { unregisterShortcut } = useKeyboard()
      
      const result = unregisterShortcut('F99')
      
      expect(result).toBe(false)
    })
  })
  
  describe('键盘事件处理', () => {
    it('应该正确处理单键快捷键', () => {
      const { registerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback)
      
      // 模拟键盘事件
      const keyEvent = new KeyboardEvent('keydown', {
        key: 'F1',
        ctrlKey: false,
        altKey: false,
        shiftKey: false,
        metaKey: false
      })
      
      // 触发事件处理器
      const handler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      handler(keyEvent)
      
      expect(callback).toHaveBeenCalled()
    })
    
    it('应该正确处理组合键快捷键', () => {
      const { registerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('Ctrl+S', callback)
      
      const keyEvent = new KeyboardEvent('keydown', {
        key: 's',
        ctrlKey: true,
        altKey: false,
        shiftKey: false,
        metaKey: false
      })
      
      const handler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      handler(keyEvent)
      
      expect(callback).toHaveBeenCalled()
    })
    
    it('应该在禁用时不触发快捷键', () => {
      const { registerShortcut, disableShortcuts } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback)
      disableShortcuts()
      
      const keyEvent = new KeyboardEvent('keydown', { key: 'F1' })
      
      const handler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      handler(keyEvent)
      
      expect(callback).not.toHaveBeenCalled()
    })
    
    it('应该正确更新修饰键状态', () => {
      const { currentModifiers } = useKeyboard()
      
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'Control',
        ctrlKey: true
      })
      
      const keyupEvent = new KeyboardEvent('keyup', {
        key: 'Control',
        ctrlKey: false
      })
      
      const keydownHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      const keyupHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keyup'
      )[1]
      
      keydownHandler(keydownEvent)
      expect(currentModifiers.value.ctrl).toBe(true)
      
      keyupHandler(keyupEvent)
      expect(currentModifiers.value.ctrl).toBe(false)
    })
  })
  
  describe('triggerShortcut', () => {
    it('应该能够手动触发快捷键', () => {
      const { registerShortcut, triggerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback)
      
      const result = triggerShortcut('F1')
      
      expect(result).toBe(true)
      expect(callback).toHaveBeenCalled()
    })
    
    it('应该处理不存在的快捷键', () => {
      const { triggerShortcut } = useKeyboard()
      
      const result = triggerShortcut('F99')
      
      expect(result).toBe(false)
    })
    
    it('应该处理禁用的快捷键', () => {
      const { registerShortcut, triggerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback, { enabled: false })
      
      const result = triggerShortcut('F1')
      
      expect(result).toBe(false)
      expect(callback).not.toHaveBeenCalled()
    })
  })
  
  describe('getShortcutInfo', () => {
    it('应该返回快捷键信息', () => {
      const { registerShortcut, getShortcutInfo } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback, { description: '帮助' })
      
      const info = getShortcutInfo('F1')
      
      expect(info).toEqual({
        key: 'F1',
        description: '帮助',
        modifiers: {
          ctrl: false,
          alt: false,
          shift: false,
          meta: false
        },
        enabled: true
      })
    })
    
    it('应该处理不存在的快捷键', () => {
      const { getShortcutInfo } = useKeyboard()
      
      const info = getShortcutInfo('F99')
      
      expect(info).toBe(null)
    })
  })
  
  describe('enableShortcuts / disableShortcuts', () => {
    it('应该能够启用和禁用快捷键', () => {
      const { enableShortcuts, disableShortcuts, isEnabled } = useKeyboard()
      
      expect(isEnabled.value).toBe(true)
      
      disableShortcuts()
      expect(isEnabled.value).toBe(false)
      
      enableShortcuts()
      expect(isEnabled.value).toBe(true)
    })
  })
  
  describe('resetShortcuts', () => {
    it('应该重置所有快捷键', () => {
      const { registerShortcut, resetShortcuts, shortcuts } = useKeyboard()
      
      registerShortcut('F1', vi.fn())
      registerShortcut('F2', vi.fn())
      
      expect(Object.keys(shortcuts.value)).toHaveLength(2)
      
      resetShortcuts()
      
      expect(Object.keys(shortcuts.value)).toHaveLength(0)
    })
  })
  
  describe('预定义快捷键', () => {
    it('应该注册默认的POS快捷键', () => {
      const { shortcuts } = useKeyboard()
      
      // 检查是否注册了默认快捷键
      expect(shortcuts.value['F1']).toBeDefined() // 帮助
      expect(shortcuts.value['F11']).toBeDefined() // 全屏
      expect(shortcuts.value['Ctrl+R']).toBeDefined() // 重置
      expect(shortcuts.value['Escape']).toBeDefined() // 取消
    })
    
    it('应该正确处理F1帮助快捷键', () => {
      const { triggerShortcut } = useKeyboard()
      
      const result = triggerShortcut('F1')
      
      expect(result).toBe(true)
      // 这里应该触发帮助功能，但由于是mock环境，我们只验证调用成功
    })
    
    it('应该正确处理F11全屏快捷键', () => {
      const { triggerShortcut } = useKeyboard()
      
      const result = triggerShortcut('F11')
      
      expect(result).toBe(true)
      // 这里应该触发全屏功能
    })
    
    it('应该正确处理Ctrl+R重置快捷键', () => {
      const { triggerShortcut } = useKeyboard()
      
      const result = triggerShortcut('Ctrl+R')
      
      expect(result).toBe(true)
      // 这里应该触发重置功能
    })
    
    it('应该正确处理Escape取消快捷键', () => {
      const { triggerShortcut } = useKeyboard()
      
      const result = triggerShortcut('Escape')
      
      expect(result).toBe(true)
      // 这里应该触发取消功能
    })
  })
  
  describe('键盘序列检测', () => {
    it('应该检测按键序列', () => {
      const { pressedKeys } = useKeyboard()
      
      const keydownEvent1 = new KeyboardEvent('keydown', { key: 'Control' })
      const keydownEvent2 = new KeyboardEvent('keydown', { key: 's' })
      const keyupEvent1 = new KeyboardEvent('keyup', { key: 'Control' })
      const keyupEvent2 = new KeyboardEvent('keyup', { key: 's' })
      
      const keydownHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      const keyupHandler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keyup'
      )[1]
      
      keydownHandler(keydownEvent1)
      expect(pressedKeys.value.has('Control')).toBe(true)
      
      keydownHandler(keydownEvent2)
      expect(pressedKeys.value.has('s')).toBe(true)
      
      keyupHandler(keyupEvent1)
      expect(pressedKeys.value.has('Control')).toBe(false)
      
      keyupHandler(keyupEvent2)
      expect(pressedKeys.value.has('s')).toBe(false)
    })
  })
  
  describe('事件清理', () => {
    it('应该在组件卸载时清理事件监听器', () => {
      const { onUnmounted } = require('vue')
      
      useKeyboard()
      
      // 触发onUnmounted回调
      const unmountCallback = onUnmounted.mock.calls[0][0]
      unmountCallback()
      
      expect(document.removeEventListener).toHaveBeenCalledWith('keydown', expect.any(Function))
      expect(document.removeEventListener).toHaveBeenCalledWith('keyup', expect.any(Function))
    })
  })
  
  describe('特殊情况处理', () => {
    it('应该忽略输入框中的快捷键', () => {
      const { registerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('F1', callback)
      
      // 模拟在输入框中按键
      const keyEvent = new KeyboardEvent('keydown', {
        key: 'F1',
        target: { tagName: 'INPUT' }
      })
      
      const handler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      handler(keyEvent)
      
      expect(callback).not.toHaveBeenCalled()
    })
    
    it('应该忽略文本域中的快捷键', () => {
      const { registerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('Ctrl+S', callback)
      
      const keyEvent = new KeyboardEvent('keydown', {
        key: 's',
        ctrlKey: true,
        target: { tagName: 'TEXTAREA' }
      })
      
      const handler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      handler(keyEvent)
      
      expect(callback).not.toHaveBeenCalled()
    })
    
    it('应该处理可编辑元素中的快捷键', () => {
      const { registerShortcut } = useKeyboard()
      
      const callback = vi.fn()
      registerShortcut('Ctrl+S', callback)
      
      const keyEvent = new KeyboardEvent('keydown', {
        key: 's',
        ctrlKey: true,
        target: { 
          tagName: 'DIV',
          contentEditable: 'true'
        }
      })
      
      const handler = document.addEventListener.mock.calls.find(
        call => call[0] === 'keydown'
      )[1]
      
      handler(keyEvent)
      
      expect(callback).not.toHaveBeenCalled()
    })
  })
})