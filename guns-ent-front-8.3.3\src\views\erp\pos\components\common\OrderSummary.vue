<template>
  <div class="order-summary" :class="{ 'mini': mini }">
    <div class="summary-item" v-if="showItemCount">
      <span class="label">商品数量:</span>
      <span class="value">{{ itemCount }}件</span>
    </div>
    <div class="summary-item" v-if="showAmount">
      <span class="label">订单金额:</span>
      <span class="value amount">{{ formatAmount(amount) }}</span>
    </div>
    <div class="summary-item" v-if="showMember && member">
      <span class="label">会员:</span>
      <span class="value member">{{ member.memberName || member.name }}</span>
    </div>
  </div>
</template>

<script setup>
import { AmountFormatter } from '../../utils/formatter'

// 定义组件名称
defineOptions({
  name: 'OrderSummary'
})

// 定义属性
const props = defineProps({
  itemCount: {
    type: Number,
    default: 0
  },
  amount: {
    type: Number,
    default: 0
  },
  member: {
    type: Object,
    default: null
  },
  mini: {
    type: Boolean,
    default: false
  },
  showItemCount: {
    type: Boolean,
    default: true
  },
  showAmount: {
    type: Boolean,
    default: true
  },
  showMember: {
    type: Boolean,
    default: true
  }
})

/**
 * 格式化金额显示
 */
const formatAmount = (amount) => {
  return AmountFormatter.formatCurrency(amount)
}
</script>

<style scoped>
.order-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.order-summary.mini {
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-item .label {
  font-size: 13px;
  color: #8c8c8c;
}

.summary-item .value {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.summary-item .value.amount {
  color: #f5222d;
  font-weight: 600;
}

.summary-item .value.member {
  color: #1890ff;
}

/* Mini模式样式 */
.order-summary.mini .summary-item .label {
  font-size: 12px;
}

.order-summary.mini .summary-item .value {
  font-size: 12px;
}
</style>