<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="24a4bf92-ecad-49fa-9961-351118c0ccec" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../JavaWorking/Maven/apache-maven-3.9.10" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\JavaWorking\Maven\apache-maven-3.9.10\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="3058iCOsMRDJ1bilpAvu2Y2OfUd" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.PosExceptionTest.testPosExceptionCreation.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.PosServiceBasicTest.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.PosServiceIntegrationTest.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.UnifiedProductQueryServiceTest.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.UnifiedProductQueryServiceTest.testSearchSuggestions.executor&quot;: &quot;Run&quot;,
    &quot;Maven.enterprise-plugins [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.erp-api [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.erp-business [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.guns [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.javaguns-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.javaguns-parent [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.kernel-d-erp [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.kernel-d-erp [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.kernel-s-system [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.roses-kernel [compile].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.GatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ProjectStartApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.TestApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Vitest.ProductEdit 商品表单组件.表单验证.应该验证必填字段.executor&quot;: &quot;Run&quot;,
    &quot;Vitest.TreeConfigFactory.executor&quot;: &quot;Run&quot;,
    &quot;Vitest.TreeDataAdapter Basic Tests.executor&quot;: &quot;Run&quot;,
    &quot;Vitest.TreeDataAdapter.executor&quot;: &quot;Run&quot;,
    &quot;Vitest.UnifiedProductQueryApi.executor&quot;: &quot;Debug&quot;,
    &quot;Vitest.UnifiedProductQueryApi.queryProducts.executor&quot;: &quot;Run&quot;,
    &quot;Vitest.UniversalTree Basic Tests.executor&quot;: &quot;Run&quot;,
    &quot;Vitest.UniversalTree.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/hyProject/sql&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\MyProgramFiles\\JetBrains\\IntelliJ IDEA 2025.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\hyProject\sql" />
      <recent name="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\src\main\java\cn\stylefeng\roses\kernel\erp\modular\supplier\mapper" />
      <recent name="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\src\main\java\cn\stylefeng\roses\kernel\erp\modular\customer\mapper" />
      <recent name="D:\hyProject\guns-ent-backend-8.3.3\Augment\sql" />
      <recent name="D:\hyProject\guns-ent-backend-8.3.3\guns-master\src\main\resources\db\migration\mysql" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\hyProject\docs" />
      <recent name="D:\hyProject\guns-ent-front-8.3.3\src\views\system\tools" />
      <recent name="D:\hyProject\sql" />
      <recent name="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-api\src\main\java\cn\stylefeng\roses\kernel\erp\api\constants" />
      <recent name="D:\hyProject\guns-ent-backend-8.3.3\kernel-d-erp\erp-business\src\main\resources\cn\stylefeng\roses\kernel\erp\modular\productcategoryrelation\mapper\mapping" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ProjectStartApplication">
    <configuration name="UnifiedProductQueryServiceTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-business" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.stylefeng.roses.kernel.erp.modular.product.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.stylefeng.roses.kernel.erp.modular.product.service" />
      <option name="MAIN_CLASS_NAME" value="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UnifiedProductQueryServiceTest.testSearchSuggestions" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="erp-business" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.stylefeng.roses.kernel.erp.modular.product.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.stylefeng.roses.kernel.erp.modular.product.service" />
      <option name="MAIN_CLASS_NAME" value="cn.stylefeng.roses.kernel.erp.modular.product.service.UnifiedProductQueryServiceTest" />
      <option name="METHOD_NAME" value="testSearchSuggestions" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UnifiedProductQueryApi" type="JavaScriptTestRunnerVitest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <vitest-package value="$PROJECT_DIR$/guns-ent-front-8.3.3/node_modules/vitest" />
      <working-dir value="$PROJECT_DIR$/guns-ent-front-8.3.3" />
      <vitest-options value="--run" />
      <envs />
      <scope-kind value="SUITE" />
      <test-file value="$PROJECT_DIR$/guns-ent-front-8.3.3/src/api/erp/__tests__/unifiedProductQuery.test.js" />
      <test-names>
        <test-name value="UnifiedProductQueryApi" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration name="UnifiedProductQueryApi.queryProducts" type="JavaScriptTestRunnerVitest" temporary="true" nameIsGenerated="true">
      <node-interpreter value="project" />
      <vitest-package value="$PROJECT_DIR$/guns-ent-front-8.3.3/node_modules/vitest" />
      <working-dir value="$PROJECT_DIR$/guns-ent-front-8.3.3" />
      <vitest-options value="--run" />
      <envs />
      <scope-kind value="SUITE" />
      <test-file value="$PROJECT_DIR$/guns-ent-front-8.3.3/src/api/erp/__tests__/unifiedProductQuery.test.js" />
      <test-names>
        <test-name value="UnifiedProductQueryApi" />
        <test-name value="queryProducts" />
      </test-names>
      <method v="2" />
    </configuration>
    <configuration name="ProjectStartApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="guns" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.stylefeng.guns.ProjectStartApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.stylefeng.guns.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ProjectStartApplication" />
        <item itemvalue="Vitest.UnifiedProductQueryApi" />
        <item itemvalue="Vitest.UnifiedProductQueryApi.queryProducts" />
        <item itemvalue="JUnit.UnifiedProductQueryServiceTest" />
        <item itemvalue="JUnit.UnifiedProductQueryServiceTest.testSearchSuggestions" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="24a4bf92-ecad-49fa-9961-351118c0ccec" name="更改" comment="" />
      <created>1752906198836</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752906198836</updated>
      <workItem from="1752906201492" duration="582000" />
      <workItem from="1752994948556" duration="8046000" />
      <workItem from="1753008215759" duration="9926000" />
      <workItem from="1753089339381" duration="747000" />
      <workItem from="1753097022106" duration="1006000" />
      <workItem from="1753098049400" duration="10415000" />
      <workItem from="1753145507134" duration="6104000" />
      <workItem from="1753167217623" duration="2036000" />
      <workItem from="1753172580941" duration="5082000" />
      <workItem from="1753180930062" duration="545000" />
      <workItem from="1753181496067" duration="11094000" />
      <workItem from="1753235079818" duration="9763000" />
      <workItem from="1753259854656" duration="449000" />
      <workItem from="1753260334841" duration="1530000" />
      <workItem from="1753275406287" duration="4000" />
      <workItem from="1753276013169" duration="7537000" />
      <workItem from="1753320132984" duration="683000" />
      <workItem from="1753320863143" duration="7938000" />
      <workItem from="1753336100538" duration="13100000" />
      <workItem from="1753353841999" duration="12541000" />
      <workItem from="1753424146369" duration="2527000" />
      <workItem from="1753428444182" duration="3325000" />
      <workItem from="1753443232120" duration="4975000" />
      <workItem from="1753451549454" duration="3021000" />
      <workItem from="1753455743983" duration="2634000" />
      <workItem from="1753510412873" duration="3794000" />
      <workItem from="1753535879156" duration="2978000" />
      <workItem from="1753598631349" duration="2748000" />
      <workItem from="1753602056680" duration="8150000" />
      <workItem from="1753678536436" duration="829000" />
      <workItem from="1753683195383" duration="8432000" />
      <workItem from="1753751469425" duration="4061000" />
      <workItem from="1753771846957" duration="6895000" />
      <workItem from="1753787058253" duration="5060000" />
      <workItem from="1753838910910" duration="5172000" />
      <workItem from="1753855291722" duration="21157000" />
      <workItem from="1753925280844" duration="6854000" />
      <workItem from="1753941815503" duration="10226000" />
      <workItem from="1754013338189" duration="5062000" />
      <workItem from="1754019748130" duration="623000" />
      <workItem from="1754028390474" duration="4148000" />
      <workItem from="1754034788766" duration="250000" />
      <workItem from="1754035168310" duration="5745000" />
      <workItem from="1754051184133" duration="1041000" />
      <workItem from="1754052249397" duration="96000" />
      <workItem from="1754052359879" duration="134000" />
      <workItem from="1754052510862" duration="8265000" />
      <workItem from="1754117509041" duration="8187000" />
      <workItem from="1754137529255" duration="1446000" />
      <workItem from="1754193831100" duration="7376000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>