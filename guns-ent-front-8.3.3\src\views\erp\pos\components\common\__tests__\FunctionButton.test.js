import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import FunctionButton from '../FunctionButton.vue'

describe('FunctionButton', () => {
  let wrapper

  const createWrapper = (props = {}) => {
    return mount(FunctionButton, {
      props: {
        title: '测试按钮',
        ...props
      },
      global: {
        stubs: {
          'icon-font': true
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染按钮', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.function-button-item').exists()).toBe(true)
      expect(wrapper.find('.function-card').exists()).toBe(true)
      expect(wrapper.find('.card-title').text()).toBe('测试按钮')
    })

    it('应该在有图标时显示图标', () => {
      wrapper = createWrapper({ icon: 'icon-test' })
      
      expect(wrapper.find('.card-icon').exists()).toBe(true)
      expect(wrapper.find('icon-font-stub').exists()).toBe(true)
    })

    it('应该在有副标题时显示副标题', () => {
      wrapper = createWrapper({ subtitle: '副标题' })
      
      expect(wrapper.find('.card-subtitle').exists()).toBe(true)
      expect(wrapper.find('.card-subtitle').text()).toBe('副标题')
    })

    it('应该在有徽章时显示徽章', () => {
      wrapper = createWrapper({ badge: 5 })
      
      expect(wrapper.find('.card-badge').exists()).toBe(true)
      expect(wrapper.find('.card-badge').text()).toBe('5')
    })

    it('应该在徽章为0时不显示徽章', () => {
      wrapper = createWrapper({ badge: 0 })
      
      expect(wrapper.find('.card-badge').exists()).toBe(false)
    })
  })

  describe('按钮类型', () => {
    it('应该正确应用默认类型样式', () => {
      wrapper = createWrapper({ type: 'default' })
      
      expect(wrapper.find('.function-card-default').exists()).toBe(true)
    })

    it('应该正确应用主要类型样式', () => {
      wrapper = createWrapper({ type: 'primary' })
      
      expect(wrapper.find('.function-card-primary').exists()).toBe(true)
    })

    it('应该正确应用成功类型样式', () => {
      wrapper = createWrapper({ type: 'success' })
      
      expect(wrapper.find('.function-card-success').exists()).toBe(true)
    })

    it('应该正确应用警告类型样式', () => {
      wrapper = createWrapper({ type: 'warning' })
      
      expect(wrapper.find('.function-card-warning').exists()).toBe(true)
    })

    it('应该正确应用危险类型样式', () => {
      wrapper = createWrapper({ type: 'danger' })
      
      expect(wrapper.find('.function-card-danger').exists()).toBe(true)
    })
  })

  describe('按钮尺寸', () => {
    it('应该正确应用小尺寸样式', () => {
      wrapper = createWrapper({ size: 'small' })
      
      expect(wrapper.find('.function-card-small').exists()).toBe(true)
    })

    it('应该正确应用正常尺寸样式', () => {
      wrapper = createWrapper({ size: 'normal' })
      
      expect(wrapper.find('.function-card-normal').exists()).toBe(true)
    })

    it('应该正确应用大尺寸样式', () => {
      wrapper = createWrapper({ size: 'large' })
      
      expect(wrapper.find('.function-card-large').exists()).toBe(true)
    })
  })

  describe('按钮状态', () => {
    it('应该在禁用时应用禁用样式', () => {
      wrapper = createWrapper({ disabled: true })
      
      expect(wrapper.find('.function-card-disabled').exists()).toBe(true)
    })

    it('应该在结账模式时应用结账样式', () => {
      wrapper = createWrapper({ isCheckout: true })
      
      expect(wrapper.find('.checkout-button').exists()).toBe(true)
      expect(wrapper.find('.function-card-checkout').exists()).toBe(true)
    })
  })

  describe('事件处理', () => {
    it('应该在点击时触发click事件', async () => {
      wrapper = createWrapper()
      
      await wrapper.find('.function-card').trigger('click')
      
      expect(wrapper.emitted('click')).toBeTruthy()
    })

    it('应该在禁用时不触发click事件', async () => {
      wrapper = createWrapper({ disabled: true })
      
      await wrapper.find('.function-card').trigger('click')
      
      expect(wrapper.emitted('click')).toBeFalsy()
    })
  })

  describe('属性验证', () => {
    it('应该正确处理所有属性', () => {
      const props = {
        title: '测试标题',
        subtitle: '测试副标题',
        icon: 'icon-test',
        badge: 10,
        type: 'primary',
        size: 'large',
        disabled: false,
        isCheckout: true
      }
      
      wrapper = createWrapper(props)
      
      expect(wrapper.props()).toMatchObject(props)
    })

    it('应该为可选属性提供默认值', () => {
      wrapper = createWrapper()
      
      expect(wrapper.props('subtitle')).toBe('')
      expect(wrapper.props('icon')).toBe('')
      expect(wrapper.props('badge')).toBe(0)
      expect(wrapper.props('type')).toBe('default')
      expect(wrapper.props('size')).toBe('normal')
      expect(wrapper.props('disabled')).toBe(false)
      expect(wrapper.props('isCheckout')).toBe(false)
    })
  })

  describe('计算属性', () => {
    it('应该正确计算卡片样式类', () => {
      wrapper = createWrapper({
        type: 'primary',
        size: 'large',
        disabled: true,
        isCheckout: true
      })
      
      const card = wrapper.find('.function-card')
      expect(card.classes()).toContain('function-card-primary')
      expect(card.classes()).toContain('function-card-large')
      expect(card.classes()).toContain('function-card-disabled')
      expect(card.classes()).toContain('function-card-checkout')
    })
  })
})