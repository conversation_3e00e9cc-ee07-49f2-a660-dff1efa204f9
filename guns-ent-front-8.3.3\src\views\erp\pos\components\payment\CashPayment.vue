<!--
  现金支付组件
  
  处理现金支付的金额输入和找零计算
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cash-payment">
    <!-- 现金输入区域 -->
    <div class="cash-input-section">
      <div class="input-group">
        <label class="input-label">
          <icon-font iconClass="icon-money" />
          实收金额
        </label>
        <a-input-number
          v-model:value="receivedAmount"
          :min="paymentAmount"
          :max="999999.99"
          :precision="2"
          :step="0.01"
          size="large"
          class="cash-input"
          placeholder="请输入实收金额"
          :disabled="loading"
          @change="handleAmountChange"
          @press-enter="handleEnterPress"
        >
          <template #addonBefore>￥</template>
        </a-input-number>
      </div>
      
      <!-- 应付金额提示 -->
      <div class="payment-info">
        <div class="info-item">
          <span class="info-label">应付金额:</span>
          <span class="info-value">{{ formatPrice(paymentAmount) }}</span>
        </div>
        <div class="info-item" v-if="receivedAmount > paymentAmount">
          <span class="info-label">实收金额:</span>
          <span class="info-value received">{{ formatPrice(receivedAmount) }}</span>
        </div>
      </div>
    </div>
    
    <!-- 快捷金额按钮 -->
    <div class="quick-amounts">
      <div class="quick-title">
        <icon-font iconClass="icon-quick" />
        <span>快捷金额</span>
      </div>
      
      <div class="amount-buttons">
        <a-button
          v-for="amount in quickAmounts"
          :key="amount"
          size="small"
          :type="receivedAmount === amount ? 'primary' : 'default'"
          :loading="loading"
          @click="setReceivedAmount(amount)"
          class="quick-amount-btn"
        >
          ￥{{ amount }}
        </a-button>
        
        <a-button
          size="small"
          :type="receivedAmount === paymentAmount ? 'primary' : 'default'"
          :loading="loading"
          @click="setReceivedAmount(paymentAmount)"
          class="quick-amount-btn exact"
        >
          <icon-font iconClass="icon-exact" />
          刚好
        </a-button>
        
        <a-button
          size="small"
          :loading="loading"
          @click="clearAmount"
          class="quick-amount-btn clear"
        >
          <icon-font iconClass="icon-clear" />
          清空
        </a-button>
      </div>
    </div>
    
    <!-- 找零显示 -->
    <div class="change-display" v-if="showChange">
      <div class="change-header">
        <icon-font iconClass="icon-change" />
        <span>找零信息</span>
      </div>
      
      <div class="change-content">
        <div class="change-amount">
          <span class="change-label">找零金额:</span>
          <span class="change-value">{{ formatPrice(changeAmount) }}</span>
        </div>
        
        <!-- 找零明细 -->
        <div class="change-breakdown" v-if="changeBreakdown.length > 0">
          <div class="breakdown-title">建议找零:</div>
          <div class="breakdown-items">
            <div 
              v-for="item in changeBreakdown" 
              :key="item.denomination"
              class="breakdown-item"
            >
              <span class="denomination">{{ item.label }}</span>
              <span class="count">{{ item.count }}{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 数字键盘 -->
    <div class="number-pad" v-if="showNumberPad">
      <div class="pad-header">
        <span>数字键盘</span>
        <a-button 
          type="text" 
          size="small"
          @click="toggleNumberPad"
        >
          <icon-font iconClass="icon-close" />
        </a-button>
      </div>
      
      <div class="pad-grid">
        <a-button
          v-for="num in [7, 8, 9, 4, 5, 6, 1, 2, 3]"
          :key="num"
          size="large"
          @click="inputNumber(num)"
          class="pad-btn number"
        >
          {{ num }}
        </a-button>
        
        <a-button
          size="large"
          @click="inputDecimal"
          class="pad-btn decimal"
          :disabled="hasDecimal"
        >
          .
        </a-button>
        
        <a-button
          size="large"
          @click="inputNumber(0)"
          class="pad-btn number"
        >
          0
        </a-button>
        
        <a-button
          size="large"
          @click="backspace"
          class="pad-btn backspace"
        >
          <icon-font iconClass="icon-backspace" />
        </a-button>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="cash-actions">
      <a-button
        v-if="!showNumberPad"
        @click="toggleNumberPad"
        class="action-btn"
      >
        <icon-font iconClass="icon-keyboard" />
        数字键盘
      </a-button>
      
      <a-button
        type="primary"
        :disabled="!canConfirm"
        :loading="loading"
        @click="handleConfirm"
        class="action-btn confirm"
      >
        <icon-font iconClass="icon-confirm" />
        确认收款
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'
import { AmountFormatter } from '../../utils/formatter'
import { CASH_DENOMINATIONS } from '../../utils/constants'

// 定义组件名称
defineOptions({
  name: 'CashPayment'
})

// 定义Props
const props = defineProps({
  // 应付金额
  paymentAmount: {
    type: Number,
    required: true
  },
  // 初始实收金额
  initialAmount: {
    type: Number,
    default: 0
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 是否显示数字键盘
  showKeypad: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'amount-change',
  'confirm-payment'
])

// 响应式状态
const receivedAmount = ref(0)
const showNumberPad = ref(props.showKeypad)
const inputValue = ref('')

// ==================== 计算属性 ====================

/**
 * 找零金额
 */
const changeAmount = computed(() => {
  return Math.max(0, receivedAmount.value - props.paymentAmount)
})

/**
 * 是否显示找零
 */
const showChange = computed(() => {
  return receivedAmount.value > props.paymentAmount
})

/**
 * 快捷金额选项
 */
const quickAmounts = computed(() => {
  const base = Math.ceil(props.paymentAmount)
  const amounts = [base, base + 10, base + 20, base + 50, base + 100]
  
  // 去重并排序
  return [...new Set(amounts)].sort((a, b) => a - b)
})

/**
 * 找零明细
 */
const changeBreakdown = computed(() => {
  if (changeAmount.value <= 0) return []
  
  const breakdown = []
  let remaining = Math.round(changeAmount.value * 100) // 转换为分
  
  // 按面额从大到小计算
  for (const denomination of CASH_DENOMINATIONS) {
    const value = denomination.value * 100 // 转换为分
    const count = Math.floor(remaining / value)
    
    if (count > 0) {
      breakdown.push({
        denomination: denomination.value,
        label: denomination.label,
        count,
        unit: denomination.unit
      })
      remaining -= count * value
    }
  }
  
  return breakdown
})

/**
 * 是否可以确认支付
 */
const canConfirm = computed(() => {
  return receivedAmount.value >= props.paymentAmount && !props.loading
})

/**
 * 输入值是否包含小数点
 */
const hasDecimal = computed(() => {
  return inputValue.value.includes('.')
})

// ==================== 方法 ====================

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @returns {string} 格式化后的价格
 */
const formatPrice = (price) => {
  return AmountFormatter.formatCurrency(price || 0)
}

/**
 * 设置实收金额
 * @param {number} amount - 金额
 */
const setReceivedAmount = (amount) => {
  receivedAmount.value = amount
  inputValue.value = amount.toString()
  handleAmountChange(amount)
}

/**
 * 清空金额
 */
const clearAmount = () => {
  receivedAmount.value = 0
  inputValue.value = ''
  handleAmountChange(0)
}

/**
 * 处理金额变化
 * @param {number} value - 新金额
 */
const handleAmountChange = (value) => {
  if (value < props.paymentAmount) {
    message.warning('实收金额不能少于应付金额')
  }
  
  emit('amount-change', {
    receivedAmount: value,
    changeAmount: changeAmount.value,
    canConfirm: canConfirm.value
  })\n}\n\n/**\n * 处理回车键\n */\nconst handleEnterPress = () => {\n  if (canConfirm.value) {\n    handleConfirm()\n  }\n}\n\n/**\n * 切换数字键盘显示\n */\nconst toggleNumberPad = () => {\n  showNumberPad.value = !showNumberPad.value\n}\n\n/**\n * 输入数字\n * @param {number} num - 数字\n */\nconst inputNumber = (num) => {\n  if (inputValue.value.length >= 10) {\n    message.warning('输入金额过长')\n    return\n  }\n  \n  inputValue.value += num.toString()\n  const value = parseFloat(inputValue.value) || 0\n  receivedAmount.value = value\n  handleAmountChange(value)\n}\n\n/**\n * 输入小数点\n */\nconst inputDecimal = () => {\n  if (!hasDecimal.value) {\n    if (inputValue.value === '') {\n      inputValue.value = '0.'\n    } else {\n      inputValue.value += '.'\n    }\n  }\n}\n\n/**\n * 退格删除\n */\nconst backspace = () => {\n  if (inputValue.value.length > 0) {\n    inputValue.value = inputValue.value.slice(0, -1)\n    const value = parseFloat(inputValue.value) || 0\n    receivedAmount.value = value\n    handleAmountChange(value)\n  }\n}\n\n/**\n * 确认支付\n */\nconst handleConfirm = () => {\n  if (!canConfirm.value) {\n    message.warning('请输入正确的实收金额')\n    return\n  }\n  \n  emit('confirm-payment', {\n    receivedAmount: receivedAmount.value,\n    changeAmount: changeAmount.value,\n    changeBreakdown: changeBreakdown.value\n  })\n}\n\n// ==================== 监听器 ====================\n\n// 监听初始金额变化\nwatch(\n  () => props.initialAmount,\n  (newAmount) => {\n    if (newAmount > 0) {\n      setReceivedAmount(newAmount)\n    }\n  },\n  { immediate: true }\n)\n\n// 监听应付金额变化，自动设置实收金额\nwatch(\n  () => props.paymentAmount,\n  (newAmount) => {\n    if (receivedAmount.value < newAmount) {\n      setReceivedAmount(newAmount)\n    }\n  }\n)\n</script>\n\n<style scoped>\n.cash-payment {\n  padding: 20px 24px;\n  background: #f9f9f9;\n  border-top: 1px solid #f0f0f0;\n}\n\n/* 现金输入区域 */\n.cash-input-section {\n  margin-bottom: 20px;\n}\n\n.input-group {\n  margin-bottom: 12px;\n}\n\n.input-label {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n}\n\n.cash-input {\n  width: 100%;\n}\n\n.payment-info {\n  display: flex;\n  justify-content: space-between;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e8e8e8;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 13px;\n}\n\n.info-label {\n  color: #595959;\n}\n\n.info-value {\n  font-weight: 600;\n  color: #262626;\n}\n\n.info-value.received {\n  color: #1890ff;\n}\n\n/* 快捷金额 */\n.quick-amounts {\n  margin-bottom: 20px;\n}\n\n.quick-title {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 12px;\n  font-size: 13px;\n  color: #595959;\n}\n\n.amount-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.quick-amount-btn {\n  min-width: 60px;\n  height: 32px;\n  border-radius: 6px;\n  font-size: 12px;\n}\n\n.quick-amount-btn.exact {\n  background: #52c41a;\n  border-color: #52c41a;\n  color: #fff;\n}\n\n.quick-amount-btn.exact:hover {\n  background: #73d13d;\n  border-color: #73d13d;\n}\n\n.quick-amount-btn.clear {\n  background: #ff7875;\n  border-color: #ff7875;\n  color: #fff;\n}\n\n.quick-amount-btn.clear:hover {\n  background: #ff9c99;\n  border-color: #ff9c99;\n}\n\n/* 找零显示 */\n.change-display {\n  margin-bottom: 20px;\n  background: #e6f7ff;\n  border-radius: 8px;\n  border-left: 4px solid #1890ff;\n  overflow: hidden;\n}\n\n.change-header {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 12px 16px;\n  background: #bae7ff;\n  font-size: 13px;\n  font-weight: 500;\n  color: #0050b3;\n}\n\n.change-content {\n  padding: 16px;\n}\n\n.change-amount {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.change-label {\n  font-size: 14px;\n  color: #262626;\n  font-weight: 500;\n}\n\n.change-value {\n  font-size: 20px;\n  font-weight: 700;\n  color: #1890ff;\n}\n\n.change-breakdown {\n  border-top: 1px solid #91d5ff;\n  padding-top: 12px;\n}\n\n.breakdown-title {\n  font-size: 12px;\n  color: #595959;\n  margin-bottom: 8px;\n}\n\n.breakdown-items {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.breakdown-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 4px 8px;\n  background: #fff;\n  border-radius: 4px;\n  font-size: 11px;\n}\n\n.denomination {\n  color: #262626;\n  font-weight: 500;\n}\n\n.count {\n  color: #1890ff;\n  font-weight: 600;\n}\n\n/* 数字键盘 */\n.number-pad {\n  margin-bottom: 20px;\n  background: #fff;\n  border-radius: 8px;\n  border: 1px solid #e8e8e8;\n  overflow: hidden;\n}\n\n.pad-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n  font-size: 13px;\n  font-weight: 500;\n  color: #262626;\n}\n\n.pad-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 1px;\n  background: #f0f0f0;\n  padding: 1px;\n}\n\n.pad-btn {\n  height: 48px;\n  border: none;\n  border-radius: 0;\n  background: #fff;\n  font-size: 16px;\n  font-weight: 500;\n  transition: all 0.2s;\n}\n\n.pad-btn:hover {\n  background: #f0f9ff;\n  color: #1890ff;\n}\n\n.pad-btn.number {\n  color: #262626;\n}\n\n.pad-btn.decimal {\n  color: #1890ff;\n}\n\n.pad-btn.backspace {\n  color: #ff4d4f;\n}\n\n/* 操作按钮 */\n.cash-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.action-btn {\n  flex: 1;\n  height: 44px;\n  font-size: 14px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.action-btn.confirm {\n  flex: 2;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .cash-payment {\n    padding: 16px 20px;\n  }\n  \n  .payment-info {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .amount-buttons {\n    gap: 6px;\n  }\n  \n  .quick-amount-btn {\n    min-width: 50px;\n    height: 28px;\n    font-size: 11px;\n  }\n  \n  .change-value {\n    font-size: 18px;\n  }\n  \n  .pad-btn {\n    height: 44px;\n    font-size: 14px;\n  }\n  \n  .cash-actions {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .action-btn {\n    height: 40px;\n  }\n}\n\n@media (max-width: 480px) {\n  .breakdown-items {\n    gap: 4px;\n  }\n  \n  .breakdown-item {\n    padding: 2px 6px;\n    font-size: 10px;\n  }\n  \n  .pad-grid {\n    gap: 0;\n  }\n  \n  .pad-btn {\n    height: 40px;\n    font-size: 13px;\n  }\n}\n\n/* 动画效果 */\n.change-display {\n  animation: slideDown 0.3s ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 触屏设备优化 */\n@media (hover: none) {\n  .pad-btn {\n    height: 52px;\n  }\n  \n  .quick-amount-btn {\n    min-height: 36px;\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .change-display {\n    border-width: 2px;\n  }\n  \n  .number-pad {\n    border-width: 2px;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .change-display {\n    animation: none;\n  }\n  \n  .pad-btn {\n    transition: none;\n  }\n}\n</style>"