/**
 * 支付API单元测试
 * 
 * 测试支付相关API接口的调用和错误处理
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PaymentApi } from '../payment'
import Request from '@/utils/request/request-util'
import { PAYMENT_METHODS } from '../../utils/constants'

// Mock Request模块
vi.mock('@/utils/request/request-util', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn()
  }
}))

// Mock 错误处理器和性能监控器
vi.mock('../../utils/error-handler', () => ({
  PosErrorHandler: {
    wrapApiCall: vi.fn((fn) => fn)
  }
}))

vi.mock('../../utils/performance-monitor', () => ({
  PerformanceMonitor: {
    measureApiCall: vi.fn((name, fn) => fn)
  }
}))

describe('PaymentApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('processCashPayment', () => {
    it('应该成功处理现金支付', async () => {
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        receivedAmount: 120,
        changeAmount: 20,
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        paymentId: 'PAY001',
        transactionId: 'TXN001',
        status: 'SUCCESS'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.processCashPayment(paymentParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/cash', {
        ...paymentParams,
        paymentMethod: PAYMENT_METHODS.CASH,
        paymentTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理现金支付失败的情况', async () => {
      const mockError = new Error('现金支付处理失败')
      Request.post.mockRejectedValue(mockError)
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        receivedAmount: 80, // 实收金额不足
        changeAmount: 0,
        cashierId: 'CASHIER001'
      }
      
      await expect(PaymentApi.processCashPayment(paymentParams)).rejects.toThrow('现金支付处理失败')
    })
  })
  
  describe('processQrCodePayment', () => {
    it('应该成功处理扫码支付', async () => {
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        paymentMethod: PAYMENT_METHODS.WECHAT,
        qrCode: 'weixin://wxpay/bizpayurl?pr=test',
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        paymentId: 'PAY002',
        qrCodeUrl: 'https://qr.alipay.com/test',
        status: 'PENDING'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.processQrCodePayment(paymentParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/qrcode', {
        ...paymentParams,
        paymentTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('queryQrCodePaymentStatus', () => {
    it('应该成功查询扫码支付状态', async () => {
      const queryParams = {
        paymentId: 'PAY002',
        outTradeNo: 'OUT_TRADE_001'
      }
      const mockResponse = {
        paymentId: 'PAY002',
        status: 'SUCCESS',
        transactionId: 'WX_TXN_001',
        paidAmount: 100,
        paidTime: '2025-01-02T10:30:00Z'
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.queryQrCodePaymentStatus(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/payment/qrcode/status', queryParams)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理支付状态查询失败', async () => {
      const mockError = new Error('查询支付状态失败')
      Request.get.mockRejectedValue(mockError)
      
      await expect(PaymentApi.queryQrCodePaymentStatus({ paymentId: 'INVALID' }))
        .rejects.toThrow('查询支付状态失败')
    })
  })
  
  describe('processMemberCardPayment', () => {
    it('应该成功处理会员卡支付', async () => {
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        memberId: 'MEMBER001',
        memberCardNo: 'CARD123456',
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        paymentId: 'PAY003',
        remainingBalance: 500,
        status: 'SUCCESS'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.processMemberCardPayment(paymentParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/member', {
        ...paymentParams,
        paymentMethod: PAYMENT_METHODS.MEMBER,
        paymentTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理会员余额不足的情况', async () => {
      const mockError = new Error('会员余额不足')
      Request.post.mockRejectedValue(mockError)
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 1000, // 超过余额
        memberId: 'MEMBER001',
        memberCardNo: 'CARD123456',
        cashierId: 'CASHIER001'
      }
      
      await expect(PaymentApi.processMemberCardPayment(paymentParams))
        .rejects.toThrow('会员余额不足')
    })
  })
  
  describe('processPointsPayment', () => {
    it('应该成功处理积分支付', async () => {
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 50,
        memberId: 'MEMBER001',
        pointsUsed: 5000,
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        paymentId: 'PAY004',
        remainingPoints: 15000,
        status: 'SUCCESS'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.processPointsPayment(paymentParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/points', {
        ...paymentParams,
        paymentMethod: PAYMENT_METHODS.POINTS,
        paymentTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('processBankCardPayment', () => {
    it('应该成功处理银行卡支付', async () => {
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        cardNo: '****1234',
        bankName: '中国银行',
        authCode: 'AUTH123456',
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        paymentId: 'PAY005',
        transactionId: 'BANK_TXN_001',
        status: 'SUCCESS'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.processBankCardPayment(paymentParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/bankcard', {
        ...paymentParams,
        paymentMethod: PAYMENT_METHODS.CARD,
        paymentTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('processComboPayment', () => {
    it('应该成功处理组合支付', async () => {
      const paymentParams = {
        orderId: 'ORDER001',
        totalAmount: 200,
        paymentMethods: [
          { method: PAYMENT_METHODS.CASH, amount: 100 },
          { method: PAYMENT_METHODS.WECHAT, amount: 100 }
        ],
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        paymentId: 'PAY006',
        payments: [
          { method: PAYMENT_METHODS.CASH, amount: 100, status: 'SUCCESS' },
          { method: PAYMENT_METHODS.WECHAT, amount: 100, status: 'SUCCESS' }
        ],
        status: 'SUCCESS'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.processComboPayment(paymentParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/combo', {
        ...paymentParams,
        paymentTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('confirmPaymentSuccess', () => {
    it('应该成功确认支付', async () => {
      const confirmParams = {
        paymentId: 'PAY002',
        transactionId: 'WX_TXN_001',
        actualAmount: 100
      }
      const mockResponse = {
        success: true,
        confirmed: true,
        confirmTime: '2025-01-02T10:35:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.confirmPaymentSuccess(confirmParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/confirm', {
        ...confirmParams,
        confirmTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('cancelPayment', () => {
    it('应该成功取消支付', async () => {
      const cancelParams = {
        paymentId: 'PAY002',
        cancelReason: '用户取消',
        cancelBy: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        cancelled: true,
        cancelTime: '2025-01-02T10:40:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.cancelPayment(cancelParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/cancel', {
        ...cancelParams,
        cancelTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('requestRefund', () => {
    it('应该成功申请退款', async () => {
      const refundParams = {
        paymentId: 'PAY001',
        refundAmount: 50,
        refundReason: '商品质量问题',
        refundBy: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        refundId: 'REFUND001',
        status: 'PROCESSING'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.requestRefund(refundParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/refund', {
        ...refundParams,
        refundTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('queryRefundStatus', () => {
    it('应该成功查询退款状态', async () => {
      const queryParams = {
        refundId: 'REFUND001',
        outRefundNo: 'OUT_REFUND_001'
      }
      const mockResponse = {
        refundId: 'REFUND001',
        status: 'SUCCESS',
        refundAmount: 50,
        refundTime: '2025-01-02T11:00:00Z'
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.queryRefundStatus(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/payment/refund/status', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getPaymentRecords', () => {
    it('应该成功获取支付记录', async () => {
      const queryParams = {
        orderId: 'ORDER001',
        paymentMethod: PAYMENT_METHODS.CASH,
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z'
      }
      const mockResponse = [
        {
          paymentId: 'PAY001',
          orderId: 'ORDER001',
          paymentMethod: PAYMENT_METHODS.CASH,
          amount: 100,
          status: 'SUCCESS',
          paymentTime: '2025-01-02T10:00:00Z'
        }
      ]
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.getPaymentRecords(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/payment/records', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getPaymentStatistics', () => {
    it('应该成功获取支付统计', async () => {
      const queryParams = {
        cashierId: 'CASHIER001',
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z'
      }
      const mockResponse = {
        totalAmount: 1000,
        totalCount: 10,
        paymentMethods: {
          [PAYMENT_METHODS.CASH]: { amount: 500, count: 5 },
          [PAYMENT_METHODS.WECHAT]: { amount: 300, count: 3 },
          [PAYMENT_METHODS.ALIPAY]: { amount: 200, count: 2 }
        }
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.getPaymentStatistics(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/payment/statistics', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('validatePaymentPassword', () => {
    it('应该成功验证支付密码', async () => {
      const validateParams = {
        password: '123456',
        cashierId: 'CASHIER001'
      }
      const mockResponse = {
        valid: true,
        message: '密码验证成功'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.validatePaymentPassword(validateParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/validatePassword', validateParams)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理密码错误的情况', async () => {
      const mockError = new Error('支付密码错误')
      Request.post.mockRejectedValue(mockError)
      
      await expect(PaymentApi.validatePaymentPassword({
        password: 'wrong',
        cashierId: 'CASHIER001'
      })).rejects.toThrow('支付密码错误')
    })
  })
  
  describe('getPaymentConfig', () => {
    it('应该成功获取支付配置', async () => {
      const mockResponse = {
        enabledMethods: [
          PAYMENT_METHODS.CASH,
          PAYMENT_METHODS.WECHAT,
          PAYMENT_METHODS.ALIPAY
        ],
        limits: {
          [PAYMENT_METHODS.CASH]: { max: 10000 },
          [PAYMENT_METHODS.WECHAT]: { max: 50000 },
          [PAYMENT_METHODS.ALIPAY]: { max: 50000 }
        }
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.getPaymentConfig({ storeId: 'STORE001' })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/payment/config', { storeId: 'STORE001' })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('testPaymentConnection', () => {
    it('应该成功测试支付连接', async () => {
      const testParams = {
        paymentMethod: PAYMENT_METHODS.WECHAT,
        config: {
          appId: 'wx123456',
          mchId: 'mch123456'
        }
      }
      const mockResponse = {
        success: true,
        connected: true,
        message: '连接测试成功'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.testPaymentConnection(testParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/test', testParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('syncPaymentStatus', () => {
    it('应该成功同步支付状态', async () => {
      const syncParams = {
        paymentId: 'PAY002',
        paymentMethod: PAYMENT_METHODS.WECHAT
      }
      const mockResponse = {
        success: true,
        synced: true,
        currentStatus: 'SUCCESS'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.syncPaymentStatus(syncParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/sync', syncParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('batchProcessPayments', () => {
    it('应该成功批量处理支付', async () => {
      const batchParams = {
        payments: [
          { orderId: 'ORDER001', amount: 100, method: PAYMENT_METHODS.CASH },
          { orderId: 'ORDER002', amount: 200, method: PAYMENT_METHODS.WECHAT }
        ],
        batchId: 'BATCH001'
      }
      const mockResponse = {
        success: true,
        batchId: 'BATCH001',
        processedCount: 2,
        results: [
          { orderId: 'ORDER001', status: 'SUCCESS' },
          { orderId: 'ORDER002', status: 'SUCCESS' }
        ]
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await PaymentApi.batchProcessPayments(batchParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/payment/batch', {
        ...batchParams,
        processTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
})