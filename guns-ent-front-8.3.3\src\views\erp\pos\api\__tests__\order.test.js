/**
 * 订单API单元测试
 * 
 * 测试订单相关API接口的调用和错误处理
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { OrderApi } from '../order'
import Request from '@/utils/request/request-util'
import { ORDER_STATUS } from '../../utils/constants'

// Mock Request模块
vi.mock('@/utils/request/request-util', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}))

// Mock 错误处理器和性能监控器
vi.mock('../../utils/error-handler', () => ({
  PosErrorHandler: {
    wrapApiCall: vi.fn((fn) => fn)
  }
}))

vi.mock('../../utils/performance-monitor', () => ({
  PerformanceMonitor: {
    measureApiCall: vi.fn((name, fn) => fn)
  }
}))

describe('OrderApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('createOrder', () => {
    it('应该成功创建订单', async () => {
      const orderData = {
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
        ],
        member: { id: 'M001', cardNo: 'VIP123456' },
        totalAmount: 11,
        discountAmount: 1,
        finalAmount: 10,
        cashierId: 'CASHIER001',
        remark: '测试订单'
      }
      const mockResponse = {
        success: true,
        orderId: 'ORDER001',
        orderNo: 'POS20250102001',
        status: ORDER_STATUS.PENDING,
        createdAt: '2025-01-02T10:00:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.createOrder(orderData)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/order/create', {
        ...orderData,
        createTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理订单创建失败的情况', async () => {
      const mockError = new Error('订单创建失败')
      Request.post.mockRejectedValue(mockError)
      
      const orderData = {
        items: [],
        totalAmount: 0,
        finalAmount: 0
      }
      
      await expect(OrderApi.createOrder(orderData)).rejects.toThrow('订单创建失败')
    })
  })
  
  describe('getOrderDetail', () => {
    it('应该成功获取订单详情', async () => {
      const mockOrder = {
        orderId: 'ORDER001',
        orderNo: 'POS20250102001',
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
        ],
        totalAmount: 11,
        finalAmount: 10,
        status: ORDER_STATUS.PAID,
        createdAt: '2025-01-02T10:00:00Z'
      }
      Request.get.mockResolvedValue(mockOrder)
      
      const result = await OrderApi.getOrderDetail('ORDER001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/detail/ORDER001')
      expect(result).toEqual(mockOrder)
    })
    
    it('应该处理订单不存在的情况', async () => {
      const mockError = new Error('订单不存在')
      Request.get.mockRejectedValue(mockError)
      
      await expect(OrderApi.getOrderDetail('INVALID')).rejects.toThrow('订单不存在')
    })
  })
  
  describe('updateOrderStatus', () => {
    it('应该成功更新订单状态', async () => {
      const updateParams = {
        orderId: 'ORDER001',
        status: ORDER_STATUS.PAID,
        updatedBy: 'CASHIER001',
        remark: '支付完成'
      }
      const mockResponse = {
        success: true,
        orderId: 'ORDER001',
        status: ORDER_STATUS.PAID,
        updatedAt: '2025-01-02T10:30:00Z'
      }
      Request.put.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.updateOrderStatus(updateParams)
      
      expect(Request.put).toHaveBeenCalledWith('/erp/pos/order/status', {
        ...updateParams,
        updateTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('cancelOrder', () => {
    it('应该成功取消订单', async () => {
      const cancelParams = {
        orderId: 'ORDER001',
        cancelReason: '用户取消',
        cancelBy: 'CASHIER001'
      }
      const mockResponse = {
        success: true,
        orderId: 'ORDER001',
        status: ORDER_STATUS.CANCELLED,
        cancelledAt: '2025-01-02T10:45:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.cancelOrder(cancelParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/order/cancel', {
        ...cancelParams,
        cancelTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理已支付订单取消失败', async () => {
      const mockError = new Error('已支付订单无法取消')
      Request.post.mockRejectedValue(mockError)
      
      await expect(OrderApi.cancelOrder({
        orderId: 'ORDER001',
        cancelReason: '测试',
        cancelBy: 'CASHIER001'
      })).rejects.toThrow('已支付订单无法取消')
    })
  })
  
  describe('getOrderList', () => {
    it('应该成功获取订单列表', async () => {
      const queryParams = {
        cashierId: 'CASHIER001',
        status: ORDER_STATUS.PAID,
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z',
        page: 1,
        limit: 20
      }
      const mockResponse = {
        orders: [
          {
            orderId: 'ORDER001',
            orderNo: 'POS20250102001',
            totalAmount: 100,
            status: ORDER_STATUS.PAID,
            createdAt: '2025-01-02T10:00:00Z'
          }
        ],
        total: 1,
        page: 1,
        limit: 20
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.getOrderList(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/list', queryParams)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该使用默认查询参数', async () => {
      Request.get.mockResolvedValue({ orders: [], total: 0 })
      
      await OrderApi.getOrderList({})
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/list', {
        page: 1,
        limit: 20
      })
    })
  })
  
  describe('searchOrders', () => {
    it('应该成功搜索订单', async () => {
      const searchParams = {
        keyword: 'POS20250102001',
        searchType: 'orderNo'
      }
      const mockResponse = [
        {
          orderId: 'ORDER001',
          orderNo: 'POS20250102001',
          totalAmount: 100,
          status: ORDER_STATUS.PAID
        }
      ]
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.searchOrders(searchParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/search', {
        ...searchParams,
        limit: 50
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('generateOrderNo', () => {
    it('应该成功生成订单号', async () => {
      const mockResponse = {
        orderNo: 'POS20250102001',
        prefix: 'POS',
        sequence: 1
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.generateOrderNo({ storeId: 'STORE001' })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/generateNo', {
        storeId: 'STORE001'
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('printOrder', () => {
    it('应该成功打印订单', async () => {
      const printParams = {
        orderId: 'ORDER001',
        printType: 'receipt',
        printerName: 'POS_PRINTER_001'
      }
      const mockResponse = {
        success: true,
        printJobId: 'PRINT001',
        status: 'QUEUED'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.printOrder(printParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/order/print', {
        ...printParams,
        printTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getOrderStatistics', () => {
    it('应该成功获取订单统计', async () => {
      const queryParams = {
        cashierId: 'CASHIER001',
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z'
      }
      const mockResponse = {
        totalOrders: 10,
        totalAmount: 1000,
        averageAmount: 100,
        statusStats: {
          [ORDER_STATUS.PAID]: 8,
          [ORDER_STATUS.CANCELLED]: 2
        }
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.getOrderStatistics(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/statistics', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('exportOrders', () => {
    it('应该成功导出订单', async () => {
      const exportParams = {
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z',
        format: 'excel',
        fields: ['orderNo', 'totalAmount', 'status', 'createdAt']
      }
      const mockResponse = {
        success: true,
        downloadUrl: '/api/download/orders_20250102.xlsx',
        fileName: 'orders_20250102.xlsx'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.exportOrders(exportParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/order/export', {
        ...exportParams,
        exportTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('validateOrderData', () => {
    it('应该成功验证订单数据', async () => {
      const orderData = {
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
        ],
        totalAmount: 11,
        finalAmount: 10
      }
      const mockResponse = {
        valid: true,
        errors: []
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.validateOrderData(orderData)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/order/validate', orderData)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理验证失败的情况', async () => {
      const invalidOrderData = {
        items: [],
        totalAmount: 0,
        finalAmount: 0
      }
      const mockResponse = {
        valid: false,
        errors: ['订单商品不能为空', '订单金额不能为0']
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.validateOrderData(invalidOrderData)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
    })
  })
  
  describe('getOrderReceipt', () => {
    it('应该成功获取订单小票', async () => {
      const receiptParams = {
        orderId: 'ORDER001',
        template: 'default'
      }
      const mockResponse = {
        receiptData: {
          orderNo: 'POS20250102001',
          items: [
            { name: '苹果', quantity: 2, price: 5.5, subtotal: 11 }
          ],
          totalAmount: 11,
          finalAmount: 10,
          printTime: '2025-01-02 10:30:00'
        },
        template: 'default'
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.getOrderReceipt(receiptParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/receipt', receiptParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('batchUpdateOrders', () => {
    it('应该成功批量更新订单', async () => {
      const batchParams = {
        orderIds: ['ORDER001', 'ORDER002'],
        updates: {
          status: ORDER_STATUS.COMPLETED,
          updatedBy: 'CASHIER001'
        }
      }
      const mockResponse = {
        success: true,
        updatedCount: 2,
        results: [
          { orderId: 'ORDER001', success: true },
          { orderId: 'ORDER002', success: true }
        ]
      }
      Request.put.mockResolvedValue(mockResponse)
      
      const result = await OrderApi.batchUpdateOrders(batchParams)
      
      expect(Request.put).toHaveBeenCalledWith('/erp/pos/order/batchUpdate', {
        ...batchParams,
        updateTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getOrderTimeline', () => {
    it('应该成功获取订单时间线', async () => {
      const mockTimeline = [
        {
          action: 'CREATE',
          status: ORDER_STATUS.PENDING,
          timestamp: '2025-01-02T10:00:00Z',
          operator: 'CASHIER001',
          remark: '订单创建'
        },
        {
          action: 'PAY',
          status: ORDER_STATUS.PAID,
          timestamp: '2025-01-02T10:30:00Z',
          operator: 'CASHIER001',
          remark: '支付完成'
        }
      ]
      Request.get.mockResolvedValue(mockTimeline)
      
      const result = await OrderApi.getOrderTimeline('ORDER001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/order/timeline/ORDER001')
      expect(result).toEqual(mockTimeline)
    })
  })
})