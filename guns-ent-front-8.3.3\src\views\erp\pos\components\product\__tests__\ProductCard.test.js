/**
 * 商品卡片组件单元测试
 * 
 * 测试商品卡片组件的渲染和交互功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ProductCard from '../ProductCard.vue'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  Card: {
    name: 'ACard',
    template: '<div class="ant-card"><slot /></div>'
  },
  Button: {
    name: 'AButton',
    template: '<button @click="$emit(\'click\')"><slot /></button>'
  },
  Tag: {
    name: 'ATag',
    template: '<span class="ant-tag"><slot /></span>'
  },
  Badge: {
    name: 'ABadge',
    template: '<span class="ant-badge"><slot /></span>'
  },
  Tooltip: {
    name: 'ATooltip',
    template: '<div><slot /></div>'
  }
}))

describe('ProductCard', () => {
  let wrapper
  let pinia
  
  const mockProduct = {
    id: 'P001',
    name: '红苹果',
    price: 5.50,
    stock: 100,
    unit: '斤',
    barcode: '1234567890123',
    categoryId: 'CAT001',
    categoryName: '水果',
    specifications: '新鲜红苹果，产地山东',
    image: '/images/apple.jpg',
    status: 'ACTIVE',
    safetyStock: 10,
    pricingType: 'NORMAL'
  }
  
  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(ProductCard, {
      props: {
        product: mockProduct,
        ...props
      },
      global: {
        plugins: [pinia],
        stubs: {
          'a-card': true,
          'a-button': true,
          'a-tag': true,
          'a-badge': true,
          'a-tooltip': true
        }
      }
    })
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  describe('基础渲染', () => {
    it('应该正确渲染商品信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.product-card').exists()).toBe(true)
      expect(wrapper.text()).toContain('红苹果')
      expect(wrapper.text()).toContain('¥5.50')
      expect(wrapper.text()).toContain('斤')
      expect(wrapper.text()).toContain('库存: 100')
    })
    
    it('应该显示商品图片', () => {
      wrapper = createWrapper()
      
      const image = wrapper.find('.product-image img')
      expect(image.exists()).toBe(true)
      expect(image.attributes('src')).toBe('/images/apple.jpg')
      expect(image.attributes('alt')).toBe('红苹果')
    })
    
    it('应该在没有图片时显示默认图片', () => {
      const productWithoutImage = { ...mockProduct, image: null }
      wrapper = createWrapper({ product: productWithoutImage })
      
      const image = wrapper.find('.product-image img')
      expect(image.attributes('src')).toContain('default-product.png')
    })
    
    it('应该显示商品价格', () => {
      wrapper = createWrapper()
      
      const priceElement = wrapper.find('.product-price')
      expect(priceElement.exists()).toBe(true)
      expect(priceElement.text()).toContain('¥5.50')
    })
    
    it('应该显示商品单位', () => {
      wrapper = createWrapper()
      
      const unitElement = wrapper.find('.product-unit')
      expect(unitElement.exists()).toBe(true)
      expect(unitElement.text()).toContain('/斤')
    })
  })
  
  describe('库存状态', () => {
    it('应该显示正常库存状态', () => {
      wrapper = createWrapper()
      
      const stockElement = wrapper.find('.product-stock')
      expect(stockElement.exists()).toBe(true)
      expect(stockElement.text()).toContain('库存: 100')
      expect(stockElement.classes()).not.toContain('stock-warning')
    })
    
    it('应该显示库存预警状态', () => {
      const lowStockProduct = {
        ...mockProduct,
        stock: 5,
        safetyStock: 10
      }
      wrapper = createWrapper({ product: lowStockProduct })
      
      const stockElement = wrapper.find('.product-stock')
      expect(stockElement.classes()).toContain('stock-warning')
      expect(wrapper.find('.stock-warning-icon').exists()).toBe(true)
    })
    
    it('应该显示缺货状态', () => {
      const outOfStockProduct = {
        ...mockProduct,
        stock: 0
      }
      wrapper = createWrapper({ product: outOfStockProduct })
      
      const stockElement = wrapper.find('.product-stock')
      expect(stockElement.classes()).toContain('out-of-stock')
      expect(stockElement.text()).toContain('缺货')
    })
    
    it('应该在缺货时禁用添加按钮', () => {
      const outOfStockProduct = {
        ...mockProduct,
        stock: 0
      }
      wrapper = createWrapper({ product: outOfStockProduct })
      
      const addButton = wrapper.find('.add-to-cart-btn')
      expect(addButton.attributes('disabled')).toBeDefined()
    })
  })
  
  describe('商品状态', () => {
    it('应该显示正常商品状态', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.product-status-inactive').exists()).toBe(false)
      expect(wrapper.find('.product-status-discontinued').exists()).toBe(false)
    })
    
    it('应该显示下架商品状态', () => {
      const inactiveProduct = {
        ...mockProduct,
        status: 'INACTIVE'
      }
      wrapper = createWrapper({ product: inactiveProduct })
      
      const statusTag = wrapper.find('.product-status-tag')
      expect(statusTag.exists()).toBe(true)
      expect(statusTag.text()).toContain('已下架')
    })
    
    it('应该显示停产商品状态', () => {
      const discontinuedProduct = {
        ...mockProduct,
        status: 'DISCONTINUED'
      }
      wrapper = createWrapper({ product: discontinuedProduct })
      
      const statusTag = wrapper.find('.product-status-tag')
      expect(statusTag.exists()).toBe(true)
      expect(statusTag.text()).toContain('停产')
    })
    
    it('应该在商品下架时禁用添加按钮', () => {
      const inactiveProduct = {
        ...mockProduct,
        status: 'INACTIVE'
      }
      wrapper = createWrapper({ product: inactiveProduct })
      
      const addButton = wrapper.find('.add-to-cart-btn')
      expect(addButton.attributes('disabled')).toBeDefined()
    })
  })
  
  describe('定价类型', () => {
    it('应该显示普通定价商品', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.pricing-type-tag').exists()).toBe(false)
    })
    
    it('应该显示计重商品标识', () => {
      const weightProduct = {
        ...mockProduct,
        pricingType: 'WEIGHT'
      }
      wrapper = createWrapper({ product: weightProduct })
      
      const pricingTag = wrapper.find('.pricing-type-tag')
      expect(pricingTag.exists()).toBe(true)
      expect(pricingTag.text()).toContain('计重')
    })
    
    it('应该显示计件商品标识', () => {
      const pieceProduct = {
        ...mockProduct,
        pricingType: 'PIECE'
      }
      wrapper = createWrapper({ product: pieceProduct })
      
      const pricingTag = wrapper.find('.pricing-type-tag')
      expect(pricingTag.exists()).toBe(true)
      expect(pricingTag.text()).toContain('计件')
    })
  })
  
  describe('商品规格', () => {
    it('应该显示商品规格信息', () => {
      wrapper = createWrapper()
      
      const specsElement = wrapper.find('.product-specifications')
      expect(specsElement.exists()).toBe(true)
      expect(specsElement.text()).toContain('新鲜红苹果，产地山东')
    })
    
    it('应该在没有规格时隐藏规格信息', () => {
      const productWithoutSpecs = { ...mockProduct, specifications: null }
      wrapper = createWrapper({ product: productWithoutSpecs })
      
      expect(wrapper.find('.product-specifications').exists()).toBe(false)
    })
    
    it('应该截断过长的规格信息', () => {
      const longSpecsProduct = {
        ...mockProduct,
        specifications: '这是一个非常长的商品规格描述，用于测试文本截断功能是否正常工作，应该会被截断显示省略号'
      }
      wrapper = createWrapper({ product: longSpecsProduct })
      
      const specsElement = wrapper.find('.product-specifications')
      expect(specsElement.text()).toContain('...')
    })
  })
  
  describe('条码信息', () => {
    it('应该显示商品条码', () => {
      wrapper = createWrapper()
      
      const barcodeElement = wrapper.find('.product-barcode')
      expect(barcodeElement.exists()).toBe(true)
      expect(barcodeElement.text()).toContain('1234567890123')
    })
    
    it('应该在没有条码时隐藏条码信息', () => {
      const productWithoutBarcode = { ...mockProduct, barcode: null }
      wrapper = createWrapper({ product: productWithoutBarcode })
      
      expect(wrapper.find('.product-barcode').exists()).toBe(false)
    })
  })
  
  describe('交互功能', () => {
    it('应该在点击商品卡片时触发选择事件', async () => {
      wrapper = createWrapper()
      
      const productCard = wrapper.find('.product-card')
      await productCard.trigger('click')
      
      expect(wrapper.emitted('product-click')).toBeTruthy()
      expect(wrapper.emitted('product-click')[0]).toEqual([mockProduct])
    })
    
    it('应该在点击添加按钮时触发添加事件', async () => {
      wrapper = createWrapper()
      
      const addButton = wrapper.find('.add-to-cart-btn')
      await addButton.trigger('click')
      
      expect(wrapper.emitted('add-to-cart')).toBeTruthy()
      expect(wrapper.emitted('add-to-cart')[0]).toEqual([mockProduct, 1])
    })
    
    it('应该支持快速添加多个数量', async () => {
      wrapper = createWrapper()
      
      const addButton = wrapper.find('.add-to-cart-btn')
      
      // 模拟长按或右键点击
      await addButton.trigger('contextmenu')
      
      const quickAddMenu = wrapper.find('.quick-add-menu')
      if (quickAddMenu.exists()) {
        const add5Button = wrapper.find('.quick-add-5')
        await add5Button.trigger('click')
        
        expect(wrapper.emitted('add-to-cart')).toBeTruthy()
        expect(wrapper.emitted('add-to-cart')[0]).toEqual([mockProduct, 5])
      }
    })
    
    it('应该在双击时快速添加到购物车', async () => {
      wrapper = createWrapper()
      
      const productCard = wrapper.find('.product-card')
      await productCard.trigger('dblclick')
      
      expect(wrapper.emitted('add-to-cart')).toBeTruthy()
      expect(wrapper.emitted('add-to-cart')[0]).toEqual([mockProduct, 1])
    })
  })
  
  describe('视觉效果', () => {
    it('应该在鼠标悬停时显示悬停效果', async () => {
      wrapper = createWrapper()
      
      const productCard = wrapper.find('.product-card')
      await productCard.trigger('mouseenter')
      
      expect(productCard.classes()).toContain('product-card--hover')
      
      await productCard.trigger('mouseleave')
      expect(productCard.classes()).not.toContain('product-card--hover')
    })
    
    it('应该在选中时显示选中状态', () => {
      wrapper = createWrapper({ selected: true })
      
      const productCard = wrapper.find('.product-card')
      expect(productCard.classes()).toContain('product-card--selected')
    })
    
    it('应该显示商品徽章', () => {
      const hotProduct = {
        ...mockProduct,
        isHot: true,
        isNew: true
      }
      wrapper = createWrapper({ product: hotProduct })
      
      expect(wrapper.find('.hot-badge').exists()).toBe(true)
      expect(wrapper.find('.new-badge').exists()).toBe(true)
    })
  })
  
  describe('促销信息', () => {
    it('应该显示促销标签', () => {
      const promotionProduct = {
        ...mockProduct,
        promotion: {
          type: 'DISCOUNT',
          value: 0.8,
          description: '8折优惠'
        }
      }
      wrapper = createWrapper({ product: promotionProduct })
      
      const promotionTag = wrapper.find('.promotion-tag')
      expect(promotionTag.exists()).toBe(true)
      expect(promotionTag.text()).toContain('8折')
    })
    
    it('应该显示原价和促销价', () => {
      const promotionProduct = {
        ...mockProduct,
        originalPrice: 6.0,
        promotionPrice: 4.8,
        promotion: {
          type: 'DISCOUNT',
          value: 0.8
        }
      }
      wrapper = createWrapper({ product: promotionProduct })
      
      expect(wrapper.find('.original-price').exists()).toBe(true)
      expect(wrapper.find('.promotion-price').exists()).toBe(true)
      expect(wrapper.text()).toContain('¥6.00')
      expect(wrapper.text()).toContain('¥4.80')
    })
  })
  
  describe('键盘导航', () => {
    it('应该支持键盘选择', async () => {
      wrapper = createWrapper()
      
      const productCard = wrapper.find('.product-card')
      await productCard.trigger('keydown', { key: 'Enter' })
      
      expect(wrapper.emitted('product-click')).toBeTruthy()
    })
    
    it('应该支持键盘添加到购物车', async () => {
      wrapper = createWrapper()
      
      const productCard = wrapper.find('.product-card')
      await productCard.trigger('keydown', { key: ' ' }) // 空格键
      
      expect(wrapper.emitted('add-to-cart')).toBeTruthy()
    })
  })
  
  describe('边界情况', () => {
    it('应该处理空的商品数据', () => {
      wrapper = createWrapper({ product: null })
      
      expect(wrapper.find('.product-card').exists()).toBe(false)
    })
    
    it('应该处理缺少必要字段的商品', () => {
      const incompleteProduct = {
        id: 'P001',
        name: '测试商品'
        // 缺少price, stock等字段
      }
      
      expect(() => {
        wrapper = createWrapper({ product: incompleteProduct })
      }).not.toThrow()
      
      expect(wrapper.find('.product-card').exists()).toBe(true)
    })
    
    it('应该处理极长的商品名称', () => {
      const longNameProduct = {
        ...mockProduct,
        name: '这是一个非常长的商品名称用于测试文本截断功能是否正常工作'
      }
      wrapper = createWrapper({ product: longNameProduct })
      
      const nameElement = wrapper.find('.product-name')
      expect(nameElement.text()).toContain('...')
    })
    
    it('应该处理负数库存', () => {
      const negativeStockProduct = {
        ...mockProduct,
        stock: -5
      }
      wrapper = createWrapper({ product: negativeStockProduct })
      
      const stockElement = wrapper.find('.product-stock')
      expect(stockElement.classes()).toContain('out-of-stock')
    })
    
    it('应该处理零价格商品', () => {
      const freeProduct = {
        ...mockProduct,
        price: 0
      }
      wrapper = createWrapper({ product: freeProduct })
      
      const priceElement = wrapper.find('.product-price')
      expect(priceElement.text()).toContain('免费')
    })
  })
  
  describe('可访问性', () => {
    it('应该有正确的ARIA标签', () => {
      wrapper = createWrapper()
      
      const productCard = wrapper.find('.product-card')
      expect(productCard.attributes('role')).toBe('button')
      expect(productCard.attributes('aria-label')).toContain('红苹果')
      expect(productCard.attributes('tabindex')).toBe('0')
    })
    
    it('应该为图片提供替代文本', () => {
      wrapper = createWrapper()
      
      const image = wrapper.find('.product-image img')
      expect(image.attributes('alt')).toBe('红苹果')
    })
    
    it('应该为禁用状态提供说明', () => {
      const outOfStockProduct = {
        ...mockProduct,
        stock: 0
      }
      wrapper = createWrapper({ product: outOfStockProduct })
      
      const productCard = wrapper.find('.product-card')
      expect(productCard.attributes('aria-disabled')).toBe('true')
    })
  })
  
  describe('性能优化', () => {
    it('应该在商品数据未变化时避免重新渲染', async () => {
      wrapper = createWrapper()
      
      const renderSpy = vi.spyOn(wrapper.vm, '$forceUpdate')
      
      await wrapper.setProps({ product: { ...mockProduct } })
      
      expect(renderSpy).not.toHaveBeenCalled()
    })
    
    it('应该正确处理商品数据的更新', async () => {
      wrapper = createWrapper()
      
      const updatedProduct = {
        ...mockProduct,
        name: '青苹果',
        price: 4.5,
        stock: 80
      }
      
      await wrapper.setProps({ product: updatedProduct })
      
      expect(wrapper.text()).toContain('青苹果')
      expect(wrapper.text()).toContain('¥4.50')
      expect(wrapper.text()).toContain('库存: 80')
    })
  })
})