/* 购物车组件样式 */

/* 购物车主容器 */
.shopping-cart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 购物车头部样式 */
.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  min-height: 40px;
  height: 40px;
  border-bottom: 2px solid #e6f7ff;
  background: #f8fbff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 1px 2px rgba(24, 144, 255, 0.06);
}

.cart-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #1890ff, #52c41a);
  border-radius: 4px 0 0 4px;
}

.cart-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
}

.cart-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 15px;
  font-weight: 600;
  color: #1890ff;
}

.item-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.count-number {
  background: #1890ff;
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 700;
  min-width: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.count-text {
  color: #595959;
  font-weight: 500;
}

.cart-badge {
  transform: scale(1.1);
}

.item-count {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.clear-all-btn {
  min-height: 24px;
  height: 24px;
  font-size: 12px;
  padding: 0 8px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.clear-all-btn .anticon {
  font-size: 13px;
  margin-right: 2px;
}

.clear-all-btn:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 77, 79, 0.25);
}

/* 购物车内容区域 */
.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.cart-items {
  padding: 0 12px;
}

/* 购物车商品项 */
.cart-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px 10px;
  margin-bottom: 12px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  animation: pos-slide-in-right 0.3s ease-out;
}

.cart-item:hover {
  background: #f8fcff;
  border-color: #e6f7ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.08);
}

/* 商品项行布局 */
.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 26px;
}

.item-row-1 {
  /* 第一行：商品名称 + 删除按钮 */
  align-items: flex-start;
}

.item-row-2 {
  /* 第二行：单价 + 数量控制 */
  align-items: center;
}

.item-row-3 {
  /* 第三行：商品编码 + 小计金额 */
  align-items: center;
}

/* 商品信息样式 */
.item-name {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.item-code {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
  flex: 1;
}

.item-unit-price {
  font-size: 13px;
  color: #1890ff;
  font-weight: 600;
  flex: 1;
}

.item-total {
  font-size: 14px;
  font-weight: 600;
  color: #52c41a;
  text-align: right;
}

/* 删除按钮 - 优化为圆角设计 */
.remove-btn {
  width: 28px;
  height: 28px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

.remove-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(255, 77, 79, 0.4);
}

.remove-btn .anticon {
  font-size: 12px;
}

/* 空购物车状态 */
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #8c8c8c;
}

.empty-cart .ant-empty-description {
  color: #bfbfbf;
  font-size: 14px;
  margin-top: 16px;
}

/* 购物车底部汇总 - 重构为左右两部分布局 */
.cart-footer {
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  gap: 16px;
  padding: 16px;
}

/* 左侧区域 - 会员信息展示 */
.cart-footer-left {
  flex: 1;
  min-width: 0;
}

.member-info-section {
  background: #f0f9ff;
  border: 1px solid #e6f7ff;
  border-radius: 8px;
  padding: 12px;
}

.member-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
}

.member-status .anticon {
  color: #1890ff;
  font-size: 16px;
}

.member-name {
  color: #1890ff;
}

.member-placeholder {
  color: #8c8c8c;
  font-style: italic;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-balance,
.member-points,
.member-coupons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.member-balance .label,
.member-points .label,
.member-coupons .label {
  color: #666;
}

.member-balance .value,
.member-points .value,
.member-coupons .value {
  color: #1890ff;
  font-weight: 600;
}

/* 右侧区域 - 价格信息和操作 */
.cart-footer-right {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 金额汇总 */
.amount-summary {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 12px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.summary-row:last-child {
  margin-bottom: 0;
  padding-top: 8px;
  border-top: 1px solid #e8e8e8;
  font-weight: 600;
  font-size: 14px;
}

.summary-row .label {
  color: #666;
}

.summary-row .value {
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.summary-row .discount {
  color: #ff4d4f;
}

.summary-row.total .value {
  color: #ff4d4f;
  font-size: 16px;
}

/* 操作按钮区域 */
.cart-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.suspend-btn {
  background: #fff;
  border: 2px solid #faad14;
  color: #faad14;
}

.suspend-btn:hover:not(:disabled) {
  background: #faad14;
  border-color: #faad14;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
}

.suspend-btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.checkout-btn {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border: 2px solid #52c41a;
  color: #fff;
}

.checkout-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
  border-color: #73d13d;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.checkout-btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cart-footer {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .cart-footer-left,
  .cart-footer-right {
    flex: none;
  }
  
  .member-details {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .member-balance,
  .member-points,
  .member-coupons {
    flex: 1;
    min-width: 80px;
  }
  
  .cart-actions {
    flex-direction: column;
  }
  
  .action-btn {
    height: 44px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .cart-header {
    padding: 4px 8px;
    min-height: 32px;
    height: 32px;
  }
  
  .clear-all-btn {
    min-height: 24px;
    height: 24px;
    font-size: 12px;
    padding: 1px 8px;
  }
  
  .cart-items {
    padding: 0 8px;
  }
  
  .cart-item {
    gap: 4px;
    padding: 8px 6px;
  }
  
  .item-name {
    font-size: 13px;
  }
  
  .item-code {
    font-size: 11px;
  }
  
  .item-unit-price {
    font-size: 12px;
  }
  
  .item-total {
    font-size: 13px;
  }
  
  .remove-btn {
    width: 24px;
    height: 24px;
  }
  
  .summary-row {
    font-size: 12px;
  }
  
  .summary-row.total {
    font-size: 13px;
  }
  
  .summary-row.total .value {
    font-size: 14px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .cart-item:hover {
    background: #fafafa;
  }
  
  .remove-btn:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .cart-item {
    border: 1px solid #d9d9d9;
  }
  
  .member-info-section {
    border: 1px solid #1890ff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .cart-item {
    animation: none;
  }
  
  .clear-all-btn,
  .remove-btn,
  .suspend-btn,
  .checkout-btn {
    transition: none;
  }
  
  .clear-all-btn:hover,
  .suspend-btn:hover:not(:disabled) {
    transform: none;
  }
}
