/**
 * POS状态持久化配置
 * 
 * 配置各个状态模块的持久化策略，确保敏感数据的安全性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

/**
 * 购物车状态持久化配置
 */
export const cartPersistenceConfig = {
  keyPrefix: 'pos_cart_',
  enableEncryption: false, // 购物车数据不需要加密
  enableCompression: true,
  expireTime: 2 * 60 * 60 * 1000, // 2小时过期
  storageEngine: 'localStorage',
  autoSaveInterval: 30000, // 30秒自动保存
  enableAutoSave: true,
  
  // 需要持久化的状态路径
  persistPaths: [
    'cartItems',
    'totalAmount',
    'discountAmount',
    'finalAmount'
  ],
  
  // 排除的状态路径
  excludePaths: [
    'isBatchUpdating' // 临时状态不需要持久化
  ]
}

/**
 * 会员状态持久化配置
 */
export const memberPersistenceConfig = {
  keyPrefix: 'pos_member_',
  enableEncryption: true, // 会员信息需要加密
  enableCompression: true,
  expireTime: 24 * 60 * 60 * 1000, // 24小时过期
  storageEngine: 'localStorage',
  autoSaveInterval: 60000, // 1分钟自动保存
  enableAutoSave: true,
  
  persistPaths: [
    'currentMember',
    'memberDiscountRate',
    'pointsDeductionAmount',
    'pointsExchangeRate'
  ],
  
  excludePaths: [
    'memberLevels' // 会员等级配置不需要持久化
  ],
  
  // 敏感数据处理
  sensitiveFields: [
    'phone',
    'idCard',
    'address',
    'email'
  ]
}

/**
 * 支付状态持久化配置
 */
export const paymentPersistenceConfig = {
  keyPrefix: 'pos_payment_',
  enableEncryption: true, // 支付信息需要加密
  enableCompression: true,
  expireTime: 30 * 60 * 1000, // 30分钟过期
  storageEngine: 'sessionStorage', // 使用会话存储，更安全
  autoSaveInterval: 10000, // 10秒自动保存
  enableAutoSave: false, // 支付状态不自动保存，手动控制
  
  persistPaths: [
    'selectedPaymentMethod',
    'receivedAmount',
    'changeAmount'
  ],
  
  excludePaths: [
    'paymentStatus', // 支付状态不持久化
    'paymentResult', // 支付结果不持久化
    'paymentStartTime', // 支付开始时间不持久化
    'paymentMethods' // 支付方式配置不持久化
  ]
}

/**
 * 订单状态持久化配置
 */
export const orderPersistenceConfig = {
  keyPrefix: 'pos_order_',
  enableEncryption: false, // 订单历史不需要加密
  enableCompression: true,
  expireTime: 7 * 24 * 60 * 60 * 1000, // 7天过期
  storageEngine: 'localStorage',
  autoSaveInterval: 60000, // 1分钟自动保存
  enableAutoSave: true,
  
  persistPaths: [
    'suspendedOrders',
    'orderHistory',
    'suspendCounter'
  ],
  
  excludePaths: [
    'currentOrder', // 当前订单不持久化
    'orderConfig' // 订单配置不持久化
  ],
  
  // 数据量限制
  maxSuspendedOrders: 50,
  maxOrderHistory: 100
}

/**
 * 主存储持久化配置
 */
export const mainPersistenceConfig = {
  keyPrefix: 'pos_main_',
  enableEncryption: false,
  enableCompression: true,
  expireTime: 24 * 60 * 60 * 1000, // 24小时过期
  storageEngine: 'localStorage',
  autoSaveInterval: 30000, // 30秒自动保存
  enableAutoSave: true,
  
  persistPaths: [
    'categories',
    'selectedCategory',
    'searchKeyword'
  ],
  
  excludePaths: [
    'products', // 商品列表不持久化，数据量太大
    'isInitialized',
    'isLoading',
    'lastUpdateTime',
    'isBatchUpdating',
    'batchUpdateQueue'
  ]
}

/**
 * 安全配置
 */
export const securityConfig = {
  // 敏感数据字段列表
  sensitiveFields: [
    'password',
    'token',
    'cardNo',
    'phone',
    'idCard',
    'address',
    'email',
    'bankAccount'
  ],
  
  // 数据脱敏规则
  maskingRules: {
    phone: (value) => value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
    idCard: (value) => value.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2'),
    cardNo: (value) => value.replace(/(\d{4})\d{8}(\d{4})/, '$1********$2'),
    email: (value) => value.replace(/(.{2}).*(@.*)/, '$1***$2')
  },
  
  // 加密密钥（实际项目中应该从环境变量获取）
  encryptionKey: 'pos_encryption_key_2025',
  
  // 数据完整性校验
  enableIntegrityCheck: true,
  
  // 自动清理过期数据
  enableAutoCleanup: true,
  autoCleanupInterval: 60 * 60 * 1000 // 1小时检查一次
}

/**
 * 性能配置
 */
export const performanceConfig = {
  // 批量保存配置
  batchSave: {
    enabled: true,
    batchSize: 10,
    batchInterval: 5000 // 5秒
  },
  
  // 压缩配置
  compression: {
    enabled: true,
    threshold: 1024, // 超过1KB才压缩
    algorithm: 'simple' // 简单压缩算法
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    maxSize: 100, // 最大缓存条目数
    ttl: 5 * 60 * 1000 // 5分钟TTL
  },
  
  // 存储配额管理
  quota: {
    enabled: true,
    maxSize: 10 * 1024 * 1024, // 10MB
    warningThreshold: 0.8, // 80%警告
    cleanupThreshold: 0.9 // 90%清理
  }
}

/**
 * 获取存储配置
 * @param {string} storeId - 存储ID
 * @returns {Object} 配置对象
 */
export function getPersistenceConfig(storeId) {
  const configs = {
    'pos-cart': cartPersistenceConfig,
    'pos-member': memberPersistenceConfig,
    'pos-payment': paymentPersistenceConfig,
    'pos-order': orderPersistenceConfig,
    'pos': mainPersistenceConfig
  }
  
  return configs[storeId] || mainPersistenceConfig
}

/**
 * 数据脱敏处理
 * @param {Object} data - 原始数据
 * @param {Array} sensitiveFields - 敏感字段列表
 * @returns {Object} 脱敏后的数据
 */
export function maskSensitiveData(data, sensitiveFields = securityConfig.sensitiveFields) {
  if (!data || typeof data !== 'object') {
    return data
  }
  
  const masked = { ...data }
  
  sensitiveFields.forEach(field => {
    if (masked[field]) {
      const maskingRule = securityConfig.maskingRules[field]
      if (maskingRule && typeof maskingRule === 'function') {
        masked[field] = maskingRule(masked[field])
      } else {
        // 默认脱敏规则
        const value = String(masked[field])
        if (value.length > 4) {
          masked[field] = value.substring(0, 2) + '***' + value.substring(value.length - 2)
        } else {
          masked[field] = '***'
        }
      }
    }
  })
  
  return masked
}

/**
 * 数据完整性校验
 * @param {Object} data - 数据对象
 * @returns {string} 校验和
 */
export function generateChecksum(data) {
  try {
    const str = JSON.stringify(data)
    let hash = 0
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return hash.toString(36)
  } catch (error) {
    console.error('生成校验和失败:', error)
    return ''
  }
}

/**
 * 验证数据完整性
 * @param {Object} data - 数据对象
 * @param {string} expectedChecksum - 期望的校验和
 * @returns {boolean} 是否通过验证
 */
export function verifyChecksum(data, expectedChecksum) {
  if (!securityConfig.enableIntegrityCheck) {
    return true
  }
  
  const actualChecksum = generateChecksum(data)
  return actualChecksum === expectedChecksum
}

/**
 * 清理过期数据
 * @param {Storage} storage - 存储引擎
 * @param {string} keyPrefix - 键前缀
 * @returns {number} 清理的条目数
 */
export function cleanupExpiredData(storage, keyPrefix) {
  let cleanedCount = 0
  
  try {
    const keysToRemove = []
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key && key.startsWith(keyPrefix)) {
        try {
          const data = JSON.parse(storage.getItem(key))
          if (data && data.timestamp) {
            const config = getPersistenceConfig(key.replace(keyPrefix, ''))
            const age = Date.now() - data.timestamp
            
            if (age > config.expireTime) {
              keysToRemove.push(key)
            }
          }
        } catch (error) {
          // 数据格式错误，也删除
          keysToRemove.push(key)
        }
      }
    }
    
    keysToRemove.forEach(key => {
      storage.removeItem(key)
      cleanedCount++
    })
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期数据项`)
    }
    
  } catch (error) {
    console.error('清理过期数据失败:', error)
  }
  
  return cleanedCount
}

/**
 * 检查存储配额
 * @param {Storage} storage - 存储引擎
 * @returns {Object} 配额信息
 */
export function checkStorageQuota(storage) {
  const quota = performanceConfig.quota
  
  if (!quota.enabled) {
    return { usage: 0, available: Infinity, percentage: 0 }
  }
  
  try {
    let totalSize = 0
    
    for (let i = 0; i < storage.length; i++) {
      const key = storage.key(i)
      if (key) {
        const value = storage.getItem(key)
        totalSize += key.length + (value ? value.length : 0)
      }
    }
    
    const percentage = totalSize / quota.maxSize
    
    return {
      usage: totalSize,
      available: quota.maxSize - totalSize,
      percentage: percentage,
      isWarning: percentage > quota.warningThreshold,
      needsCleanup: percentage > quota.cleanupThreshold
    }
    
  } catch (error) {
    console.error('检查存储配额失败:', error)
    return { usage: 0, available: 0, percentage: 1 }
  }
}

/**
 * 自动清理存储空间
 * @param {Storage} storage - 存储引擎
 * @param {string} keyPrefix - 键前缀
 * @returns {boolean} 是否清理成功
 */
export function autoCleanupStorage(storage, keyPrefix) {
  try {
    const quotaInfo = checkStorageQuota(storage)
    
    if (!quotaInfo.needsCleanup) {
      return true
    }
    
    console.warn('存储空间不足，开始自动清理...')
    
    // 1. 清理过期数据
    const expiredCount = cleanupExpiredData(storage, keyPrefix)
    
    // 2. 如果还不够，清理最旧的数据
    const updatedQuota = checkStorageQuota(storage)
    if (updatedQuota.needsCleanup) {
      const oldestKeys = []
      
      for (let i = 0; i < storage.length; i++) {
        const key = storage.key(i)
        if (key && key.startsWith(keyPrefix)) {
          try {
            const data = JSON.parse(storage.getItem(key))
            if (data && data.timestamp) {
              oldestKeys.push({ key, timestamp: data.timestamp })
            }
          } catch (error) {
            // 忽略解析错误
          }
        }
      }
      
      // 按时间排序，删除最旧的数据
      oldestKeys.sort((a, b) => a.timestamp - b.timestamp)
      const toDelete = Math.ceil(oldestKeys.length * 0.2) // 删除20%最旧的数据
      
      for (let i = 0; i < toDelete; i++) {
        storage.removeItem(oldestKeys[i].key)
      }
      
      console.log(`🧹 额外清理了 ${toDelete} 个最旧的数据项`)
    }
    
    return true
    
  } catch (error) {
    console.error('自动清理存储空间失败:', error)
    return false
  }
}

// 默认导出配置对象
export default {
  cart: cartPersistenceConfig,
  member: memberPersistenceConfig,
  payment: paymentPersistenceConfig,
  order: orderPersistenceConfig,
  main: mainPersistenceConfig,
  security: securityConfig,
  performance: performanceConfig,
  
  // 工具函数
  getPersistenceConfig,
  maskSensitiveData,
  generateChecksum,
  verifyChecksum,
  cleanupExpiredData,
  checkStorageQuota,
  autoCleanupStorage
}