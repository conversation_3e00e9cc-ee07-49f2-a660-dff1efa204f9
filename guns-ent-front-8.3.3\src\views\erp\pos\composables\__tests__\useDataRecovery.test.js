import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useDataRecovery } from '../useDataRecovery'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    warning: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  },
  Modal: {
    confirm: vi.fn()
  }
}))

// Mock constants
vi.mock('../../utils/constants', () => ({
  STORAGE_KEYS: {
    DATA_BACKUP_PREFIX: 'pos_backup',
    CART_BACKUP: 'pos_cart_backup'
  },
  NUMERIC_CONSTANTS: {
    CART_BACKUP_EXPIRE_HOURS: 24
  }
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn()
}
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
})

// Mock Vue
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    onMounted: vi.fn(fn => fn()),
    onUnmounted: vi.fn()
  }
})

describe('useDataRecovery', () => {
  let dataRecovery
  
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    dataRecovery = useDataRecovery()
  })
  
  afterEach(() => {
    vi.useRealTimers()
    vi.restoreAllMocks()
  })

  describe('基础功能', () => {
    it('应该返回所有必要的状态和方法', () => {
      expect(dataRecovery.recoveryState).toBeDefined()
      expect(dataRecovery.config).toBeDefined()
      expect(dataRecovery.canRecover).toBeDefined()
      expect(dataRecovery.backupStatus).toBeDefined()
      expect(dataRecovery.backupTypes).toBeDefined()
      
      expect(dataRecovery.saveData).toBeDefined()
      expect(dataRecovery.restoreData).toBeDefined()
      expect(dataRecovery.checkRecoverableData).toBeDefined()
      expect(dataRecovery.showRecoveryPrompt).toBeDefined()
      expect(dataRecovery.performBatchRecovery).toBeDefined()
      expect(dataRecovery.setupAutoBackup).toBeDefined()
      expect(dataRecovery.stopAutoBackup).toBeDefined()
      expect(dataRecovery.clearAllBackups).toBeDefined()
      expect(dataRecovery.getBackupStatistics).toBeDefined()
      expect(dataRecovery.exportBackupData).toBeDefined()
      
      // 兼容性方法
      expect(dataRecovery.saveCartState).toBeDefined()
      expect(dataRecovery.restoreCartState).toBeDefined()
      expect(dataRecovery.clearBackup).toBeDefined()
    })

    it('应该正确初始化状态', () => {
      expect(dataRecovery.recoveryState.value.isRecovering).toBe(false)
      expect(dataRecovery.recoveryState.value.hasRecoverableData).toBe(false)
      expect(dataRecovery.recoveryState.value.lastBackupTime).toBe(null)
      expect(dataRecovery.recoveryState.value.backupCount).toBe(0)
      expect(dataRecovery.recoveryState.value.recoveryHistory).toEqual([])
    })

    it('应该正确初始化配置', () => {
      expect(dataRecovery.config.value.enableAutoBackup).toBe(true)
      expect(dataRecovery.config.value.backupInterval).toBe(30000)
      expect(dataRecovery.config.value.maxBackupCount).toBe(10)
      expect(dataRecovery.config.value.enableCrashDetection).toBe(true)
      expect(dataRecovery.config.value.enableRecoveryPrompt).toBe(true)
    })
  })

  describe('数据保存', () => {
    it('应该能够保存购物车数据', async () => {
      const cartData = {
        items: [{ id: 1, name: 'Product 1', price: 10 }],
        total: 10
      }
      
      const result = await dataRecovery.saveData('cart', cartData)
      
      expect(result).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
      expect(dataRecovery.recoveryState.value.hasRecoverableData).toBe(true)
      expect(dataRecovery.recoveryState.value.backupCount).toBe(1)
    })

    it('应该能够保存订单数据', async () => {
      const orderData = {
        orderId: 'ORDER_001',
        items: [{ id: 1, name: 'Product 1', price: 10 }],
        total: 10
      }
      
      const result = await dataRecovery.saveData('order', orderData)
      
      expect(result).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('应该处理无效数据', async () => {
      const invalidData = null
      
      const result = await dataRecovery.saveData('cart', invalidData)
      
      expect(result).toBe(false)
    })

    it('应该处理存储失败', async () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })
      
      const cartData = { items: [], total: 0 }
      const result = await dataRecovery.saveData('cart', cartData)
      
      expect(result).toBe(false)
    })
  })

  describe('数据恢复', () => {
    it('应该能够恢复购物车数据', async () => {
      const cartData = {
        items: [{ id: 1, name: 'Product 1', price: 10 }],
        total: 10
      }
      
      // 模拟存储的数据
      const backupData = {
        type: 'cart',
        data: cartData,
        timestamp: Date.now(),
        version: '2.0',
        checksum: 'test_checksum',
        metadata: {}
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(backupData))
      
      const restoredData = await dataRecovery.restoreData('cart')
      
      expect(restoredData).toEqual(cartData)
      expect(dataRecovery.recoveryState.value.recoveryHistory).toHaveLength(1)
    })

    it('应该处理不存在的备份数据', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)
      
      const restoredData = await dataRecovery.restoreData('cart')
      
      expect(restoredData).toBe(null)
    })

    it('应该处理损坏的备份数据', async () => {
      mockLocalStorage.getItem.mockReturnValue('invalid_json')
      
      const restoredData = await dataRecovery.restoreData('cart')
      
      expect(restoredData).toBe(null)
      expect(dataRecovery.recoveryState.value.recoveryHistory[0].success).toBe(false)
    })

    it('应该检查数据年龄', async () => {
      const oldData = {
        type: 'cart',
        data: { items: [], total: 0 },
        timestamp: Date.now() - 25 * 60 * 60 * 1000, // 25小时前
        version: '2.0',
        checksum: 'test_checksum',
        metadata: {}
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(oldData))
      
      const restoredData = await dataRecovery.restoreData('cart', {
        maxAge: 24 * 60 * 60 * 1000 // 24小时
      })
      
      expect(restoredData).toBe(null)
    })
  })

  describe('批量恢复', () => {
    it('应该能够批量恢复多种数据', async () => {
      const cartData = { items: [], total: 0 }
      const orderData = { orderId: 'ORDER_001', items: [] }
      
      mockLocalStorage.getItem
        .mockReturnValueOnce(JSON.stringify({
          type: 'cart',
          data: cartData,
          timestamp: Date.now(),
          version: '2.0',
          checksum: 'cart_checksum',
          metadata: {}
        }))
        .mockReturnValueOnce(JSON.stringify({
          type: 'order',
          data: orderData,
          timestamp: Date.now(),
          version: '2.0',
          checksum: 'order_checksum',
          metadata: {}
        }))
      
      const results = await dataRecovery.performBatchRecovery(['cart', 'order'])
      
      expect(results).toHaveLength(2)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(true)
    })
  })

  describe('自动备份', () => {
    it('应该能够设置自动备份', () => {
      const dataProvider = vi.fn().mockResolvedValue({
        cart: { items: [], total: 0 }
      })
      
      dataRecovery.setupAutoBackup(dataProvider)
      
      // 快进时间触发自动备份
      vi.advanceTimersByTime(30000)
      
      expect(dataProvider).toHaveBeenCalled()
    })

    it('应该能够停止自动备份', () => {
      const dataProvider = vi.fn()
      
      dataRecovery.setupAutoBackup(dataProvider)
      dataRecovery.stopAutoBackup()
      
      // 快进时间，不应该触发备份
      vi.advanceTimersByTime(30000)
      
      expect(dataProvider).not.toHaveBeenCalled()
    })
  })

  describe('崩溃检测', () => {
    it('应该设置心跳检测', () => {
      // 崩溃检测在onMounted中自动设置
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'pos_heartbeat',
        expect.any(String)
      )
    })

    it('应该检测异常退出', async () => {
      // 模拟旧的心跳时间（超过30秒）
      const oldHeartbeat = Date.now() - 35000
      mockLocalStorage.getItem.mockReturnValue(oldHeartbeat.toString())
      
      // 重新创建实例以触发检测
      const newDataRecovery = useDataRecovery()
      
      // 应该检查可恢复数据
      expect(newDataRecovery.recoveryState).toBeDefined()
    })
  })

  describe('数据清理', () => {
    it('应该能够清除指定类型的备份', () => {
      dataRecovery.clearAllBackups('cart')
      
      // 应该删除所有cart类型的备份
      expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(10) // maxBackupCount
    })

    it('应该能够清除所有备份', () => {
      dataRecovery.clearAllBackups()
      
      // 应该删除所有类型的备份
      expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(60) // 6 types * 10 backups
      expect(dataRecovery.recoveryState.value.hasRecoverableData).toBe(false)
    })
  })

  describe('统计信息', () => {
    it('应该能够获取备份统计', () => {
      // 模拟一些备份数据
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key.includes('cart')) {
          return JSON.stringify({
            type: 'cart',
            data: { items: [], total: 0 },
            timestamp: Date.now(),
            version: '2.0',
            checksum: 'test_checksum',
            metadata: {}
          })
        }
        return null
      })
      
      const stats = dataRecovery.getBackupStatistics()
      
      expect(stats.totalBackups).toBeGreaterThan(0)
      expect(stats.typeStats).toBeDefined()
    })

    it('应该能够导出备份数据', () => {
      // 模拟备份数据
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        type: 'cart',
        data: { items: [], total: 0 },
        timestamp: Date.now(),
        version: '2.0',
        checksum: 'test_checksum',
        metadata: {}
      }))
      
      const jsonExport = dataRecovery.exportBackupData('json')
      const csvExport = dataRecovery.exportBackupData('csv')
      
      expect(jsonExport).toContain('exportTime')
      expect(csvExport).toContain('Type,Timestamp,Size,Checksum')
    })
  })

  describe('兼容性方法', () => {
    it('应该支持旧的购物车保存方法', async () => {
      const cartData = { items: [], total: 0 }
      
      const result = await dataRecovery.saveCartState(cartData)
      
      expect(result).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('应该支持旧的购物车恢复方法', async () => {
      const cartData = { items: [], total: 0 }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        type: 'cart',
        data: cartData,
        timestamp: Date.now(),
        version: '2.0',
        checksum: 'test_checksum',
        metadata: {}
      }))
      
      const restoredData = await dataRecovery.restoreCartState()
      
      expect(restoredData).toEqual(cartData)
    })

    it('应该支持旧的清除备份方法', () => {
      dataRecovery.clearBackup()
      
      expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(10)
    })
  })
})