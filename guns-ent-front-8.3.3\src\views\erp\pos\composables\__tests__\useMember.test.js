/**
 * 会员组合式函数单元测试
 * 
 * 测试会员业务逻辑的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useMember } from '../useMember'
import { usePosStore } from '@/stores/pos'
import { MemberApi } from '../../api/member'
import { message } from 'ant-design-vue'

// Mock dependencies
vi.mock('../../api/member')
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('useMember', () => {
  let pinia
  let store
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = usePosStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('基础状态', () => {
    it('应该正确初始化会员状态', () => {
      const { currentMember, hasMember, memberDiscount, memberPoints } = useMember()
      
      expect(currentMember.value).toBe(null)
      expect(hasMember.value).toBe(false)
      expect(memberDiscount.value).toBe(0)
      expect(memberPoints.value).toBe(0)
    })
    
    it('应该正确计算会员状态', () => {
      const { currentMember, hasMember, memberDiscount, memberPoints } = useMember()
      
      // 设置会员数据
      store.currentMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        discountRate: 0.1,
        points: 1000,
        balance: 500
      }
      
      expect(currentMember.value).toEqual(store.currentMember)
      expect(hasMember.value).toBe(true)
      expect(memberDiscount.value).toBe(0.1)
      expect(memberPoints.value).toBe(1000)
    })
  })
  
  describe('searchMember', () => {
    it('应该成功根据卡号搜索会员', async () => {
      const { searchMember } = useMember()
      
      const mockMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        phone: '13812345678',
        level: 'GOLD',
        discountRate: 0.1,
        points: 1000,
        balance: 500,
        status: 'ACTIVE'
      }
      
      MemberApi.searchMember.mockResolvedValue(mockMember)
      
      const result = await searchMember('VIP123456')
      
      expect(result).toEqual(mockMember)
      expect(MemberApi.searchMember).toHaveBeenCalledWith({
        cardNo: 'VIP123456'
      })
    })
    
    it('应该成功根据手机号搜索会员', async () => {
      const { searchMember } = useMember()
      
      const mockMember = {
        id: 'M001',
        phone: '13812345678',
        name: '张三'
      }
      
      MemberApi.searchMember.mockResolvedValue(mockMember)
      
      const result = await searchMember('13812345678')
      
      expect(result).toEqual(mockMember)
      expect(MemberApi.searchMember).toHaveBeenCalledWith({
        phone: '13812345678'
      })
    })
    
    it('应该处理会员不存在的情况', async () => {
      const { searchMember } = useMember()
      
      MemberApi.searchMember.mockResolvedValue(null)
      
      const result = await searchMember('INVALID')
      
      expect(result).toBe(null)
      expect(message.warning).toHaveBeenCalledWith('未找到对应的会员')
    })
    
    it('应该处理搜索失败的情况', async () => {
      const { searchMember } = useMember()
      
      const mockError = new Error('搜索失败')
      MemberApi.searchMember.mockRejectedValue(mockError)
      
      const result = await searchMember('VIP123456')
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('会员搜索失败: 搜索失败')
    })
  })
  
  describe('selectMember', () => {
    it('应该成功选择会员', async () => {
      const { selectMember, currentMember } = useMember()
      
      const member = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        discountRate: 0.1,
        points: 1000,
        balance: 500
      }
      
      const result = await selectMember(member)
      
      expect(result).toBe(true)
      expect(currentMember.value).toEqual(member)
      expect(message.success).toHaveBeenCalledWith('已选择会员: 张三 (VIP123456)')
    })
    
    it('应该处理无效会员', async () => {
      const { selectMember } = useMember()
      
      const result = await selectMember(null)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('会员信息无效')
    })
    
    it('应该处理已冻结的会员', async () => {
      const { selectMember } = useMember()
      
      const frozenMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        status: 'FROZEN'
      }
      
      const result = await selectMember(frozenMember)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('会员已被冻结，无法使用')
    })
  })
  
  describe('clearMember', () => {
    it('应该成功清除会员', async () => {
      const { selectMember, clearMember, currentMember } = useMember()
      
      // 先选择一个会员
      const member = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三'
      }
      await selectMember(member)
      
      // 清除会员
      const result = await clearMember()
      
      expect(result).toBe(true)
      expect(currentMember.value).toBe(null)
      expect(message.info).toHaveBeenCalledWith('已清除会员信息')
    })
  })
  
  describe('calculateMemberDiscount', () => {
    it('应该正确计算会员折扣', async () => {
      const { selectMember, calculateMemberDiscount } = useMember()
      
      const member = {
        id: 'M001',
        discountRate: 0.1,
        minConsumption: 50
      }
      await selectMember(member)
      
      const result = await calculateMemberDiscount(100)
      
      expect(result).toEqual({
        discountAmount: 10,
        finalAmount: 90,
        discountRate: 0.1,
        applicable: true
      })
    })
    
    it('应该处理未达到最低消费的情况', async () => {
      const { selectMember, calculateMemberDiscount } = useMember()
      
      const member = {
        id: 'M001',
        discountRate: 0.1,
        minConsumption: 100
      }
      await selectMember(member)
      
      const result = await calculateMemberDiscount(50)
      
      expect(result).toEqual({
        discountAmount: 0,
        finalAmount: 50,
        discountRate: 0,
        applicable: false,
        reason: '未达到最低消费金额100元'
      })
    })
    
    it('应该处理无会员的情况', async () => {
      const { calculateMemberDiscount } = useMember()
      
      const result = await calculateMemberDiscount(100)
      
      expect(result).toEqual({
        discountAmount: 0,
        finalAmount: 100,
        discountRate: 0,
        applicable: false,
        reason: '未选择会员'
      })
    })
  })
  
  describe('calculatePointsDeduction', () => {
    it('应该正确计算积分抵扣', async () => {
      const { selectMember, calculatePointsDeduction } = useMember()
      
      const member = {
        id: 'M001',
        points: 1000,
        allowPointsPayment: true
      }
      await selectMember(member)
      
      const result = await calculatePointsDeduction(500, 100) // 500积分，100元订单
      
      expect(result).toEqual({
        pointsUsed: 500,
        deductionAmount: 5, // 100积分=1元
        remainingPoints: 500,
        remainingAmount: 95,
        applicable: true
      })
    })
    
    it('应该处理积分不足的情况', async () => {
      const { selectMember, calculatePointsDeduction } = useMember()
      
      const member = {
        id: 'M001',
        points: 100,
        allowPointsPayment: true
      }
      await selectMember(member)
      
      const result = await calculatePointsDeduction(500, 100)
      
      expect(result).toEqual({
        pointsUsed: 100,
        deductionAmount: 1,
        remainingPoints: 0,
        remainingAmount: 99,
        applicable: true,
        warning: '积分不足，只能使用100积分'
      })
    })
    
    it('应该处理不允许积分支付的情况', async () => {
      const { selectMember, calculatePointsDeduction } = useMember()
      
      const member = {
        id: 'M001',
        points: 1000,
        allowPointsPayment: false
      }
      await selectMember(member)
      
      const result = await calculatePointsDeduction(500, 100)
      
      expect(result).toEqual({
        pointsUsed: 0,
        deductionAmount: 0,
        remainingPoints: 1000,
        remainingAmount: 100,
        applicable: false,
        reason: '该会员不允许使用积分支付'
      })
    })
  })
  
  describe('checkMemberBalance', () => {
    it('应该正确检查会员余额', async () => {
      const { selectMember, checkMemberBalance } = useMember()
      
      const member = {
        id: 'M001',
        balance: 500,
        allowBalancePayment: true
      }
      await selectMember(member)
      
      const result = await checkMemberBalance(300)
      
      expect(result).toEqual({
        sufficient: true,
        balance: 500,
        paymentAmount: 300,
        remainingBalance: 200,
        applicable: true
      })
    })
    
    it('应该处理余额不足的情况', async () => {
      const { selectMember, checkMemberBalance } = useMember()
      
      const member = {
        id: 'M001',
        balance: 100,
        allowBalancePayment: true
      }
      await selectMember(member)
      
      const result = await checkMemberBalance(300)
      
      expect(result).toEqual({
        sufficient: false,
        balance: 100,
        paymentAmount: 300,
        shortfall: 200,
        applicable: false,
        reason: '余额不足，还需200元'
      })
    })
  })
  
  describe('getMemberHistory', () => {
    it('应该成功获取会员消费历史', async () => {
      const { selectMember, getMemberHistory } = useMember()
      
      const member = { id: 'M001' }
      await selectMember(member)
      
      const mockHistory = [
        {
          orderId: 'ORDER001',
          orderNo: 'POS20250102001',
          amount: 100,
          discountAmount: 10,
          pointsEarned: 90,
          createdAt: '2025-01-02T10:00:00Z'
        }
      ]
      
      MemberApi.getMemberHistory.mockResolvedValue(mockHistory)
      
      const result = await getMemberHistory({ limit: 10 })
      
      expect(result).toEqual(mockHistory)
      expect(MemberApi.getMemberHistory).toHaveBeenCalledWith({
        memberId: 'M001',
        limit: 10
      })
    })
    
    it('应该处理无会员的情况', async () => {
      const { getMemberHistory } = useMember()
      
      const result = await getMemberHistory()
      
      expect(result).toEqual([])
      expect(message.warning).toHaveBeenCalledWith('请先选择会员')
    })
  })
  
  describe('updateMemberPoints', () => {
    it('应该成功更新会员积分', async () => {
      const { selectMember, updateMemberPoints, currentMember } = useMember()
      
      const member = {
        id: 'M001',
        points: 1000
      }
      await selectMember(member)
      
      const updateParams = {
        changeType: 'EARN',
        points: 100,
        reason: '消费获得',
        orderId: 'ORDER001'
      }
      
      const mockResponse = {
        success: true,
        oldPoints: 1000,
        newPoints: 1100,
        changePoints: 100
      }
      
      MemberApi.updateMemberPoints.mockResolvedValue(mockResponse)
      
      const result = await updateMemberPoints(updateParams)
      
      expect(result).toBe(true)
      expect(currentMember.value.points).toBe(1100)
      expect(MemberApi.updateMemberPoints).toHaveBeenCalledWith({
        memberId: 'M001',
        ...updateParams
      })
    })
  })
  
  describe('validateMemberCard', () => {
    it('应该成功验证会员卡', async () => {
      const { validateMemberCard } = useMember()
      
      const mockResponse = {
        valid: true,
        member: {
          id: 'M001',
          cardNo: 'VIP123456',
          name: '张三'
        }
      }
      
      MemberApi.validateMemberCard.mockResolvedValue(mockResponse)
      
      const result = await validateMemberCard('VIP123456')
      
      expect(result).toEqual(mockResponse)
      expect(MemberApi.validateMemberCard).toHaveBeenCalledWith('VIP123456')
    })
    
    it('应该处理无效会员卡', async () => {
      const { validateMemberCard } = useMember()
      
      const mockResponse = {
        valid: false,
        reason: '会员卡不存在或已失效'
      }
      
      MemberApi.validateMemberCard.mockResolvedValue(mockResponse)
      
      const result = await validateMemberCard('INVALID')
      
      expect(result).toEqual(mockResponse)
      expect(message.error).toHaveBeenCalledWith('会员卡验证失败: 会员卡不存在或已失效')
    })
  })
  
  describe('getMemberLevels', () => {
    it('应该成功获取会员等级列表', async () => {
      const { getMemberLevels } = useMember()
      
      const mockLevels = [
        {
          level: 'BRONZE',
          name: '铜牌会员',
          discountRate: 0.05,
          minConsumption: 0
        },
        {
          level: 'SILVER',
          name: '银牌会员',
          discountRate: 0.08,
          minConsumption: 1000
        },
        {
          level: 'GOLD',
          name: '金牌会员',
          discountRate: 0.1,
          minConsumption: 5000
        }
      ]
      
      MemberApi.getMemberLevels.mockResolvedValue(mockLevels)
      
      const result = await getMemberLevels()
      
      expect(result).toEqual(mockLevels)
      expect(MemberApi.getMemberLevels).toHaveBeenCalled()
    })
  })
  
  describe('quickRegisterMember', () => {
    it('应该成功快速注册会员', async () => {
      const { quickRegisterMember } = useMember()
      
      const memberData = {
        name: '李四',
        phone: '13987654321'
      }
      
      const mockResponse = {
        success: true,
        member: {
          id: 'M002',
          cardNo: 'VIP789012',
          name: '李四',
          phone: '13987654321',
          level: 'BRONZE',
          discountRate: 0.05,
          points: 0,
          balance: 0
        }
      }
      
      MemberApi.quickRegisterMember.mockResolvedValue(mockResponse)
      
      const result = await quickRegisterMember(memberData)
      
      expect(result).toEqual(mockResponse.member)
      expect(message.success).toHaveBeenCalledWith('会员注册成功，卡号: VIP789012')
      expect(MemberApi.quickRegisterMember).toHaveBeenCalledWith(memberData)
    })
    
    it('应该处理注册失败的情况', async () => {
      const { quickRegisterMember } = useMember()
      
      const mockError = new Error('手机号已存在')
      MemberApi.quickRegisterMember.mockRejectedValue(mockError)
      
      const result = await quickRegisterMember({
        name: '李四',
        phone: '13987654321'
      })
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('会员注册失败: 手机号已存在')
    })
  })
})