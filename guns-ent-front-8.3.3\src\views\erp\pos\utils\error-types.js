/**
 * POS模块错误类型定义
 * 
 * 定义系统中可能出现的各种错误类型和对应的错误消息
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

/**
 * 错误类型常量
 */
export const PosErrorTypes = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  
  // 业务错误
  INSUFFICIENT_INVENTORY: 'INSUFFICIENT_INVENTORY',
  INVALID_MEMBER: 'INVALID_MEMBER',
  MEMBER_EXPIRED: 'MEMBER_EXPIRED',
  MEMBER_INACTIVE: 'MEMBER_INACTIVE',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_TIMEOUT: 'PAYMENT_TIMEOUT',
  PAYMENT_CANCELLED: 'PAYMENT_CANCELLED',
  ORDER_NOT_FOUND: 'ORDER_NOT_FOUND',
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  CATEGORY_NOT_FOUND: 'CATEGORY_NOT_FOUND',
  
  // 验证错误
  INVALID_QUANTITY: 'INVALID_QUANTITY',
  INVALID_PRICE: 'INVALID_PRICE',
  INVALID_AMOUNT: 'INVALID_AMOUNT',
  INVALID_DISCOUNT: 'INVALID_DISCOUNT',
  INVALID_POINTS: 'INVALID_POINTS',
  EMPTY_CART: 'EMPTY_CART',
  CART_ITEM_LIMIT_EXCEEDED: 'CART_ITEM_LIMIT_EXCEEDED',
  INVALID_BARCODE: 'INVALID_BARCODE',
  INVALID_CARD_NO: 'INVALID_CARD_NO',
  INVALID_PHONE: 'INVALID_PHONE',
  
  // 权限错误
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  ACCESS_FORBIDDEN: 'ACCESS_FORBIDDEN',
  
  // 系统错误
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  SYSTEM_MAINTENANCE: 'SYSTEM_MAINTENANCE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // 配置错误
  INVALID_CONFIG: 'INVALID_CONFIG',
  MISSING_CONFIG: 'MISSING_CONFIG',
  
  // 文件错误
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_UPLOAD_FAILED: 'FILE_UPLOAD_FAILED',
  FILE_SIZE_EXCEEDED: 'FILE_SIZE_EXCEEDED',
  
  // 缓存错误
  CACHE_ERROR: 'CACHE_ERROR',
  CACHE_EXPIRED: 'CACHE_EXPIRED',
  
  // 并发错误
  CONCURRENT_MODIFICATION: 'CONCURRENT_MODIFICATION',
  RESOURCE_LOCKED: 'RESOURCE_LOCKED',
  
  // 业务规则错误
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  TIME_RESTRICTION: 'TIME_RESTRICTION',
  
  // 未知错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

/**
 * 错误消息映射
 */
export const ErrorMessages = {
  // 网络错误
  [PosErrorTypes.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [PosErrorTypes.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [PosErrorTypes.CONNECTION_ERROR]: '连接服务器失败，请检查网络连接',
  
  // 业务错误
  [PosErrorTypes.INSUFFICIENT_INVENTORY]: '商品库存不足',
  [PosErrorTypes.INVALID_MEMBER]: '会员信息无效或不存在',
  [PosErrorTypes.MEMBER_EXPIRED]: '会员卡已过期',
  [PosErrorTypes.MEMBER_INACTIVE]: '会员账户已被冻结或停用',
  [PosErrorTypes.PAYMENT_FAILED]: '支付失败，请重试',
  [PosErrorTypes.PAYMENT_TIMEOUT]: '支付超时，请重新发起支付',
  [PosErrorTypes.PAYMENT_CANCELLED]: '支付已取消',
  [PosErrorTypes.ORDER_NOT_FOUND]: '订单不存在',
  [PosErrorTypes.PRODUCT_NOT_FOUND]: '商品不存在',
  [PosErrorTypes.CATEGORY_NOT_FOUND]: '商品分类不存在',
  
  // 验证错误
  [PosErrorTypes.INVALID_QUANTITY]: '商品数量必须大于0',
  [PosErrorTypes.INVALID_PRICE]: '商品价格格式不正确',
  [PosErrorTypes.INVALID_AMOUNT]: '金额格式不正确',
  [PosErrorTypes.INVALID_DISCOUNT]: '折扣金额不能超过商品总价',
  [PosErrorTypes.INVALID_POINTS]: '积分数量格式不正确',
  [PosErrorTypes.EMPTY_CART]: '购物车为空，无法结算',
  [PosErrorTypes.CART_ITEM_LIMIT_EXCEEDED]: '购物车商品数量超过限制',
  [PosErrorTypes.INVALID_BARCODE]: '商品条码格式不正确',
  [PosErrorTypes.INVALID_CARD_NO]: '卡号格式不正确',
  [PosErrorTypes.INVALID_PHONE]: '手机号码格式不正确',
  
  // 权限错误
  [PosErrorTypes.PERMISSION_DENIED]: '权限不足，无法执行此操作',
  [PosErrorTypes.UNAUTHORIZED]: '用户未登录或登录已过期',
  [PosErrorTypes.ACCESS_FORBIDDEN]: '访问被拒绝',
  
  // 系统错误
  [PosErrorTypes.SYSTEM_ERROR]: '系统错误，请联系管理员',
  [PosErrorTypes.SYSTEM_MAINTENANCE]: '系统正在维护中，请稍后再试',
  [PosErrorTypes.DATABASE_ERROR]: '数据库连接错误',
  [PosErrorTypes.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  
  // 配置错误
  [PosErrorTypes.INVALID_CONFIG]: '系统配置错误',
  [PosErrorTypes.MISSING_CONFIG]: '缺少必要的系统配置',
  
  // 文件错误
  [PosErrorTypes.FILE_NOT_FOUND]: '文件不存在',
  [PosErrorTypes.FILE_UPLOAD_FAILED]: '文件上传失败',
  [PosErrorTypes.FILE_SIZE_EXCEEDED]: '文件大小超过限制',
  
  // 缓存错误
  [PosErrorTypes.CACHE_ERROR]: '缓存操作失败',
  [PosErrorTypes.CACHE_EXPIRED]: '缓存已过期',
  
  // 并发错误
  [PosErrorTypes.CONCURRENT_MODIFICATION]: '数据已被其他用户修改，请刷新后重试',
  [PosErrorTypes.RESOURCE_LOCKED]: '资源被锁定，请稍后重试',
  
  // 业务规则错误
  [PosErrorTypes.BUSINESS_RULE_VIOLATION]: '违反业务规则',
  [PosErrorTypes.OPERATION_NOT_ALLOWED]: '当前状态下不允许此操作',
  [PosErrorTypes.TIME_RESTRICTION]: '当前时间不允许此操作',
  
  // 未知错误
  [PosErrorTypes.UNKNOWN_ERROR]: '未知错误，请重试'
}

/**
 * 错误严重级别
 */
export const ErrorSeverity = {
  LOW: 'LOW',         // 低级别：用户输入错误等
  MEDIUM: 'MEDIUM',   // 中级别：业务逻辑错误等
  HIGH: 'HIGH',       // 高级别：系统错误等
  CRITICAL: 'CRITICAL' // 严重级别：系统崩溃等
}

/**
 * 错误严重级别映射
 */
export const ErrorSeverityMap = {
  // 低级别错误
  [PosErrorTypes.INVALID_QUANTITY]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_PRICE]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_AMOUNT]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_DISCOUNT]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_POINTS]: ErrorSeverity.LOW,
  [PosErrorTypes.EMPTY_CART]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_BARCODE]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_CARD_NO]: ErrorSeverity.LOW,
  [PosErrorTypes.INVALID_PHONE]: ErrorSeverity.LOW,
  
  // 中级别错误
  [PosErrorTypes.INSUFFICIENT_INVENTORY]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.INVALID_MEMBER]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.MEMBER_EXPIRED]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.MEMBER_INACTIVE]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.PAYMENT_FAILED]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.PAYMENT_TIMEOUT]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.ORDER_NOT_FOUND]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.PRODUCT_NOT_FOUND]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.CATEGORY_NOT_FOUND]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.CART_ITEM_LIMIT_EXCEEDED]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.BUSINESS_RULE_VIOLATION]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.OPERATION_NOT_ALLOWED]: ErrorSeverity.MEDIUM,
  [PosErrorTypes.TIME_RESTRICTION]: ErrorSeverity.MEDIUM,
  
  // 高级别错误
  [PosErrorTypes.NETWORK_ERROR]: ErrorSeverity.HIGH,
  [PosErrorTypes.TIMEOUT_ERROR]: ErrorSeverity.HIGH,
  [PosErrorTypes.CONNECTION_ERROR]: ErrorSeverity.HIGH,
  [PosErrorTypes.PERMISSION_DENIED]: ErrorSeverity.HIGH,
  [PosErrorTypes.UNAUTHORIZED]: ErrorSeverity.HIGH,
  [PosErrorTypes.ACCESS_FORBIDDEN]: ErrorSeverity.HIGH,
  [PosErrorTypes.INVALID_CONFIG]: ErrorSeverity.HIGH,
  [PosErrorTypes.MISSING_CONFIG]: ErrorSeverity.HIGH,
  [PosErrorTypes.CONCURRENT_MODIFICATION]: ErrorSeverity.HIGH,
  [PosErrorTypes.RESOURCE_LOCKED]: ErrorSeverity.HIGH,
  
  // 严重级别错误
  [PosErrorTypes.SYSTEM_ERROR]: ErrorSeverity.CRITICAL,
  [PosErrorTypes.SYSTEM_MAINTENANCE]: ErrorSeverity.CRITICAL,
  [PosErrorTypes.DATABASE_ERROR]: ErrorSeverity.CRITICAL,
  [PosErrorTypes.SERVICE_UNAVAILABLE]: ErrorSeverity.CRITICAL,
  [PosErrorTypes.CACHE_ERROR]: ErrorSeverity.CRITICAL,
  
  // 默认为中级别
  [PosErrorTypes.UNKNOWN_ERROR]: ErrorSeverity.MEDIUM
}

/**
 * 获取错误消息
 * @param {string} errorType - 错误类型
 * @param {string} customMessage - 自定义错误消息
 * @returns {string} 错误消息
 */
export function getErrorMessage(errorType, customMessage = '') {
  if (customMessage) {
    return customMessage
  }
  
  return ErrorMessages[errorType] || ErrorMessages[PosErrorTypes.UNKNOWN_ERROR]
}

/**
 * 获取错误严重级别
 * @param {string} errorType - 错误类型
 * @returns {string} 错误严重级别
 */
export function getErrorSeverity(errorType) {
  return ErrorSeverityMap[errorType] || ErrorSeverity.MEDIUM
}

/**
 * 判断是否为可重试的错误
 * @param {string} errorType - 错误类型
 * @returns {boolean} 是否可重试
 */
export function isRetryableError(errorType) {
  const retryableErrors = [
    PosErrorTypes.NETWORK_ERROR,
    PosErrorTypes.TIMEOUT_ERROR,
    PosErrorTypes.CONNECTION_ERROR,
    PosErrorTypes.SERVICE_UNAVAILABLE,
    PosErrorTypes.DATABASE_ERROR,
    PosErrorTypes.CACHE_ERROR,
    PosErrorTypes.RESOURCE_LOCKED
  ]
  
  return retryableErrors.includes(errorType)
}

/**
 * 判断是否需要显示给用户的错误
 * @param {string} errorType - 错误类型
 * @returns {boolean} 是否需要显示给用户
 */
export function isUserFacingError(errorType) {
  const systemErrors = [
    PosErrorTypes.SYSTEM_ERROR,
    PosErrorTypes.DATABASE_ERROR,
    PosErrorTypes.CACHE_ERROR,
    PosErrorTypes.INVALID_CONFIG,
    PosErrorTypes.MISSING_CONFIG
  ]
  
  return !systemErrors.includes(errorType)
}

/**
 * 创建标准化的错误对象
 * @param {string} type - 错误类型
 * @param {string} message - 错误消息（可选）
 * @param {Object} details - 错误详情（可选）
 * @returns {Object} 标准化的错误对象
 */
export function createError(type, message = '', details = {}) {
  return {
    type,
    message: message || getErrorMessage(type),
    severity: getErrorSeverity(type),
    retryable: isRetryableError(type),
    userFacing: isUserFacingError(type),
    timestamp: new Date().toISOString(),
    details
  }
}