<!--
  支付结果组件
  
  显示支付成功、失败等结果状态
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="payment-result">
    <!-- 结果图标和状态 -->
    <div class="result-header">
      <div class="result-icon" :class="resultClass">
        <check-circle-outlined v-if="status === 'success'" />
        <close-circle-outlined v-else-if="status === 'failed'" />
        <exclamation-circle-outlined v-else-if="status === 'cancelled'" />
        <clock-circle-outlined v-else-if="status === 'processing'" />
        <question-circle-outlined v-else />
      </div>
      
      <div class="result-title">{{ resultTitle }}</div>
      <div class="result-message" v-if="message">{{ message }}</div>
    </div>
    
    <!-- 支付详情 -->\n    <div class="payment-details" v-if="paymentInfo">\n      <div class="details-title">\n        <icon-font iconClass="icon-detail" />\n        <span>支付详情</span>\n      </div>\n      \n      <div class="details-content">\n        <!-- 支付方式 -->\n        <div class="detail-row">\n          <span class="detail-label">支付方式:</span>\n          <span class="detail-value">\n            <icon-font :iconClass="paymentMethodIcon" />\n            {{ paymentMethodName }}\n          </span>\n        </div>\n        \n        <!-- 支付金额 -->\n        <div class="detail-row">\n          <span class="detail-label">支付金额:</span>\n          <span class="detail-value amount">{{ formatPrice(paymentInfo.amount) }}</span>\n        </div>\n        \n        <!-- 实收金额（现金支付） -->\n        <div class="detail-row" v-if="paymentInfo.receivedAmount && paymentInfo.receivedAmount !== paymentInfo.amount">\n          <span class="detail-label">实收金额:</span>\n          <span class="detail-value">{{ formatPrice(paymentInfo.receivedAmount) }}</span>\n        </div>\n        \n        <!-- 找零金额（现金支付） -->\n        <div class="detail-row" v-if="paymentInfo.changeAmount && paymentInfo.changeAmount > 0">\n          <span class="detail-label">找零金额:</span>\n          <span class="detail-value change">{{ formatPrice(paymentInfo.changeAmount) }}</span>\n        </div>\n        \n        <!-- 交易流水号 -->\n        <div class="detail-row" v-if="paymentInfo.transactionId">\n          <span class="detail-label">交易流水:</span>\n          <span class="detail-value transaction-id">\n            {{ paymentInfo.transactionId }}\n            <a-button \n              type="text" \n              size="small"\n              @click="copyTransactionId"\n              class="copy-btn"\n            >\n              <icon-font iconClass="icon-copy" />\n            </a-button>\n          </span>\n        </div>\n        \n        <!-- 支付时间 -->\n        <div class="detail-row" v-if="paymentInfo.paymentTime">\n          <span class="detail-label">支付时间:</span>\n          <span class="detail-value">{{ formatDateTime(paymentInfo.paymentTime) }}</span>\n        </div>\n        \n        <!-- 商户订单号 -->\n        <div class="detail-row" v-if="paymentInfo.orderNo">\n          <span class="detail-label">订单编号:</span>\n          <span class="detail-value">{{ paymentInfo.orderNo }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 错误信息 -->\n    <div class="error-details" v-if="status === 'failed' && errorInfo">\n      <div class="error-title">\n        <icon-font iconClass="icon-error" />\n        <span>错误详情</span>\n      </div>\n      \n      <div class="error-content">\n        <div class="error-code" v-if="errorInfo.code">\n          错误代码: {{ errorInfo.code }}\n        </div>\n        <div class="error-message">\n          {{ errorInfo.message || '支付失败，请重试' }}\n        </div>\n        <div class="error-suggestion" v-if="errorInfo.suggestion">\n          建议: {{ errorInfo.suggestion }}\n        </div>\n      </div>\n    </div>\n    \n    <!-- 操作按钮 -->\n    <div class="result-actions">\n      <!-- 成功状态按钮 -->\n      <template v-if="status === 'success'">\n        <a-button \n          v-if="showPrintButton"\n          @click="handlePrint"\n          :loading="printing"\n          class="action-btn"\n        >\n          <icon-font iconClass="icon-print" />\n          打印小票\n        </a-button>\n        \n        <a-button \n          type="primary"\n          @click="handleComplete"\n          class="action-btn"\n        >\n          <icon-font iconClass="icon-check" />\n          完成\n        </a-button>\n      </template>\n      \n      <!-- 失败状态按钮 -->\n      <template v-else-if="status === 'failed'">\n        <a-button \n          @click="handleRetry"\n          :loading="retrying"\n          class="action-btn"\n        >\n          <icon-font iconClass="icon-retry" />\n          重试支付\n        </a-button>\n        \n        <a-button \n          @click="handleCancel"\n          class="action-btn"\n        >\n          <icon-font iconClass="icon-close" />\n          取消订单\n        </a-button>\n      </template>\n      \n      <!-- 取消状态按钮 -->\n      <template v-else-if="status === 'cancelled'">\n        <a-button \n          @click="handleRetry"\n          type="primary"\n          class="action-btn"\n        >\n          <icon-font iconClass="icon-retry" />\n          重新支付\n        </a-button>\n      </template>\n      \n      <!-- 处理中状态按钮 -->\n      <template v-else-if="status === 'processing'">\n        <a-button \n          @click="handleCancel"\n          :loading="cancelling"\n          class="action-btn"\n        >\n          <icon-font iconClass="icon-stop" />\n          取消支付\n        </a-button>\n      </template>\n    </div>\n    \n    <!-- 额外信息 -->\n    <div class="extra-info" v-if="extraInfo">\n      <div class="info-item" v-for="(info, index) in extraInfo" :key="index">\n        <icon-font :iconClass="info.icon || 'icon-info'" />\n        <span>{{ info.text }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed, ref } from 'vue'\nimport { message } from 'ant-design-vue'\nimport {\n  CheckCircleOutlined,\n  CloseCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined,\n  QuestionCircleOutlined\n} from '@ant-design/icons-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\nimport { AmountFormatter, DateTimeFormatter } from '../../utils/formatter'\nimport { PAYMENT_METHODS } from '../../utils/constants'\n\n// 定义组件名称\ndefineOptions({\n  name: 'PaymentResult'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 支付状态\n  status: {\n    type: String,\n    required: true,\n    validator: (value) => ['success', 'failed', 'cancelled', 'processing'].includes(value)\n  },\n  // 支付信息\n  paymentInfo: {\n    type: Object,\n    default: null\n  },\n  // 错误信息\n  errorInfo: {\n    type: Object,\n    default: null\n  },\n  // 结果消息\n  message: {\n    type: String,\n    default: ''\n  },\n  // 是否显示打印按钮\n  showPrintButton: {\n    type: Boolean,\n    default: true\n  },\n  // 额外信息\n  extraInfo: {\n    type: Array,\n    default: () => []\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'complete',\n  'retry',\n  'cancel',\n  'print'\n])\n\n// 响应式状态\nconst printing = ref(false)\nconst retrying = ref(false)\nconst cancelling = ref(false)\n\n// ==================== 计算属性 ====================\n\n/**\n * 结果样式类\n */\nconst resultClass = computed(() => {\n  return {\n    'success': props.status === 'success',\n    'failed': props.status === 'failed',\n    'cancelled': props.status === 'cancelled',\n    'processing': props.status === 'processing'\n  }\n})\n\n/**\n * 结果标题\n */\nconst resultTitle = computed(() => {\n  switch (props.status) {\n    case 'success':\n      return '支付成功'\n    case 'failed':\n      return '支付失败'\n    case 'cancelled':\n      return '支付已取消'\n    case 'processing':\n      return '支付处理中'\n    default:\n      return '未知状态'\n  }\n})\n\n/**\n * 支付方式名称\n */\nconst paymentMethodName = computed(() => {\n  if (!props.paymentInfo?.paymentMethod) return ''\n  return PAYMENT_METHODS[props.paymentInfo.paymentMethod]?.label || props.paymentInfo.paymentMethod\n})\n\n/**\n * 支付方式图标\n */\nconst paymentMethodIcon = computed(() => {\n  if (!props.paymentInfo?.paymentMethod) return 'icon-payment'\n  return PAYMENT_METHODS[props.paymentInfo.paymentMethod]?.icon || 'icon-payment'\n})\n\n// ==================== 方法 ====================\n\n/**\n * 格式化价格显示\n * @param {number} price - 价格\n * @returns {string} 格式化后的价格\n */\nconst formatPrice = (price) => {\n  return AmountFormatter.formatCurrency(price || 0)\n}\n\n/**\n * 格式化日期时间\n * @param {string|Date} dateTime - 日期时间\n * @returns {string} 格式化后的日期时间\n */\nconst formatDateTime = (dateTime) => {\n  return DateTimeFormatter.formatDateTime(dateTime)\n}\n\n/**\n * 复制交易流水号\n */\nconst copyTransactionId = async () => {\n  if (!props.paymentInfo?.transactionId) return\n  \n  try {\n    await navigator.clipboard.writeText(props.paymentInfo.transactionId)\n    message.success('交易流水号已复制到剪贴板')\n  } catch (err) {\n    // 兼容旧浏览器\n    const textArea = document.createElement('textarea')\n    textArea.value = props.paymentInfo.transactionId\n    document.body.appendChild(textArea)\n    textArea.select()\n    try {\n      document.execCommand('copy')\n      message.success('交易流水号已复制到剪贴板')\n    } catch (copyErr) {\n      message.error('复制失败')\n    }\n    document.body.removeChild(textArea)\n  }\n}\n\n/**\n * 处理完成\n */\nconst handleComplete = () => {\n  emit('complete', {\n    paymentInfo: props.paymentInfo\n  })\n}\n\n/**\n * 处理重试\n */\nconst handleRetry = async () => {\n  retrying.value = true\n  try {\n    emit('retry', {\n      paymentInfo: props.paymentInfo,\n      errorInfo: props.errorInfo\n    })\n  } finally {\n    retrying.value = false\n  }\n}\n\n/**\n * 处理取消\n */\nconst handleCancel = async () => {\n  cancelling.value = true\n  try {\n    emit('cancel', {\n      paymentInfo: props.paymentInfo\n    })\n  } finally {\n    cancelling.value = false\n  }\n}\n\n/**\n * 处理打印\n */\nconst handlePrint = async () => {\n  printing.value = true\n  try {\n    emit('print', {\n      paymentInfo: props.paymentInfo\n    })\n  } finally {\n    printing.value = false\n  }\n}\n</script>\n\n<style scoped>\n.payment-result {\n  padding: 24px;\n  background: #fff;\n  text-align: center;\n}\n\n/* 结果头部 */\n.result-header {\n  margin-bottom: 24px;\n}\n\n.result-icon {\n  font-size: 64px;\n  margin-bottom: 16px;\n}\n\n.result-icon.success {\n  color: #52c41a;\n}\n\n.result-icon.failed {\n  color: #ff4d4f;\n}\n\n.result-icon.cancelled {\n  color: #faad14;\n}\n\n.result-icon.processing {\n  color: #1890ff;\n}\n\n.result-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 8px;\n}\n\n.result-message {\n  font-size: 14px;\n  color: #8c8c8c;\n  line-height: 1.5;\n}\n\n/* 支付详情 */\n.payment-details {\n  margin-bottom: 24px;\n  text-align: left;\n}\n\n.details-title {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 16px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.details-content {\n  background: #fafafa;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  font-size: 14px;\n}\n\n.detail-row:last-child {\n  margin-bottom: 0;\n}\n\n.detail-label {\n  color: #595959;\n  min-width: 80px;\n}\n\n.detail-value {\n  color: #262626;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.detail-value.amount {\n  color: #ff4d4f;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.detail-value.change {\n  color: #52c41a;\n  font-weight: 600;\n}\n\n.transaction-id {\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n}\n\n.copy-btn {\n  padding: 0;\n  height: auto;\n  font-size: 12px;\n}\n\n/* 错误详情 */\n.error-details {\n  margin-bottom: 24px;\n  text-align: left;\n}\n\n.error-title {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 16px;\n  font-size: 16px;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.error-content {\n  background: #fff2f0;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid #ff4d4f;\n}\n\n.error-code {\n  font-size: 12px;\n  color: #8c8c8c;\n  margin-bottom: 8px;\n  font-family: 'Courier New', monospace;\n}\n\n.error-message {\n  font-size: 14px;\n  color: #ff4d4f;\n  font-weight: 500;\n  margin-bottom: 8px;\n}\n\n.error-suggestion {\n  font-size: 13px;\n  color: #595959;\n  line-height: 1.5;\n}\n\n/* 操作按钮 */\n.result-actions {\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n  margin-bottom: 24px;\n}\n\n.action-btn {\n  min-width: 120px;\n  height: 44px;\n  font-size: 14px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n/* 额外信息 */\n.extra-info {\n  text-align: left;\n}\n\n.info-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin-bottom: 8px;\n  font-size: 13px;\n  color: #595959;\n  line-height: 1.5;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-item .anticon {\n  color: #1890ff;\n  margin-top: 2px;\n  flex-shrink: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .payment-result {\n    padding: 20px 16px;\n  }\n  \n  .result-icon {\n    font-size: 48px;\n    margin-bottom: 12px;\n  }\n  \n  .result-title {\n    font-size: 18px;\n  }\n  \n  .details-title {\n    font-size: 15px;\n  }\n  \n  .details-content {\n    padding: 12px;\n  }\n  \n  .detail-row {\n    font-size: 13px;\n    margin-bottom: 10px;\n  }\n  \n  .detail-value.amount {\n    font-size: 15px;\n  }\n  \n  .result-actions {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .action-btn {\n    width: 100%;\n    height: 40px;\n  }\n}\n\n@media (max-width: 480px) {\n  .detail-row {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n  \n  .detail-label {\n    min-width: auto;\n    font-size: 12px;\n  }\n  \n  .detail-value {\n    font-size: 13px;\n  }\n  \n  .detail-value.amount {\n    font-size: 14px;\n  }\n}\n\n/* 动画效果 */\n.result-icon {\n  animation: bounceIn 0.6s ease-out;\n}\n\n@keyframes bounceIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.3);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05);\n  }\n  70% {\n    transform: scale(0.9);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.payment-details,\n.error-details {\n  animation: fadeInUp 0.4s ease-out 0.2s both;\n}\n\n.result-actions {\n  animation: fadeInUp 0.4s ease-out 0.4s both;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .details-content,\n  .error-content {\n    border: 2px solid #000;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .result-icon,\n  .payment-details,\n  .error-details,\n  .result-actions {\n    animation: none;\n  }\n}\n</style>"