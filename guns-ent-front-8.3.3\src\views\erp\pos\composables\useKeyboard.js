/**
 * 键盘快捷键组合式函数
 * 
 * 提供POS系统的键盘快捷键支持
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { onMounted, onUnmounted } from 'vue'

export function useKeyboard() {
  
  /**
   * 处理键盘事件
   */
  const handleKeydown = (event) => {
    // 防止在输入框中触发快捷键
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return
    }
    
    // F1 - 显示帮助
    if (event.key === 'F1') {
      event.preventDefault()
      // 触发帮助显示事件
      document.dispatchEvent(new CustomEvent('pos:showHelp'))
      return
    }
    
    // F11 - 切换全屏
    if (event.key === 'F11') {
      event.preventDefault()
      // 触发全屏切换事件
      document.dispatchEvent(new CustomEvent('pos:toggleFullscreen'))
      return
    }
    
    // Ctrl+R - 重置所有状态
    if (event.ctrlKey && event.key === 'r') {
      event.preventDefault()
      // 触发重置事件
      document.dispatchEvent(new CustomEvent('pos:resetAll'))
      return
    }
    
    // Escape - 关闭弹窗/取消操作
    if (event.key === 'Escape') {
      // 触发取消事件
      document.dispatchEvent(new CustomEvent('pos:cancel'))
      return
    }
    
    // Enter - 确认操作
    if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault()
      // 触发确认事件
      document.dispatchEvent(new CustomEvent('pos:confirm'))
      return
    }
    
    // Delete - 删除选中项
    if (event.key === 'Delete') {
      // 触发删除事件
      document.dispatchEvent(new CustomEvent('pos:delete'))
      return
    }
    
    // 数字键 - 快速添加商品数量
    if (event.key >= '0' && event.key <= '9' && event.altKey) {
      event.preventDefault()
      // 触发数字输入事件
      document.dispatchEvent(new CustomEvent('pos:numberInput', {
        detail: { number: event.key }
      }))
      return
    }
  }
  
  /**
   * 组件挂载时添加键盘监听
   */
  onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
  })
  
  /**
   * 组件卸载时移除键盘监听
   */
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
  
  return {
    // 可以返回一些键盘相关的状态或方法
  }
}