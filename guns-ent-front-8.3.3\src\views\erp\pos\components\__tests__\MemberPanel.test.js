import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON> } from 'pinia'
import MemberPanel from '../MemberPanel.vue'

// Mock子组件
vi.mock('../member', () => ({
  MemberSearch: {
    name: 'MemberSearch',
    template: '<div class="member-search-mock">MemberSearch</div>',
    emits: ['memberFound', 'searchError'],
    methods: {
      resetSearch: vi.fn()
    }
  },
  MemberSelector: {
    name: 'MemberSelector', 
    template: '<div class="member-selector-mock">MemberSelector</div>',
    emits: ['memberSelect'],
    methods: {
      addToRecentMembers: vi.fn()
    }
  },
  MemberInfo: {
    name: 'MemberInfo',
    template: '<div class="member-info-mock">MemberInfo</div>',
    props: ['member']
  },
  MemberDiscount: {
    name: 'MemberDiscount',
    template: '<div class="member-discount-mock">MemberDiscount</div>',
    props: ['member', 'discountRate', 'finalAmount', 'pointsExchangeRate'],
    emits: ['pointsDeductionChange'],
    methods: {
      resetPointsDeduction: vi.fn()
    }
  }
}))

// Mock POS Store
const mockPosStore = {
  currentMember: null,
  memberDiscountRate: 0,
  finalAmount: 0,
  pointsExchangeRate: 100,
  setCurrentMember: vi.fn(),
  clearCurrentMember: vi.fn(),
  applyMemberDiscount: vi.fn(),
  clearMemberDiscount: vi.fn(),
  setPointsDeduction: vi.fn()
}

vi.mock('@/stores/pos', () => ({
  usePosStore: () => mockPosStore
}))

describe('MemberPanel', () => {
  let wrapper
  let pinia

  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(MemberPanel, {
      props,
      global: {
        plugins: [pinia],
        stubs: {
          'icon-font': true
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockPosStore.currentMember = null
    mockPosStore.memberDiscountRate = 0
    mockPosStore.finalAmount = 0
  })

  describe('组件渲染', () => {
    it('应该正确渲染会员面板标题', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.member-title').exists()).toBe(true)
      expect(wrapper.text()).toContain('会员管理')
    })

    it('当没有选择会员时应该显示搜索和选择组件', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.member-search-mock').exists()).toBe(true)
      expect(wrapper.find('.member-selector-mock').exists()).toBe(true)
      expect(wrapper.find('.member-info-mock').exists()).toBe(false)
      expect(wrapper.find('.member-discount-mock').exists()).toBe(false)
    })

    it('当选择会员时应该显示会员信息和折扣组件', () => {
      mockPosStore.currentMember = {
        memberId: '1',
        memberName: '测试会员',
        phone: '13800138000'
      }
      
      wrapper = createWrapper()
      
      expect(wrapper.find('.member-search-mock').exists()).toBe(false)
      expect(wrapper.find('.member-selector-mock').exists()).toBe(false)
      expect(wrapper.find('.member-info-mock').exists()).toBe(true)
      expect(wrapper.find('.member-discount-mock').exists()).toBe(true)
    })

    it('当有会员时应该显示清除按钮', () => {
      mockPosStore.currentMember = {
        memberId: '1',
        memberName: '测试会员'
      }
      
      wrapper = createWrapper()
      
      expect(wrapper.find('.clear-btn').exists()).toBe(true)
    })
  })

  describe('会员搜索功能', () => {
    it('应该处理会员搜索找到结果', async () => {
      wrapper = createWrapper()
      
      const testMember = {
        memberId: '1',
        memberName: '测试会员',
        phone: '13800138000'
      }
      
      // 触发memberFound事件
      const memberSearch = wrapper.findComponent({ name: 'MemberSearch' })
      await memberSearch.vm.$emit('memberFound', testMember)
      
      expect(mockPosStore.setCurrentMember).toHaveBeenCalledWith(testMember)
      expect(wrapper.emitted('memberChange')).toBeTruthy()
      expect(wrapper.emitted('memberSelect')).toBeTruthy()
    })

    it('应该处理会员搜索错误', async () => {
      wrapper = createWrapper()
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      const testError = new Error('搜索失败')
      
      // 触发searchError事件
      const memberSearch = wrapper.findComponent({ name: 'MemberSearch' })
      await memberSearch.vm.$emit('searchError', testError)
      
      expect(consoleSpy).toHaveBeenCalledWith('会员搜索错误:', testError)
      
      consoleSpy.mockRestore()
    })
  })

  describe('会员选择功能', () => {
    it('应该处理会员选择', async () => {
      wrapper = createWrapper()
      
      const testMember = {
        memberId: '2',
        memberName: '选择会员',
        phone: '13900139000'
      }
      
      // 触发memberSelect事件
      const memberSelector = wrapper.findComponent({ name: 'MemberSelector' })
      await memberSelector.vm.$emit('memberSelect', testMember)
      
      expect(mockPosStore.setCurrentMember).toHaveBeenCalledWith(testMember)
      expect(wrapper.emitted('memberChange')).toBeTruthy()
      expect(wrapper.emitted('memberSelect')).toBeTruthy()
    })
  })

  describe('会员清除功能', () => {
    it('应该能够清除会员', async () => {
      mockPosStore.currentMember = {
        memberId: '1',
        memberName: '测试会员'
      }
      
      wrapper = createWrapper()
      
      // 点击清除按钮
      await wrapper.find('.clear-btn').trigger('click')
      
      expect(mockPosStore.clearCurrentMember).toHaveBeenCalled()
      expect(wrapper.emitted('memberChange')).toBeTruthy()
      expect(wrapper.emitted('memberClear')).toBeTruthy()
    })
  })

  describe('积分抵扣功能', () => {
    it('应该处理积分抵扣变化', async () => {
      mockPosStore.currentMember = {
        memberId: '1',
        memberName: '测试会员',
        points: 1000
      }
      
      wrapper = createWrapper()
      
      const pointsData = { points: 500, amount: 5 }
      
      // 触发pointsDeductionChange事件
      const memberDiscount = wrapper.findComponent({ name: 'MemberDiscount' })
      await memberDiscount.vm.$emit('pointsDeductionChange', pointsData)
      
      expect(mockPosStore.setPointsDeduction).toHaveBeenCalledWith(500, 5)
    })
  })

  describe('会员折扣应用', () => {
    it('应该在选择会员时自动应用折扣', async () => {
      const testMember = {
        memberId: '1',
        memberName: '测试会员'
      }
      
      mockPosStore.currentMember = testMember
      
      wrapper = createWrapper()
      
      // 等待组件更新
      await wrapper.vm.$nextTick()
      
      expect(mockPosStore.applyMemberDiscount).toHaveBeenCalledWith('1')
    })

    it('应该在清除会员时清除折扣', async () => {
      mockPosStore.currentMember = {
        memberId: '1',
        memberName: '测试会员'
      }
      
      wrapper = createWrapper()
      
      // 清除会员
      mockPosStore.currentMember = null
      await wrapper.vm.$nextTick()
      
      expect(mockPosStore.clearMemberDiscount).toHaveBeenCalled()
    })
  })

  describe('组件方法暴露', () => {
    it('应该暴露selectMember方法', () => {
      wrapper = createWrapper()
      
      expect(wrapper.vm.selectMember).toBeDefined()
      expect(typeof wrapper.vm.selectMember).toBe('function')
    })

    it('应该暴露clearMember方法', () => {
      wrapper = createWrapper()
      
      expect(wrapper.vm.clearMember).toBeDefined()
      expect(typeof wrapper.vm.clearMember).toBe('function')
    })
  })
})