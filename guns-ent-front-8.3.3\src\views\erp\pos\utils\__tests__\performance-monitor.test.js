/**
 * 性能监控器单元测试
 * 
 * 测试性能监控功能的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PerformanceMonitor } from '../performance-monitor'

// Mock performance API
const mockPerformance = {
  now: vi.fn(),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(),
  getEntriesByName: vi.fn(),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
  memory: {
    usedJSHeapSize: 10000000,
    totalJSHeapSize: 20000000,
    jsHeapSizeLimit: 100000000
  }
}

global.performance = mockPerformance

// Mock console
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
}
global.console = mockConsole

// Mock window.posMetrics
global.window = {
  posMetrics: []
}

describe('PerformanceMonitor', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockPerformance.now.mockReturnValue(1000)
    window.posMetrics = []
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('measureComponentRender', () => {
    it('应该测量组件渲染时间', () => {
      let callCount = 0
      mockPerformance.now
        .mockReturnValueOnce(1000) // 开始时间
        .mockReturnValueOnce(1050) // 结束时间
      
      const renderFunction = vi.fn(() => 'rendered')
      
      const result = PerformanceMonitor.measureComponentRender('TestComponent', renderFunction)
      
      expect(result).toBe('rendered')
      expect(renderFunction).toHaveBeenCalled()
      expect(window.posMetrics).toHaveLength(1)
      expect(window.posMetrics[0]).toEqual({
        type: 'component_render',
        component: 'TestComponent',
        renderTime: 50,
        timestamp: expect.any(Number)
      })
    })
    
    it('应该在渲染时间过长时发出警告', () => {
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1150) // 150ms，超过100ms阈值
      
      const renderFunction = vi.fn(() => 'slow render')
      
      PerformanceMonitor.measureComponentRender('SlowComponent', renderFunction)
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        '组件 SlowComponent 渲染时间过长: 150ms'
      )
    })
    
    it('应该处理渲染函数抛出的异常', () => {
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1020)
      
      const renderFunction = vi.fn(() => {
        throw new Error('渲染错误')
      })
      
      expect(() => {
        PerformanceMonitor.measureComponentRender('ErrorComponent', renderFunction)
      }).toThrow('渲染错误')
      
      // 即使出错也应该记录性能数据
      expect(window.posMetrics).toHaveLength(1)
    })
  })
  
  describe('measureApiCall', () => {
    it('应该测量API调用时间', async () => {
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1200)
      
      const apiFunction = vi.fn().mockResolvedValue({ data: 'success' })
      const measuredFunction = PerformanceMonitor.measureApiCall('testApi', apiFunction)
      
      const result = await measuredFunction('param1', 'param2')
      
      expect(result).toEqual({ data: 'success' })
      expect(apiFunction).toHaveBeenCalledWith('param1', 'param2')
      expect(window.posMetrics).toHaveLength(1)
      expect(window.posMetrics[0]).toEqual({
        type: 'api_call',
        api: 'testApi',
        duration: 200,
        status: 'success',
        timestamp: expect.any(Number)
      })
    })
    
    it('应该处理API调用失败的情况', async () => {
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1100)
      
      const apiError = new Error('API调用失败')
      const apiFunction = vi.fn().mockRejectedValue(apiError)
      const measuredFunction = PerformanceMonitor.measureApiCall('failApi', apiFunction)
      
      await expect(measuredFunction()).rejects.toThrow('API调用失败')
      
      expect(window.posMetrics).toHaveLength(1)
      expect(window.posMetrics[0]).toEqual({
        type: 'api_call',
        api: 'failApi',
        duration: 100,
        status: 'error',
        error: 'API调用失败',
        timestamp: expect.any(Number)
      })
    })
  })
  
  describe('recordMetric', () => {
    it('应该记录自定义指标', () => {
      const metricData = {
        operation: 'custom_operation',
        duration: 150,
        success: true
      }
      
      PerformanceMonitor.recordMetric('custom_metric', metricData)
      
      expect(window.posMetrics).toHaveLength(1)
      expect(window.posMetrics[0]).toEqual({
        type: 'custom_metric',
        operation: 'custom_operation',
        duration: 150,
        success: true
      })
    })
    
    it('应该处理window.posMetrics不存在的情况', () => {
      delete window.posMetrics
      
      const metricData = { test: 'data' }
      
      // 不应该抛出错误
      expect(() => {
        PerformanceMonitor.recordMetric('test_metric', metricData)
      }).not.toThrow()
    })
  })
  
  describe('getMetrics', () => {
    it('应该返回所有性能指标', () => {
      const metric1 = { type: 'test1', value: 100 }
      const metric2 = { type: 'test2', value: 200 }
      
      window.posMetrics = [metric1, metric2]
      
      const metrics = PerformanceMonitor.getMetrics()
      
      expect(metrics).toEqual([metric1, metric2])
    })
    
    it('应该在window.posMetrics不存在时返回空数组', () => {
      delete window.posMetrics
      
      const metrics = PerformanceMonitor.getMetrics()
      
      expect(metrics).toEqual([])
    })
  })
  
  describe('clearMetrics', () => {
    it('应该清除所有性能指标', () => {
      window.posMetrics = [
        { type: 'test1', value: 100 },
        { type: 'test2', value: 200 }
      ]
      
      PerformanceMonitor.clearMetrics()
      
      expect(window.posMetrics).toEqual([])
    })
  })
  
  describe('getMetricsSummary', () => {
    it('应该返回性能指标摘要', () => {
      window.posMetrics = [
        { type: 'component_render', component: 'A', renderTime: 50 },
        { type: 'component_render', component: 'B', renderTime: 100 },
        { type: 'api_call', api: 'test', duration: 200, status: 'success' },
        { type: 'api_call', api: 'test2', duration: 300, status: 'error' }
      ]
      
      const summary = PerformanceMonitor.getMetricsSummary()
      
      expect(summary).toEqual({
        totalMetrics: 4,
        componentRenders: {
          count: 2,
          averageTime: 75,
          maxTime: 100,
          minTime: 50
        },
        apiCalls: {
          count: 2,
          averageTime: 250,
          successCount: 1,
          errorCount: 1,
          successRate: 0.5
        }
      })
    })
    
    it('应该处理空指标的情况', () => {
      window.posMetrics = []
      
      const summary = PerformanceMonitor.getMetricsSummary()
      
      expect(summary).toEqual({
        totalMetrics: 0,
        componentRenders: {
          count: 0,
          averageTime: 0,
          maxTime: 0,
          minTime: 0
        },
        apiCalls: {
          count: 0,
          averageTime: 0,
          successCount: 0,
          errorCount: 0,
          successRate: 0
        }
      })
    })
  })
  
  describe('startTimer', () => {
    it('应该开始计时器', () => {
      mockPerformance.now.mockReturnValue(1000)
      
      const timer = PerformanceMonitor.startTimer('test_timer')
      
      expect(timer).toEqual({
        name: 'test_timer',
        startTime: 1000,
        end: expect.any(Function)
      })
      expect(mockPerformance.mark).toHaveBeenCalledWith('test_timer_start')
    })
  })
  
  describe('endTimer', () => {
    it('应该结束计时器并返回持续时间', () => {
      mockPerformance.now
        .mockReturnValueOnce(1000) // 开始时间
        .mockReturnValueOnce(1250) // 结束时间
      
      const timer = PerformanceMonitor.startTimer('test_timer')
      const duration = timer.end()
      
      expect(duration).toBe(250)
      expect(mockPerformance.mark).toHaveBeenCalledWith('test_timer_end')
      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'test_timer',
        'test_timer_start',
        'test_timer_end'
      )
    })
  })
  
  describe('measureFunction', () => {
    it('应该测量函数执行时间', () => {
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1150)
      
      const testFunction = vi.fn(() => 'result')
      
      const result = PerformanceMonitor.measureFunction('testFunc', testFunction, 'arg1', 'arg2')
      
      expect(result).toBe('result')
      expect(testFunction).toHaveBeenCalledWith('arg1', 'arg2')
      expect(window.posMetrics).toHaveLength(1)
      expect(window.posMetrics[0]).toEqual({
        type: 'function_execution',
        functionName: 'testFunc',
        duration: 150,
        timestamp: expect.any(Number)
      })
    })
    
    it('应该测量异步函数执行时间', async () => {
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1300)
      
      const asyncFunction = vi.fn().mockResolvedValue('async result')
      
      const result = await PerformanceMonitor.measureFunction('asyncFunc', asyncFunction)
      
      expect(result).toBe('async result')
      expect(window.posMetrics).toHaveLength(1)
      expect(window.posMetrics[0].duration).toBe(300)
    })
  })
  
  describe('getMemoryUsage', () => {
    it('应该返回内存使用情况', () => {
      const memoryUsage = PerformanceMonitor.getMemoryUsage()
      
      expect(memoryUsage).toEqual({
        used: 10000000,
        total: 20000000,
        limit: 100000000,
        usagePercent: 10,
        available: 90000000
      })
    })
    
    it('应该处理performance.memory不存在的情况', () => {
      delete mockPerformance.memory
      
      const memoryUsage = PerformanceMonitor.getMemoryUsage()
      
      expect(memoryUsage).toEqual({
        used: 0,
        total: 0,
        limit: 0,
        usagePercent: 0,
        available: 0
      })
    })
  })
  
  describe('checkMemoryUsage', () => {
    it('应该检查内存使用情况并发出警告', () => {
      // 设置高内存使用率
      mockPerformance.memory = {
        usedJSHeapSize: 85000000,
        totalJSHeapSize: 90000000,
        jsHeapSizeLimit: 100000000
      }
      
      const result = PerformanceMonitor.checkMemoryUsage()
      
      expect(result).toEqual({
        status: 'warning',
        usagePercent: 85,
        message: '内存使用率较高: 85%'
      })
      expect(mockConsole.warn).toHaveBeenCalledWith('内存使用率过高:', '85%')
    })
    
    it('应该在内存使用正常时返回正常状态', () => {
      mockPerformance.memory = {
        usedJSHeapSize: 30000000,
        totalJSHeapSize: 50000000,
        jsHeapSizeLimit: 100000000
      }
      
      const result = PerformanceMonitor.checkMemoryUsage()
      
      expect(result).toEqual({
        status: 'normal',
        usagePercent: 30,
        message: '内存使用正常'
      })
    })
  })
  
  describe('exportMetrics', () => {
    it('应该导出JSON格式的指标', () => {
      window.posMetrics = [
        { type: 'test', value: 100, timestamp: 1000 },
        { type: 'test2', value: 200, timestamp: 2000 }
      ]
      
      const exported = PerformanceMonitor.exportMetrics('json')
      const parsed = JSON.parse(exported)
      
      expect(parsed).toEqual({
        exportTime: expect.any(String),
        metrics: window.posMetrics,
        summary: expect.any(Object)
      })
    })
    
    it('应该导出CSV格式的指标', () => {
      window.posMetrics = [
        { type: 'component_render', component: 'A', renderTime: 50 },
        { type: 'api_call', api: 'test', duration: 200 }
      ]
      
      const exported = PerformanceMonitor.exportMetrics('csv')
      
      expect(exported).toContain('Type,Component,RenderTime,API,Duration')
      expect(exported).toContain('component_render,A,50,,')
      expect(exported).toContain('api_call,,200,test,200')
    })
  })
  
  describe('setPerformanceThresholds', () => {
    it('应该设置性能阈值', () => {
      const thresholds = {
        componentRender: 80,
        apiCall: 1500,
        memoryUsage: 85
      }
      
      PerformanceMonitor.setPerformanceThresholds(thresholds)
      
      // 测试新阈值是否生效
      mockPerformance.now
        .mockReturnValueOnce(1000)
        .mockReturnValueOnce(1090) // 90ms，超过新阈值80ms
      
      const renderFunction = vi.fn(() => 'test')
      PerformanceMonitor.measureComponentRender('TestComponent', renderFunction)
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        '组件 TestComponent 渲染时间过长: 90ms'
      )
    })
  })
  
  describe('startPerformanceMonitoring', () => {
    it('应该开始性能监控', () => {
      vi.useFakeTimers()
      
      PerformanceMonitor.startPerformanceMonitoring(1000)
      
      // 快进时间触发监控
      vi.advanceTimersByTime(1000)
      
      // 应该记录内存使用情况
      expect(window.posMetrics.some(m => m.type === 'memory_usage')).toBe(true)
      
      vi.useRealTimers()
    })
  })
  
  describe('stopPerformanceMonitoring', () => {
    it('应该停止性能监控', () => {
      vi.useFakeTimers()
      
      PerformanceMonitor.startPerformanceMonitoring(1000)
      PerformanceMonitor.stopPerformanceMonitoring()
      
      const initialMetricsCount = window.posMetrics.length
      
      // 快进时间，不应该再记录新的指标
      vi.advanceTimersByTime(2000)
      
      expect(window.posMetrics.length).toBe(initialMetricsCount)
      
      vi.useRealTimers()
    })
  })
})