/**
 * 支付组合式函数单元测试
 * 
 * 测试支付业务逻辑的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { usePayment } from '../usePayment'
import { usePosStore } from '@/stores/pos'
import { PaymentApi } from '../../api/payment'
import { PAYMENT_METHODS } from '../../utils/constants'
import { message } from 'ant-design-vue'

// Mock dependencies
vi.mock('../../api/payment')
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('usePayment', () => {
  let pinia
  let store
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = usePosStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('基础状态', () => {
    it('应该正确初始化支付状态', () => {
      const { 
        paymentStatus, 
        selectedPaymentMethod, 
        paymentAmount, 
        receivedAmount, 
        changeAmount,
        canPay 
      } = usePayment()
      
      expect(paymentStatus.value).toBe('idle')
      expect(selectedPaymentMethod.value).toBe('')
      expect(paymentAmount.value).toBe(0)
      expect(receivedAmount.value).toBe(0)
      expect(changeAmount.value).toBe(0)
      expect(canPay.value).toBe(false)
    })
    
    it('应该正确计算找零金额', () => {
      const { setReceivedAmount, changeAmount } = usePayment()
      
      store.finalAmount = 85.5
      setReceivedAmount(100)
      
      expect(changeAmount.value).toBe(14.5)
    })
    
    it('应该正确判断是否可以支付', () => {
      const { setPaymentMethod, canPay } = usePayment()
      
      store.finalAmount = 100
      setPaymentMethod(PAYMENT_METHODS.CASH)
      
      expect(canPay.value).toBe(true)
    })
  })
  
  describe('setPaymentMethod', () => {
    it('应该成功设置支付方式', () => {
      const { setPaymentMethod, selectedPaymentMethod } = usePayment()
      
      const result = setPaymentMethod(PAYMENT_METHODS.CASH)
      
      expect(result).toBe(true)
      expect(selectedPaymentMethod.value).toBe(PAYMENT_METHODS.CASH)
    })
    
    it('应该处理无效的支付方式', () => {
      const { setPaymentMethod } = usePayment()
      
      const result = setPaymentMethod('INVALID_METHOD')
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('不支持的支付方式')
    })
  })
  
  describe('processCashPayment', () => {
    it('应该成功处理现金支付', async () => {
      const { processCashPayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        receivedAmount: 120
      }
      
      const mockResponse = {
        success: true,
        paymentId: 'PAY001',
        transactionId: 'TXN001',
        status: 'SUCCESS'
      }
      
      PaymentApi.processCashPayment.mockResolvedValue(mockResponse)
      
      const result = await processCashPayment(paymentParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.processCashPayment).toHaveBeenCalledWith({
        ...paymentParams,
        changeAmount: 20
      })
      expect(message.success).toHaveBeenCalledWith('现金支付成功')
    })
    
    it('应该处理实收金额不足的情况', async () => {
      const { processCashPayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        receivedAmount: 80
      }
      
      const result = await processCashPayment(paymentParams)
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('实收金额不能少于应付金额')
    })
    
    it('应该处理支付失败的情况', async () => {
      const { processCashPayment } = usePayment()
      
      const mockError = new Error('支付处理失败')
      PaymentApi.processCashPayment.mockRejectedValue(mockError)
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        receivedAmount: 120
      }
      
      const result = await processCashPayment(paymentParams)
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('现金支付失败: 支付处理失败')
    })
  })
  
  describe('processQrCodePayment', () => {
    it('应该成功处理扫码支付', async () => {
      const { processQrCodePayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        paymentMethod: PAYMENT_METHODS.WECHAT
      }
      
      const mockResponse = {
        success: true,
        paymentId: 'PAY002',
        qrCodeUrl: 'https://qr.alipay.com/test',
        status: 'PENDING'
      }
      
      PaymentApi.processQrCodePayment.mockResolvedValue(mockResponse)
      
      const result = await processQrCodePayment(paymentParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.processQrCodePayment).toHaveBeenCalledWith(paymentParams)
      expect(message.info).toHaveBeenCalledWith('请使用微信扫码支付')
    })
    
    it('应该处理不支持的扫码支付方式', async () => {
      const { processQrCodePayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        paymentMethod: 'INVALID_QR'
      }
      
      const result = await processQrCodePayment(paymentParams)
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('不支持的扫码支付方式')
    })
  })
  
  describe('queryPaymentStatus', () => {
    it('应该成功查询支付状态', async () => {
      const { queryPaymentStatus } = usePayment()
      
      const queryParams = {
        paymentId: 'PAY002',
        paymentMethod: PAYMENT_METHODS.WECHAT
      }
      
      const mockResponse = {
        paymentId: 'PAY002',
        status: 'SUCCESS',
        transactionId: 'WX_TXN_001',
        paidAmount: 100,
        paidTime: '2025-01-02T10:30:00Z'
      }
      
      PaymentApi.queryQrCodePaymentStatus.mockResolvedValue(mockResponse)
      
      const result = await queryPaymentStatus(queryParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.queryQrCodePaymentStatus).toHaveBeenCalledWith(queryParams)
    })
    
    it('应该处理查询失败的情况', async () => {
      const { queryPaymentStatus } = usePayment()
      
      const mockError = new Error('查询失败')
      PaymentApi.queryQrCodePaymentStatus.mockRejectedValue(mockError)
      
      const result = await queryPaymentStatus({ paymentId: 'PAY002' })
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('支付状态查询失败: 查询失败')
    })
  })
  
  describe('processMemberCardPayment', () => {
    it('应该成功处理会员卡支付', async () => {
      const { processMemberCardPayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 100,
        memberId: 'M001',
        memberCardNo: 'VIP123456'
      }
      
      const mockResponse = {
        success: true,
        paymentId: 'PAY003',
        remainingBalance: 400,
        status: 'SUCCESS'
      }
      
      PaymentApi.processMemberCardPayment.mockResolvedValue(mockResponse)
      
      const result = await processMemberCardPayment(paymentParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.processMemberCardPayment).toHaveBeenCalledWith(paymentParams)
      expect(message.success).toHaveBeenCalledWith('会员卡支付成功，余额: ¥400.00')
    })
    
    it('应该处理余额不足的情况', async () => {
      const { processMemberCardPayment } = usePayment()
      
      const mockError = new Error('会员余额不足')
      PaymentApi.processMemberCardPayment.mockRejectedValue(mockError)
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 1000,
        memberId: 'M001'
      }
      
      const result = await processMemberCardPayment(paymentParams)
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('会员卡支付失败: 会员余额不足')
    })
  })
  
  describe('processPointsPayment', () => {
    it('应该成功处理积分支付', async () => {
      const { processPointsPayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        paymentAmount: 50,
        memberId: 'M001',
        pointsUsed: 5000
      }
      
      const mockResponse = {
        success: true,
        paymentId: 'PAY004',
        remainingPoints: 15000,
        status: 'SUCCESS'
      }
      
      PaymentApi.processPointsPayment.mockResolvedValue(mockResponse)
      
      const result = await processPointsPayment(paymentParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.processPointsPayment).toHaveBeenCalledWith(paymentParams)
      expect(message.success).toHaveBeenCalledWith('积分支付成功，剩余积分: 15,000')
    })
  })
  
  describe('processComboPayment', () => {
    it('应该成功处理组合支付', async () => {
      const { processComboPayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        totalAmount: 200,
        paymentMethods: [
          { method: PAYMENT_METHODS.CASH, amount: 100 },
          { method: PAYMENT_METHODS.WECHAT, amount: 100 }
        ]
      }
      
      const mockResponse = {
        success: true,
        paymentId: 'PAY006',
        payments: [
          { method: PAYMENT_METHODS.CASH, amount: 100, status: 'SUCCESS' },
          { method: PAYMENT_METHODS.WECHAT, amount: 100, status: 'SUCCESS' }
        ],
        status: 'SUCCESS'
      }
      
      PaymentApi.processComboPayment.mockResolvedValue(mockResponse)
      
      const result = await processComboPayment(paymentParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.processComboPayment).toHaveBeenCalledWith(paymentParams)
      expect(message.success).toHaveBeenCalledWith('组合支付成功')
    })
    
    it('应该验证组合支付金额', async () => {
      const { processComboPayment } = usePayment()
      
      const paymentParams = {
        orderId: 'ORDER001',
        totalAmount: 200,
        paymentMethods: [
          { method: PAYMENT_METHODS.CASH, amount: 100 },
          { method: PAYMENT_METHODS.WECHAT, amount: 50 } // 总额不匹配
        ]
      }
      
      const result = await processComboPayment(paymentParams)
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('组合支付金额不匹配')
    })
  })
  
  describe('cancelPayment', () => {
    it('应该成功取消支付', async () => {
      const { cancelPayment } = usePayment()
      
      const cancelParams = {
        paymentId: 'PAY002',
        cancelReason: '用户取消'
      }
      
      const mockResponse = {
        success: true,
        cancelled: true,
        cancelTime: '2025-01-02T10:40:00Z'
      }
      
      PaymentApi.cancelPayment.mockResolvedValue(mockResponse)
      
      const result = await cancelPayment(cancelParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.cancelPayment).toHaveBeenCalledWith(cancelParams)
      expect(message.success).toHaveBeenCalledWith('支付已取消')
    })
  })
  
  describe('requestRefund', () => {
    it('应该成功申请退款', async () => {
      const { requestRefund } = usePayment()
      
      const refundParams = {
        paymentId: 'PAY001',
        refundAmount: 50,
        refundReason: '商品质量问题'
      }
      
      const mockResponse = {
        success: true,
        refundId: 'REFUND001',
        status: 'PROCESSING'
      }
      
      PaymentApi.requestRefund.mockResolvedValue(mockResponse)
      
      const result = await requestRefund(refundParams)
      
      expect(result).toEqual(mockResponse)
      expect(PaymentApi.requestRefund).toHaveBeenCalledWith(refundParams)
      expect(message.success).toHaveBeenCalledWith('退款申请已提交，退款单号: REFUND001')
    })
  })
  
  describe('validatePaymentPassword', () => {
    it('应该成功验证支付密码', async () => {
      const { validatePaymentPassword } = usePayment()
      
      const mockResponse = {
        valid: true,
        message: '密码验证成功'
      }
      
      PaymentApi.validatePaymentPassword.mockResolvedValue(mockResponse)
      
      const result = await validatePaymentPassword('123456')
      
      expect(result).toBe(true)
      expect(PaymentApi.validatePaymentPassword).toHaveBeenCalledWith({
        password: '123456',
        cashierId: expect.any(String)
      })
    })
    
    it('应该处理密码错误的情况', async () => {
      const { validatePaymentPassword } = usePayment()
      
      const mockError = new Error('支付密码错误')
      PaymentApi.validatePaymentPassword.mockRejectedValue(mockError)
      
      const result = await validatePaymentPassword('wrong')
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('支付密码错误')
    })
  })
  
  describe('getPaymentConfig', () => {
    it('应该成功获取支付配置', async () => {
      const { getPaymentConfig } = usePayment()
      
      const mockConfig = {
        enabledMethods: [
          PAYMENT_METHODS.CASH,
          PAYMENT_METHODS.WECHAT,
          PAYMENT_METHODS.ALIPAY
        ],
        limits: {
          [PAYMENT_METHODS.CASH]: { max: 10000 },
          [PAYMENT_METHODS.WECHAT]: { max: 50000 }
        }
      }
      
      PaymentApi.getPaymentConfig.mockResolvedValue(mockConfig)
      
      const result = await getPaymentConfig()
      
      expect(result).toEqual(mockConfig)
      expect(PaymentApi.getPaymentConfig).toHaveBeenCalled()
    })
  })
  
  describe('startPaymentPolling', () => {
    it('应该开始支付状态轮询', async () => {
      const { startPaymentPolling, stopPaymentPolling } = usePayment()
      
      vi.useFakeTimers()
      
      const mockCallback = vi.fn()
      const paymentId = 'PAY002'
      
      PaymentApi.queryQrCodePaymentStatus.mockResolvedValue({
        status: 'PENDING'
      })
      
      startPaymentPolling(paymentId, mockCallback, 1000)
      
      // 快进时间触发轮询
      vi.advanceTimersByTime(1000)
      
      expect(PaymentApi.queryQrCodePaymentStatus).toHaveBeenCalledWith({
        paymentId: 'PAY002'
      })
      
      stopPaymentPolling()
      vi.useRealTimers()
    })
    
    it('应该在支付成功时停止轮询', async () => {
      const { startPaymentPolling } = usePayment()
      
      vi.useFakeTimers()
      
      const mockCallback = vi.fn()
      const paymentId = 'PAY002'
      
      PaymentApi.queryQrCodePaymentStatus.mockResolvedValue({
        status: 'SUCCESS',
        transactionId: 'TXN001'
      })
      
      startPaymentPolling(paymentId, mockCallback, 1000)
      
      vi.advanceTimersByTime(1000)
      
      expect(mockCallback).toHaveBeenCalledWith({
        status: 'SUCCESS',
        transactionId: 'TXN001'
      })
      
      vi.useRealTimers()
    })
  })
  
  describe('calculatePaymentSummary', () => {
    it('应该正确计算支付汇总', () => {
      const { calculatePaymentSummary } = usePayment()
      
      const payments = [
        { method: PAYMENT_METHODS.CASH, amount: 100 },
        { method: PAYMENT_METHODS.WECHAT, amount: 50 },
        { method: PAYMENT_METHODS.POINTS, amount: 20 }
      ]
      
      const result = calculatePaymentSummary(payments)
      
      expect(result).toEqual({
        totalAmount: 170,
        methodCount: 3,
        methods: {
          [PAYMENT_METHODS.CASH]: 100,
          [PAYMENT_METHODS.WECHAT]: 50,
          [PAYMENT_METHODS.POINTS]: 20
        }
      })
    })
  })
  
  describe('resetPaymentState', () => {
    it('应该重置支付状态', () => {
      const { 
        setPaymentMethod, 
        setReceivedAmount, 
        resetPaymentState,
        selectedPaymentMethod,
        receivedAmount,
        paymentStatus
      } = usePayment()
      
      // 设置一些状态
      setPaymentMethod(PAYMENT_METHODS.CASH)
      setReceivedAmount(100)
      store.paymentStatus = 'processing'
      
      // 重置状态
      resetPaymentState()
      
      expect(selectedPaymentMethod.value).toBe('')
      expect(receivedAmount.value).toBe(0)
      expect(paymentStatus.value).toBe('idle')
    })
  })
})