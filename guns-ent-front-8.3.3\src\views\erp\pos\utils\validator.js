/**
 * POS模块验证工具函数
 * 
 * 提供商品验证、数量验证、价格验证、会员信息验证等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { NUMERIC_CONSTANTS, PRICING_TYPES } from './constants'

/**
 * 基础验证工具类
 */
class BaseValidator {
  
  /**
   * 验证是否为有效数字
   * @param {any} value - 待验证的值
   * @param {Object} options - 验证选项
   * @param {boolean} options.allowZero - 是否允许为0，默认true
   * @param {boolean} options.allowNegative - 是否允许负数，默认false
   * @param {number} options.min - 最小值
   * @param {number} options.max - 最大值
   * @returns {Object} 验证结果 {isValid: boolean, message: string}
   */
  static validateNumber(value, options = {}) {
    const {
      allowZero = true,
      allowNegative = false,
      min,
      max
    } = options
    
    // 检查是否为数字
    if (typeof value !== 'number' || isNaN(value) || !isFinite(value)) {
      return {
        isValid: false,
        message: '必须是有效的数字'
      }
    }
    
    // 检查是否允许为0
    if (!allowZero && value === 0) {
      return {
        isValid: false,
        message: '不能为0'
      }
    }
    
    // 检查是否允许负数
    if (!allowNegative && value < 0) {
      return {
        isValid: false,
        message: '不能为负数'
      }
    }
    
    // 检查最小值
    if (typeof min === 'number' && value < min) {
      return {
        isValid: false,
        message: `不能小于${min}`
      }
    }
    
    // 检查最大值
    if (typeof max === 'number' && value > max) {
      return {
        isValid: false,
        message: `不能大于${max}`
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证字符串
   * @param {any} value - 待验证的值
   * @param {Object} options - 验证选项
   * @param {boolean} options.required - 是否必填，默认true
   * @param {number} options.minLength - 最小长度
   * @param {number} options.maxLength - 最大长度
   * @param {RegExp} options.pattern - 正则表达式模式
   * @param {string} options.patternMessage - 正则验证失败的提示信息
   * @returns {Object} 验证结果
   */
  static validateString(value, options = {}) {
    const {
      required = true,
      minLength,
      maxLength,
      pattern,
      patternMessage = '格式不正确'
    } = options
    
    // 检查是否为字符串
    if (typeof value !== 'string') {
      if (required) {
        return {
          isValid: false,
          message: '必须是字符串'
        }
      } else if (value == null) {
        return {
          isValid: true,
          message: ''
        }
      }
    }
    
    const str = String(value || '')
    
    // 检查是否必填
    if (required && str.trim().length === 0) {
      return {
        isValid: false,
        message: '不能为空'
      }
    }
    
    // 检查最小长度
    if (typeof minLength === 'number' && str.length < minLength) {
      return {
        isValid: false,
        message: `长度不能少于${minLength}个字符`
      }
    }
    
    // 检查最大长度
    if (typeof maxLength === 'number' && str.length > maxLength) {
      return {
        isValid: false,
        message: `长度不能超过${maxLength}个字符`
      }
    }
    
    // 检查正则表达式
    if (pattern instanceof RegExp && str.length > 0 && !pattern.test(str)) {
      return {
        isValid: false,
        message: patternMessage
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证数组
   * @param {any} value - 待验证的值
   * @param {Object} options - 验证选项
   * @param {boolean} options.required - 是否必填，默认true
   * @param {number} options.minLength - 最小长度
   * @param {number} options.maxLength - 最大长度
   * @returns {Object} 验证结果
   */
  static validateArray(value, options = {}) {
    const {
      required = true,
      minLength,
      maxLength
    } = options
    
    // 检查是否为数组
    if (!Array.isArray(value)) {
      if (required) {
        return {
          isValid: false,
          message: '必须是数组'
        }
      } else if (value == null) {
        return {
          isValid: true,
          message: ''
        }
      }
    }
    
    const arr = Array.isArray(value) ? value : []
    
    // 检查是否必填
    if (required && arr.length === 0) {
      return {
        isValid: false,
        message: '不能为空'
      }
    }
    
    // 检查最小长度
    if (typeof minLength === 'number' && arr.length < minLength) {
      return {
        isValid: false,
        message: `至少需要${minLength}个元素`
      }
    }
    
    // 检查最大长度
    if (typeof maxLength === 'number' && arr.length > maxLength) {
      return {
        isValid: false,
        message: `最多只能有${maxLength}个元素`
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
}

/**
 * 购物车验证工具类
 */
export class CartValidator {
  
  /**
   * 验证商品对象
   * @param {Object} product - 商品对象
   * @returns {Object} 验证结果
   */
  static validateProduct(product) {
    if (!product || typeof product !== 'object') {
      return {
        isValid: false,
        message: '商品信息不能为空'
      }
    }
    
    // 验证商品ID
    const idValidation = BaseValidator.validateString(product.id, {
      required: true,
      minLength: 1,
      maxLength: 50
    })
    if (!idValidation.isValid) {
      return {
        isValid: false,
        message: `商品ID${idValidation.message}`
      }
    }
    
    // 验证商品名称
    const nameValidation = BaseValidator.validateString(product.name, {
      required: true,
      minLength: 1,
      maxLength: 100
    })
    if (!nameValidation.isValid) {
      return {
        isValid: false,
        message: `商品名称${nameValidation.message}`
      }
    }
    
    // 验证商品价格
    const priceValidation = BaseValidator.validateNumber(product.price, {
      allowZero: false,
      allowNegative: false,
      min: 0.01,
      max: 999999.99
    })
    if (!priceValidation.isValid) {
      return {
        isValid: false,
        message: `商品价格${priceValidation.message}`
      }
    }
    
    // 验证计价类型
    if (product.pricingType && !Object.values(PRICING_TYPES).includes(product.pricingType)) {
      return {
        isValid: false,
        message: '无效的计价类型'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证商品数量
   * @param {number} quantity - 数量
   * @param {Object} options - 验证选项
   * @param {number} options.maxQuantity - 最大数量，默认1000
   * @param {number} options.precision - 精度，默认3位小数
   * @returns {Object} 验证结果
   */
  static validateQuantity(quantity, options = {}) {
    const {
      maxQuantity = 1000,
      precision = NUMERIC_CONSTANTS.QUANTITY_PRECISION
    } = options
    
    const numberValidation = BaseValidator.validateNumber(quantity, {
      allowZero: false,
      allowNegative: false,
      min: 0.001,
      max: maxQuantity
    })
    
    if (!numberValidation.isValid) {
      return {
        isValid: false,
        message: `商品数量${numberValidation.message}`
      }
    }
    
    // 检查精度
    const decimalPlaces = (quantity.toString().split('.')[1] || '').length
    if (decimalPlaces > precision) {
      return {
        isValid: false,
        message: `数量精度不能超过${precision}位小数`
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证购物车项
   * @param {Object} cartItem - 购物车项
   * @returns {Object} 验证结果
   */
  static validateCartItem(cartItem) {
    if (!cartItem || typeof cartItem !== 'object') {
      return {
        isValid: false,
        message: '购物车项不能为空'
      }
    }
    
    // 验证商品信息
    const productValidation = this.validateProduct(cartItem)
    if (!productValidation.isValid) {
      return productValidation
    }
    
    // 验证数量
    const quantityValidation = this.validateQuantity(cartItem.quantity)
    if (!quantityValidation.isValid) {
      return quantityValidation
    }
    
    // 验证小计金额
    if (typeof cartItem.subtotal === 'number') {
      const expectedSubtotal = cartItem.price * cartItem.quantity
      const tolerance = 0.01 // 允许1分钱的误差
      
      if (Math.abs(cartItem.subtotal - expectedSubtotal) > tolerance) {
        return {
          isValid: false,
          message: '小计金额计算错误'
        }
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证购物车
   * @param {Array} cartItems - 购物车商品列表
   * @returns {Object} 验证结果
   */
  static validateCart(cartItems) {
    const arrayValidation = BaseValidator.validateArray(cartItems, {
      required: true,
      minLength: 1,
      maxLength: NUMERIC_CONSTANTS.MAX_CART_ITEMS
    })
    
    if (!arrayValidation.isValid) {
      return {
        isValid: false,
        message: `购物车${arrayValidation.message}`
      }
    }
    
    // 验证每个购物车项
    for (let i = 0; i < cartItems.length; i++) {
      const itemValidation = this.validateCartItem(cartItems[i])
      if (!itemValidation.isValid) {
        return {
          isValid: false,
          message: `第${i + 1}个商品${itemValidation.message}`
        }
      }
    }
    
    // 检查是否有重复商品
    const productIds = cartItems.map(item => item.id)
    const uniqueIds = [...new Set(productIds)]
    if (productIds.length !== uniqueIds.length) {
      return {
        isValid: false,
        message: '购物车中存在重复商品'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
}

/**
 * 支付验证工具类
 */
export class PaymentValidator {
  
  /**
   * 验证支付金额
   * @param {number} amount - 支付金额
   * @param {Object} options - 验证选项
   * @param {number} options.maxAmount - 最大金额，默认999999.99
   * @returns {Object} 验证结果
   */
  static validatePaymentAmount(amount, options = {}) {
    const { maxAmount = 999999.99 } = options
    
    return BaseValidator.validateNumber(amount, {
      allowZero: false,
      allowNegative: false,
      min: 0.01,
      max: maxAmount
    })
  }
  
  /**
   * 验证现金支付
   * @param {Object} paymentData - 支付数据
   * @param {number} paymentData.payableAmount - 应付金额
   * @param {number} paymentData.receivedAmount - 实收金额
   * @returns {Object} 验证结果
   */
  static validateCashPayment(paymentData) {
    if (!paymentData || typeof paymentData !== 'object') {
      return {
        isValid: false,
        message: '支付数据不能为空'
      }
    }
    
    // 验证应付金额
    const payableValidation = this.validatePaymentAmount(paymentData.payableAmount)
    if (!payableValidation.isValid) {
      return {
        isValid: false,
        message: `应付金额${payableValidation.message}`
      }
    }
    
    // 验证实收金额
    const receivedValidation = this.validatePaymentAmount(paymentData.receivedAmount)
    if (!receivedValidation.isValid) {
      return {
        isValid: false,
        message: `实收金额${receivedValidation.message}`
      }
    }
    
    // 验证实收金额不能少于应付金额
    if (paymentData.receivedAmount < paymentData.payableAmount) {
      return {
        isValid: false,
        message: '实收金额不能少于应付金额'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证扫码支付
   * @param {Object} paymentData - 支付数据
   * @param {number} paymentData.payableAmount - 应付金额
   * @param {string} paymentData.paymentMethod - 支付方式
   * @returns {Object} 验证结果
   */
  static validateQrCodePayment(paymentData) {
    if (!paymentData || typeof paymentData !== 'object') {
      return {
        isValid: false,
        message: '支付数据不能为空'
      }
    }
    
    // 验证应付金额
    const amountValidation = this.validatePaymentAmount(paymentData.payableAmount)
    if (!amountValidation.isValid) {
      return {
        isValid: false,
        message: `应付金额${amountValidation.message}`
      }
    }
    
    // 验证支付方式
    const validMethods = ['WECHAT', 'ALIPAY']
    if (!validMethods.includes(paymentData.paymentMethod)) {
      return {
        isValid: false,
        message: '无效的扫码支付方式'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证银行卡支付
   * @param {Object} paymentData - 支付数据
   * @param {number} paymentData.payableAmount - 应付金额
   * @param {string} paymentData.cardNo - 银行卡号
   * @returns {Object} 验证结果
   */
  static validateBankCardPayment(paymentData) {
    if (!paymentData || typeof paymentData !== 'object') {
      return {
        isValid: false,
        message: '支付数据不能为空'
      }
    }
    
    // 验证应付金额
    const amountValidation = this.validatePaymentAmount(paymentData.payableAmount)
    if (!amountValidation.isValid) {
      return {
        isValid: false,
        message: `应付金额${amountValidation.message}`
      }
    }
    
    // 验证银行卡号
    const cardNoValidation = BaseValidator.validateString(paymentData.cardNo, {
      required: true,
      minLength: 16,
      maxLength: 19,
      pattern: /^\d+$/,
      patternMessage: '银行卡号只能包含数字'
    })
    if (!cardNoValidation.isValid) {
      return {
        isValid: false,
        message: `银行卡号${cardNoValidation.message}`
      }
    }
    
    // 简单的银行卡号校验（Luhn算法）
    if (!this.validateLuhn(paymentData.cardNo)) {
      return {
        isValid: false,
        message: '银行卡号格式不正确'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * Luhn算法验证银行卡号
   * @param {string} cardNo - 银行卡号
   * @returns {boolean} 是否有效
   */
  static validateLuhn(cardNo) {
    if (typeof cardNo !== 'string' || !/^\d+$/.test(cardNo)) {
      return false
    }
    
    let sum = 0
    let alternate = false
    
    // 从右到左遍历
    for (let i = cardNo.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNo.charAt(i), 10)
      
      if (alternate) {
        digit *= 2
        if (digit > 9) {
          digit = (digit % 10) + 1
        }
      }
      
      sum += digit
      alternate = !alternate
    }
    
    return sum % 10 === 0
  }
}

/**
 * 会员验证工具类
 */
export class MemberValidator {
  
  /**
   * 验证会员卡号
   * @param {string} cardNo - 会员卡号
   * @returns {Object} 验证结果
   */
  static validateMemberCardNo(cardNo) {
    return BaseValidator.validateString(cardNo, {
      required: true,
      minLength: 6,
      maxLength: 20,
      pattern: /^[A-Za-z0-9]+$/,
      patternMessage: '会员卡号只能包含字母和数字'
    })
  }
  
  /**
   * 验证手机号
   * @param {string} phone - 手机号
   * @returns {Object} 验证结果
   */
  static validatePhone(phone) {
    return BaseValidator.validateString(phone, {
      required: true,
      minLength: 11,
      maxLength: 11,
      pattern: /^1[3-9]\d{9}$/,
      patternMessage: '请输入正确的手机号码'
    })
  }
  
  /**
   * 验证会员姓名
   * @param {string} name - 会员姓名
   * @returns {Object} 验证结果
   */
  static validateMemberName(name) {
    return BaseValidator.validateString(name, {
      required: true,
      minLength: 2,
      maxLength: 20,
      pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
      patternMessage: '姓名只能包含中文、英文和空格'
    })
  }
  
  /**
   * 验证积分数量
   * @param {number} points - 积分数量
   * @param {Object} options - 验证选项
   * @param {number} options.maxPoints - 最大积分，默认999999999
   * @returns {Object} 验证结果
   */
  static validatePoints(points, options = {}) {
    const { maxPoints = 999999999 } = options
    
    const numberValidation = BaseValidator.validateNumber(points, {
      allowZero: true,
      allowNegative: false,
      min: 0,
      max: maxPoints
    })
    
    if (!numberValidation.isValid) {
      return {
        isValid: false,
        message: `积分${numberValidation.message}`
      }
    }
    
    // 积分必须是整数
    if (!Number.isInteger(points)) {
      return {
        isValid: false,
        message: '积分必须是整数'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证会员余额
   * @param {number} balance - 余额
   * @param {Object} options - 验证选项
   * @param {number} options.maxBalance - 最大余额，默认999999.99
   * @returns {Object} 验证结果
   */
  static validateBalance(balance, options = {}) {
    const { maxBalance = 999999.99 } = options
    
    return BaseValidator.validateNumber(balance, {
      allowZero: true,
      allowNegative: false,
      min: 0,
      max: maxBalance
    })
  }
  
  /**
   * 验证折扣率
   * @param {number} discountRate - 折扣率（0-1之间）
   * @returns {Object} 验证结果
   */
  static validateDiscountRate(discountRate) {
    return BaseValidator.validateNumber(discountRate, {
      allowZero: true,
      allowNegative: false,
      min: 0,
      max: 1
    })
  }
}

/**
 * 订单验证工具类
 */
export class OrderValidator {
  
  /**
   * 验证订单基本信息
   * @param {Object} orderData - 订单数据
   * @returns {Object} 验证结果
   */
  static validateOrderData(orderData) {
    if (!orderData || typeof orderData !== 'object') {
      return {
        isValid: false,
        message: '订单数据不能为空'
      }
    }
    
    // 验证订单商品
    const itemsValidation = CartValidator.validateCart(orderData.items)
    if (!itemsValidation.isValid) {
      return {
        isValid: false,
        message: `订单商品${itemsValidation.message}`
      }
    }
    
    // 验证总金额
    const totalValidation = BaseValidator.validateNumber(orderData.totalAmount, {
      allowZero: false,
      allowNegative: false,
      min: 0.01,
      max: 999999.99
    })
    if (!totalValidation.isValid) {
      return {
        isValid: false,
        message: `订单总金额${totalValidation.message}`
      }
    }
    
    // 验证折扣金额
    if (typeof orderData.discountAmount === 'number') {
      const discountValidation = BaseValidator.validateNumber(orderData.discountAmount, {
        allowZero: true,
        allowNegative: false,
        min: 0,
        max: orderData.totalAmount
      })
      if (!discountValidation.isValid) {
        return {
          isValid: false,
          message: `折扣金额${discountValidation.message}`
        }
      }
    }
    
    // 验证实付金额
    const finalValidation = BaseValidator.validateNumber(orderData.finalAmount, {
      allowZero: false,
      allowNegative: false,
      min: 0.01,
      max: 999999.99
    })
    if (!finalValidation.isValid) {
      return {
        isValid: false,
        message: `实付金额${finalValidation.message}`
      }
    }
    
    // 验证金额计算是否正确
    const expectedFinalAmount = orderData.totalAmount - (orderData.discountAmount || 0)
    const tolerance = 0.01 // 允许1分钱的误差
    
    if (Math.abs(orderData.finalAmount - expectedFinalAmount) > tolerance) {
      return {
        isValid: false,
        message: '订单金额计算错误'
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证订单号
   * @param {string} orderNo - 订单号
   * @returns {Object} 验证结果
   */
  static validateOrderNo(orderNo) {
    return BaseValidator.validateString(orderNo, {
      required: true,
      minLength: 10,
      maxLength: 30,
      pattern: /^[A-Za-z0-9]+$/,
      patternMessage: '订单号只能包含字母和数字'
    })
  }
  
  /**
   * 验证订单备注
   * @param {string} remark - 备注
   * @returns {Object} 验证结果
   */
  static validateOrderRemark(remark) {
    return BaseValidator.validateString(remark, {
      required: false,
      maxLength: 200
    })
  }
}

/**
 * 商品验证工具类
 */
export class ProductValidator {
  
  /**
   * 验证商品条码
   * @param {string} barcode - 商品条码
   * @returns {Object} 验证结果
   */
  static validateBarcode(barcode) {
    return BaseValidator.validateString(barcode, {
      required: false,
      minLength: 8,
      maxLength: 18,
      pattern: /^\d+$/,
      patternMessage: '商品条码只能包含数字'
    })
  }
  
  /**
   * 验证商品分类ID
   * @param {string} categoryId - 分类ID
   * @returns {Object} 验证结果
   */
  static validateCategoryId(categoryId) {
    return BaseValidator.validateString(categoryId, {
      required: true,
      minLength: 1,
      maxLength: 50
    })
  }
  
  /**
   * 验证商品库存
   * @param {number} stock - 库存数量
   * @returns {Object} 验证结果
   */
  static validateStock(stock) {
    return BaseValidator.validateNumber(stock, {
      allowZero: true,
      allowNegative: false,
      min: 0,
      max: 999999
    })
  }
  
  /**
   * 验证商品规格
   * @param {string} specifications - 商品规格
   * @returns {Object} 验证结果
   */
  static validateSpecifications(specifications) {
    return BaseValidator.validateString(specifications, {
      required: false,
      maxLength: 100
    })
  }
  
  /**
   * 验证商品单位
   * @param {string} unit - 商品单位
   * @returns {Object} 验证结果
   */
  static validateUnit(unit) {
    return BaseValidator.validateString(unit, {
      required: false,
      maxLength: 10,
      pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
      patternMessage: '单位只能包含中文或英文'
    })
  }
}

/**
 * 业务规则验证工具类
 */
export class BusinessValidator {
  
  /**
   * 验证营业时间
   * @param {Date} currentTime - 当前时间
   * @param {Object} businessHours - 营业时间配置
   * @param {string} businessHours.openTime - 开始营业时间 'HH:mm'
   * @param {string} businessHours.closeTime - 结束营业时间 'HH:mm'
   * @returns {Object} 验证结果
   */
  static validateBusinessHours(currentTime = new Date(), businessHours = {}) {
    const { openTime = '08:00', closeTime = '22:00' } = businessHours
    
    const now = currentTime
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const currentTimeStr = `${String(currentHour).padStart(2, '0')}:${String(currentMinute).padStart(2, '0')}`
    
    if (currentTimeStr < openTime || currentTimeStr > closeTime) {
      return {
        isValid: false,
        message: `当前时间不在营业时间内（${openTime}-${closeTime}）`
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证库存充足性
   * @param {Array} cartItems - 购物车商品
   * @param {Array} stockData - 库存数据
   * @returns {Object} 验证结果
   */
  static validateStockAvailability(cartItems, stockData) {
    if (!Array.isArray(cartItems) || !Array.isArray(stockData)) {
      return {
        isValid: false,
        message: '参数格式错误'
      }
    }
    
    const stockMap = new Map()
    stockData.forEach(item => {
      stockMap.set(item.productId, item.stock || 0)
    })
    
    for (const cartItem of cartItems) {
      const availableStock = stockMap.get(cartItem.id) || 0
      
      if (cartItem.quantity > availableStock) {
        return {
          isValid: false,
          message: `商品"${cartItem.name}"库存不足，当前库存：${availableStock}，需要：${cartItem.quantity}`
        }
      }
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
  
  /**
   * 验证会员权限
   * @param {Object} member - 会员信息
   * @param {string} operation - 操作类型
   * @returns {Object} 验证结果
   */
  static validateMemberPermission(member, operation) {
    if (!member) {
      return {
        isValid: true,
        message: ''
      }
    }
    
    // 检查会员状态
    if (member.status !== 'active') {
      return {
        isValid: false,
        message: '会员账户已被冻结或停用'
      }
    }
    
    // 检查会员卡是否过期
    if (member.expireDate && new Date(member.expireDate) < new Date()) {
      return {
        isValid: false,
        message: '会员卡已过期'
      }
    }
    
    // 根据操作类型检查权限
    switch (operation) {
      case 'discount':
        // 检查是否有折扣权限
        if (!member.allowDiscount) {
          return {
            isValid: false,
            message: '该会员不享受折扣优惠'
          }
        }
        break
        
      case 'points':
        // 检查是否有积分权限
        if (!member.allowPoints) {
          return {
            isValid: false,
            message: '该会员不参与积分活动'
          }
        }
        break
        
      case 'balance':
        // 检查余额支付权限
        if (!member.allowBalancePayment) {
          return {
            isValid: false,
            message: '该会员不支持余额支付'
          }
        }
        break
    }
    
    return {
      isValid: true,
      message: ''
    }
  }
}