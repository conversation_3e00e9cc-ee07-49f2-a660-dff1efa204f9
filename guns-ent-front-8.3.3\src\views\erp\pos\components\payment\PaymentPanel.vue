<!--\n  支付面板主组件\n  \n  重构后的支付面板，拆分为多个子组件，职责更加清晰\n  使用Composables管理业务逻辑，组件专注于UI渲染\n  \n  <AUTHOR>  @since 2025/01/02\n-->\n<template>\n  <a-modal\n    :open=\"visible\"\n    @update:open=\"handleVisibleChange\"\n    title=\"收银结算\"\n    width=\"600px\"\n    :closable=\"false\"\n    :maskClosable=\"false\"\n    :footer=\"null\"\n    class=\"payment-modal\"\n  >\n    <div class=\"payment-panel\">\n      <!-- 订单信息汇总 -->\n      <div class=\"order-summary\">\n        <div class=\"summary-header\">\n          <h4>订单信息</h4>\n          <div class=\"order-items-count\">共 {{ orderSummary.itemCount }} 件商品</div>\n        </div>\n        \n        <div class=\"amount-details\">\n          <div class=\"amount-row\">\n            <span class=\"label\">商品总额:</span>\n            <span class=\"value\">{{ formatPrice(orderSummary.totalAmount) }}</span>\n          </div>\n          <div class=\"amount-row\" v-if=\"orderSummary.discountAmount > 0\">\n            <span class=\"label\">优惠金额:</span>\n            <span class=\"value discount\">-{{ formatPrice(orderSummary.discountAmount) }}</span>\n          </div>\n          <div class=\"amount-row\" v-if=\"orderSummary.pointsDeductionAmount > 0\">\n            <span class=\"label\">积分抵扣:</span>\n            <span class=\"value discount\">-{{ formatPrice(orderSummary.pointsDeductionAmount) }}</span>\n          </div>\n          <div class=\"amount-row total\">\n            <span class=\"label\">应付金额:</span>\n            <span class=\"value\">{{ formatPrice(orderSummary.finalAmount) }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 支付方式选择 -->\n      <PaymentMethod\n        :selected-method=\"selectedPaymentMethod\"\n        :methods=\"availablePaymentMethods\"\n        :payment-amount=\"orderSummary.finalAmount\"\n        :member-info=\"memberInfo\"\n        :loading=\"loading\"\n        @method-select=\"handleMethodSelect\"\n        @method-change=\"handleMethodChange\"\n      />\n\n      <!-- 支付方式特定组件 -->\n      <div class=\"payment-content\">\n        <!-- 现金支付 -->\n        <CashPayment\n          v-if=\"selectedPaymentMethod === 'CASH'\"\n          :payment-amount=\"orderSummary.finalAmount\"\n          :initial-amount=\"orderSummary.finalAmount\"\n          :loading=\"loading\"\n          @amount-change=\"handleCashAmountChange\"\n          @confirm-payment=\"handleCashPayment\"\n        />\n        \n        <!-- 扫码支付 -->\n        <QrPayment\n          v-else-if=\"isQrPayment\"\n          :payment-method=\"selectedPaymentMethod\"\n          :payment-amount=\"orderSummary.finalAmount\"\n          :order-info=\"orderInfo\"\n          @payment-success=\"handlePaymentSuccess\"\n          @payment-failed=\"handlePaymentFailed\"\n          @payment-cancelled=\"handlePaymentCancelled\"\n        />\n        \n        <!-- 会员卡支付 -->\n        <div class=\"member-payment\" v-else-if=\"selectedPaymentMethod === 'MEMBER'\">\n          <div class=\"member-balance\" v-if=\"memberInfo\">\n            <div class=\"balance-info\">\n              <span class=\"label\">会员余额:</span>\n              <span class=\"value\">{{ formatPrice(memberInfo.balance || 0) }}</span>\n            </div>\n            <div class=\"balance-check\" v-if=\"memberInfo.balance < orderSummary.finalAmount\">\n              <a-alert\n                message=\"余额不足\"\n                description=\"会员余额不足以支付此订单，请选择其他支付方式或充值后再试。\"\n                type=\"warning\"\n                show-icon\n              />\n            </div>\n          </div>\n          <div class=\"no-member\" v-else>\n            <a-alert\n              message=\"请先选择会员\"\n              description=\"使用会员卡支付需要先选择会员，请返回选择会员后再试。\"\n              type=\"info\"\n              show-icon\n            />\n          </div>\n        </div>\n        \n        <!-- 银行卡支付 -->\n        <div class=\"card-payment\" v-else-if=\"selectedPaymentMethod === 'CARD'\">\n          <div class=\"card-tips\">\n            <div class=\"tip-icon\">\n              <icon-font iconClass=\"icon-card\" />\n            </div>\n            <div class=\"tip-content\">\n              <div class=\"tip-title\">请刷卡或插卡</div>\n              <div class=\"tip-desc\">请将银行卡插入读卡器或在POS机上刷卡</div>\n            </div>\n          </div>\n          \n          <div class=\"card-status\" v-if=\"cardStatus\">\n            <a-spin v-if=\"cardStatus === 'reading'\" />\n            <span class=\"status-text\">{{ getCardStatusText() }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 支付结果 -->\n      <PaymentResult\n        v-if=\"showPaymentResult\"\n        :status=\"paymentStatus\"\n        :payment-info=\"paymentResult\"\n        :error-info=\"paymentError\"\n        :message=\"paymentMessage\"\n        @complete=\"handlePaymentComplete\"\n        @retry=\"handlePaymentRetry\"\n        @cancel=\"handlePaymentCancel\"\n        @print=\"handlePrintReceipt\"\n      />\n\n      <!-- 操作按钮 -->\n      <div class=\"payment-actions\" v-if=\"!showPaymentResult\">\n        <a-button size=\"large\" @click=\"cancelPayment\" class=\"cancel-btn\">\n          取消\n        </a-button>\n        <a-button\n          type=\"primary\"\n          size=\"large\"\n          @click=\"confirmPayment\"\n          :loading=\"loading\"\n          :disabled=\"!canConfirmPayment\"\n          class=\"confirm-btn\"\n        >\n          {{ getConfirmButtonText() }}\n        </a-button>\n      </div>\n    </div>\n  </a-modal>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport { message } from 'ant-design-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\nimport PaymentMethod from './PaymentMethod.vue'\nimport CashPayment from './CashPayment.vue'\nimport QrPayment from './QrPayment.vue'\nimport PaymentResult from './PaymentResult.vue'\nimport { usePayment } from '../../composables/usePayment'\nimport { AmountFormatter } from '../../utils/formatter'\nimport { PAYMENT_METHODS } from '../../utils/constants'\n\n// 定义组件名称\ndefineOptions({\n  name: 'PaymentPanel'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 弹窗显示状态\n  visible: {\n    type: Boolean,\n    default: false\n  },\n  // 订单信息\n  orderInfo: {\n    type: Object,\n    default: () => ({})\n  },\n  // 会员信息\n  memberInfo: {\n    type: Object,\n    default: null\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'update:visible',\n  'payment-success',\n  'payment-cancel'\n])\n\n// 使用支付业务逻辑\nconst {\n  selectedPaymentMethod,\n  paymentStatus,\n  paymentResult,\n  paymentError,\n  paymentMessage,\n  loading,\n  availablePaymentMethods,\n  canConfirmPayment,\n  selectPaymentMethod,\n  processPayment,\n  cancelPayment: cancelPaymentProcess,\n  resetPaymentStatus\n} = usePayment()\n\n// 响应式状态\nconst cashAmount = ref(0)\nconst cardStatus = ref('')\nconst showPaymentResult = ref(false)\n\n// ==================== 计算属性 ====================\n\n/**\n * 订单汇总信息\n */\nconst orderSummary = computed(() => {\n  return {\n    itemCount: props.orderInfo.itemCount || 0,\n    totalAmount: props.orderInfo.totalAmount || 0,\n    discountAmount: props.orderInfo.discountAmount || 0,\n    pointsDeductionAmount: props.orderInfo.pointsDeductionAmount || 0,\n    finalAmount: props.orderInfo.finalAmount || 0\n  }\n})\n\n/**\n * 是否为扫码支付\n */\nconst isQrPayment = computed(() => {\n  return ['WECHAT', 'ALIPAY'].includes(selectedPaymentMethod.value)\n})\n\n// ==================== 方法 ====================\n\n/**\n * 格式化价格显示\n * @param {number} price - 价格\n * @returns {string} 格式化后的价格\n */\nconst formatPrice = (price) => {\n  return AmountFormatter.formatCurrency(price || 0).replace('￥', '')\n}\n\n/**\n * 处理支付方式选择\n * @param {string} method - 支付方式\n */\nconst handleMethodSelect = (method) => {\n  selectPaymentMethod(method)\n  \n  // 重置相关状态\n  cashAmount.value = 0\n  cardStatus.value = ''\n  showPaymentResult.value = false\n}\n\n/**\n * 处理支付方式变化\n * @param {Object} data - 变化数据\n */\nconst handleMethodChange = (data) => {\n  // 可以在这里处理支付方式变化的额外逻辑\n  console.log('支付方式变化:', data)\n}\n\n/**\n * 处理现金金额变化\n * @param {Object} data - 金额数据\n */\nconst handleCashAmountChange = (data) => {\n  cashAmount.value = data.receivedAmount\n}\n\n/**\n * 处理现金支付\n * @param {Object} data - 支付数据\n */\nconst handleCashPayment = async (data) => {\n  const paymentData = {\n    paymentMethod: 'CASH',\n    paymentAmount: orderSummary.value.finalAmount,\n    receivedAmount: data.receivedAmount,\n    changeAmount: data.changeAmount,\n    changeBreakdown: data.changeBreakdown\n  }\n  \n  await processPayment(paymentData)\n  showPaymentResult.value = true\n}\n\n/**\n * 处理支付成功\n * @param {Object} result - 支付结果\n */\nconst handlePaymentSuccess = (result) => {\n  showPaymentResult.value = true\n  message.success('支付成功')\n}\n\n/**\n * 处理支付失败\n * @param {Object} error - 错误信息\n */\nconst handlePaymentFailed = (error) => {\n  showPaymentResult.value = true\n  message.error('支付失败: ' + (error.error || '未知错误'))\n}\n\n/**\n * 处理支付取消\n */\nconst handlePaymentCancelled = () => {\n  showPaymentResult.value = true\n}\n\n/**\n * 获取银行卡状态文本\n */\nconst getCardStatusText = () => {\n  switch (cardStatus.value) {\n    case 'reading':\n      return '正在读取银行卡信息...'\n    case 'processing':\n      return '正在处理支付...'\n    case 'success':\n      return '支付成功'\n    case 'failed':\n      return '支付失败'\n    default:\n      return ''\n  }\n}\n\n/**\n * 获取确认按钮文本\n */\nconst getConfirmButtonText = () => {\n  if (loading.value) {\n    return '处理中...'\n  }\n  \n  switch (selectedPaymentMethod.value) {\n    case 'CASH':\n      return '确认收款'\n    case 'MEMBER':\n      return '确认扣款'\n    case 'WECHAT':\n    case 'ALIPAY':\n      return '确认支付'\n    case 'CARD':\n      return '确认刷卡'\n    default:\n      return '确认支付'\n  }\n}\n\n/**\n * 确认支付\n */\nconst confirmPayment = async () => {\n  if (!canConfirmPayment.value) {\n    message.warning('请完善支付信息')\n    return\n  }\n  \n  try {\n    let paymentData = {\n      paymentMethod: selectedPaymentMethod.value,\n      paymentAmount: orderSummary.value.finalAmount\n    }\n    \n    // 根据支付方式添加特定数据\n    switch (selectedPaymentMethod.value) {\n      case 'CASH':\n        paymentData.receivedAmount = cashAmount.value\n        break\n      case 'MEMBER':\n        paymentData.memberId = props.memberInfo?.memberId\n        break\n      case 'CARD':\n        cardStatus.value = 'processing'\n        break\n    }\n    \n    await processPayment(paymentData)\n    showPaymentResult.value = true\n    \n  } catch (error) {\n    console.error('支付失败:', error)\n    message.error('支付失败: ' + (error.message || '未知错误'))\n  }\n}\n\n/**\n * 取消支付\n */\nconst cancelPayment = () => {\n  cancelPaymentProcess()\n  emit('payment-cancel')\n  closeModal()\n}\n\n/**\n * 处理支付完成\n */\nconst handlePaymentComplete = (data) => {\n  emit('payment-success', data.paymentInfo)\n  closeModal()\n}\n\n/**\n * 处理支付重试\n */\nconst handlePaymentRetry = () => {\n  showPaymentResult.value = false\n  resetPaymentStatus()\n}\n\n/**\n * 处理支付取消（从结果页面）\n */\nconst handlePaymentCancel = () => {\n  emit('payment-cancel')\n  closeModal()\n}\n\n/**\n * 处理打印小票\n */\nconst handlePrintReceipt = (data) => {\n  // 这里可以调用打印服务\n  console.log('打印小票:', data.paymentInfo)\n  message.success('小票打印中...')\n}\n\n/**\n * 处理弹窗显示状态变化\n */\nconst handleVisibleChange = (visible) => {\n  emit('update:visible', visible)\n}\n\n/**\n * 关闭弹窗\n */\nconst closeModal = () => {\n  emit('update:visible', false)\n  \n  // 重置状态\n  setTimeout(() => {\n    resetPaymentStatus()\n    cashAmount.value = 0\n    cardStatus.value = ''\n    showPaymentResult.value = false\n  }, 300)\n}\n\n// ==================== 监听器 ====================\n\n// 监听弹窗显示状态\nwatch(\n  () => props.visible,\n  (visible) => {\n    if (visible) {\n      // 弹窗打开时重置状态\n      resetPaymentStatus()\n      showPaymentResult.value = false\n      cashAmount.value = orderSummary.value.finalAmount\n    }\n  }\n)\n\n// 组件卸载时清理\nonUnmounted(() => {\n  resetPaymentStatus()\n})\n</script>\n\n<style scoped>\n.payment-modal :deep(.ant-modal-content) {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.payment-modal :deep(.ant-modal-header) {\n  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n  border-bottom: none;\n  padding: 20px 24px;\n}\n\n.payment-modal :deep(.ant-modal-title) {\n  color: #fff;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.payment-panel {\n  padding: 0;\n}\n\n/* 订单汇总 */\n.order-summary {\n  padding: 20px 24px;\n  background: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.summary-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.summary-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.order-items-count {\n  font-size: 13px;\n  color: #8c8c8c;\n}\n\n.amount-details {\n  background: #fff;\n  border-radius: 8px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.amount-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.amount-row:last-child {\n  margin-bottom: 0;\n}\n\n.amount-row.total {\n  margin-top: 12px;\n  padding-top: 12px;\n  border-top: 1px solid #f0f0f0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.amount-row .label {\n  color: #595959;\n}\n\n.amount-row .value {\n  color: #262626;\n  font-weight: 500;\n}\n\n.amount-row .value.discount {\n  color: #52c41a;\n}\n\n.amount-row.total .value {\n  color: #ff4d4f;\n  font-size: 18px;\n}\n\n/* 支付内容区域 */\n.payment-content {\n  min-height: 200px;\n}\n\n/* 会员支付 */\n.member-payment {\n  padding: 20px 24px;\n  background: #f9f9f9;\n  border-top: 1px solid #f0f0f0;\n}\n\n.member-balance {\n  background: #fff;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.balance-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.balance-info .label {\n  font-size: 14px;\n  color: #595959;\n}\n\n.balance-info .value {\n  font-size: 16px;\n  font-weight: 600;\n  color: #52c41a;\n}\n\n/* 银行卡支付 */\n.card-payment {\n  padding: 20px 24px;\n  background: #f9f9f9;\n  border-top: 1px solid #f0f0f0;\n}\n\n.card-tips {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 8px;\n  margin-bottom: 16px;\n}\n\n.tip-icon {\n  font-size: 48px;\n  color: #1890ff;\n}\n\n.tip-content {\n  flex: 1;\n}\n\n.tip-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 4px;\n}\n\n.tip-desc {\n  font-size: 13px;\n  color: #8c8c8c;\n  line-height: 1.5;\n}\n\n.card-status {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  padding: 16px;\n  background: #e6f7ff;\n  border-radius: 6px;\n  border-left: 3px solid #1890ff;\n}\n\n.status-text {\n  font-size: 14px;\n  color: #1890ff;\n  font-weight: 500;\n}\n\n/* 操作按钮 */\n.payment-actions {\n  display: flex;\n  gap: 12px;\n  padding: 20px 24px;\n  background: #fff;\n  border-top: 1px solid #f0f0f0;\n}\n\n.cancel-btn {\n  flex: 1;\n  height: 44px;\n  font-size: 15px;\n}\n\n.confirm-btn {\n  flex: 2;\n  height: 44px;\n  font-size: 15px;\n  font-weight: 600;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .payment-modal :deep(.ant-modal-content) {\n    margin: 0;\n    max-width: 100vw;\n    max-height: 100vh;\n  }\n\n  .order-summary,\n  .member-payment,\n  .card-payment,\n  .payment-actions {\n    padding: 16px 20px;\n  }\n\n  .summary-header h4 {\n    font-size: 15px;\n  }\n\n  .amount-row {\n    font-size: 13px;\n  }\n\n  .amount-row.total {\n    font-size: 15px;\n  }\n\n  .amount-row.total .value {\n    font-size: 16px;\n  }\n\n  .card-tips {\n    flex-direction: column;\n    text-align: center;\n    gap: 12px;\n  }\n\n  .tip-icon {\n    font-size: 40px;\n  }\n\n  .payment-actions {\n    gap: 10px;\n  }\n\n  .cancel-btn,\n  .confirm-btn {\n    height: 40px;\n    font-size: 14px;\n  }\n}\n\n@media (max-width: 480px) {\n  .payment-modal :deep(.ant-modal-header) {\n    padding: 16px 20px;\n  }\n\n  .payment-modal :deep(.ant-modal-title) {\n    font-size: 16px;\n  }\n\n  .amount-details {\n    padding: 12px;\n  }\n\n  .payment-actions {\n    flex-direction: column;\n  }\n\n  .cancel-btn,\n  .confirm-btn {\n    flex: none;\n  }\n}\n\n/* 动画效果 */\n.payment-content {\n  transition: all 0.3s ease;\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .amount-details,\n  .member-balance,\n  .card-tips {\n    border: 2px solid #000;\n  }\n}\n</style>"