<template>
  <div class="member-panel pos-card">
    <!-- 会员面板标题 -->
    <div class="member-header">
      <h3 class="member-title">
        <icon-font iconClass="icon-member" />
        会员管理
      </h3>
      <a-button 
        v-if="currentMember" 
        type="text" 
        size="small" 
        @click="clearMember"
        class="clear-btn"
      >
        <template #icon>
          <close-outlined />
        </template>
        清除
      </a-button>
    </div>

    <!-- 会员搜索区域 -->
    <div class="member-content" v-if="!currentMember">
      <MemberSearch 
        ref="memberSearchRef"
        @memberFound="handleMemberFound"
        @searchError="handleSearchError"
      />
      
      <MemberSelector 
        ref="memberSelectorRef"
        @memberSelect="handleMemberSelect"
      />
    </div>

    <!-- 会员信息显示 -->
    <div class="member-content" v-if="currentMember">
      <MemberInfo 
        :member="currentMember"
      />
      
      <MemberDiscount 
        ref="memberDiscountRef"
        :member="currentMember"
        :discountRate="memberDiscountRate"
        :finalAmount="finalAmount"
        :pointsExchangeRate="pointsExchangeRate"
        @pointsDeductionChange="handlePointsDeductionChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { usePosStore } from '@/stores/pos'

// 导入子组件
import { MemberSearch, MemberSelector, MemberInfo, MemberDiscount } from './member'

// 导入样式文件
import '../styles/common.css'
import '../styles/member.css'

// 定义组件名称
defineOptions({
  name: 'MemberPanel'
})

// 定义事件
const emit = defineEmits(['memberChange', 'memberSelect', 'memberClear'])

// 组件引用
const memberSearchRef = ref(null)
const memberSelectorRef = ref(null)
const memberDiscountRef = ref(null)

// 使用POS状态管理
const posStore = usePosStore()

// 计算属性
const currentMember = computed(() => posStore.currentMember)
const memberDiscountRate = computed(() => posStore.memberDiscountRate)
const finalAmount = computed(() => posStore.finalAmount)
const pointsExchangeRate = computed(() => posStore.pointsExchangeRate || 100)

/**
 * 处理会员搜索找到结果
 */
const handleMemberFound = (member) => {
  selectMember(member)
  // 添加到最近使用会员
  if (memberSelectorRef.value) {
    memberSelectorRef.value.addToRecentMembers(member)
  }
}

/**
 * 处理会员搜索错误
 */
const handleSearchError = (error) => {
  console.error('会员搜索错误:', error)
}

/**
 * 处理会员选择
 */
const handleMemberSelect = (member) => {
  selectMember(member)
}

/**
 * 选择会员
 */
const selectMember = (member) => {
  posStore.setCurrentMember(member)
  
  // 重置搜索状态
  if (memberSearchRef.value) {
    memberSearchRef.value.resetSearch()
  }
  
  // 重置积分抵扣
  if (memberDiscountRef.value) {
    memberDiscountRef.value.resetPointsDeduction()
  }
  
  emit('memberChange', member)
  emit('memberSelect', member)
  
  console.log('选择会员:', member.memberName)
}

/**
 * 清除会员
 */
const clearMember = () => {
  posStore.clearCurrentMember()
  
  // 重置积分抵扣
  if (memberDiscountRef.value) {
    memberDiscountRef.value.resetPointsDeduction()
  }
  
  emit('memberChange', null)
  emit('memberClear')
  
  console.log('清除会员')
}

/**
 * 处理积分抵扣变化
 */
const handlePointsDeductionChange = ({ points, amount }) => {
  posStore.setPointsDeduction(points, amount)
}

// 监听会员变化，自动应用折扣
watch(
  () => currentMember.value,
  (newMember) => {
    if (newMember) {
      posStore.applyMemberDiscount(newMember.memberId)
    } else {
      posStore.clearMemberDiscount()
    }
  },
  { deep: true }
)

// 暴露方法给父组件
defineExpose({
  selectMember,
  clearMember
})
</script>

<style scoped>
.member-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.member-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.member-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-btn {
  color: #ff4d4f;
  transition: all 0.3s;
}

.clear-btn:hover:not(:disabled) {
  color: #ff7875;
  background: #fff2f0;
}

.member-content {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
}
</style>
