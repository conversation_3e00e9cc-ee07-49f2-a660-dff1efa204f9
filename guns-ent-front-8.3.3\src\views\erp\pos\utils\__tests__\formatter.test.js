/**
 * 格式化工具函数单元测试
 * 
 * 测试金额格式化、日期格式化、商品信息格式化等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect } from 'vitest'
import {
  AmountFormatter,
  DateFormatter,
  ProductFormatter,
  OrderFormatter,
  MemberFormatter,
  TextFormatter
} from '../formatter'

describe('AmountFormatter', () => {
  describe('formatCurrency', () => {
    it('应该正确格式化货币金额', () => {
      expect(AmountFormatter.formatCurrency(123.45)).toBe('¥123.45')
      expect(AmountFormatter.formatCurrency(1234.56)).toBe('¥1,234.56')
      expect(AmountFormatter.formatCurrency(1234567.89)).toBe('¥1,234,567.89')
    })
    
    it('应该处理无效输入', () => {
      expect(AmountFormatter.formatCurrency(null)).toBe('¥0.00')
      expect(AmountFormatter.formatCurrency(undefined)).toBe('¥0.00')
      expect(AmountFormatter.formatCurrency(NaN)).toBe('¥0.00')
    })
    
    it('应该支持自定义选项', () => {
      expect(AmountFormatter.formatCurrency(123.45, { currency: '$' })).toBe('$123.45')
      expect(AmountFormatter.formatCurrency(123.456, { precision: 3 })).toBe('¥123.456')
      expect(AmountFormatter.formatCurrency(123.45, { showSymbol: false })).toBe('123.45')
      expect(AmountFormatter.formatCurrency(1234.56, { showThousandsSeparator: false })).toBe('¥1234.56')
    })
  })
  
  describe('formatPercentage', () => {
    it('应该正确格式化百分比', () => {
      expect(AmountFormatter.formatPercentage(0.15)).toBe('15.00%')
      expect(AmountFormatter.formatPercentage(0.1234)).toBe('12.34%')
      expect(AmountFormatter.formatPercentage(1)).toBe('100.00%')
    })
    
    it('应该处理无效输入', () => {
      expect(AmountFormatter.formatPercentage(null)).toBe('0.00%')
      expect(AmountFormatter.formatPercentage(NaN)).toBe('0.00%')
    })
    
    it('应该支持自定义选项', () => {
      expect(AmountFormatter.formatPercentage(0.15, { precision: 1 })).toBe('15.0%')
      expect(AmountFormatter.formatPercentage(0.15, { showSymbol: false })).toBe('15.00')
    })
  })
  
  describe('formatQuantity', () => {
    it('应该正确格式化数量', () => {
      expect(AmountFormatter.formatQuantity(5)).toBe('5')
      expect(AmountFormatter.formatQuantity(5.500)).toBe('5.5')
      expect(AmountFormatter.formatQuantity(5.123)).toBe('5.123')
    })
    
    it('应该支持单位显示', () => {
      expect(AmountFormatter.formatQuantity(5, { unit: '件', showUnit: true })).toBe('5件')
      expect(AmountFormatter.formatQuantity(2.5, { unit: 'kg', showUnit: true })).toBe('2.5kg')
    })
  })
  
  describe('formatDiscountDisplay', () => {
    it('应该正确格式化折扣显示', () => {
      expect(AmountFormatter.formatDiscountDisplay(100, 10)).toBe('¥100.00 - ¥10.00 = ¥90.00')
      expect(AmountFormatter.formatDiscountDisplay(100, 0)).toBe('¥100.00')
    })
  })
  
  describe('formatChangeDisplay', () => {
    it('应该正确格式化找零显示', () => {
      const result = AmountFormatter.formatChangeDisplay(100, 85.5)
      expect(result).toBe('实收: ¥100.00 | 应付: ¥85.50 | 找零: ¥14.50')
    })
  })
})

describe('DateFormatter', () => {
  describe('formatDateTime', () => {
    it('应该正确格式化日期时间', () => {
      const date = new Date('2025-01-02 15:30:45')
      expect(DateFormatter.formatDateTime(date)).toBe('2025-01-02 15:30:45')
      expect(DateFormatter.formatDateTime(date, 'YYYY/MM/DD')).toBe('2025/01/02')
    })
    
    it('应该处理无效日期', () => {
      expect(DateFormatter.formatDateTime('invalid')).toBe('无效日期')
      expect(DateFormatter.formatDateTime(null)).toBe('无效日期')
    })
    
    it('应该处理不同输入类型', () => {
      const timestamp = new Date('2025-01-02 15:30:45').getTime()
      expect(DateFormatter.formatDateTime(timestamp)).toBe('2025-01-02 15:30:45')
      expect(DateFormatter.formatDateTime('2025-01-02 15:30:45')).toBe('2025-01-02 15:30:45')
    })
  })
  
  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2025-01-02 15:30:45')
      expect(DateFormatter.formatDate(date)).toBe('2025-01-02')
    })
  })
  
  describe('formatTime', () => {
    it('应该正确格式化时间', () => {
      const date = new Date('2025-01-02 15:30:45')
      expect(DateFormatter.formatTime(date)).toBe('15:30:45')
    })
  })
  
  describe('formatRelativeTime', () => {
    it('应该正确格式化相对时间', () => {
      const now = new Date()
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      
      expect(DateFormatter.formatRelativeTime(new Date(now.getTime() - 30 * 1000))).toBe('刚刚')
      expect(DateFormatter.formatRelativeTime(oneMinuteAgo)).toBe('1分钟前')
      expect(DateFormatter.formatRelativeTime(oneHourAgo)).toBe('1小时前')
      expect(DateFormatter.formatRelativeTime(oneDayAgo)).toBe('1天前')
    })
  })
  
  describe('formatDuration', () => {
    it('应该正确格式化时长', () => {
      expect(DateFormatter.formatDuration(3661)).toBe('01:01:01')
      expect(DateFormatter.formatDuration(125)).toBe('00:02:05')
      expect(DateFormatter.formatDuration(0)).toBe('00:00:00')
    })
    
    it('应该处理无效输入', () => {
      expect(DateFormatter.formatDuration(-1)).toBe('00:00:00')
      expect(DateFormatter.formatDuration('invalid')).toBe('00:00:00')
    })
  })
})

describe('ProductFormatter', () => {
  describe('formatProductName', () => {
    it('应该正确格式化商品名称', () => {
      const product = { name: '苹果', specifications: '500g' }
      expect(ProductFormatter.formatProductName(product)).toBe('苹果 (500g)')
      expect(ProductFormatter.formatProductName(product, { showSpecs: false })).toBe('苹果')
    })
    
    it('应该截断过长的名称', () => {
      const product = { name: '这是一个非常长的商品名称用于测试截断功能' }
      const result = ProductFormatter.formatProductName(product, { maxLength: 10 })
      expect(result).toBe('这是一个非常长的商...')
    })
    
    it('应该处理无效输入', () => {
      expect(ProductFormatter.formatProductName(null)).toBe('未知商品')
      expect(ProductFormatter.formatProductName({})).toBe('未知商品')
    })
  })
  
  describe('formatProductPrice', () => {
    it('应该正确格式化商品价格', () => {
      const product = { price: 12.5, unit: 'kg' }
      expect(ProductFormatter.formatProductPrice(product)).toBe('¥12.50/kg')
    })
    
    it('应该处理无单位的商品', () => {
      const product = { price: 10 }
      expect(ProductFormatter.formatProductPrice(product)).toBe('¥10.00')
    })
  })
  
  describe('formatProductStock', () => {
    it('应该正确格式化库存信息', () => {
      const product = { stock: 100, unit: '件', safetyStock: 10 }
      expect(ProductFormatter.formatProductStock(product)).toBe('库存: 100件')
    })
    
    it('应该显示库存状态', () => {
      const outOfStock = { stock: 0, unit: '件' }
      const lowStock = { stock: 5, unit: '件', safetyStock: 10 }
      
      expect(ProductFormatter.formatProductStock(outOfStock)).toBe('库存: 0件 (缺货)')
      expect(ProductFormatter.formatProductStock(lowStock)).toBe('库存: 5件 (预警)')
    })
  })
  
  describe('formatBarcode', () => {
    it('应该正确格式化条码', () => {
      expect(ProductFormatter.formatBarcode('1234567890123')).toBe('条码: 1234 5678 9012 3')
      expect(ProductFormatter.formatBarcode('1234567890123', { showPrefix: false })).toBe('1234 5678 9012 3')
    })
    
    it('应该处理空条码', () => {
      expect(ProductFormatter.formatBarcode('')).toBe('条码: 无')
      expect(ProductFormatter.formatBarcode(null)).toBe('条码: 无')
    })
  })
  
  describe('formatCategoryPath', () => {
    it('应该正确格式化分类路径', () => {
      const categoryPath = [
        { name: '食品' },
        { name: '水果' },
        { name: '苹果' }
      ]
      expect(ProductFormatter.formatCategoryPath(categoryPath)).toBe('食品 > 水果 > 苹果')
    })
    
    it('应该处理空分类路径', () => {
      expect(ProductFormatter.formatCategoryPath([])).toBe('未分类')
      expect(ProductFormatter.formatCategoryPath(null)).toBe('未分类')
    })
  })
})

describe('OrderFormatter', () => {
  describe('formatOrderNo', () => {
    it('应该正确格式化订单号', () => {
      expect(OrderFormatter.formatOrderNo('POS20250102001')).toBe('订单号: POS20250102001')
      expect(OrderFormatter.formatOrderNo('POS20250102001', { showPrefix: false })).toBe('POS20250102001')
    })
    
    it('应该处理空订单号', () => {
      expect(OrderFormatter.formatOrderNo('')).toBe('订单号: 无')
      expect(OrderFormatter.formatOrderNo(null)).toBe('订单号: 无')
    })
  })
  
  describe('formatOrderStatus', () => {
    it('应该正确格式化订单状态', () => {
      const statusOptions = [
        { label: '已支付', value: 'PAID', color: 'green' }
      ]
      const result = OrderFormatter.formatOrderStatus('PAID', statusOptions)
      expect(result).toEqual({
        label: '已支付',
        color: 'green',
        value: 'PAID'
      })
    })
    
    it('应该处理未知状态', () => {
      const result = OrderFormatter.formatOrderStatus('UNKNOWN', [])
      expect(result).toEqual({
        label: 'UNKNOWN',
        color: 'default',
        value: 'UNKNOWN'
      })
    })
  })
  
  describe('formatOrderSummary', () => {
    it('应该正确格式化订单摘要', () => {
      const order = {
        orderNo: 'POS20250102001',
        items: [{ id: 1 }, { id: 2 }],
        finalAmount: 100.5,
        createdAt: '2025-01-02 15:30:45'
      }
      const result = OrderFormatter.formatOrderSummary(order)
      expect(result).toBe('POS20250102001 | 2件商品 | ¥100.50 | 2025-01-02 15:30:45')
    })
    
    it('应该处理无效订单', () => {
      expect(OrderFormatter.formatOrderSummary(null)).toBe('无效订单')
    })
  })
})

describe('MemberFormatter', () => {
  describe('formatMemberCardNo', () => {
    it('应该正确格式化会员卡号', () => {
      expect(MemberFormatter.formatMemberCardNo('1234567890')).toBe('卡号: 1234567890')
      expect(MemberFormatter.formatMemberCardNo('1234567890', { showPrefix: false })).toBe('1234567890')
    })
    
    it('应该支持遮罩显示', () => {
      expect(MemberFormatter.formatMemberCardNo('1234567890', { maskMiddle: true })).toBe('卡号: 123****890')
    })
    
    it('应该处理空卡号', () => {
      expect(MemberFormatter.formatMemberCardNo('')).toBe('卡号: 无')
    })
  })
  
  describe('formatMemberPhone', () => {
    it('应该正确格式化手机号', () => {
      expect(MemberFormatter.formatMemberPhone('13812345678')).toBe('手机: 138****5678')
      expect(MemberFormatter.formatMemberPhone('13812345678', { maskMiddle: false })).toBe('手机: 13812345678')
    })
  })
  
  describe('formatMemberLevel', () => {
    it('应该正确格式化会员等级', () => {
      const levelOptions = [
        { label: '金牌会员', value: 'GOLD', color: '#FFD700' }
      ]
      const result = MemberFormatter.formatMemberLevel('GOLD', levelOptions)
      expect(result).toEqual({
        label: '金牌会员',
        color: '#FFD700',
        value: 'GOLD'
      })
    })
  })
  
  describe('formatMemberPoints', () => {
    it('应该正确格式化积分', () => {
      expect(MemberFormatter.formatMemberPoints(12345)).toBe('积分: 12,345')
      expect(MemberFormatter.formatMemberPoints(12345, { showThousandsSeparator: false })).toBe('积分: 12345')
    })
    
    it('应该处理无效积分', () => {
      expect(MemberFormatter.formatMemberPoints(null)).toBe('积分: 0')
      expect(MemberFormatter.formatMemberPoints(NaN)).toBe('积分: 0')
    })
  })
  
  describe('formatMemberBalance', () => {
    it('应该正确格式化余额', () => {
      expect(MemberFormatter.formatMemberBalance(123.45)).toBe('余额: ¥123.45')
      expect(MemberFormatter.formatMemberBalance(null)).toBe('余额: ¥0.00')
    })
  })
})

describe('TextFormatter', () => {
  describe('truncate', () => {
    it('应该正确截断文本', () => {
      expect(TextFormatter.truncate('这是一个很长的文本', 5)).toBe('这是...')
      expect(TextFormatter.truncate('短文本', 10)).toBe('短文本')
    })
    
    it('应该支持自定义后缀', () => {
      expect(TextFormatter.truncate('这是一个很长的文本', 5, '…')).toBe('这是一个…')
    })
    
    it('应该处理无效输入', () => {
      expect(TextFormatter.truncate(null, 5)).toBe('')
      expect(TextFormatter.truncate(123, 5)).toBe('')
    })
  })
  
  describe('capitalize', () => {
    it('应该正确首字母大写', () => {
      expect(TextFormatter.capitalize('hello')).toBe('Hello')
      expect(TextFormatter.capitalize('WORLD')).toBe('World')
    })
    
    it('应该处理无效输入', () => {
      expect(TextFormatter.capitalize('')).toBe('')
      expect(TextFormatter.capitalize(null)).toBe('')
    })
  })
  
  describe('toCamelCase', () => {
    it('应该正确转换为驼峰命名', () => {
      expect(TextFormatter.toCamelCase('hello-world')).toBe('helloWorld')
      expect(TextFormatter.toCamelCase('my-long-variable-name')).toBe('myLongVariableName')
    })
    
    it('应该支持自定义分隔符', () => {
      expect(TextFormatter.toCamelCase('hello_world', '_')).toBe('helloWorld')
    })
  })
  
  describe('formatFileSize', () => {
    it('应该正确格式化文件大小', () => {
      expect(TextFormatter.formatFileSize(0)).toBe('0 Bytes')
      expect(TextFormatter.formatFileSize(1024)).toBe('1 KB')
      expect(TextFormatter.formatFileSize(1048576)).toBe('1 MB')
      expect(TextFormatter.formatFileSize(1073741824)).toBe('1 GB')
    })
    
    it('应该支持自定义小数位数', () => {
      expect(TextFormatter.formatFileSize(1536, 1)).toBe('1.5 KB')
      expect(TextFormatter.formatFileSize(1536, 0)).toBe('2 KB')
    })
  })
})