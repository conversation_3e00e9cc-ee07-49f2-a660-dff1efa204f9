<template>
  <!-- 抽屉模式 -->
  <a-drawer
    v-if="visible !== undefined"
    v-model:open="drawerVisible"
    title="挂单管理"
    placement="right"
    width="500"
    class="suspend-drawer"
  >
    <div class="order-suspend drawer-content pos-card">
      <!-- 挂单面板标题 -->
      <div class="suspend-header">
        <h3 class="suspend-title">
          <icon-font iconClass="icon-suspend" />
          挂单管理
        </h3>
        <div class="suspend-actions">
          <a-button 
            type="primary" 
            :disabled="!canSuspend"
            @click="suspendCurrentOrder"
            :loading="suspending"
            class="suspend-btn"
          >
            <template #icon>
              <pause-outlined />
            </template>
            挂起当前订单
          </a-button>
          <a-button 
            v-if="hasSuspendedOrders"
            type="text" 
            @click="clearExpiredOrders"
            class="clear-btn"
          >
            <template #icon>
              <delete-outlined />
            </template>
            清理过期
          </a-button>
        </div>
      </div>

      <!-- 当前订单信息 -->
      <div class="current-order" v-if="hasCartItems">
        <OrderSummary
          :itemCount="cartItemCount"
          :amount="totalAmount"
          :member="currentMember"
        />
      </div>

      <!-- 挂起订单列表 -->
      <div class="suspended-orders">
        <div class="orders-header">
          <span class="orders-title">挂起订单 ({{ suspendedOrders.length }})</span>
          <a-button 
            v-if="suspendedOrders.length > 0"
            type="text" 
            size="small"
            @click="refreshSuspendedOrders"
            :loading="refreshing"
          >
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
        </div>

        <!-- 订单列表 -->
        <div class="orders-list" v-if="suspendedOrders.length > 0">
          <SuspendedOrderItem
            v-for="order in suspendedOrders"
            :key="order.suspendId"
            :order="order"
            @resume="resumeOrder"
            @delete="deleteOrder"
          />
        </div>

        <!-- 空状态 -->
        <div class="empty-orders" v-else>
          <a-empty 
            description="暂无挂起订单"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          >
            <p class="empty-tip">当前没有挂起的订单</p>
          </a-empty>
        </div>
      </div>

      <!-- 确认对话框 -->
      <a-modal
        v-model:open="showConfirmModal"
        :title="confirmModalTitle"
        :ok-text="confirmModalOkText"
        cancel-text="取消"
        @ok="handleConfirmOk"
        @cancel="handleConfirmCancel"
        :confirm-loading="confirmLoading"
      >
        <p>{{ confirmModalContent }}</p>
      </a-modal>
    </div>
  </a-drawer>

  <!-- 普通模式 -->
  <div v-else class="order-suspend pos-card">
    <!-- 这里可以放置普通模式的内容，如果需要的话 -->
    <div class="suspend-placeholder">
      <a-empty description="请点击挂单列表按钮查看挂单" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message, Modal, Empty } from 'ant-design-vue'
import { 
  PauseOutlined, 
  DeleteOutlined, 
  ReloadOutlined
} from '@ant-design/icons-vue'
import { usePosStore } from '@/stores/pos'
import OrderSummary from './common/OrderSummary.vue'
import SuspendedOrderItem from './common/SuspendedOrderItem.vue'

// 导入样式文件
import '../styles/common.css'

// 定义组件名称
defineOptions({
  name: 'OrderSuspend'
})

// 定义Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: undefined
  }
})

// 定义事件
const emit = defineEmits(['orderSuspended', 'orderResumed', 'orderDeleted', 'update:visible'])

// 抽屉可见性控制
const drawerVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 响应式数据
const suspending = ref(false)
const refreshing = ref(false)
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalContent = ref('')
const confirmModalOkText = ref('确定')
const confirmLoading = ref(false)
const pendingAction = ref(null)

// 使用POS状态管理
const posStore = usePosStore()

// 计算属性
const cartItemCount = computed(() => posStore.cartItemCount)
const totalAmount = computed(() => posStore.totalAmount)
const hasCartItems = computed(() => posStore.hasCartItems)
const hasMember = computed(() => posStore.hasMember)
const currentMember = computed(() => posStore.currentMember)
const suspendedOrders = computed(() => posStore.suspendedOrders)
const hasSuspendedOrders = computed(() => posStore.hasSuspendedOrders)

// 是否可以挂单
const canSuspend = computed(() => {
  return hasCartItems.value && !suspending.value
})

/* 格式化方法已移至子组件 */

/**
 * 挂起当前订单
 */
const suspendCurrentOrder = async () => {
  if (!canSuspend.value) {
    message.warning('当前没有可挂起的订单')
    return
  }

  try {
    suspending.value = true
    
    const success = await posStore.suspendCurrentOrder()
    if (success) {
      emit('orderSuspended')
      message.success('订单已挂起')
    }
  } catch (error) {
    console.error('挂起订单失败:', error)
    message.error('挂起订单失败，请重试')
  } finally {
    suspending.value = false
  }
}

/**
 * 恢复挂起的订单
 */
const resumeOrder = (suspendId) => {
  confirmModalTitle.value = '恢复订单'
  confirmModalContent.value = '恢复此订单将清空当前购物车，是否继续？'
  confirmModalOkText.value = '恢复订单'
  pendingAction.value = { type: 'resume', suspendId }
  showConfirmModal.value = true
}

/**
 * 删除挂起的订单
 */
const deleteOrder = (suspendId) => {
  confirmModalTitle.value = '删除订单'
  confirmModalContent.value = '确定要删除这个挂起的订单吗？此操作不可恢复。'
  confirmModalOkText.value = '删除'
  pendingAction.value = { type: 'delete', suspendId }
  showConfirmModal.value = true
}

/**
 * 清理过期订单
 */
const clearExpiredOrders = () => {
  confirmModalTitle.value = '清理过期订单'
  confirmModalContent.value = '确定要清理所有过期的挂起订单吗？'
  confirmModalOkText.value = '清理'
  pendingAction.value = { type: 'clearExpired' }
  showConfirmModal.value = true
}

/**
 * 刷新挂起订单列表
 */
const refreshSuspendedOrders = async () => {
  try {
    refreshing.value = true
    // 这里可以调用API刷新挂起订单列表
    // 目前从本地存储加载
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟API调用
    message.success('刷新完成')
  } catch (error) {
    console.error('刷新挂起订单失败:', error)
    message.error('刷新失败，请重试')
  } finally {
    refreshing.value = false
  }
}

/**
 * 处理确认对话框确定按钮
 */
const handleConfirmOk = async () => {
  if (!pendingAction.value) return

  try {
    confirmLoading.value = true
    
    const { type, suspendId } = pendingAction.value
    
    switch (type) {
      case 'resume':
        await posStore.resumeSuspendedOrder(suspendId)
        emit('orderResumed', suspendId)
        message.success('订单已恢复')
        break
        
      case 'delete':
        await posStore.deleteSuspendedOrder(suspendId)
        emit('orderDeleted', suspendId)
        message.success('订单已删除')
        break
        
      case 'clearExpired':
        await posStore.clearExpiredSuspendedOrders()
        message.success('过期订单已清理')
        break
    }
    
    showConfirmModal.value = false
  } catch (error) {
    console.error('操作失败:', error)
    message.error('操作失败，请重试')
  } finally {
    confirmLoading.value = false
    pendingAction.value = null
  }
}

/**
 * 处理确认对话框取消按钮
 */
const handleConfirmCancel = () => {
  showConfirmModal.value = false
  pendingAction.value = null
}

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里加载挂起订单数据
})

// 暴露方法给父组件
defineExpose({
  suspendCurrentOrder,
  resumeOrder,
  deleteOrder,
  refreshSuspendedOrders
})
</script>

<style scoped>
.order-suspend {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.suspend-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.suspend-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.suspend-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.suspend-btn {
  background: #722ed1;
  border-color: #722ed1;
}

.suspend-btn:hover:not(:disabled) {
  background: #9254de;
  border-color: #9254de;
}

.clear-btn {
  color: #ff4d4f;
}

.clear-btn:hover:not(:disabled) {
  color: #ff7875;
  background: #fff2f0;
}

.current-order {
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #f0f0f0;
}

.suspended-orders {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.orders-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 8px;
}

.orders-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.orders-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px 20px;
}

/* 订单项样式已移至SuspendedOrderItem组件 */

.empty-orders {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-tip {
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

/* 抽屉模式样式 */
.suspend-drawer .drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.suspend-drawer .order-suspend {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.suspend-drawer .suspended-orders {
  flex: 1;
  overflow-y: auto;
}

/* 普通模式占位符 */
.suspend-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}
</style>
