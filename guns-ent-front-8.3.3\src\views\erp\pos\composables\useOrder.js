/**
 * 订单业务逻辑组合式函数
 * 
 * 提供订单相关的业务逻辑处理，包括挂单、恢复等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { usePosStore } from '@/stores/pos'

export function useOrder() {
  const posStore = usePosStore()
  
  /**
   * 挂起订单
   */
  const suspendOrder = async (orderData) => {
    try {
      const success = await posStore.suspendCurrentOrder(orderData)
      return success
    } catch (error) {
      console.error('挂起订单失败:', error)
      throw error
    }
  }
  
  /**
   * 恢复订单
   */
  const resumeOrder = async (suspendId) => {
    try {
      const orderData = await posStore.resumeSuspendedOrder(suspendId)
      return orderData
    } catch (error) {
      console.error('恢复订单失败:', error)
      throw error
    }
  }
  
  /**
   * 删除挂起的订单
   */
  const deleteOrder = async (suspendId) => {
    try {
      await posStore.deleteSuspendedOrder(suspendId)
      return true
    } catch (error) {
      console.error('删除订单失败:', error)
      throw error
    }
  }
  
  /**
   * 创建订单
   */
  const createOrder = async (orderData) => {
    try {
      const order = await posStore.createOrder(orderData)
      return order
    } catch (error) {
      console.error('创建订单失败:', error)
      throw error
    }
  }
  
  /**
   * 获取订单列表
   */
  const getOrderList = async (params = {}) => {
    try {
      const orders = await posStore.getOrderList(params)
      return orders
    } catch (error) {
      console.error('获取订单列表失败:', error)
      throw error
    }
  }
  
  return {
    suspendOrder,
    resumeOrder,
    deleteOrder,
    createOrder,
    getOrderList
  }
}