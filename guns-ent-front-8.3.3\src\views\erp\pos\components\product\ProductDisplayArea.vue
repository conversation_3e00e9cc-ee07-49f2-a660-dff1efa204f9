<!--\n  商品显示区域主组件\n  \n  重构后的商品显示区域，拆分为多个子组件，职责更加清晰\n  使用Composables管理业务逻辑，组件专注于UI渲染\n  \n  <AUTHOR>  @since 2025/01/02\n-->\n<template>\n  <div class=\"product-display-area\">\n    <!-- 商品分类导航 -->\n    <div class=\"category-section\">\n      <ProductCategory\n        :categories=\"categories\"\n        :selected-category=\"selectedCategoryId\"\n        :all-products-count=\"totalProductsCount\"\n        :loading=\"categoriesLoading\"\n        :show-category-icons=\"true\"\n        :show-scroll-indicators=\"true\"\n        :show-manage-button=\"true\"\n        @category-change=\"handleCategoryChange\"\n        @refresh-categories=\"handleRefreshCategories\"\n        @manage-categories=\"handleManageCategories\"\n        @add-category=\"handleAddCategory\"\n      />\n    </div>\n\n    <!-- 商品搜索和过滤 -->\n    <div class=\"search-section\">\n      <ProductSearch\n        :initial-keyword=\"searchKeyword\"\n        :show-search-button=\"false\"\n        :show-filters=\"true\"\n        :show-stats=\"true\"\n        :show-history=\"true\"\n        :total-results=\"totalProductsCount\"\n        :loading=\"productsLoading\"\n        @search=\"handleSearch\"\n        @filter-change=\"handleFilterChange\"\n        @sort-change=\"handleSortChange\"\n        @reset=\"handleResetFilters\"\n        @clear-all=\"handleClearAllFilters\"\n      />\n    </div>\n\n    <!-- 商品网格展示 -->\n    <div class=\"grid-section\">\n      <ProductGrid\n        :category-id=\"selectedCategoryId\"\n        :search-keyword=\"searchKeyword\"\n        :price-filter=\"priceFilter\"\n        :stock-filter=\"stockFilter\"\n        :sort-by=\"sortBy\"\n        :show-toolbar=\"true\"\n        :container-height=\"gridContainerHeight\"\n        :page-size=\"pageSize\"\n        @product-click=\"handleProductClick\"\n        @product-add=\"handleProductAdd\"\n        @product-select=\"handleProductSelect\"\n        @view-mode-change=\"handleViewModeChange\"\n        @clear-filters=\"handleClearAllFilters\"\n      />\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport { message } from 'ant-design-vue'\nimport ProductCategory from './ProductCategory.vue'\nimport ProductSearch from './ProductSearch.vue'\nimport ProductGrid from './ProductGrid.vue'\nimport { useProductCategory } from '../../composables/useProductCategory'\nimport { useProductFilter } from '../../composables/useProductFilter'\n\n// 定义组件名称\ndefineOptions({\n  name: 'ProductDisplayArea'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 容器高度\n  containerHeight: {\n    type: Number,\n    default: 800\n  },\n  // 每页商品数量\n  pageSize: {\n    type: Number,\n    default: 50\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'product-add',\n  'product-select',\n  'category-change',\n  'search-change'\n])\n\n// 使用商品分类业务逻辑\nconst {\n  categories,\n  categoriesLoading,\n  totalProductsCount,\n  loadCategories,\n  refreshCategories\n} = useProductCategory()\n\n// 使用商品过滤业务逻辑\nconst {\n  selectedCategoryId,\n  searchKeyword,\n  priceFilter,\n  stockFilter,\n  sortBy,\n  productsLoading,\n  setCategory,\n  setSearch,\n  setFilters,\n  resetFilters,\n  clearAllFilters\n} = useProductFilter()\n\n// 响应式状态\nconst viewMode = ref('grid')\nconst resizeObserver = ref(null)\n\n// ==================== 计算属性 ====================\n\n/**\n * 网格容器高度\n */\nconst gridContainerHeight = computed(() => {\n  // 减去分类导航和搜索区域的高度\n  return props.containerHeight - 180\n})\n\n// ==================== 方法 ====================\n\n/**\n * 处理分类变化\n * @param {Object} data - 分类数据\n */\nconst handleCategoryChange = (data) => {\n  setCategory(data.categoryId)\n  \n  emit('category-change', {\n    categoryId: data.categoryId,\n    categoryInfo: data.categoryInfo\n  })\n}\n\n/**\n * 处理搜索\n * @param {Object} data - 搜索数据\n */\nconst handleSearch = (data) => {\n  setSearch(data.keyword)\n  setFilters({\n    priceFilter: data.priceFilter,\n    stockFilter: data.stockFilter,\n    sortBy: data.sortBy\n  })\n  \n  emit('search-change', data)\n}\n\n/**\n * 处理过滤条件变化\n * @param {Object} data - 过滤数据\n */\nconst handleFilterChange = (data) => {\n  setFilters({\n    [data.type + 'Filter']: data.value\n  })\n}\n\n/**\n * 处理排序变化\n * @param {Object} data - 排序数据\n */\nconst handleSortChange = (data) => {\n  setFilters({\n    sortBy: data.sortBy\n  })\n}\n\n/**\n * 重置过滤条件\n */\nconst handleResetFilters = () => {\n  resetFilters()\n  message.success('已重置所有过滤条件')\n}\n\n/**\n * 清除所有过滤条件\n */\nconst handleClearAllFilters = () => {\n  clearAllFilters()\n  message.success('已清除所有条件')\n}\n\n/**\n * 处理商品点击\n * @param {Object} product - 商品信息\n */\nconst handleProductClick = (product) => {\n  // 可以在这里添加商品点击的额外逻辑\n  console.log('商品点击:', product)\n}\n\n/**\n * 处理商品添加到购物车\n * @param {Object} product - 商品信息\n */\nconst handleProductAdd = (product) => {\n  emit('product-add', product)\n}\n\n/**\n * 处理商品选择\n * @param {Object} data - 选择数据\n */\nconst handleProductSelect = (data) => {\n  emit('product-select', data)\n}\n\n/**\n * 处理视图模式变化\n * @param {string} mode - 视图模式\n */\nconst handleViewModeChange = (mode) => {\n  viewMode.value = mode\n  \n  // 保存用户偏好\n  try {\n    localStorage.setItem('pos_view_mode', mode)\n  } catch (error) {\n    console.warn('保存视图模式偏好失败:', error)\n  }\n}\n\n/**\n * 刷新分类列表\n */\nconst handleRefreshCategories = async () => {\n  try {\n    await refreshCategories()\n    message.success('分类列表已刷新')\n  } catch (error) {\n    console.error('刷新分类失败:', error)\n    message.error('刷新分类失败')\n  }\n}\n\n/**\n * 处理分类管理\n */\nconst handleManageCategories = () => {\n  // 这里可以打开分类管理弹窗或跳转到分类管理页面\n  message.info('分类管理功能开发中...')\n}\n\n/**\n * 处理添加分类\n */\nconst handleAddCategory = () => {\n  // 这里可以打开添加分类弹窗\n  message.info('添加分类功能开发中...')\n}\n\n/**\n * 处理容器大小变化\n */\nconst handleResize = () => {\n  // 可以在这里处理响应式布局调整\n}\n\n/**\n * 初始化组件\n */\nconst initializeComponent = async () => {\n  try {\n    // 加载分类列表\n    await loadCategories()\n    \n    // 加载用户偏好设置\n    const savedViewMode = localStorage.getItem('pos_view_mode')\n    if (savedViewMode) {\n      viewMode.value = savedViewMode\n    }\n    \n  } catch (error) {\n    console.error('初始化商品显示区域失败:', error)\n    message.error('初始化失败，请刷新页面重试')\n  }\n}\n\n// ==================== 生命周期 ====================\n\nonMounted(() => {\n  initializeComponent()\n  \n  // 设置容器大小监听\n  if (window.ResizeObserver) {\n    resizeObserver.value = new ResizeObserver(handleResize)\n    const container = document.querySelector('.product-display-area')\n    if (container) {\n      resizeObserver.value.observe(container)\n    }\n  }\n})\n\nonUnmounted(() => {\n  // 清理资源\n  if (resizeObserver.value) {\n    resizeObserver.value.disconnect()\n  }\n})\n\n// ==================== 监听器 ====================\n\n// 监听容器高度变化\nwatch(\n  () => props.containerHeight,\n  () => {\n    // 可以在这里处理高度变化的逻辑\n  }\n)\n\n// 暴露方法给父组件\ndefineExpose({\n  refreshCategories: handleRefreshCategories,\n  resetFilters: handleResetFilters,\n  clearAllFilters: handleClearAllFilters,\n  setCategory: (categoryId) => {\n    setCategory(categoryId)\n  },\n  setSearch: (keyword) => {\n    setSearch(keyword)\n  }\n})\n</script>\n\n<style scoped>\n.product-display-area {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n/* 分类区域 */\n.category-section {\n  flex-shrink: 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 搜索区域 */\n.search-section {\n  flex-shrink: 0;\n  padding: 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n/* 网格区域 */\n.grid-section {\n  flex: 1;\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .search-section {\n    padding: 12px;\n  }\n}\n\n@media (max-width: 768px) {\n  .product-display-area {\n    border-radius: 0;\n    box-shadow: none;\n  }\n  \n  .search-section {\n    padding: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .search-section {\n    padding: 8px 4px;\n  }\n}\n\n/* 加载状态 */\n.product-display-area.loading {\n  pointer-events: none;\n  opacity: 0.7;\n}\n\n/* 动画效果 */\n.category-section,\n.search-section,\n.grid-section {\n  transition: all 0.3s ease;\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .product-display-area {\n    border: 2px solid #000;\n  }\n  \n  .category-section,\n  .search-section {\n    border-bottom: 2px solid #000;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .category-section,\n  .search-section,\n  .grid-section {\n    transition: none;\n  }\n}\n\n/* 打印样式 */\n@media print {\n  .product-display-area {\n    box-shadow: none;\n    border: 1px solid #000;\n  }\n  \n  .search-section {\n    display: none;\n  }\n}\n</style>"