<!--\n  商品分类组件\n  \n  显示商品分类导航，支持水平滚动和分类切换\n  \n  <AUTHOR>  @since 2025/01/02\n-->\n<template>\n  <div class=\"product-category\">\n    <!-- 分类标签页 -->\n    <div class=\"category-tabs-wrapper\">\n      <a-tabs\n        v-model:activeKey=\"selectedCategoryKey\"\n        type=\"card\"\n        size=\"small\"\n        @tab-click=\"handleCategoryClick\"\n        class=\"pos-category-tabs\"\n        :loading=\"loading\"\n      >\n        <!-- 全部商品 -->\n        <a-tab-pane key=\"all\" tab=\"全部商品\">\n          <template #tab>\n            <span class=\"tab-content\">\n              <icon-font iconClass=\"icon-all\" />\n              <span>全部商品</span>\n              <a-badge \n                v-if=\"allProductsCount > 0\" \n                :count=\"allProductsCount\" \n                :number-style=\"{ backgroundColor: '#52c41a', fontSize: '10px' }\"\n                class=\"tab-badge\"\n              />\n            </span>\n          </template>\n        </a-tab-pane>\n        \n        <!-- 分类标签 -->\n        <a-tab-pane\n          v-for=\"category in categories\"\n          :key=\"category.categoryId\"\n          :tab=\"category.categoryName\"\n        >\n          <template #tab>\n            <span class=\"tab-content\">\n              <icon-font \n                :iconClass=\"category.icon || 'icon-category'\" \n                v-if=\"showCategoryIcons\"\n              />\n              <span>{{ category.categoryName }}</span>\n              <a-badge \n                v-if=\"category.productCount > 0\" \n                :count=\"category.productCount\" \n                :number-style=\"{ backgroundColor: '#1890ff', fontSize: '10px' }\"\n                class=\"tab-badge\"\n              />\n            </span>\n          </template>\n        </a-tab-pane>\n      </a-tabs>\n      \n      <!-- 滚动指示器 -->\n      <div class=\"scroll-indicators\" v-if=\"showScrollIndicators\">\n        <a-button \n          type=\"text\" \n          size=\"small\"\n          @click=\"scrollLeft\"\n          :disabled=\"!canScrollLeft\"\n          class=\"scroll-btn left\"\n        >\n          <template #icon>\n            <left-outlined />\n          </template>\n        </a-button>\n        \n        <a-button \n          type=\"text\" \n          size=\"small\"\n          @click=\"scrollRight\"\n          :disabled=\"!canScrollRight\"\n          class=\"scroll-btn right\"\n        >\n          <template #icon>\n            <right-outlined />\n          </template>\n        </a-button>\n      </div>\n    </div>\n    \n    <!-- 分类管理按钮 -->\n    <div class=\"category-actions\" v-if=\"showManageButton\">\n      <a-dropdown :trigger=\"['click']\" placement=\"bottomRight\">\n        <a-button type=\"text\" size=\"small\" class=\"manage-btn\">\n          <template #icon>\n            <more-outlined />\n          </template>\n        </a-button>\n        \n        <template #overlay>\n          <a-menu @click=\"handleMenuClick\">\n            <a-menu-item key=\"refresh\">\n              <icon-font iconClass=\"icon-refresh\" />\n              刷新分类\n            </a-menu-item>\n            <a-menu-item key=\"manage\">\n              <icon-font iconClass=\"icon-setting\" />\n              管理分类\n            </a-menu-item>\n            <a-menu-divider />\n            <a-menu-item key=\"add\">\n              <icon-font iconClass=\"icon-plus\" />\n              添加分类\n            </a-menu-item>\n          </a-menu>\n        </template>\n      </a-dropdown>\n    </div>\n    \n    <!-- 分类信息面板 -->\n    <div class=\"category-info\" v-if=\"showCategoryInfo && selectedCategoryInfo\">\n      <div class=\"info-content\">\n        <div class=\"info-item\">\n          <span class=\"info-label\">当前分类:</span>\n          <span class=\"info-value\">{{ selectedCategoryInfo.name }}</span>\n        </div>\n        \n        <div class=\"info-item\" v-if=\"selectedCategoryInfo.description\">\n          <span class=\"info-label\">分类描述:</span>\n          <span class=\"info-value\">{{ selectedCategoryInfo.description }}</span>\n        </div>\n        \n        <div class=\"info-item\">\n          <span class=\"info-label\">商品数量:</span>\n          <span class=\"info-value\">{{ selectedCategoryInfo.productCount }} 个</span>\n        </div>\n      </div>\n      \n      <a-button \n        type=\"text\" \n        size=\"small\"\n        @click=\"hideCategoryInfo\"\n        class=\"close-info-btn\"\n      >\n        <template #icon>\n          <close-outlined />\n        </template>\n      </a-button>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'\nimport { message } from 'ant-design-vue'\nimport {\n  LeftOutlined,\n  RightOutlined,\n  MoreOutlined,\n  CloseOutlined\n} from '@ant-design/icons-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\n\n// 定义组件名称\ndefineOptions({\n  name: 'ProductCategory'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 分类列表\n  categories: {\n    type: Array,\n    default: () => []\n  },\n  // 当前选中的分类ID\n  selectedCategory: {\n    type: String,\n    default: 'all'\n  },\n  // 全部商品数量\n  allProductsCount: {\n    type: Number,\n    default: 0\n  },\n  // 加载状态\n  loading: {\n    type: Boolean,\n    default: false\n  },\n  // 是否显示分类图标\n  showCategoryIcons: {\n    type: Boolean,\n    default: true\n  },\n  // 是否显示滚动指示器\n  showScrollIndicators: {\n    type: Boolean,\n    default: true\n  },\n  // 是否显示管理按钮\n  showManageButton: {\n    type: Boolean,\n    default: true\n  },\n  // 是否显示分类信息\n  showCategoryInfo: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'category-change',\n  'category-click',\n  'refresh-categories',\n  'manage-categories',\n  'add-category'\n])\n\n// 响应式状态\nconst selectedCategoryKey = ref(props.selectedCategory)\nconst canScrollLeft = ref(false)\nconst canScrollRight = ref(false)\nconst tabsRef = ref(null)\nconst showInfoPanel = ref(false)\n\n// ==================== 计算属性 ====================\n\n/**\n * 当前选中分类的详细信息\n */\nconst selectedCategoryInfo = computed(() => {\n  if (selectedCategoryKey.value === 'all') {\n    return {\n      name: '全部商品',\n      description: '显示所有可用商品',\n      productCount: props.allProductsCount\n    }\n  }\n  \n  const category = props.categories.find(c => c.categoryId === selectedCategoryKey.value)\n  if (category) {\n    return {\n      name: category.categoryName,\n      description: category.description,\n      productCount: category.productCount || 0\n    }\n  }\n  \n  return null\n})\n\n// ==================== 方法 ====================\n\n/**\n * 处理分类点击\n * @param {string} categoryKey - 分类键\n */\nconst handleCategoryClick = (categoryKey) => {\n  // 如果点击的是当前已选中的分类，则取消选中（回到全部商品）\n  if (selectedCategoryKey.value === categoryKey && categoryKey !== 'all') {\n    selectedCategoryKey.value = 'all'\n  } else {\n    selectedCategoryKey.value = categoryKey\n  }\n  \n  const categoryInfo = selectedCategoryInfo.value\n  \n  emit('category-click', {\n    categoryId: selectedCategoryKey.value,\n    categoryInfo\n  })\n  \n  emit('category-change', {\n    categoryId: selectedCategoryKey.value,\n    categoryInfo\n  })\n}\n\n/**\n * 向左滚动\n */\nconst scrollLeft = () => {\n  const tabsElement = document.querySelector('.pos-category-tabs .ant-tabs-nav-wrap')\n  if (tabsElement) {\n    tabsElement.scrollBy({ left: -200, behavior: 'smooth' })\n    updateScrollIndicators()\n  }\n}\n\n/**\n * 向右滚动\n */\nconst scrollRight = () => {\n  const tabsElement = document.querySelector('.pos-category-tabs .ant-tabs-nav-wrap')\n  if (tabsElement) {\n    tabsElement.scrollBy({ left: 200, behavior: 'smooth' })\n    updateScrollIndicators()\n  }\n}\n\n/**\n * 更新滚动指示器状态\n */\nconst updateScrollIndicators = () => {\n  const tabsElement = document.querySelector('.pos-category-tabs .ant-tabs-nav-wrap')\n  if (tabsElement) {\n    canScrollLeft.value = tabsElement.scrollLeft > 0\n    canScrollRight.value = \n      tabsElement.scrollLeft < (tabsElement.scrollWidth - tabsElement.clientWidth)\n  }\n}\n\n/**\n * 处理菜单点击\n * @param {Object} menuInfo - 菜单信息\n */\nconst handleMenuClick = ({ key }) => {\n  switch (key) {\n    case 'refresh':\n      emit('refresh-categories')\n      message.success('正在刷新分类...')\n      break\n    case 'manage':\n      emit('manage-categories')\n      break\n    case 'add':\n      emit('add-category')\n      break\n  }\n}\n\n/**\n * 隐藏分类信息面板\n */\nconst hideCategoryInfo = () => {\n  showInfoPanel.value = false\n}\n\n/**\n * 滚动到指定分类\n * @param {string} categoryId - 分类ID\n */\nconst scrollToCategory = (categoryId) => {\n  nextTick(() => {\n    const tabElement = document.querySelector(`[data-node-key=\"${categoryId}\"]`)\n    if (tabElement) {\n      tabElement.scrollIntoView({ behavior: 'smooth', inline: 'center' })\n    }\n  })\n}\n\n/**\n * 处理窗口大小变化\n */\nconst handleResize = () => {\n  updateScrollIndicators()\n}\n\n// ==================== 生命周期 ====================\n\nonMounted(() => {\n  // 监听滚动事件\n  nextTick(() => {\n    const tabsElement = document.querySelector('.pos-category-tabs .ant-tabs-nav-wrap')\n    if (tabsElement) {\n      tabsElement.addEventListener('scroll', updateScrollIndicators)\n      updateScrollIndicators()\n    }\n  })\n  \n  // 监听窗口大小变化\n  window.addEventListener('resize', handleResize)\n})\n\nonUnmounted(() => {\n  // 清理事件监听器\n  const tabsElement = document.querySelector('.pos-category-tabs .ant-tabs-nav-wrap')\n  if (tabsElement) {\n    tabsElement.removeEventListener('scroll', updateScrollIndicators)\n  }\n  \n  window.removeEventListener('resize', handleResize)\n})\n\n// ==================== 监听器 ====================\n\n// 监听选中分类变化\nwatch(\n  () => props.selectedCategory,\n  (newCategory) => {\n    selectedCategoryKey.value = newCategory\n    scrollToCategory(newCategory)\n  }\n)\n\n// 监听分类列表变化\nwatch(\n  () => props.categories,\n  () => {\n    nextTick(() => {\n      updateScrollIndicators()\n    })\n  },\n  { deep: true }\n)\n\n// 暴露方法给父组件\ndefineExpose({\n  scrollToCategory,\n  updateScrollIndicators,\n  selectCategory: (categoryId) => {\n    selectedCategoryKey.value = categoryId\n    handleCategoryClick(categoryId)\n  }\n})\n</script>\n\n<style scoped>\n.product-category {\n  position: relative;\n  background: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n/* 分类标签页包装器 */\n.category-tabs-wrapper {\n  position: relative;\n  height: 60px;\n  border-bottom: 1px solid #f0f0f0;\n  padding: 8px 16px 12px 16px;\n}\n\n/* 分类标签页 */\n.pos-category-tabs {\n  height: 100%;\n  width: 100%;\n}\n\n.pos-category-tabs :deep(.ant-tabs-content-holder) {\n  display: none;\n}\n\n.pos-category-tabs :deep(.ant-tabs-nav) {\n  margin-bottom: 0;\n  padding-bottom: 12px;\n  width: 100%;\n}\n\n.pos-category-tabs :deep(.ant-tabs-nav-wrap) {\n  overflow-x: auto;\n  overflow-y: hidden;\n  scrollbar-width: thin;\n  scrollbar-color: #d9d9d9 transparent;\n  padding-bottom: 4px;\n  width: 100%;\n}\n\n.pos-category-tabs :deep(.ant-tabs-nav-list) {\n  display: flex;\n  flex-wrap: nowrap;\n  min-width: max-content;\n}\n\n/* 自定义滚动条样式 */\n.pos-category-tabs :deep(.ant-tabs-nav-wrap::-webkit-scrollbar) {\n  height: 6px;\n}\n\n.pos-category-tabs :deep(.ant-tabs-nav-wrap::-webkit-scrollbar-track) {\n  background: #f5f5f5;\n  border-radius: 3px;\n  margin: 0 8px;\n}\n\n.pos-category-tabs :deep(.ant-tabs-nav-wrap::-webkit-scrollbar-thumb) {\n  background: #d9d9d9;\n  border-radius: 3px;\n  border: 1px solid #f5f5f5;\n}\n\n.pos-category-tabs :deep(.ant-tabs-nav-wrap::-webkit-scrollbar-thumb:hover) {\n  background: #bfbfbf;\n}\n\n/* 标签页样式 */\n.pos-category-tabs :deep(.ant-tabs-tab) {\n  padding: 6px 12px !important;\n  margin: 0 4px !important;\n  font-size: 12px !important;\n  border-radius: 6px !important;\n  transition: all 0.3s ease !important;\n  position: relative;\n}\n\n.pos-category-tabs :deep(.ant-tabs-tab:hover) {\n  background: #f0f9ff !important;\n  color: #1890ff !important;\n}\n\n.pos-category-tabs :deep(.ant-tabs-tab-active) {\n  background: #1890ff !important;\n  color: #fff !important;\n  border-color: #1890ff !important;\n}\n\n/* 标签内容 */\n.tab-content {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  position: relative;\n}\n\n.tab-badge {\n  margin-left: 4px;\n}\n\n.tab-badge :deep(.ant-badge-count) {\n  height: 16px;\n  min-width: 16px;\n  line-height: 16px;\n  padding: 0 4px;\n  font-size: 10px;\n}\n\n/* 滚动指示器 */\n.scroll-indicators {\n  position: absolute;\n  right: 16px;\n  top: 50%;\n  transform: translateY(-50%);\n  display: flex;\n  gap: 4px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 4px;\n  padding: 2px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.scroll-btn {\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 2px;\n}\n\n.scroll-btn:disabled {\n  opacity: 0.3;\n}\n\n/* 分类管理按钮 */\n.category-actions {\n  position: absolute;\n  right: 8px;\n  top: 8px;\n  z-index: 10;\n}\n\n.manage-btn {\n  width: 28px;\n  height: 28px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.manage-btn:hover {\n  background: #f0f9ff;\n  color: #1890ff;\n}\n\n/* 分类信息面板 */\n.category-info {\n  position: relative;\n  padding: 12px 16px;\n  background: #f0f9ff;\n  border-bottom: 1px solid #91d5ff;\n  font-size: 13px;\n}\n\n.info-content {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.info-label {\n  color: #595959;\n  font-weight: 500;\n}\n\n.info-value {\n  color: #1890ff;\n  font-weight: 600;\n}\n\n.close-info-btn {\n  position: absolute;\n  right: 8px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 20px;\n  height: 20px;\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .pos-category-tabs :deep(.ant-tabs-tab) {\n    padding: 6px 10px !important;\n    font-size: 11px !important;\n  }\n  \n  .scroll-indicators {\n    right: 12px;\n  }\n}\n\n@media (max-width: 768px) {\n  .category-tabs-wrapper {\n    height: auto;\n    min-height: 50px;\n    padding: 8px 12px;\n  }\n  \n  .pos-category-tabs :deep(.ant-tabs-tab) {\n    padding: 4px 8px !important;\n    font-size: 11px !important;\n    margin: 0 2px !important;\n  }\n  \n  .scroll-indicators {\n    display: none;\n  }\n  \n  .category-actions {\n    right: 4px;\n    top: 4px;\n  }\n  \n  .info-content {\n    flex-direction: column;\n    gap: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .pos-category-tabs :deep(.ant-tabs-tab) {\n    padding: 4px 6px !important;\n    font-size: 10px !important;\n    min-width: 50px;\n  }\n  \n  .tab-content {\n    flex-direction: column;\n    gap: 2px;\n  }\n  \n  .tab-badge {\n    margin-left: 0;\n    margin-top: 2px;\n  }\n}\n\n/* 动画效果 */\n.category-info {\n  animation: slideDown 0.3s ease-out;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.pos-category-tabs :deep(.ant-tabs-tab) {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .product-category {\n    border: 2px solid #000;\n  }\n  \n  .pos-category-tabs :deep(.ant-tabs-tab) {\n    border: 1px solid #000 !important;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .category-info,\n  .pos-category-tabs :deep(.ant-tabs-tab) {\n    animation: none;\n  }\n  \n  .pos-category-tabs :deep(.ant-tabs-tab) {\n    transition: none !important;\n  }\n}\n\n/* 触屏设备优化 */\n@media (hover: none) {\n  .pos-category-tabs :deep(.ant-tabs-tab:hover) {\n    background: transparent !important;\n    color: inherit !important;\n  }\n  \n  .pos-category-tabs :deep(.ant-tabs-tab:active) {\n    transform: scale(0.98);\n  }\n}\n</style>"