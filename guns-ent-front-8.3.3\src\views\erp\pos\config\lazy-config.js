/**
 * POS模块懒加载配置
 * 
 * 配置各个组件和模块的懒加载策略，优化应用性能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { createLazyComponent, lazyRoute } from '../utils/lazy-loader'

/**
 * 组件懒加载配置
 */
export const lazyComponents = {
  // 购物车相关组件
  cart: {
    ShoppingCart: createLazyComponent(
      () => import('../components/cart/ShoppingCart.vue'),
      { delay: 100 }
    ),
    CartItem: createLazyComponent(
      () => import('../components/cart/CartItem.vue'),
      { delay: 50 }
    ),
    CartSummary: createLazyComponent(
      () => import('../components/cart/CartSummary.vue'),
      { delay: 50 }
    ),
    CartActions: createLazyComponent(
      () => import('../components/cart/CartActions.vue'),
      { delay: 50 }
    )
  },
  
  // 支付相关组件
  payment: {
    PaymentPanel: createLazyComponent(
      () => import('../components/payment/PaymentPanel.vue'),
      { delay: 150 }
    ),
    PaymentMethod: createLazyComponent(
      () => import('../components/payment/PaymentMethod.vue'),
      { delay: 100 }
    ),
    PaymentResult: createLazyComponent(
      () => import('../components/payment/PaymentResult.vue'),
      { delay: 100 }
    ),
    CashPayment: createLazyComponent(
      () => import('../components/payment/CashPayment.vue'),
      { delay: 100 }
    ),
    CardPayment: createLazyComponent(
      () => import('../components/payment/CardPayment.vue'),
      { delay: 100 }
    ),
    QRPayment: createLazyComponent(
      () => import('../components/payment/QRPayment.vue'),
      { delay: 100 }
    )
  },
  
  // 商品相关组件
  product: {
    ProductDisplayArea: createLazyComponent(
      () => import('../components/product/ProductDisplayArea.vue'),
      { delay: 200 }
    ),
    ProductGrid: createLazyComponent(
      () => import('../components/product/ProductGrid.vue'),
      { delay: 150 }
    ),
    ProductSearch: createLazyComponent(
      () => import('../components/product/ProductSearch.vue'),
      { delay: 100 }
    ),
    ProductCategory: createLazyComponent(
      () => import('../components/product/ProductCategory.vue'),
      { delay: 100 }
    ),
    ProductItem: createLazyComponent(
      () => import('../components/product/ProductItem.vue'),
      { delay: 50 }
    )
  },
  
  // 会员相关组件
  member: {
    MemberPanel: createLazyComponent(
      () => import('../components/member/MemberPanel.vue'),
      { delay: 150 }
    ),
    MemberSelector: createLazyComponent(
      () => import('../components/member/MemberSelector.vue'),
      { delay: 100 }
    ),
    MemberInfo: createLazyComponent(
      () => import('../components/member/MemberInfo.vue'),
      { delay: 100 }
    ),
    MemberDiscount: createLazyComponent(
      () => import('../components/member/MemberDiscount.vue'),
      { delay: 100 }
    )
  },
  
  // 工具栏相关组件
  toolbar: {
    ToolbarPanel: createLazyComponent(
      () => import('../components/toolbar/ToolbarPanel.vue'),
      { delay: 100 }
    ),
    OrderSuspend: createLazyComponent(
      () => import('../components/toolbar/OrderSuspend.vue'),
      { delay: 150 }
    )
  },
  
  // 通用组件（按需加载）
  common: {
    ErrorBoundary: createLazyComponent(
      () => import('../components/common/ErrorBoundary.vue'),
      { delay: 50 }
    ),
    PerformanceMonitor: createLazyComponent(
      () => import('../components/common/PerformanceMonitor.vue'),
      { delay: 100 }
    )
  }
}

/**
 * 模块懒加载配置
 */
export const lazyModules = {
  // API模块
  api: {
    cart: () => import('../api/cart'),
    payment: () => import('../api/payment'),
    member: () => import('../api/member'),
    product: () => import('../api/product'),
    order: () => import('../api/order')
  },
  
  // Composables模块
  composables: {
    useCart: () => import('../composables/useCart'),
    usePayment: () => import('../composables/usePayment'),
    useMember: () => import('../composables/useMember'),
    useOrder: () => import('../composables/useOrder'),
    useKeyboard: () => import('../composables/useKeyboard'),
    useDataRecovery: () => import('../composables/useDataRecovery'),
    useOptimization: () => import('../composables/useOptimization'),
    useMemoryMonitor: () => import('../composables/useMemoryMonitor'),
    useErrorHandler: () => import('../composables/useErrorHandler'),
    usePerformanceMonitor: () => import('../composables/usePerformanceMonitor')
  },
  
  // 工具模块
  utils: {
    calculations: () => import('../utils/calculations'),
    formatters: () => import('../utils/formatters'),
    validators: () => import('../utils/validators'),
    errorHandler: () => import('../utils/error-handler'),
    performanceMonitor: () => import('../utils/performance-monitor'),
    lazyLoader: () => import('../utils/lazy-loader')
  }
}

/**
 * CSS懒加载配置
 */
export const lazyStyles = {
  // 组件样式
  cart: '/static/css/pos/cart.css',
  payment: '/static/css/pos/payment.css',
  product: '/static/css/pos/product.css',
  member: '/static/css/pos/member.css',
  
  // 主题样式
  themes: {
    dark: '/static/css/themes/dark.css',
    light: '/static/css/themes/light.css',
    compact: '/static/css/themes/compact.css'
  },
  
  // 第三方库样式
  vendors: {
    charts: '/static/css/vendors/charts.css',
    icons: '/static/css/vendors/icons.css'
  }
}

/**
 * 预加载配置
 */
export const preloadConfig = {
  // 空闲时预加载的资源
  idleResources: [
    lazyModules.composables.useDataRecovery,
    lazyModules.composables.useOptimization,
    lazyModules.utils.performanceMonitor
  ],
  
  // 基于可视区域的预加载
  intersectionResources: {
    'payment-panel': lazyModules.composables.usePayment,
    'member-panel': lazyModules.composables.useMember,
    'product-grid': lazyModules.composables.useOptimization
  },
  
  // 鼠标悬停预加载
  hoverResources: {
    'payment-button': lazyModules.api.payment,
    'member-button': lazyModules.api.member,
    'suspend-button': lazyModules.composables.useDataRecovery
  }
}

/**
 * 路由懒加载配置
 */
export const lazyRoutes = {
  // POS主页面
  pos: lazyRoute(() => import('../index.vue')),
  
  // 设置页面
  settings: lazyRoute(() => import('../views/Settings.vue')),
  
  // 报表页面
  reports: lazyRoute(() => import('../views/Reports.vue')),
  
  // 帮助页面
  help: lazyRoute(() => import('../views/Help.vue'))
}

/**
 * 分包策略配置
 */
export const chunkConfig = {
  // 核心包（立即加载）
  core: [
    'vue',
    'vue-router',
    'pinia',
    'ant-design-vue'
  ],
  
  // 业务包（按需加载）
  business: {
    pos: [
      '../composables/usePos',
      '../composables/useCart',
      '../utils/calculations',
      '../utils/formatters'
    ],
    payment: [
      '../composables/usePayment',
      '../api/payment',
      '../utils/validators'
    ],
    member: [
      '../composables/useMember',
      '../api/member'
    ],
    product: [
      '../api/product',
      '../utils/formatters'
    ]
  },
  
  // 工具包（延迟加载）
  utils: [
    '../utils/error-handler',
    '../utils/performance-monitor',
    '../utils/lazy-loader',
    '../composables/useErrorHandler',
    '../composables/usePerformanceMonitor'
  ],
  
  // 第三方库包
  vendors: {
    charts: ['echarts', 'chart.js'],
    utils: ['lodash', 'dayjs', 'crypto-js']
  }
}

/**
 * 加载优先级配置
 */
export const loadPriority = {
  // 高优先级（立即加载）
  high: [
    'usePos',
    'useCart',
    'calculations',
    'formatters'
  ],
  
  // 中优先级（用户交互时加载）
  medium: [
    'usePayment',
    'useMember',
    'useOrder',
    'validators'
  ],
  
  // 低优先级（空闲时加载）
  low: [
    'useDataRecovery',
    'useOptimization',
    'useMemoryMonitor',
    'useErrorHandler',
    'usePerformanceMonitor'
  ]
}

/**
 * 条件加载配置
 */
export const conditionalLoads = {
  // 基于设备类型
  device: {
    mobile: {
      components: ['MobilePayment', 'TouchKeyboard'],
      styles: ['/static/css/mobile.css']
    },
    desktop: {
      components: ['DesktopShortcuts', 'MultiWindow'],
      styles: ['/static/css/desktop.css']
    }
  },
  
  // 基于功能权限
  permissions: {
    admin: {
      components: ['AdminPanel', 'SystemSettings'],
      modules: ['adminApi', 'systemUtils']
    },
    cashier: {
      components: ['CashierTools'],
      modules: ['cashierApi']
    }
  },
  
  // 基于网络状况
  network: {
    offline: {
      components: ['OfflineMode'],
      modules: ['offlineStorage', 'syncManager']
    },
    slow: {
      components: ['LightweightComponents'],
      styles: ['/static/css/minimal.css']
    }
  }
}

/**
 * 获取懒加载组件
 * @param {string} category - 组件分类
 * @param {string} name - 组件名称
 * @returns {Object} 懒加载组件
 */
export function getLazyComponent(category, name) {
  return lazyComponents[category]?.[name]
}

/**
 * 获取懒加载模块
 * @param {string} category - 模块分类
 * @param {string} name - 模块名称
 * @returns {Function} 懒加载函数
 */
export function getLazyModule(category, name) {
  return lazyModules[category]?.[name]
}

/**
 * 批量预加载指定优先级的资源
 * @param {string} priority - 优先级
 * @returns {Promise} 预加载Promise
 */
export async function preloadByPriority(priority) {
  const resources = loadPriority[priority] || []
  const loaders = []
  
  resources.forEach(resourceName => {
    // 查找对应的加载器
    Object.values(lazyModules).forEach(category => {
      if (category[resourceName]) {
        loaders.push(category[resourceName])
      }
    })
  })
  
  if (loaders.length > 0) {
    const { LazyLoader } = await import('../utils/lazy-loader')
    return LazyLoader.preload(loaders, {
      priority: priority === 'high' ? 'high' : 'low',
      concurrent: priority === 'high' ? 5 : 2
    })
  }
}

/**
 * 条件加载资源
 * @param {string} condition - 条件类型
 * @param {string} value - 条件值
 * @returns {Promise} 加载Promise
 */
export async function conditionalLoad(condition, value) {
  const config = conditionalLoads[condition]?.[value]
  if (!config) return
  
  const { LazyLoader } = await import('../utils/lazy-loader')
  const promises = []
  
  // 加载组件
  if (config.components) {
    config.components.forEach(componentName => {
      promises.push(
        LazyLoader.lazyModule(() => import(`../components/${componentName}.vue`))
      )
    })
  }
  
  // 加载模块
  if (config.modules) {
    config.modules.forEach(moduleName => {
      promises.push(
        LazyLoader.lazyModule(() => import(`../modules/${moduleName}`))
      )
    })
  }
  
  // 加载样式
  if (config.styles) {
    config.styles.forEach(stylePath => {
      promises.push(LazyLoader.lazyCSS(stylePath))
    })
  }
  
  return Promise.allSettled(promises)
}

// 默认导出配置对象
export default {
  lazyComponents,
  lazyModules,
  lazyStyles,
  preloadConfig,
  lazyRoutes,
  chunkConfig,
  loadPriority,
  conditionalLoads,
  getLazyComponent,
  getLazyModule,
  preloadByPriority,
  conditionalLoad
}