/**
 * POS收银流程集成测试
 * 
 * 测试完整的收银流程：商品选择→购物车→会员→支付→订单
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import PosMain from '../../index.vue'
import { usePosStore } from '@/stores/pos'

// Mock APIs
const mockCartApi = {
  checkInventory: vi.fn(),
  getProductByBarcode: vi.fn(),
  validateCart: vi.fn()
}

const mockMemberApi = {
  searchMember: vi.fn(),
  validateMemberCard: vi.fn(),
  updateMemberPoints: vi.fn()
}

const mockPaymentApi = {
  processCashPayment: vi.fn(),
  processQrCodePayment: vi.fn(),
  queryQrCodePaymentStatus: vi.fn(),
  processMemberCardPayment: vi.fn()
}

const mockOrderApi = {
  createOrder: vi.fn(),
  generateOrderNo: vi.fn(),
  printOrder: vi.fn()
}

const mockProductApi = {
  getProductList: vi.fn(),
  searchProducts: vi.fn(),
  checkProductStock: vi.fn()
}

vi.mock('../../api/cart', () => ({ CartApi: mockCartApi }))
vi.mock('../../api/member', () => ({ MemberApi: mockMemberApi }))
vi.mock('../../api/payment', () => ({ PaymentApi: mockPaymentApi }))
vi.mock('../../api/order', () => ({ OrderApi: mockOrderApi }))
vi.mock('../../api/product', () => ({ ProductApi: mockProductApi }))

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  notification: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

describe('POS收银流程集成测试', () => {
  let wrapper
  let pinia
  let store
  let router
  
  // 测试数据
  const mockProducts = [
    {
      id: 'P001',
      name: '红苹果',
      price: 5.5,
      stock: 100,
      unit: '斤',
      barcode: '1234567890123',
      categoryId: 'CAT001'
    },
    {
      id: 'P002',
      name: '香蕉',
      price: 3.0,
      stock: 50,
      unit: '斤',
      barcode: '1234567890124',
      categoryId: 'CAT001'
    }
  ]
  
  const mockMember = {
    id: 'M001',
    cardNo: 'VIP123456',
    name: '张三',
    phone: '13812345678',
    level: 'GOLD',
    discountRate: 0.1,
    points: 1000,
    balance: 500,
    status: 'ACTIVE'
  }
  
  const createWrapper = () => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = usePosStore()
    
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/pos', component: PosMain }
      ]
    })
    
    return mount(PosMain, {
      global: {
        plugins: [pinia, router],
        stubs: {
          'router-link': true,
          'router-view': true
        }
      }
    })
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置默认的API响应
    mockProductApi.getProductList.mockResolvedValue({
      products: mockProducts,
      total: mockProducts.length
    })
    
    mockCartApi.checkInventory.mockResolvedValue({
      available: true,
      stock: 100
    })
    
    mockOrderApi.generateOrderNo.mockResolvedValue({
      orderNo: 'POS20250102001'
    })
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('完整收银流程', () => {
    it('应该完成完整的现金支付流程', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 1. 添加商品到购物车
      const addToCartSpy = vi.spyOn(wrapper.vm, 'handleAddToCart')
      await wrapper.vm.handleAddToCart(mockProducts[0], 2)
      await wrapper.vm.handleAddToCart(mockProducts[1], 1)
      
      expect(addToCartSpy).toHaveBeenCalledTimes(2)
      expect(mockCartApi.checkInventory).toHaveBeenCalledTimes(2)
      
      // 验证购物车状态
      expect(store.cartItems).toHaveLength(2)
      expect(store.totalAmount).toBe(14) // 5.5*2 + 3*1
      
      // 2. 选择会员
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      const selectMemberSpy = vi.spyOn(wrapper.vm, 'handleMemberChange')
      await wrapper.vm.handleMemberChange(mockMember)
      
      expect(selectMemberSpy).toHaveBeenCalledWith(mockMember)
      expect(store.currentMember).toEqual(mockMember)
      
      // 验证会员折扣计算
      expect(store.discountAmount).toBe(1.4) // 14 * 0.1
      expect(store.finalAmount).toBe(12.6) // 14 - 1.4
      
      // 3. 创建订单
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER001',
        orderNo: 'POS20250102001'
      })
      
      const createOrderSpy = vi.spyOn(wrapper.vm, 'createOrder')
      const orderResult = await wrapper.vm.createOrder()
      
      expect(createOrderSpy).toHaveBeenCalled()
      expect(mockOrderApi.createOrder).toHaveBeenCalledWith({
        items: store.cartItems,
        member: store.currentMember,
        totalAmount: 14,
        discountAmount: 1.4,
        finalAmount: 12.6,
        cashierId: expect.any(String)
      })
      
      // 4. 处理现金支付
      mockPaymentApi.processCashPayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY001',
        status: 'SUCCESS'
      })
      
      const paymentSpy = vi.spyOn(wrapper.vm, 'handlePayment')
      const paymentResult = await wrapper.vm.handlePayment({
        method: 'CASH',
        receivedAmount: 15,
        orderId: 'ORDER001'
      })
      
      expect(paymentSpy).toHaveBeenCalled()
      expect(mockPaymentApi.processCashPayment).toHaveBeenCalledWith({
        orderId: 'ORDER001',
        paymentAmount: 12.6,
        receivedAmount: 15,
        changeAmount: 2.4,
        cashierId: expect.any(String)
      })
      
      // 5. 更新会员积分
      mockMemberApi.updateMemberPoints.mockResolvedValue({
        success: true,
        newPoints: 1012 // 原积分1000 + 消费积分12
      })
      
      expect(mockMemberApi.updateMemberPoints).toHaveBeenCalledWith({
        memberId: 'M001',
        changeType: 'EARN',
        points: 12, // 消费12.6元获得12积分
        reason: '消费获得',
        orderId: 'ORDER001'
      })
      
      // 6. 验证最终状态
      expect(paymentResult.success).toBe(true)
      expect(store.paymentStatus).toBe('success')
      
      // 验证购物车已清空
      expect(store.cartItems).toHaveLength(0)
      expect(store.totalAmount).toBe(0)
    })
    
    it('应该完成完整的扫码支付流程', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 1. 添加商品
      await wrapper.vm.handleAddToCart(mockProducts[0], 1)
      
      // 2. 不选择会员，直接支付
      expect(store.finalAmount).toBe(5.5)
      
      // 3. 创建订单
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER002',
        orderNo: 'POS20250102002'
      })
      
      await wrapper.vm.createOrder()
      
      // 4. 处理微信扫码支付
      mockPaymentApi.processQrCodePayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY002',
        qrCodeUrl: 'https://qr.weixin.com/test',
        status: 'PENDING'
      })
      
      const paymentResult = await wrapper.vm.handlePayment({
        method: 'WECHAT',
        orderId: 'ORDER002'
      })
      
      expect(mockPaymentApi.processQrCodePayment).toHaveBeenCalledWith({
        orderId: 'ORDER002',
        paymentAmount: 5.5,
        paymentMethod: 'WECHAT',
        cashierId: expect.any(String)
      })
      
      // 5. 模拟支付状态查询
      mockPaymentApi.queryQrCodePaymentStatus.mockResolvedValue({
        paymentId: 'PAY002',
        status: 'SUCCESS',
        transactionId: 'WX_TXN_001'
      })
      
      // 模拟轮询查询支付状态
      const statusResult = await wrapper.vm.queryPaymentStatus('PAY002')
      
      expect(mockPaymentApi.queryQrCodePaymentStatus).toHaveBeenCalledWith({
        paymentId: 'PAY002'
      })
      
      expect(statusResult.status).toBe('SUCCESS')
    })
    
    it('应该完成会员卡支付流程', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 1. 添加商品
      await wrapper.vm.handleAddToCart(mockProducts[1], 2) // 香蕉 3*2=6元
      
      // 2. 选择会员
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      await wrapper.vm.handleMemberChange(mockMember)
      
      // 验证会员折扣
      expect(store.finalAmount).toBe(5.4) // 6 - 0.6(折扣)
      
      // 3. 创建订单
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER003',
        orderNo: 'POS20250102003'
      })
      
      await wrapper.vm.createOrder()
      
      // 4. 使用会员卡支付
      mockPaymentApi.processMemberCardPayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY003',
        remainingBalance: 494.6, // 500 - 5.4
        status: 'SUCCESS'
      })
      
      const paymentResult = await wrapper.vm.handlePayment({
        method: 'MEMBER',
        orderId: 'ORDER003',
        memberId: 'M001'
      })
      
      expect(mockPaymentApi.processMemberCardPayment).toHaveBeenCalledWith({
        orderId: 'ORDER003',
        paymentAmount: 5.4,
        memberId: 'M001',
        memberCardNo: 'VIP123456',
        cashierId: expect.any(String)
      })
      
      expect(paymentResult.success).toBe(true)
    })
  })
  
  describe('异常情况处理', () => {
    it('应该处理库存不足的情况', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 模拟库存不足
      mockCartApi.checkInventory.mockResolvedValue({
        available: false,
        stock: 1
      })
      
      const result = await wrapper.vm.handleAddToCart(mockProducts[0], 5)
      
      expect(result).toBe(false)
      expect(store.cartItems).toHaveLength(0)
    })
    
    it('应该处理会员不存在的情况', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      mockMemberApi.searchMember.mockResolvedValue(null)
      
      const result = await wrapper.vm.searchMember('INVALID')
      
      expect(result).toBe(null)
      expect(store.currentMember).toBe(null)
    })
    
    it('应该处理支付失败的情况', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 添加商品和创建订单
      await wrapper.vm.handleAddToCart(mockProducts[0], 1)
      
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER004'
      })
      
      await wrapper.vm.createOrder()
      
      // 模拟支付失败
      mockPaymentApi.processCashPayment.mockRejectedValue(
        new Error('支付处理失败')
      )
      
      const paymentResult = await wrapper.vm.handlePayment({
        method: 'CASH',
        receivedAmount: 10,
        orderId: 'ORDER004'
      })
      
      expect(paymentResult).toBe(null)
      expect(store.paymentStatus).toBe('failed')
    })
    
    it('应该处理订单创建失败的情况', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      await wrapper.vm.handleAddToCart(mockProducts[0], 1)
      
      // 模拟订单创建失败
      mockOrderApi.createOrder.mockRejectedValue(
        new Error('订单创建失败')
      )
      
      const orderResult = await wrapper.vm.createOrder()
      
      expect(orderResult).toBe(null)
      // 购物车应该保持不变
      expect(store.cartItems).toHaveLength(1)
    })
  })
  
  describe('业务规则验证', () => {
    it('应该验证购物车不能为空', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 尝试创建空购物车的订单
      const orderResult = await wrapper.vm.createOrder()
      
      expect(orderResult).toBe(null)
      expect(mockOrderApi.createOrder).not.toHaveBeenCalled()
    })
    
    it('应该验证现金支付实收金额不能少于应付金额', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      await wrapper.vm.handleAddToCart(mockProducts[0], 1)
      
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER005'
      })
      
      await wrapper.vm.createOrder()
      
      // 实收金额不足
      const paymentResult = await wrapper.vm.handlePayment({
        method: 'CASH',
        receivedAmount: 3, // 少于应付金额5.5
        orderId: 'ORDER005'
      })
      
      expect(paymentResult).toBe(null)
      expect(mockPaymentApi.processCashPayment).not.toHaveBeenCalled()
    })
    
    it('应该验证会员余额是否充足', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 添加高价商品
      const expensiveProduct = {
        ...mockProducts[0],
        price: 600 // 超过会员余额500
      }
      
      await wrapper.vm.handleAddToCart(expensiveProduct, 1)
      await wrapper.vm.handleMemberChange(mockMember)
      
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER006'
      })
      
      await wrapper.vm.createOrder()
      
      // 模拟余额不足
      mockPaymentApi.processMemberCardPayment.mockRejectedValue(
        new Error('会员余额不足')
      )
      
      const paymentResult = await wrapper.vm.handlePayment({
        method: 'MEMBER',
        orderId: 'ORDER006',
        memberId: 'M001'
      })
      
      expect(paymentResult).toBe(null)
    })
  })
  
  describe('数据一致性验证', () => {
    it('应该确保购物车金额计算正确', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 添加多个商品
      await wrapper.vm.handleAddToCart(mockProducts[0], 2.5) // 5.5 * 2.5 = 13.75
      await wrapper.vm.handleAddToCart(mockProducts[1], 1.2) // 3 * 1.2 = 3.6
      
      expect(store.totalAmount).toBe(17.35)
      
      // 选择会员
      await wrapper.vm.handleMemberChange(mockMember)
      
      expect(store.discountAmount).toBe(1.735) // 17.35 * 0.1
      expect(store.finalAmount).toBe(15.615) // 17.35 - 1.735
    })
    
    it('应该确保会员积分计算正确', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      await wrapper.vm.handleAddToCart(mockProducts[0], 1)
      await wrapper.vm.handleMemberChange(mockMember)
      
      const finalAmount = store.finalAmount // 4.95 (5.5 - 0.55)
      
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER007'
      })
      
      await wrapper.vm.createOrder()
      
      mockPaymentApi.processCashPayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY007'
      })
      
      mockMemberApi.updateMemberPoints.mockResolvedValue({
        success: true,
        newPoints: 1004 // 1000 + 4积分
      })
      
      await wrapper.vm.handlePayment({
        method: 'CASH',
        receivedAmount: 10,
        orderId: 'ORDER007'
      })
      
      // 验证积分计算：消费4.95元应该获得4积分（向下取整）
      expect(mockMemberApi.updateMemberPoints).toHaveBeenCalledWith({
        memberId: 'M001',
        changeType: 'EARN',
        points: 4,
        reason: '消费获得',
        orderId: 'ORDER007'
      })
    })
  })
  
  describe('状态管理验证', () => {
    it('应该正确管理POS系统状态', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      
      // 初始状态
      expect(store.cartItems).toHaveLength(0)
      expect(store.currentMember).toBe(null)
      expect(store.paymentStatus).toBe('idle')
      
      // 添加商品后的状态
      await wrapper.vm.handleAddToCart(mockProducts[0], 1)
      expect(store.cartItems).toHaveLength(1)
      expect(store.totalAmount).toBe(5.5)
      
      // 选择会员后的状态
      await wrapper.vm.handleMemberChange(mockMember)
      expect(store.currentMember).toEqual(mockMember)
      expect(store.discountAmount).toBe(0.55)
      
      // 支付完成后的状态
      mockOrderApi.createOrder.mockResolvedValue({
        success: true,
        orderId: 'ORDER008'
      })
      
      mockPaymentApi.processCashPayment.mockResolvedValue({
        success: true,
        paymentId: 'PAY008'
      })
      
      await wrapper.vm.createOrder()
      await wrapper.vm.handlePayment({
        method: 'CASH',
        receivedAmount: 10,
        orderId: 'ORDER008'
      })
      
      // 验证支付完成后状态重置
      expect(store.cartItems).toHaveLength(0)
      expect(store.totalAmount).toBe(0)
      expect(store.paymentStatus).toBe('success')
    })
  })
})