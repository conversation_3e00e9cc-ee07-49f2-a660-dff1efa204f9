import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { EnhancedPosErrorHandler } from '../enhanced-error-handler'
import { PosErrorTypes } from '../error-types'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn()
  },
  notification: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn()
  },
  Modal: {
    error: vi.fn((config) => {
      // 模拟用户点击确定
      setTimeout(() => config.onOk && config.onOk(), 0)
    })
  }
}))

// Mock RetryHandler
vi.mock('../retry-handler', () => ({
  RetryHandler: {
    withRetry: vi.fn(async (fn, options) => {
      // 简单的重试逻辑模拟
      let lastError
      for (let i = 0; i <= (options.maxRetries || 0); i++) {
        try {
          return await fn()
        } catch (error) {
          lastError = error
          if (i === (options.maxRetries || 0)) {
            throw error
          }
          await new Promise(resolve => setTimeout(resolve, options.retryDelay || 0))
        }
      }
      throw lastError
    })
  }
}))

describe('EnhancedPosErrorHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    EnhancedPosErrorHandler.clearErrorStats()
    
    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('初始化', () => {
    it('应该正确初始化错误处理器', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener')
      
      EnhancedPosErrorHandler.initialize()
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('error', expect.any(Function))
      expect(addEventListenerSpy).toHaveBeenCalledWith('unhandledrejection', expect.any(Function))
    })
  })

  describe('API调用包装', () => {
    it('应该成功执行API调用', async () => {
      const mockApi = vi.fn().mockResolvedValue('success')
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试API'
      })
      
      const result = await wrappedApi('arg1', 'arg2')
      
      expect(result).toBe('success')
      expect(mockApi).toHaveBeenCalledWith('arg1', 'arg2')
    })

    it('应该处理API调用失败', async () => {
      const mockError = new Error('API调用失败')
      const mockApi = vi.fn().mockRejectedValue(mockError)
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试API',
        showMessage: true
      })
      
      await expect(wrappedApi()).rejects.toThrow()
      
      // 验证错误统计被更新
      const stats = EnhancedPosErrorHandler.getErrorStats()
      expect(stats.total).toBe(1)
    })

    it('应该支持重试机制', async () => {
      let callCount = 0
      const mockApi = vi.fn().mockImplementation(() => {
        callCount++
        if (callCount < 3) {
          throw new Error('临时失败')
        }
        return 'success'
      })
      
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试重试',
        retryOptions: { maxRetries: 3, retryDelay: 10 }
      })
      
      const result = await wrappedApi()
      
      expect(result).toBe('success')
      expect(mockApi).toHaveBeenCalledTimes(3)
    })

    it('应该支持fallback值', async () => {
      const mockApi = vi.fn().mockRejectedValue(new Error('API失败'))
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试fallback',
        fallbackValue: 'fallback_result',
        showMessage: false
      })
      
      const result = await wrappedApi()
      
      expect(result).toBe('fallback_result')
    })

    it('应该支持超时控制', async () => {
      const mockApi = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 1000))
      )
      
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试超时',
        timeout: 100,
        showMessage: false
      })
      
      await expect(wrappedApi()).rejects.toThrow('请求超时')
    })
  })

  describe('错误恢复策略', () => {
    it('应该执行自定义恢复策略', async () => {
      const mockRecovery = vi.fn().mockResolvedValue('recovered')
      const mockApi = vi.fn().mockRejectedValue(new Error('API失败'))
      
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试恢复',
        recoveryStrategy: mockRecovery,
        showMessage: false
      })
      
      const result = await wrappedApi('test_arg')
      
      expect(result).toBe('recovered')
      expect(mockRecovery).toHaveBeenCalledWith(
        expect.any(Object),
        ['test_arg']
      )
    })

    it('应该注册和使用命名恢复策略', async () => {
      const mockStrategy = vi.fn().mockResolvedValue('strategy_result')
      EnhancedPosErrorHandler.registerRecoveryStrategy('testStrategy', mockStrategy)
      
      const mockApi = vi.fn().mockRejectedValue(new Error('API失败'))
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '测试命名策略',
        recoveryStrategy: 'testStrategy',
        showMessage: false
      })
      
      const result = await wrappedApi()
      
      expect(result).toBe('strategy_result')
      expect(mockStrategy).toHaveBeenCalled()
    })
  })

  describe('错误统计', () => {
    it('应该正确统计错误', async () => {
      const mockApi = vi.fn().mockRejectedValue(new Error('测试错误'))
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '统计测试',
        showMessage: false
      })
      
      try {
        await wrappedApi()
      } catch (error) {
        // 忽略错误
      }
      
      const stats = EnhancedPosErrorHandler.getErrorStats()
      expect(stats.total).toBe(1)
      expect(stats.byContext['统计测试']).toBe(1)
      expect(stats.recentErrors).toHaveLength(1)
    })

    it('应该检测高频错误', async () => {
      const mockApi = vi.fn().mockRejectedValue(new Error('高频错误'))
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '高频测试',
        showMessage: false,
        retryOptions: { maxRetries: 0 }
      })
      
      // 触发多次相同错误
      for (let i = 0; i < 6; i++) {
        try {
          await wrappedApi()
        } catch (error) {
          // 忽略错误
        }
      }
      
      // 验证高频错误警告
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('检测到高频错误')
      )
    })

    it('应该正确计算错误统计摘要', () => {
      // 手动添加一些错误统计
      EnhancedPosErrorHandler.errorStats.total = 10
      EnhancedPosErrorHandler.errorStats.byType = {
        [PosErrorTypes.NETWORK_ERROR]: 5,
        [PosErrorTypes.PAYMENT_FAILED]: 3
      }
      EnhancedPosErrorHandler.errorStats.byContext = {
        '支付': 4,
        '网络': 6
      }
      
      const stats = EnhancedPosErrorHandler.getErrorStats()
      
      expect(stats.total).toBe(10)
      expect(stats.mostCommonErrorType).toBe(PosErrorTypes.NETWORK_ERROR)
      expect(stats.mostCommonContext).toBe('网络')
    })
  })

  describe('错误监听器', () => {
    it('应该正确管理错误监听器', () => {
      const listener1 = vi.fn()
      const listener2 = vi.fn()
      
      EnhancedPosErrorHandler.addErrorListener(listener1)
      EnhancedPosErrorHandler.addErrorListener(listener2)
      
      // 触发错误通知
      const testError = { type: 'TEST_ERROR', message: '测试' }
      EnhancedPosErrorHandler.notifyErrorListeners(testError, '测试上下文')
      
      expect(listener1).toHaveBeenCalledWith(testError, '测试上下文')
      expect(listener2).toHaveBeenCalledWith(testError, '测试上下文')
      
      // 移除监听器
      EnhancedPosErrorHandler.removeErrorListener(listener1)
      EnhancedPosErrorHandler.notifyErrorListeners(testError, '测试上下文2')
      
      expect(listener1).toHaveBeenCalledTimes(1) // 没有再次调用
      expect(listener2).toHaveBeenCalledTimes(2) // 调用了两次
    })
  })

  describe('严重错误处理', () => {
    it('应该为严重错误显示模态框', async () => {
      const { Modal } = await import('ant-design-vue')
      const mockApi = vi.fn().mockRejectedValue(new Error('严重错误'))
      
      // 模拟严重错误
      vi.spyOn(EnhancedPosErrorHandler, 'processError').mockReturnValue({
        type: PosErrorTypes.SYSTEM_ERROR,
        message: '严重错误',
        severity: 'CRITICAL',
        userFacing: true,
        timestamp: new Date().toISOString()
      })
      
      const wrappedApi = EnhancedPosErrorHandler.wrapApiCall(mockApi, {
        context: '严重错误测试',
        retryOptions: { maxRetries: 0 }
      })
      
      try {
        await wrappedApi()
      } catch (error) {
        // 忽略错误
      }
      
      expect(Modal.error).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '系统错误',
          content: expect.stringContaining('严重错误')
        })
      )
    })
  })

  describe('错误报告导出', () => {
    it('应该正确导出错误报告', () => {
      // 添加一些测试数据
      EnhancedPosErrorHandler.errorStats.total = 5
      EnhancedPosErrorHandler.errorStats.byType = {
        [PosErrorTypes.NETWORK_ERROR]: 3
      }
      
      const report = EnhancedPosErrorHandler.exportErrorReport()
      
      expect(report).toHaveProperty('timestamp')
      expect(report).toHaveProperty('stats')
      expect(report).toHaveProperty('systemInfo')
      expect(report.stats.total).toBe(5)
      expect(report.systemInfo).toHaveProperty('userAgent')
      expect(report.systemInfo).toHaveProperty('url')
    })
  })

  describe('Vue错误处理', () => {
    it('应该正确处理Vue组件错误', () => {
      const mockVm = {
        $options: { name: 'TestComponent' }
      }
      const testError = new Error('Vue组件错误')
      const errorInfo = 'render function'
      
      EnhancedPosErrorHandler.vueErrorHandler(testError, mockVm, errorInfo)
      
      const stats = EnhancedPosErrorHandler.getErrorStats()
      expect(stats.total).toBe(1)
      expect(stats.byContext['Vue组件 TestComponent']).toBe(1)
    })
  })

  describe('清理功能', () => {
    it('应该正确清理错误统计', () => {
      // 添加一些数据
      EnhancedPosErrorHandler.errorStats.total = 10
      EnhancedPosErrorHandler.errorStats.byType = { test: 5 }
      
      EnhancedPosErrorHandler.clearErrorStats()
      
      const stats = EnhancedPosErrorHandler.getErrorStats()
      expect(stats.total).toBe(0)
      expect(Object.keys(stats.byType)).toHaveLength(0)
      expect(stats.recentErrors).toHaveLength(0)
    })
  })
})