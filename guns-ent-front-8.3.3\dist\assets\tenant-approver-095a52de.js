import{_ as G,r as u,s as j,o as z,k as v,a as f,f as k,w as t,d as e,b as h,g as D,c as O,F as P,e as S,t as J,m as K,cj as W,al as X,u as Q,v as Z,B as ee,W as te,J as ae,I as ne,G as le,H as oe,M as se}from"./index-18a1ea24.js";/* empty css              *//* empty css              */import{T as w}from"./TenantApi-e23e3174.js";import{g as L,G as ie}from"./time-util-d1d9a3df.js";const re={style:{"margin-bottom":"10px"}},ue={__name:"tenant-approver",props:{visible:Bo<PERSON>an,approverList:Array},emits:["update:visible","done"],setup(T,{emit:Y}){const g=T,b=Y,c=u(!1),s=u({tenantIdList:g.approverList,activeDate:L()+" 00:00:00",expireDate:ie(L(),1)+" 00:00:00",tenantLinkList:[]}),U=j({activeDate:[{required:!0,message:"\u8BF7\u9009\u62E9\u79DF\u6237\u751F\u6548\u65F6\u95F4",type:"string",trigger:"change"}],expireDate:[{required:!0,message:"\u8BF7\u9009\u62E9\u5230\u671F\u65F6\u95F4",type:"string",trigger:"change"}]}),y=u(null),p=u(null),x=u([]);z(()=>{I()});const I=()=>{w.tenantPackageList().then(l=>{x.value=l.data})},_=l=>{b("update:visible",l)},M=async()=>{y.value.validate().then(async l=>{l&&(c.value=!0,w.auditTenant(s.value).then(async n=>{c.value=!1,K.success(n.message),_(!1),b("done")}).catch(()=>{c.value=!1}))})},V=()=>{let l={trialFlag:"N",packageId:null,serviceEndTime:""};s.value.tenantLinkList.push(l)},B=async l=>{await W.modal.confirm("\u60A8\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u6570\u636E?")==="confirm"&&p.value.remove(l);const i=p.value.getTableData().tableData;s.value.tenantLinkList=i};return(l,n)=>{const i=X,m=Q,d=Z,C=ee,r=v("vxe-column"),F=te,N=ae,H=v("vxe-switch"),E=ne,R=v("vxe-table"),A=le,$=oe,q=se;return f(),k(q,{width:900,maskClosable:!1,visible:g.visible,"confirm-loading":c.value,forceRender:!0,title:"\u79DF\u6237\u5BA1\u6279","body-style":{paddingBottom:"8px"},"onUpdate:visible":_,onOk:M,onClose:n[2]||(n[2]=a=>_(!1))},{default:t(()=>[e($,{ref_key:"formRef",ref:y,model:s.value,rules:U,layout:"vertical"},{default:t(()=>[e(A,{gutter:20},{default:t(()=>[e(d,{span:12},{default:t(()=>[e(m,{label:"\u79DF\u6237\u751F\u6548\u65F6\u95F4:",name:"activeDate"},{default:t(()=>[e(i,{value:s.value.activeDate,"onUpdate:value":n[0]||(n[0]=a=>s.value.activeDate=a),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u8BF7\u9009\u62E9\u79DF\u6237\u751F\u6548\u65F6\u95F4",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(d,{span:12},{default:t(()=>[e(m,{label:"\u5230\u671F\u65F6\u95F4:",name:"expireDate"},{default:t(()=>[e(i,{value:s.value.expireDate,"onUpdate:value":n[1]||(n[1]=a=>s.value.expireDate=a),"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u8BF7\u9009\u62E9\u5230\u671F\u65F6\u95F4",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(d,{span:24},{default:t(()=>n[3]||(n[3]=[h("div",{class:"company"},"\u5F00\u901A\u529F\u80FD",-1)])),_:1,__:[3]}),e(d,{span:24},{default:t(()=>[h("div",re,[e(C,{type:"primary",class:"border-radius",onClick:V},{default:t(()=>n[4]||(n[4]=[D("+ \u6DFB\u52A0\u529F\u80FD")])),_:1,__:[4]})]),e(R,{border:"",style:{"margin-bottom":"20px"},"show-overflow":"",data:s.value.tenantLinkList,"row-config":{useKey:!0},"column-config":{resizable:!0},height:"300",ref_key:"xTableRef",ref:p},{default:t(()=>[e(r,{type:"seq",width:"60",title:"\u5E8F\u53F7",align:"center"}),e(r,{field:"packageId",title:"\u529F\u80FD\u5305",align:"center"},{default:t(({row:a})=>[e(N,{value:a.packageId,"onUpdate:value":o=>a.packageId=o,style:{width:"100%","border-radius":"4px","text-align":"left"},placeholder:"\u8BF7\u9009\u62E9\u529F\u80FD\u5305"},{default:t(()=>[(f(!0),O(P,null,S(x.value,o=>(f(),k(F,{value:o.packageId,key:o.packageId},{default:t(()=>[D(J(o.packageName),1)]),_:2},1032,["value"]))),128))]),_:2},1032,["value","onUpdate:value"])]),_:1}),e(r,{field:"serviceEndTime",title:"\u5230\u671F\u65F6\u95F4",width:"200",align:"center"},{default:t(({row:a})=>[e(i,{value:a.serviceEndTime,"onUpdate:value":o=>a.serviceEndTime=o,"value-format":"YYYY-MM-DD HH:mm:ss",placeholder:"\u8BF7\u9009\u62E9\u5230\u671F\u65F6\u95F4",style:{width:"100%","border-radius":"4px"}},null,8,["value","onUpdate:value"])]),_:1}),e(r,{field:"trialFlag",title:"\u662F\u5426\u8BD5\u7528",width:"100",align:"center"},{default:t(({row:a})=>[e(H,{modelValue:a.trialFlag,"onUpdate:modelValue":o=>a.trialFlag=o,"open-value":"Y","close-value":"N"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(r,{title:"\u64CD\u4F5C",width:"100",align:"center"},{default:t(({row:a})=>[e(E,{iconClass:"icon-opt-shanchu","font-size":"24px",title:"\u5220\u9664",color:"#60666b",onClick:o=>B(a)},null,8,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["visible","confirm-loading"])}}},ve=G(ue,[["__scopeId","data-v-ebb55fa9"]]);export{ve as default};
