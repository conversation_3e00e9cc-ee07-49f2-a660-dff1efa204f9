/**
 * 会员组件统一导出
 */

/**
 * 会员组件统一导出
 * 
 * 使用方式：
 * import { MemberSearch, MemberSelector } from './member'
 * 或
 * import MemberComponents from './member'
 * const { MemberSearch } = MemberComponents
 */

// 导入所有会员相关组件
import MemberPanel from './MemberPanel.vue'
import MemberSearch from './MemberSearch.vue'
import MemberSelector from './MemberSelector.vue'
import MemberInfo from './MemberInfo.vue'
import MemberDiscount from './MemberDiscount.vue'
import MemberSelectDialog from './MemberSelectDialog.vue'

// 统一导出
export {
  MemberPanel,
  MemberSearch,
  MemberSelector,
  MemberInfo,
  MemberDiscount,
  MemberSelectDialog
}

// 默认导出（用于按需导入）
export default {
  MemberPanel,
  MemberSearch,
  MemberSelector,
  MemberInfo,
  MemberDiscount,
  MemberSelectDialog
}