import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { usePosStore, useCartStore, useMemberStore, usePaymentStore, useOrderStore } from '../index'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    loading: vi.fn(),
    destroy: vi.fn()
  }
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('POS Store Integration', () => {
  let pinia
  let posStore
  let cartStore
  let memberStore
  let paymentStore
  let orderStore
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    posStore = usePosStore()
    cartStore = useCartStore()
    memberStore = useMemberStore()
    paymentStore = usePaymentStore()
    orderStore = useOrderStore()
    
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Store Initialization', () => {
    it('应该正确初始化所有子模块', () => {
      expect(posStore.cartStore).toBeDefined()
      expect(posStore.memberStore).toBeDefined()
      expect(posStore.paymentStore).toBeDefined()
      expect(posStore.orderStore).toBeDefined()
    })

    it('应该正确初始化全局状态', () => {
      expect(posStore.categories).toBeDefined()
      expect(posStore.selectedCategory).toBeDefined()
      expect(posStore.products).toBeDefined()
      expect(posStore.searchKeyword).toBeDefined()
      expect(posStore.isInitialized).toBeDefined()
      expect(posStore.isLoading).toBeDefined()
    })

    it('应该正确初始化计算属性', () => {
      expect(posStore.canCheckout).toBeDefined()
      expect(posStore.orderSummary).toBeDefined()
      expect(posStore.systemStatus).toBeDefined()
    })
  })

  describe('Cart Store', () => {
    it('应该能够添加商品到购物车', () => {
      const product = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      
      const result = cartStore.addToCart(product, 2)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems.length).toBe(1)
      expect(cartStore.cartItems[0].quantity).toBe(2)
      expect(cartStore.cartItems[0].totalPrice).toBe(20.00)
      expect(cartStore.totalAmount).toBe(20.00)
    })

    it('应该能够更新商品数量', () => {
      const product = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      
      cartStore.addToCart(product, 1)
      cartStore.updateQuantity(1, 3)
      
      expect(cartStore.cartItems[0].quantity).toBe(3)
      expect(cartStore.cartItems[0].totalPrice).toBe(30.00)
      expect(cartStore.totalAmount).toBe(30.00)
    })

    it('应该能够移除商品', () => {
      const product = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      
      cartStore.addToCart(product, 1)
      expect(cartStore.cartItems.length).toBe(1)
      
      cartStore.removeFromCart(1)
      expect(cartStore.cartItems.length).toBe(0)
      expect(cartStore.totalAmount).toBe(0)
    })

    it('应该能够批量更新商品数量', () => {
      const products = [
        { productId: 1, productName: '商品1', price: 10.00, stock: 100 },
        { productId: 2, productName: '商品2', price: 20.00, stock: 100 }
      ]
      
      products.forEach(product => cartStore.addToCart(product, 1))
      
      const updates = [
        { productId: 1, quantity: 3 },
        { productId: 2, quantity: 2 }
      ]
      
      const result = cartStore.batchUpdateQuantities(updates)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems[0].quantity).toBe(3)
      expect(cartStore.cartItems[1].quantity).toBe(2)
      expect(cartStore.totalAmount).toBe(70.00) // 3*10 + 2*20
    })
  })

  describe('Member Store', () => {
    it('应该能够设置会员信息', () => {
      const member = {
        memberId: 1,
        memberName: '测试会员',
        memberCode: 'M001',
        discountRate: 10 // 9折
      }
      
      const result = memberStore.setCurrentMember(member)
      
      expect(result).toBe(true)
      expect(memberStore.currentMember).toEqual(expect.objectContaining(member))
      expect(memberStore.memberDiscountRate).toBe(10)
      expect(memberStore.hasMember).toBe(true)
    })

    it('应该能够设置积分抵扣', () => {
      const member = {
        memberId: 1,
        memberName: '测试会员',
        points: 1000
      }
      
      memberStore.setCurrentMember(member)
      
      const result = memberStore.setPointsDeduction(500, 5.00) // 500积分抵扣5元
      
      expect(result).toBe(true)
      expect(memberStore.pointsDeductionAmount).toBe(5.00)
    })

    it('应该能够计算会员折扣', () => {
      const member = {
        memberId: 1,
        memberName: '测试会员',
        discountRate: 10 // 9折
      }
      
      memberStore.setCurrentMember(member)
      
      const discountResult = memberStore.applyMemberDiscount(100)
      
      expect(discountResult.success).toBe(true)
      expect(discountResult.discountAmount).toBe(10) // 100 * 0.1
      expect(discountResult.finalAmount).toBe(90)
    })
  })

  describe('Payment Store', () => {
    it('应该能够设置支付方式', () => {
      const result = paymentStore.setPaymentMethod('CASH')
      
      expect(result).toBe(true)
      expect(paymentStore.selectedPaymentMethod).toBe('CASH')
      expect(paymentStore.isCashPayment).toBe(true)
    })

    it('应该能够设置现金实收金额', () => {
      paymentStore.setPaymentMethod('CASH')
      
      const result = paymentStore.setReceivedAmount(100, 80) // 实收100，应付80
      
      expect(result).toBe(true)
      expect(paymentStore.receivedAmount).toBe(100)
      expect(paymentStore.changeAmount).toBe(20) // 找零20
    })

    it('应该能够开始支付流程', () => {
      paymentStore.setPaymentMethod('ALIPAY')
      
      const result = paymentStore.startPayment({ amount: 100 })
      
      expect(result).toBe(true)
      expect(paymentStore.paymentStatus).toBe('processing')
      expect(paymentStore.isPaymentProcessing).toBe(true)
    })

    it('应该能够处理支付成功', () => {
      paymentStore.setPaymentMethod('ALIPAY')
      paymentStore.startPayment({ amount: 100 })
      
      paymentStore.paymentSuccess({ 
        transactionId: 'TXN123',
        amount: 100
      })
      
      expect(paymentStore.paymentStatus).toBe('success')
      expect(paymentStore.isPaymentSuccess).toBe(true)
      expect(paymentStore.paymentResult.transactionId).toBe('TXN123')
    })
  })

  describe('Order Store', () => {
    it('应该能够创建订单', () => {
      const orderData = {
        cartItems: [
          { productId: 1, productName: '商品1', quantity: 2, totalPrice: 20 }
        ],
        totalAmount: 20,
        finalAmount: 20
      }
      
      const order = orderStore.createOrder(orderData)
      
      expect(order).toBeTruthy()
      expect(order.orderNo).toBeTruthy()
      expect(order.status).toBe('pending')
      expect(orderStore.currentOrder).toBe(order)
    })

    it('应该能够挂起订单', () => {
      const orderData = {
        cartItems: [
          { productId: 1, productName: '商品1', quantity: 2, totalPrice: 20 }
        ],
        totalAmount: 20,
        finalAmount: 20
      }
      
      const result = orderStore.suspendCurrentOrder(orderData, '测试挂单')
      
      expect(result).toBe(true)
      expect(orderStore.suspendedOrders.length).toBe(1)
      expect(orderStore.suspendedOrders[0].remark).toBe('测试挂单')
    })

    it('应该能够恢复挂单', () => {
      const orderData = {
        cartItems: [
          { productId: 1, productName: '商品1', quantity: 2, totalPrice: 20 }
        ],
        totalAmount: 20,
        finalAmount: 20
      }
      
      orderStore.suspendCurrentOrder(orderData, '测试挂单')
      const suspendId = orderStore.suspendedOrders[0].suspendId
      
      const restoredData = orderStore.resumeSuspendedOrder(suspendId)
      
      expect(restoredData).toBeTruthy()
      expect(restoredData.cartItems).toEqual(orderData.cartItems)
      expect(orderStore.suspendedOrders.length).toBe(0)
    })

    it('应该能够完成订单', () => {
      const orderData = {
        cartItems: [
          { productId: 1, productName: '商品1', quantity: 2, totalPrice: 20 }
        ],
        totalAmount: 20,
        finalAmount: 20
      }
      
      orderStore.createOrder(orderData)
      
      const result = orderStore.completeOrder({ 
        paymentMethod: 'CASH',
        amount: 20
      })
      
      expect(result).toBe(true)
      expect(orderStore.currentOrder).toBe(null)
      expect(orderStore.orderHistory.length).toBe(1)
      expect(orderStore.orderHistory[0].status).toBe('completed')
    })
  })

  describe('Integrated Workflows', () => {
    it('应该能够完成完整的购买流程', async () => {
      // 1. 添加商品到购物车
      const product = {
        productId: 1,
        productName: '测试商品',
        price: 50.00,
        stock: 100
      }
      
      const addResult = await posStore.addToCart(product, 2)
      expect(addResult).toBe(true)
      expect(posStore.orderSummary.finalAmount).toBe('100.00')
      
      // 2. 设置会员（应用折扣）
      const member = {
        memberId: 1,
        memberName: '测试会员',
        discountRate: 10 // 9折
      }
      
      const memberResult = await posStore.setMember(member)
      expect(memberResult).toBe(true)
      expect(parseFloat(posStore.orderSummary.finalAmount)).toBe(90.00) // 100 * 0.9
      
      // 3. 开始结算
      const checkoutResult = await posStore.startCheckout('CASH')
      expect(checkoutResult).toBe(true)
      expect(paymentStore.isPaymentProcessing).toBe(true)
      
      // 4. 完成支付
      const completeResult = await posStore.completeOrder({
        paymentMethod: 'CASH',
        amount: 90.00,
        receivedAmount: 100.00,
        changeAmount: 10.00
      })
      
      expect(completeResult).toBe(true)
      expect(cartStore.cartItems.length).toBe(0)
      expect(memberStore.currentMember).toBe(null)
      expect(paymentStore.paymentStatus).toBe('idle')
      expect(orderStore.orderHistory.length).toBe(1)
    })

    it('应该能够挂起和恢复订单', async () => {
      // 1. 添加商品和会员
      const product = {
        productId: 1,
        productName: '测试商品',
        price: 30.00,
        stock: 100
      }
      
      await posStore.addToCart(product, 1)
      
      const member = {
        memberId: 1,
        memberName: '测试会员',
        discountRate: 20 // 8折
      }
      
      await posStore.setMember(member)
      
      // 2. 挂起订单
      const suspendResult = await posStore.suspendOrder('临时挂单')
      expect(suspendResult).toBe(true)
      expect(cartStore.cartItems.length).toBe(0)
      expect(memberStore.currentMember).toBe(null)
      expect(orderStore.suspendedOrders.length).toBe(1)
      
      // 3. 恢复订单
      const suspendId = orderStore.suspendedOrders[0].suspendId
      const resumeResult = await posStore.resumeOrder(suspendId)
      
      expect(resumeResult).toBe(true)
      expect(cartStore.cartItems.length).toBe(1)
      expect(cartStore.cartItems[0].productName).toBe('测试商品')
      expect(memberStore.currentMember.memberName).toBe('测试会员')
      expect(orderStore.suspendedOrders.length).toBe(0)
    })
  })

  describe('Batch Updates', () => {
    it('应该能够执行批量更新', async () => {
      posStore.startBatchUpdate()
      
      // 添加多个更新操作到队列
      posStore.addUpdateToQueue(() => {
        cartStore.addToCart({ productId: 1, productName: '商品1', price: 10, stock: 100 }, 1)
      }, '添加商品1')
      
      posStore.addUpdateToQueue(() => {
        cartStore.addToCart({ productId: 2, productName: '商品2', price: 20, stock: 100 }, 2)
      }, '添加商品2')
      
      posStore.addUpdateToQueue(() => {
        memberStore.setCurrentMember({
          memberId: 1,
          memberName: '测试会员',
          discountRate: 10
        })
      }, '设置会员')
      
      // 执行批量更新
      await posStore.executeBatchUpdate()
      
      expect(cartStore.cartItems.length).toBe(2)
      expect(cartStore.totalAmount).toBe(50) // 10*1 + 20*2
      expect(memberStore.hasMember).toBe(true)
      expect(posStore.isBatchUpdating).toBe(false)
    })
  })

  describe('State Persistence', () => {
    it('应该能够保存状态到本地存储', () => {
      // 添加一些数据
      cartStore.addToCart({ productId: 1, productName: '商品1', price: 10, stock: 100 }, 1)
      memberStore.setCurrentMember({ memberId: 1, memberName: '会员1' })
      
      posStore.saveStateToLocal()
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'pos_current_state',
        expect.any(String)
      )
    })

    it('应该能够从本地存储恢复状态', () => {
      const mockState = {
        cart: {
          cartItems: [{ productId: 1, productName: '商品1', quantity: 1 }],
          totalAmount: 10
        },
        member: {
          currentMember: { memberId: 1, memberName: '会员1' }
        },
        timestamp: Date.now()
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockState))
      
      posStore.loadStateFromLocal()
      
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('pos_current_state')
    })
  })

  describe('System Management', () => {
    it('应该能够重置所有状态', async () => {
      // 添加一些数据
      cartStore.addToCart({ productId: 1, productName: '商品1', price: 10, stock: 100 }, 1)
      memberStore.setCurrentMember({ memberId: 1, memberName: '会员1' })
      paymentStore.setPaymentMethod('CASH')
      
      expect(cartStore.cartItems.length).toBe(1)
      expect(memberStore.hasMember).toBe(true)
      expect(paymentStore.selectedPaymentMethod).toBe('CASH')
      
      // 重置状态
      posStore.resetAllState()
      
      expect(cartStore.cartItems.length).toBe(0)
      expect(memberStore.hasMember).toBe(false)
      expect(paymentStore.selectedPaymentMethod).toBe('')
    })

    it('应该能够获取系统状态概览', () => {
      cartStore.addToCart({ productId: 1, productName: '商品1', price: 10, stock: 100 }, 2)
      
      const status = posStore.systemStatus
      
      expect(status.cartItems).toBe(2)
      expect(status.suspendedOrders).toBe(0)
      expect(status.paymentStatus).toBe('idle')
      expect(status.initialized).toBeDefined()
    })
  })
})