<!--
  购物车主组件
  
  重构后的购物车组件，拆分为多个子组件
  使用useCart组合式函数管理业务逻辑
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="shopping-cart">
    <!-- 购物车头部 -->
    <CartHeader
      v-if="!isEmpty"
      :item-count="cartSummary.itemCount"
      :is-empty="isEmpty"
      :loading="loading"
      @clear-cart="handleClearCart"
    />

    <!-- 购物车内容区域 -->
    <div class="cart-content">
      <!-- 商品列表 -->
      <div v-if="!isEmpty" class="cart-items">
        <CartItem
          v-for="item in items"
          :key="item.id"
          :item="item"
          :loading="loading"
          @update-quantity="handleUpdateQuantity"
          @remove-item="handleRemoveItem"
        />
      </div>

      <!-- 空购物车状态 -->
      <div v-else class="empty-cart">
        <a-empty 
          description="购物车为空"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        >
          <p class="empty-tip">请选择商品添加到购物车</p>
        </a-empty>
      </div>
    </div>

    <!-- 购物车汇总 -->
    <CartSummary
      v-if="!isEmpty"
      :summary="cartSummary"
      :total="cartTotal"
      :member="props.currentMember"
      :can-checkout="canCheckout"
      :loading="loading"
      @checkout="handleCheckout"
      @suspend-order="handleSuspendOrder"
      @select-member="handleSelectMember"
      @clear-member="handleClearMember"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Empty, Modal } from 'ant-design-vue'
import { useCart } from '../../composables/useCart'
import { usePosStore } from '@/stores/pos'
import CartHeader from './CartHeader.vue'
import CartItem from './CartItem.vue'
import CartSummary from './CartSummary.vue'

// 定义组件名称
defineOptions({
  name: 'ShoppingCart'
})

// 定义props
const props = defineProps({
  // 当前会员信息
  currentMember: {
    type: Object,
    default: null
  }
})

// 定义emits
const emit = defineEmits([
  'checkout',
  'suspend-order',
  'item-change',
  'memberChange',
  'memberSelect',
  'memberClear'
])

// 使用store
const store = usePosStore()

// 使用购物车组合式函数
const {
  items,
  cartTotal,
  cartSummary,
  isEmpty,
  canCheckout,
  loading,
  updateQuantity,
  removeItem,
  clearCart
} = useCart()

// ==================== 事件处理 ====================

/**
 * 处理更新商品数量
 */
const handleUpdateQuantity = async (itemId, quantity) => {
  const success = await updateQuantity(itemId, quantity)
  if (success) {
    emit('item-change', {
      type: 'update',
      itemId,
      quantity
    })
  }
}

/**
 * 处理移除商品
 */
const handleRemoveItem = async (itemId) => {
  const success = await removeItem(itemId)
  if (success) {
    emit('item-change', {
      type: 'remove',
      itemId
    })
  }
}

/**
 * 处理清空购物车
 */
const handleClearCart = () => {
  Modal.confirm({
    title: '确认清空购物车',
    content: '确定要清空购物车中的所有商品吗？',
    okText: '确认清空',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      const success = await clearCart()
      if (success) {
        emit('item-change', {
          type: 'clear'
        })
      }
    }
  })
}

/**
 * 处理结账
 */
const handleCheckout = () => {
  if (!canCheckout.value) {
    return
  }
  
  emit('checkout', {
    items: items.value,
    total: cartTotal.value,
    member: props.currentMember
  })
}

/**
 * 处理挂单
 */
const handleSuspendOrder = () => {
  if (isEmpty.value) {
    return
  }
  
  emit('suspend-order', {
    items: items.value,
    total: cartTotal.value,
    member: props.currentMember
  })
}

/**
 * 处理选择会员
 */
const handleSelectMember = () => {
  emit('memberSelect')
}

/**
 * 处理清除会员
 */
const handleClearMember = () => {
  emit('memberClear')
}


</script>

<style scoped>
.shopping-cart {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.cart-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cart-items {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.empty-cart {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-tip {
  color: #8c8c8c;
  font-size: 14px;
  margin-top: 8px;
}

/* 滚动条样式 */
.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.cart-items::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>