<template>
  <div v-if="isDevelopment && showMonitor" class="performance-monitor">
    <!-- 监控面板切换按钮 -->
    <div class="monitor-toggle" @click="togglePanel">
      <div class="toggle-icon">
        <dashboard-outlined />
      </div>
      <div class="toggle-text">性能监控</div>
      <div class="status-indicator" :class="statusClass"></div>
    </div>
    
    <!-- 监控面板 -->
    <div v-if="showPanel" class="monitor-panel">
      <div class="panel-header">
        <h4>性能监控面板</h4>
        <div class="panel-actions">
          <a-button size="small" @click="refreshData">
            <template #icon>
              <reload-outlined />
            </template>
            刷新
          </a-button>
          <a-button size="small" @click="exportData">
            <template #icon>
              <download-outlined />
            </template>
            导出
          </a-button>
          <a-button size="small" @click="clearData">
            <template #icon>
              <clear-outlined />
            </template>
            清除
          </a-button>
          <a-button size="small" type="text" @click="closePanel">
            <template #icon>
              <close-outlined />
            </template>
          </a-button>
        </div>
      </div>
      
      <!-- 性能概览 -->
      <div class="performance-overview">
        <div class="metric-card">
          <div class="metric-title">平均渲染时间</div>
          <div class="metric-value" :class="getRenderTimeClass()">
            {{ performanceStats.averageRenderTime.toFixed(2) }}ms
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">内存使用</div>
          <div class="metric-value" :class="getMemoryClass()">
            {{ currentMemoryUsage }}MB
          </div>
          <div class="metric-trend" :class="memoryTrendClass">
            {{ memoryTrendText }}
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">用户交互</div>
          <div class="metric-value">
            {{ performanceStats.interactionCount }}
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">慢操作</div>
          <div class="metric-value" :class="getSlowOperationClass()">
            {{ performanceStats.slowOperations }}
          </div>
        </div>
      </div>
      
      <!-- 性能警告 -->
      <div v-if="hasPerformanceIssues" class="performance-warnings">
        <h5>性能警告</h5>
        <div class="warning-list">
          <div 
            v-for="warning in performanceWarnings.slice(0, 5)" 
            :key="warning.id"
            class="warning-item"
            :class="getWarningClass(warning.type)"
          >
            <div class="warning-icon">
              <exclamation-circle-outlined v-if="warning.type === 'slow_render'" />
              <memory-outlined v-else-if="warning.type === 'high_memory_usage'" />
              <api-outlined v-else-if="warning.type === 'slow_api'" />
              <warning-outlined v-else />
            </div>
            <div class="warning-content">
              <div class="warning-message">{{ warning.message }}</div>
              <div class="warning-time">{{ formatTime(warning.timestamp) }}</div>
            </div>
            <div class="warning-actions">
              <a-button 
                size="small" 
                type="text" 
                @click="clearPerformanceWarnings(warning.id)"
              >
                <template #icon>
                  <close-outlined />
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 性能图表 -->
      <div class="performance-charts">
        <a-tabs v-model:activeKey="activeTab" size="small">
          <a-tab-pane key="render" tab="渲染性能">
            <div class="chart-container">
              <canvas ref="renderChart" width="400" height="200"></canvas>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="memory" tab="内存使用">
            <div class="chart-container">
              <canvas ref="memoryChart" width="400" height="200"></canvas>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="api" tab="API性能">
            <div class="chart-container">
              <div class="api-list">
                <div 
                  v-for="api in recentApiCalls" 
                  :key="api.timestamp"
                  class="api-item"
                  :class="getApiClass(api.duration)"
                >
                  <div class="api-name">{{ api.name }}</div>
                  <div class="api-duration">{{ api.duration.toFixed(2) }}ms</div>
                  <div class="api-status" :class="api.status">{{ api.status }}</div>
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      
      <!-- 性能建议 -->
      <div v-if="performanceSuggestions.length > 0" class="performance-suggestions">
        <h5>优化建议</h5>
        <div class="suggestion-list">
          <div 
            v-for="(suggestion, index) in performanceSuggestions" 
            :key="index"
            class="suggestion-item"
            :class="suggestion.priority"
          >
            <div class="suggestion-priority">
              <div class="priority-badge" :class="suggestion.priority">
                {{ getPriorityText(suggestion.priority) }}
              </div>
            </div>
            <div class="suggestion-content">
              <div class="suggestion-message">{{ suggestion.message }}</div>
              <div class="suggestion-details">{{ suggestion.details }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  DashboardOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ClearOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { usePerformanceMonitor } from '../../composables/usePerformanceMonitor'

// 定义组件名称
defineOptions({
  name: 'PerformanceMonitor'
})

// 定义属性
const props = defineProps({
  // 是否显示监控器
  visible: {
    type: Boolean,
    default: true
  },
  // 监控的组件名称
  componentName: {
    type: String,
    default: 'POS'
  },
  // 是否自动开始监控
  autoStart: {
    type: Boolean,
    default: true
  }
})

// 使用性能监控
const {
  isMonitoring,
  performanceData,
  performanceStats,
  performanceWarnings,
  hasPerformanceIssues,
  startMonitoring,
  stopMonitoring,
  clearPerformanceWarnings,
  getPerformanceReport,
  exportPerformanceData,
  getPerformanceSuggestions
} = usePerformanceMonitor(props.componentName)

// 响应式状态
const showMonitor = ref(props.visible)
const showPanel = ref(false)
const activeTab = ref('render')
const renderChart = ref(null)
const memoryChart = ref(null)

// 计算属性
const isDevelopment = computed(() => {
  return process.env.NODE_ENV === 'development'
})

const statusClass = computed(() => {
  if (!isMonitoring.value) return 'status-inactive'
  if (hasPerformanceIssues.value) return 'status-warning'
  return 'status-active'
})

const currentMemoryUsage = computed(() => {
  const memoryData = performanceData.value.memoryUsage
  if (memoryData.length === 0) return 0
  return memoryData[memoryData.length - 1].used
})

const memoryTrendClass = computed(() => {
  return `trend-${performanceStats.value.memoryTrend}`
})

const memoryTrendText = computed(() => {
  const trends = {
    stable: '稳定',
    increasing: '上升',
    decreasing: '下降'
  }
  return trends[performanceStats.value.memoryTrend] || '未知'
})

const recentApiCalls = computed(() => {
  return performanceData.value.apiCalls.slice(-10).reverse()
})

const performanceSuggestions = computed(() => {
  return getPerformanceSuggestions()
})

/**
 * 切换监控面板
 */
const togglePanel = () => {
  showPanel.value = !showPanel.value
  
  if (showPanel.value) {
    nextTick(() => {
      updateCharts()
    })
  }
}

/**
 * 关闭面板
 */
const closePanel = () => {
  showPanel.value = false
}

/**
 * 刷新数据
 */
const refreshData = () => {
  updateCharts()
  message.success('数据已刷新')
}

/**
 * 导出数据
 */
const exportData = () => {
  try {
    const data = exportPerformanceData('json')
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-data-${props.componentName}-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    
    URL.revokeObjectURL(url)
    message.success('性能数据已导出')
  } catch (error) {
    console.error('导出数据失败:', error)
    message.error('导出失败')
  }
}

/**
 * 清除数据
 */
const clearData = () => {
  performanceData.value.renderTimes = []
  performanceData.value.memoryUsage = []
  performanceData.value.apiCalls = []
  performanceData.value.userInteractions = []
  clearPerformanceWarnings()
  
  updateCharts()
  message.success('数据已清除')
}

/**
 * 获取渲染时间样式类
 */
const getRenderTimeClass = () => {
  const avgTime = performanceStats.value.averageRenderTime
  if (avgTime > 50) return 'metric-danger'
  if (avgTime > 16) return 'metric-warning'
  return 'metric-good'
}

/**
 * 获取内存样式类
 */
const getMemoryClass = () => {
  const usage = currentMemoryUsage.value
  if (usage > 100) return 'metric-danger'
  if (usage > 50) return 'metric-warning'
  return 'metric-good'
}

/**
 * 获取慢操作样式类
 */
const getSlowOperationClass = () => {
  const count = performanceStats.value.slowOperations
  if (count > 10) return 'metric-danger'
  if (count > 5) return 'metric-warning'
  return 'metric-good'
}

/**
 * 获取警告样式类
 */
const getWarningClass = (type) => {
  const classes = {
    slow_render: 'warning-render',
    high_memory_usage: 'warning-memory',
    slow_api: 'warning-api'
  }
  return classes[type] || 'warning-default'
}

/**
 * 获取API样式类
 */
const getApiClass = (duration) => {
  if (duration > 1000) return 'api-slow'
  if (duration > 500) return 'api-medium'
  return 'api-fast'
}

/**
 * 获取优先级文本
 */
const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[priority] || '未知'
}

/**
 * 格式化时间
 */
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

/**
 * 更新图表
 */
const updateCharts = () => {
  nextTick(() => {
    updateRenderChart()
    updateMemoryChart()
  })
}

/**
 * 更新渲染性能图表
 */
const updateRenderChart = () => {
  if (!renderChart.value) return
  
  const ctx = renderChart.value.getContext('2d')
  const renderTimes = performanceData.value.renderTimes.slice(-20)
  
  if (renderTimes.length === 0) return
  
  // 清除画布
  ctx.clearRect(0, 0, 400, 200)
  
  // 绘制网格
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1
  
  // 垂直网格线
  for (let i = 0; i <= 10; i++) {
    const x = (i / 10) * 400
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, 200)
    ctx.stroke()
  }
  
  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = (i / 5) * 200
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(400, y)
    ctx.stroke()
  }
  
  // 绘制渲染时间曲线
  if (renderTimes.length > 1) {
    const maxTime = Math.max(...renderTimes.map(r => r.time))\n    const minTime = Math.min(...renderTimes.map(r => r.time))\n    const range = maxTime - minTime || 1\n    \n    ctx.strokeStyle = '#1890ff'\n    ctx.lineWidth = 2\n    ctx.beginPath()\n    \n    renderTimes.forEach((render, index) => {\n      const x = (index / (renderTimes.length - 1)) * 400\n      const y = 200 - ((render.time - minTime) / range) * 180\n      \n      if (index === 0) {\n        ctx.moveTo(x, y)\n      } else {\n        ctx.lineTo(x, y)\n      }\n    })\n    \n    ctx.stroke()\n    \n    // 绘制数据点\n    ctx.fillStyle = '#1890ff'\n    renderTimes.forEach((render, index) => {\n      const x = (index / (renderTimes.length - 1)) * 400\n      const y = 200 - ((render.time - minTime) / range) * 180\n      \n      ctx.beginPath()\n      ctx.arc(x, y, 3, 0, 2 * Math.PI)\n      ctx.fill()\n    })\n  }\n  \n  // 绘制阈值线\n  const thresholdY = 200 - (16 / (Math.max(...renderTimes.map(r => r.time)) || 16)) * 180\n  ctx.strokeStyle = '#ff4d4f'\n  ctx.lineWidth = 1\n  ctx.setLineDash([5, 5])\n  ctx.beginPath()\n  ctx.moveTo(0, thresholdY)\n  ctx.lineTo(400, thresholdY)\n  ctx.stroke()\n  ctx.setLineDash([])\n}\n\n/**\n * 更新内存使用图表\n */\nconst updateMemoryChart = () => {\n  if (!memoryChart.value) return\n  \n  const ctx = memoryChart.value.getContext('2d')\n  const memoryData = performanceData.value.memoryUsage.slice(-20)\n  \n  if (memoryData.length === 0) return\n  \n  // 清除画布\n  ctx.clearRect(0, 0, 400, 200)\n  \n  // 绘制网格\n  ctx.strokeStyle = '#f0f0f0'\n  ctx.lineWidth = 1\n  \n  // 垂直网格线\n  for (let i = 0; i <= 10; i++) {\n    const x = (i / 10) * 400\n    ctx.beginPath()\n    ctx.moveTo(x, 0)\n    ctx.lineTo(x, 200)\n    ctx.stroke()\n  }\n  \n  // 水平网格线\n  for (let i = 0; i <= 5; i++) {\n    const y = (i / 5) * 200\n    ctx.beginPath()\n    ctx.moveTo(0, y)\n    ctx.lineTo(400, y)\n    ctx.stroke()\n  }\n  \n  // 绘制内存使用曲线\n  if (memoryData.length > 1) {\n    const maxMemory = Math.max(...memoryData.map(m => m.used))\n    const minMemory = Math.min(...memoryData.map(m => m.used))\n    const range = maxMemory - minMemory || 1\n    \n    // 已使用内存\n    ctx.strokeStyle = '#52c41a'\n    ctx.lineWidth = 2\n    ctx.beginPath()\n    \n    memoryData.forEach((memory, index) => {\n      const x = (index / (memoryData.length - 1)) * 400\n      const y = 200 - ((memory.used - minMemory) / range) * 180\n      \n      if (index === 0) {\n        ctx.moveTo(x, y)\n      } else {\n        ctx.lineTo(x, y)\n      }\n    })\n    \n    ctx.stroke()\n    \n    // 总内存\n    ctx.strokeStyle = '#faad14'\n    ctx.lineWidth = 1\n    ctx.beginPath()\n    \n    memoryData.forEach((memory, index) => {\n      const x = (index / (memoryData.length - 1)) * 400\n      const y = 200 - ((memory.total - minMemory) / range) * 180\n      \n      if (index === 0) {\n        ctx.moveTo(x, y)\n      } else {\n        ctx.lineTo(x, y)\n      }\n    })\n    \n    ctx.stroke()\n  }\n}\n\n// 监听数据变化，自动更新图表\nwatch(\n  () => [performanceData.value.renderTimes.length, performanceData.value.memoryUsage.length],\n  () => {\n    if (showPanel.value) {\n      updateCharts()\n    }\n  }\n)\n\n// 生命周期\nonMounted(() => {\n  if (props.autoStart && isDevelopment.value) {\n    startMonitoring()\n  }\n})\n\nonUnmounted(() => {\n  stopMonitoring()\n})\n</script>\n\n<style scoped>\n.performance-monitor {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  z-index: 9999;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.monitor-toggle {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  background: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s;\n}\n\n.monitor-toggle:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.toggle-icon {\n  margin-right: 6px;\n  font-size: 14px;\n  color: #1890ff;\n}\n\n.toggle-text {\n  font-size: 12px;\n  color: #666;\n  margin-right: 8px;\n}\n\n.status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  transition: background-color 0.3s;\n}\n\n.status-active {\n  background-color: #52c41a;\n}\n\n.status-warning {\n  background-color: #faad14;\n}\n\n.status-inactive {\n  background-color: #d9d9d9;\n}\n\n.monitor-panel {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  width: 500px;\n  max-height: 600px;\n  margin-top: 8px;\n  background: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fafafa;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.panel-actions {\n  display: flex;\n  gap: 4px;\n}\n\n.performance-overview {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n  padding: 16px;\n}\n\n.metric-card {\n  padding: 12px;\n  background: #fafafa;\n  border-radius: 6px;\n  text-align: center;\n}\n\n.metric-title {\n  font-size: 12px;\n  color: #8c8c8c;\n  margin-bottom: 4px;\n}\n\n.metric-value {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 4px;\n}\n\n.metric-good {\n  color: #52c41a;\n}\n\n.metric-warning {\n  color: #faad14;\n}\n\n.metric-danger {\n  color: #ff4d4f;\n}\n\n.metric-trend {\n  font-size: 11px;\n  padding: 2px 6px;\n  border-radius: 10px;\n  display: inline-block;\n}\n\n.trend-stable {\n  background: #f6ffed;\n  color: #52c41a;\n}\n\n.trend-increasing {\n  background: #fff2e8;\n  color: #fa8c16;\n}\n\n.trend-decreasing {\n  background: #f6ffed;\n  color: #52c41a;\n}\n\n.performance-warnings {\n  padding: 0 16px 16px;\n}\n\n.performance-warnings h5 {\n  margin: 0 0 8px 0;\n  font-size: 13px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.warning-list {\n  max-height: 120px;\n  overflow-y: auto;\n}\n\n.warning-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 8px;\n  margin-bottom: 4px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.warning-render {\n  background: #fff2e8;\n  border-left: 3px solid #fa8c16;\n}\n\n.warning-memory {\n  background: #fff1f0;\n  border-left: 3px solid #ff4d4f;\n}\n\n.warning-api {\n  background: #f6ffed;\n  border-left: 3px solid #52c41a;\n}\n\n.warning-default {\n  background: #f0f0f0;\n  border-left: 3px solid #d9d9d9;\n}\n\n.warning-icon {\n  margin-right: 8px;\n  margin-top: 1px;\n}\n\n.warning-content {\n  flex: 1;\n}\n\n.warning-message {\n  color: #262626;\n  margin-bottom: 2px;\n}\n\n.warning-time {\n  color: #8c8c8c;\n  font-size: 11px;\n}\n\n.warning-actions {\n  margin-left: 8px;\n}\n\n.performance-charts {\n  padding: 0 16px 16px;\n}\n\n.chart-container {\n  height: 220px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.api-list {\n  width: 100%;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n.api-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 8px;\n  margin-bottom: 4px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.api-fast {\n  background: #f6ffed;\n  border-left: 3px solid #52c41a;\n}\n\n.api-medium {\n  background: #fff7e6;\n  border-left: 3px solid #faad14;\n}\n\n.api-slow {\n  background: #fff2f0;\n  border-left: 3px solid #ff4d4f;\n}\n\n.api-name {\n  flex: 1;\n  color: #262626;\n}\n\n.api-duration {\n  margin: 0 8px;\n  font-weight: 600;\n}\n\n.api-status {\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n}\n\n.api-status.success {\n  background: #f6ffed;\n  color: #52c41a;\n}\n\n.api-status.error {\n  background: #fff2f0;\n  color: #ff4d4f;\n}\n\n.performance-suggestions {\n  padding: 0 16px 16px;\n}\n\n.performance-suggestions h5 {\n  margin: 0 0 8px 0;\n  font-size: 13px;\n  font-weight: 600;\n  color: #262626;\n}\n\n.suggestion-list {\n  max-height: 120px;\n  overflow-y: auto;\n}\n\n.suggestion-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 8px;\n  margin-bottom: 4px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.suggestion-item.high {\n  background: #fff2f0;\n  border-left: 3px solid #ff4d4f;\n}\n\n.suggestion-item.medium {\n  background: #fff7e6;\n  border-left: 3px solid #faad14;\n}\n\n.suggestion-item.low {\n  background: #f6ffed;\n  border-left: 3px solid #52c41a;\n}\n\n.suggestion-priority {\n  margin-right: 8px;\n}\n\n.priority-badge {\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n}\n\n.priority-badge.high {\n  background: #ff4d4f;\n  color: #fff;\n}\n\n.priority-badge.medium {\n  background: #faad14;\n  color: #fff;\n}\n\n.priority-badge.low {\n  background: #52c41a;\n  color: #fff;\n}\n\n.suggestion-content {\n  flex: 1;\n}\n\n.suggestion-message {\n  color: #262626;\n  margin-bottom: 2px;\n}\n\n.suggestion-details {\n  color: #8c8c8c;\n  font-size: 11px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .monitor-panel {\n    width: 90vw;\n    right: -45vw;\n    margin-left: 50%;\n  }\n  \n  .performance-overview {\n    grid-template-columns: 1fr;\n  }\n}\n</style>