<!--
  扫码支付组件
  
  处理微信、支付宝等扫码支付方式
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="qr-payment">
    <!-- 二维码显示区域 -->
    <div class="qr-container">
      <!-- 二维码 -->
      <div class="qr-code-wrapper">
        <div class="qr-code" v-if="qrCodeUrl && !qrExpired">
          <img :src="qrCodeUrl" :alt="`${paymentMethodName}支付二维码`" />
          
          <!-- 支付方式标识 -->
          <div class="payment-brand">
            <icon-font :iconClass="paymentMethodIcon" />
          </div>
          
          <!-- 倒计时 -->
          <div class="countdown" v-if="countdown > 0">
            <div class="countdown-circle">
              <span>{{ countdown }}</span>
            </div>
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div class="qr-loading" v-else-if="loading">
          <a-spin size="large" />
          <p>正在生成支付二维码...</p>
        </div>
        
        <!-- 二维码过期 -->
        <div class="qr-expired" v-else-if="qrExpired">
          <div class="expired-icon">
            <icon-font iconClass="icon-expired" />
          </div>\n          <p>二维码已过期</p>\n          <a-button type="primary" @click="refreshQrCode" :loading="loading">\n            重新生成\n          </a-button>\n        </div>\n        \n        <!-- 生成失败 -->\n        <div class="qr-error" v-else-if="error">\n          <div class="error-icon">\n            <icon-font iconClass="icon-error" />\n          </div>\n          <p>{{ error }}</p>\n          <a-button @click="refreshQrCode" :loading="loading">\n            重试\n          </a-button>\n        </div>\n      </div>\n      \n      <!-- 支付提示 -->\n      <div class="qr-tips">\n        <div class="tip-main">\n          请使用{{ paymentMethodName }}扫码支付\n        </div>\n        <div class="tip-amount">\n          支付金额: <span class="amount">{{ formatPrice(paymentAmount) }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 支付状态 -->\n    <div class="payment-status" v-if="paymentStatus !== 'idle'">\n      <div class="status-content">\n        <!-- 处理中 -->\n        <template v-if="paymentStatus === 'processing'">\n          <a-spin size="small" />\n          <span class="status-text">等待支付...</span>\n          <div class="status-progress">\n            <a-progress \n              :percent="progressPercent" \n              :show-info="false" \n              size="small"\n              stroke-color="#1890ff"\n            />\n          </div>\n        </template>\n        \n        <!-- 成功 -->\n        <template v-else-if="paymentStatus === 'success'">\n          <check-circle-outlined class="success-icon" />\n          <span class="status-text success">支付成功</span>\n        </template>\n        \n        <!-- 失败 -->\n        <template v-else-if="paymentStatus === 'failed'">\n          <close-circle-outlined class="error-icon" />\n          <span class="status-text error">支付失败</span>\n        </template>\n        \n        <!-- 取消 -->\n        <template v-else-if="paymentStatus === 'cancelled'">\n          <info-circle-outlined class="info-icon" />\n          <span class="status-text info">支付已取消</span>\n        </template>\n      </div>\n    </div>\n    \n    <!-- 操作按钮 -->\n    <div class="qr-actions">\n      <a-button \n        @click="refreshQrCode" \n        :loading="loading"\n        :disabled="paymentStatus === 'processing'"\n        class="action-btn"\n      >\n        <icon-font iconClass="icon-refresh" />\n        刷新二维码\n      </a-button>\n      \n      <a-button \n        type="primary"\n        @click="checkPaymentStatus"\n        :loading="checking"\n        :disabled="!qrCodeUrl || qrExpired"\n        class="action-btn"\n      >\n        <icon-font iconClass="icon-check" />\n        检查支付状态\n      </a-button>\n    </div>\n    \n    <!-- 支付说明 -->\n    <div class="payment-instructions">\n      <div class="instruction-title">\n        <icon-font iconClass="icon-help" />\n        <span>支付说明</span>\n      </div>\n      \n      <div class="instruction-list">\n        <div class="instruction-item">\n          <icon-font iconClass="icon-step1" />\n          <span>打开{{ paymentMethodName }}APP</span>\n        </div>\n        <div class="instruction-item">\n          <icon-font iconClass="icon-step2" />\n          <span>扫描上方二维码</span>\n        </div>\n        <div class="instruction-item">\n          <icon-font iconClass="icon-step3" />\n          <span>确认支付金额</span>\n        </div>\n        <div class="instruction-item">\n          <icon-font iconClass="icon-step4" />\n          <span>完成支付</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue'\nimport { message } from 'ant-design-vue'\nimport { \n  CheckCircleOutlined, \n  CloseCircleOutlined, \n  InfoCircleOutlined \n} from '@ant-design/icons-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\nimport { AmountFormatter } from '../../utils/formatter'\nimport { PAYMENT_METHODS, QR_CODE_CONFIG } from '../../utils/constants'\n\n// 定义组件名称\ndefineOptions({\n  name: 'QrPayment'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 支付方式\n  paymentMethod: {\n    type: String,\n    required: true,\n    validator: (value) => ['WECHAT', 'ALIPAY'].includes(value)\n  },\n  // 支付金额\n  paymentAmount: {\n    type: Number,\n    required: true\n  },\n  // 订单信息\n  orderInfo: {\n    type: Object,\n    default: () => ({})\n  },\n  // 自动刷新间隔（秒）\n  refreshInterval: {\n    type: Number,\n    default: 3\n  },\n  // 二维码有效期（秒）\n  qrExpireTime: {\n    type: Number,\n    default: 300\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'payment-success',\n  'payment-failed',\n  'payment-cancelled',\n  'qr-generated',\n  'qr-expired'\n])\n\n// 响应式状态\nconst qrCodeUrl = ref('')\nconst paymentStatus = ref('idle') // idle, processing, success, failed, cancelled\nconst loading = ref(false)\nconst checking = ref(false)\nconst error = ref('')\nconst countdown = ref(0)\nconst qrExpired = ref(false)\nconst progressPercent = ref(0)\n\n// 定时器\nconst statusTimer = ref(null)\nconst countdownTimer = ref(null)\nconst progressTimer = ref(null)\n\n// ==================== 计算属性 ====================\n\n/**\n * 支付方式名称\n */\nconst paymentMethodName = computed(() => {\n  return PAYMENT_METHODS[props.paymentMethod]?.label || props.paymentMethod\n})\n\n/**\n * 支付方式图标\n */\nconst paymentMethodIcon = computed(() => {\n  return PAYMENT_METHODS[props.paymentMethod]?.icon || 'icon-qr'\n})\n\n// ==================== 方法 ====================\n\n/**\n * 格式化价格显示\n * @param {number} price - 价格\n * @returns {string} 格式化后的价格\n */\nconst formatPrice = (price) => {\n  return AmountFormatter.formatCurrency(price || 0)\n}\n\n/**\n * 生成二维码\n */\nconst generateQrCode = async () => {\n  try {\n    loading.value = true\n    error.value = ''\n    qrExpired.value = false\n    \n    // 构建支付参数\n    const paymentData = {\n      paymentMethod: props.paymentMethod,\n      amount: props.paymentAmount,\n      orderInfo: props.orderInfo,\n      timestamp: Date.now()\n    }\n    \n    // 调用API生成二维码\n    // 这里应该调用实际的后端API\n    // const response = await PaymentApi.generateQrCode(paymentData)\n    \n    // 模拟API调用\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    \n    // 模拟生成二维码URL\n    const qrData = encodeURIComponent(JSON.stringify(paymentData))\n    qrCodeUrl.value = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${qrData}`\n    \n    // 开始倒计时\n    startCountdown()\n    \n    // 开始轮询支付状态\n    startStatusPolling()\n    \n    emit('qr-generated', {\n      qrCodeUrl: qrCodeUrl.value,\n      paymentMethod: props.paymentMethod\n    })\n    \n  } catch (err) {\n    console.error('生成二维码失败:', err)\n    error.value = err.message || '生成二维码失败，请重试'\n    message.error('生成支付二维码失败')\n  } finally {\n    loading.value = false\n  }\n}\n\n/**\n * 刷新二维码\n */\nconst refreshQrCode = () => {\n  stopAllTimers()\n  paymentStatus.value = 'idle'\n  progressPercent.value = 0\n  generateQrCode()\n}\n\n/**\n * 开始倒计时\n */\nconst startCountdown = () => {\n  countdown.value = props.qrExpireTime\n  \n  countdownTimer.value = setInterval(() => {\n    countdown.value--\n    \n    if (countdown.value <= 0) {\n      handleQrExpired()\n    }\n  }, 1000)\n}\n\n/**\n * 处理二维码过期\n */\nconst handleQrExpired = () => {\n  qrExpired.value = true\n  stopAllTimers()\n  \n  if (paymentStatus.value === 'processing') {\n    paymentStatus.value = 'cancelled'\n  }\n  \n  emit('qr-expired')\n  message.warning('二维码已过期，请重新生成')\n}\n\n/**\n * 开始支付状态轮询\n */\nconst startStatusPolling = () => {\n  paymentStatus.value = 'processing'\n  progressPercent.value = 0\n  \n  // 进度条动画\n  progressTimer.value = setInterval(() => {\n    if (progressPercent.value < 95) {\n      progressPercent.value += 1\n    }\n  }, 1000)\n  \n  // 状态轮询\n  statusTimer.value = setInterval(async () => {\n    await checkPaymentStatus()\n  }, props.refreshInterval * 1000)\n}\n\n/**\n * 检查支付状态\n */\nconst checkPaymentStatus = async () => {\n  if (checking.value || qrExpired.value) return\n  \n  try {\n    checking.value = true\n    \n    // 调用API检查支付状态\n    // 这里应该调用实际的后端API\n    // const response = await PaymentApi.checkPaymentStatus(orderInfo)\n    \n    // 模拟API调用\n    await new Promise(resolve => setTimeout(resolve, 500))\n    \n    // 模拟支付状态\n    const random = Math.random()\n    if (random > 0.95) {\n      // 模拟支付成功\n      handlePaymentSuccess()\n    } else if (random < 0.02) {\n      // 模拟支付失败\n      handlePaymentFailed('支付失败，请重试')\n    }\n    \n  } catch (err) {\n    console.error('检查支付状态失败:', err)\n  } finally {\n    checking.value = false\n  }\n}\n\n/**\n * 处理支付成功\n */\nconst handlePaymentSuccess = (result = {}) => {\n  paymentStatus.value = 'success'\n  progressPercent.value = 100\n  stopAllTimers()\n  \n  message.success('支付成功')\n  \n  emit('payment-success', {\n    paymentMethod: props.paymentMethod,\n    amount: props.paymentAmount,\n    result\n  })\n}\n\n/**\n * 处理支付失败\n */\nconst handlePaymentFailed = (errorMsg = '支付失败') => {\n  paymentStatus.value = 'failed'\n  stopAllTimers()\n  \n  message.error(errorMsg)\n  \n  emit('payment-failed', {\n    paymentMethod: props.paymentMethod,\n    error: errorMsg\n  })\n}\n\n/**\n * 处理支付取消\n */\nconst handlePaymentCancelled = () => {\n  paymentStatus.value = 'cancelled'\n  stopAllTimers()\n  \n  emit('payment-cancelled', {\n    paymentMethod: props.paymentMethod\n  })\n}\n\n/**\n * 停止所有定时器\n */\nconst stopAllTimers = () => {\n  if (statusTimer.value) {\n    clearInterval(statusTimer.value)\n    statusTimer.value = null\n  }\n  \n  if (countdownTimer.value) {\n    clearInterval(countdownTimer.value)\n    countdownTimer.value = null\n  }\n  \n  if (progressTimer.value) {\n    clearInterval(progressTimer.value)\n    progressTimer.value = null\n  }\n}\n\n// ==================== 生命周期 ====================\n\n// 组件挂载时生成二维码\nonMounted(() => {\n  generateQrCode()\n})\n\n// 组件卸载时清理定时器\nonUnmounted(() => {\n  stopAllTimers()\n})\n\n// ==================== 监听器 ====================\n\n// 监听支付方式变化，重新生成二维码\nwatch(\n  () => props.paymentMethod,\n  () => {\n    refreshQrCode()\n  }\n)\n\n// 监听支付金额变化，重新生成二维码\nwatch(\n  () => props.paymentAmount,\n  () => {\n    refreshQrCode()\n  }\n)\n\n// 暴露方法给父组件\ndefineExpose({\n  refreshQrCode,\n  checkPaymentStatus,\n  handlePaymentCancelled\n})\n</script>\n\n<style scoped>\n.qr-payment {\n  padding: 20px 24px;\n  background: #f9f9f9;\n  border-top: 1px solid #f0f0f0;\n}\n\n/* 二维码容器 */\n.qr-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n/* 二维码包装器 */\n.qr-code-wrapper {\n  position: relative;\n  margin-bottom: 16px;\n}\n\n/* 二维码 */\n.qr-code {\n  position: relative;\n  width: 200px;\n  height: 200px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);\n  background: #fff;\n}\n\n.qr-code img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 支付方式标识 */\n.payment-brand {\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  color: #1890ff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 倒计时 */\n.countdown {\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n}\n\n.countdown-circle {\n  width: 32px;\n  height: 32px;\n  background: rgba(24, 144, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 12px;\n  font-weight: 600;\n  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\n}\n\n/* 加载状态 */\n.qr-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 200px;\n  height: 200px;\n  background: #f5f5f5;\n  border-radius: 12px;\n  border: 2px dashed #d9d9d9;\n}\n\n.qr-loading p {\n  margin-top: 16px;\n  color: #8c8c8c;\n  font-size: 13px;\n}\n\n/* 二维码过期 */\n.qr-expired {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 200px;\n  height: 200px;\n  background: #fff2f0;\n  border-radius: 12px;\n  border: 2px dashed #ffccc7;\n}\n\n.expired-icon {\n  font-size: 48px;\n  color: #ff7875;\n  margin-bottom: 12px;\n}\n\n.qr-expired p {\n  margin-bottom: 16px;\n  color: #ff4d4f;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* 错误状态 */\n.qr-error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 200px;\n  height: 200px;\n  background: #fff2f0;\n  border-radius: 12px;\n  border: 2px dashed #ffccc7;\n}\n\n.error-icon {\n  font-size: 48px;\n  color: #ff4d4f;\n  margin-bottom: 12px;\n}\n\n.qr-error p {\n  margin-bottom: 16px;\n  color: #ff4d4f;\n  font-size: 13px;\n  text-align: center;\n  padding: 0 16px;\n}\n\n/* 支付提示 */\n.qr-tips {\n  text-align: center;\n}\n\n.tip-main {\n  font-size: 14px;\n  color: #595959;\n  margin-bottom: 8px;\n}\n\n.tip-amount {\n  font-size: 13px;\n  color: #8c8c8c;\n}\n\n.amount {\n  color: #ff4d4f;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n/* 支付状态 */\n.payment-status {\n  margin-bottom: 20px;\n  padding: 16px;\n  background: #fff;\n  border-radius: 8px;\n  border: 1px solid #e8e8e8;\n}\n\n.status-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-text {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.status-text.success {\n  color: #52c41a;\n}\n\n.status-text.error {\n  color: #ff4d4f;\n}\n\n.status-text.info {\n  color: #1890ff;\n}\n\n.success-icon {\n  color: #52c41a;\n  font-size: 20px;\n}\n\n.error-icon {\n  color: #ff4d4f;\n  font-size: 20px;\n}\n\n.info-icon {\n  color: #1890ff;\n  font-size: 20px;\n}\n\n.status-progress {\n  width: 100%;\n  margin-top: 8px;\n}\n\n/* 操作按钮 */\n.qr-actions {\n  display: flex;\n  gap: 12px;\n  margin-bottom: 20px;\n}\n\n.action-btn {\n  flex: 1;\n  height: 40px;\n  font-size: 13px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n/* 支付说明 */\n.payment-instructions {\n  background: #fff;\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid #e8e8e8;\n}\n\n.instruction-title {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 12px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #262626;\n}\n\n.instruction-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.instruction-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: #595959;\n}\n\n.instruction-item .anticon {\n  color: #1890ff;\n  font-size: 14px;\n  width: 16px;\n  flex-shrink: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .qr-payment {\n    padding: 16px 20px;\n  }\n  \n  .qr-code,\n  .qr-loading,\n  .qr-expired,\n  .qr-error {\n    width: 160px;\n    height: 160px;\n  }\n  \n  .expired-icon,\n  .error-icon {\n    font-size: 40px;\n  }\n  \n  .payment-brand {\n    width: 28px;\n    height: 28px;\n    font-size: 14px;\n  }\n  \n  .countdown-circle {\n    width: 28px;\n    height: 28px;\n    font-size: 11px;\n  }\n  \n  .qr-actions {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .action-btn {\n    height: 36px;\n  }\n}\n\n@media (max-width: 480px) {\n  .qr-code,\n  .qr-loading,\n  .qr-expired,\n  .qr-error {\n    width: 140px;\n    height: 140px;\n  }\n  \n  .payment-instructions {\n    padding: 12px;\n  }\n  \n  .instruction-list {\n    gap: 6px;\n  }\n  \n  .instruction-item {\n    font-size: 11px;\n  }\n}\n\n/* 动画效果 */\n.qr-code {\n  animation: fadeInScale 0.5s ease-out;\n}\n\n@keyframes fadeInScale {\n  from {\n    opacity: 0;\n    transform: scale(0.8);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.countdown-circle {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .qr-code {\n    border: 2px solid #000;\n  }\n  \n  .payment-status,\n  .payment-instructions {\n    border-width: 2px;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .qr-code {\n    animation: none;\n  }\n  \n  .countdown-circle {\n    animation: none;\n  }\n}\n</style>"