<!--
  支付方式选择组件
  
  显示可用的支付方式并处理用户选择
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="payment-method">
    <div class="method-header">
      <h4>选择支付方式</h4>
      <div class="method-count" v-if="availableMethods.length > 0">
        共{{ availableMethods.length }}种方式
      </div>
    </div>
    
    <div class="method-grid">
      <div
        v-for="method in availableMethods"
        :key="method.value"
        class="method-card"
        :class="{ 
          active: selectedMethod === method.value,
          disabled: method.disabled
        }"
        @click="handleMethodSelect(method)"
      >
        <div class="method-icon">
          <icon-font :iconClass="method.icon" />
        </div>
        <div class="method-name">{{ method.label }}</div>
        <div class="method-desc" v-if="method.description">
          {{ method.description }}
        </div>
        
        <!-- 支付方式状态标识 -->
        <div class="method-status" v-if="method.status">
          <a-tag 
            :color="getStatusColor(method.status)"
            size="small"
          >
            {{ getStatusText(method.status) }}
          </a-tag>
        </div>
        
        <!-- 禁用遮罩 -->
        <div v-if="method.disabled" class="disabled-overlay">
          <icon-font iconClass="icon-lock" />
          <span>{{ method.disabledReason || '暂不可用' }}</span>
        </div>
      </div>
    </div>
    
    <!-- 支付方式说明 -->
    <div class="method-tips" v-if="selectedMethodInfo">
      <div class="tip-item" v-for="tip in selectedMethodInfo.tips" :key="tip">
        <icon-font iconClass="icon-info" />
        <span>{{ tip }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { message } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'
import { PAYMENT_METHODS, PAYMENT_STATUS } from '../../utils/constants'

// 定义组件名称
defineOptions({
  name: 'PaymentMethod'
})

// 定义Props
const props = defineProps({
  // 当前选中的支付方式
  selectedMethod: {
    type: String,
    default: ''
  },
  // 可用的支付方式列表
  methods: {
    type: Array,
    default: () => []
  },
  // 支付金额（用于判断某些支付方式的可用性）
  paymentAmount: {
    type: Number,
    default: 0
  },
  // 会员信息（用于会员卡支付）
  memberInfo: {
    type: Object,
    default: null
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'method-select',
  'method-change'
])

// ==================== 计算属性 ====================

/**
 * 可用的支付方式列表
 */
const availableMethods = computed(() => {
  return props.methods.map(method => {
    const methodConfig = PAYMENT_METHODS[method.value] || {}
    
    // 检查支付方式是否可用
    let disabled = false
    let disabledReason = ''
    
    // 会员卡支付需要检查会员余额
    if (method.value === 'MEMBER') {
      if (!props.memberInfo) {
        disabled = true
        disabledReason = '请先选择会员'
      } else if (props.memberInfo.balance < props.paymentAmount) {
        disabled = true
        disabledReason = '余额不足'
      }
    }
    
    // 检查最小支付金额限制
    if (methodConfig.minAmount && props.paymentAmount < methodConfig.minAmount) {
      disabled = true
      disabledReason = `最小支付金额￥${methodConfig.minAmount}`
    }
    
    return {
      ...method,
      ...methodConfig,
      disabled,
      disabledReason
    }
  })
})

/**
 * 当前选中支付方式的详细信息
 */
const selectedMethodInfo = computed(() => {
  if (!props.selectedMethod) return null
  
  const method = availableMethods.value.find(m => m.value === props.selectedMethod)
  return method || null
})

// ==================== 方法 ====================

/**
 * 处理支付方式选择
 * @param {Object} method - 支付方式信息
 */
const handleMethodSelect = (method) => {
  if (method.disabled || props.loading) {
    if (method.disabledReason) {
      message.warning(method.disabledReason)
    }
    return
  }
  
  // 如果已经选中相同的支付方式，则不重复触发
  if (props.selectedMethod === method.value) {
    return
  }
  
  emit('method-select', method.value)
  emit('method-change', {
    method: method.value,
    methodInfo: method
  })
}

/**
 * 获取状态颜色
 * @param {string} status - 状态
 * @returns {string} 颜色
 */
const getStatusColor = (status) => {
  switch (status) {
    case PAYMENT_STATUS.AVAILABLE:
      return 'green'
    case PAYMENT_STATUS.UNAVAILABLE:
      return 'red'
    case PAYMENT_STATUS.MAINTENANCE:
      return 'orange'
    default:
      return 'default'
  }
}

/**
 * 获取状态文本
 * @param {string} status - 状态
 * @returns {string} 状态文本
 */
const getStatusText = (status) => {
  switch (status) {
    case PAYMENT_STATUS.AVAILABLE:
      return '可用'
    case PAYMENT_STATUS.UNAVAILABLE:
      return '不可用'
    case PAYMENT_STATUS.MAINTENANCE:
      return '维护中'
    default:
      return status
  }
}
</script>

<style scoped>
.payment-method {
  padding: 20px 0;
}

/* 方式头部 */
.method-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 24px;
}

.method-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.method-count {
  font-size: 13px;
  color: #8c8c8c;
}

/* 支付方式网格 */
.method-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  padding: 0 24px;
}

/* 支付方式卡片 */
.method-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
  min-height: 100px;
}

.method-card:hover:not(.disabled) {
  border-color: #40a9ff;
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.method-card.active {
  border-color: #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

.method-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

/* 支付方式图标 */
.method-icon {
  font-size: 32px;
  color: #8c8c8c;
  margin-bottom: 8px;
  transition: color 0.3s;
}

.method-card:hover:not(.disabled) .method-icon,
.method-card.active .method-icon {
  color: #1890ff;
}

.method-card.disabled .method-icon {
  color: #bfbfbf;
}

/* 支付方式名称 */
.method-name {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  text-align: center;
  margin-bottom: 4px;
}

.method-card.disabled .method-name {
  color: #bfbfbf;
}

/* 支付方式描述 */
.method-desc {
  font-size: 11px;
  color: #8c8c8c;
  text-align: center;
  line-height: 1.4;
}

.method-card.disabled .method-desc {
  color: #bfbfbf;
}

/* 支付方式状态 */
.method-status {
  position: absolute;
  top: 8px;
  right: 8px;
}

/* 禁用遮罩 */
.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  font-size: 11px;
  color: #8c8c8c;
  gap: 4px;
}

.disabled-overlay .anticon {
  font-size: 16px;
}

/* 支付方式说明 */
.method-tips {
  margin-top: 16px;
  padding: 0 24px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #595959;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item .anticon {
  color: #1890ff;
  margin-top: 2px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .method-header,
  .method-grid,
  .method-tips {
    padding: 0 20px;
  }
  
  .method-header h4 {
    font-size: 15px;
  }
  
  .method-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
  }
  
  .method-card {
    padding: 12px 8px;
    min-height: 80px;
  }
  
  .method-icon {
    font-size: 28px;
    margin-bottom: 6px;
  }
  
  .method-name {
    font-size: 12px;
  }
  
  .method-desc {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .method-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .method-card {
    min-height: 70px;
  }
  
  .method-icon {
    font-size: 24px;
  }
}

/* 动画效果 */
.method-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .method-card:hover {
    transform: none;
    box-shadow: none;
  }
  
  .method-card:active:not(.disabled) {
    transform: scale(0.98);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .method-card {
    border-width: 3px;
  }
  
  .method-card.active {
    border-width: 3px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .method-card {
    animation: none;
    transition: none;
  }
  
  .method-icon {
    transition: none;
  }
}
</style>