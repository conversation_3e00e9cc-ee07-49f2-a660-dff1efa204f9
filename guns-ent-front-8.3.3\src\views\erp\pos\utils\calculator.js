/**
 * POS模块计算工具函数
 * 
 * 提供购物车金额计算、折扣计算、税费计算等核心计算逻辑
 * 所有计算都保证精度，避免浮点数计算误差
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { NUMERIC_CONSTANTS } from './constants'

/**
 * 精确数值计算工具类
 * 解决JavaScript浮点数计算精度问题
 */
class PrecisionCalculator {
  
  /**
   * 精确加法
   * @param {number} num1 - 加数1
   * @param {number} num2 - 加数2
   * @returns {number} 计算结果
   */
  static add(num1, num2) {
    const factor = Math.pow(10, NUMERIC_CONSTANTS.AMOUNT_PRECISION)
    return Math.round((num1 + num2) * factor) / factor
  }
  
  /**
   * 精确减法
   * @param {number} num1 - 被减数
   * @param {number} num2 - 减数
   * @returns {number} 计算结果
   */
  static subtract(num1, num2) {
    const factor = Math.pow(10, NUMERIC_CONSTANTS.AMOUNT_PRECISION)
    return Math.round((num1 - num2) * factor) / factor
  }
  
  /**
   * 精确乘法
   * @param {number} num1 - 乘数1
   * @param {number} num2 - 乘数2
   * @returns {number} 计算结果
   */
  static multiply(num1, num2) {
    const factor = Math.pow(10, NUMERIC_CONSTANTS.AMOUNT_PRECISION)
    return Math.round(num1 * num2 * factor) / factor
  }
  
  /**
   * 精确除法
   * @param {number} num1 - 被除数
   * @param {number} num2 - 除数
   * @returns {number} 计算结果
   */
  static divide(num1, num2) {
    if (num2 === 0) {
      throw new Error('除数不能为零')
    }
    const factor = Math.pow(10, NUMERIC_CONSTANTS.AMOUNT_PRECISION)
    return Math.round((num1 / num2) * factor) / factor
  }
  
  /**
   * 四舍五入到指定精度
   * @param {number} num - 数值
   * @param {number} precision - 精度位数
   * @returns {number} 四舍五入后的结果
   */
  static round(num, precision = NUMERIC_CONSTANTS.AMOUNT_PRECISION) {
    const factor = Math.pow(10, precision)
    return Math.round(num * factor) / factor
  }
}

/**
 * 购物车计算工具类
 */
export class CartCalculator {
  
  /**
   * 计算商品小计
   * @param {number} price - 单价
   * @param {number} quantity - 数量
   * @returns {number} 小计金额
   */
  static calculateSubtotal(price, quantity) {
    if (price < 0 || quantity < 0) {
      throw new Error('价格和数量不能为负数')
    }
    return PrecisionCalculator.multiply(price, quantity)
  }
  
  /**
   * 计算购物车总金额
   * @param {Array} cartItems - 购物车商品列表
   * @returns {number} 总金额
   */
  static calculateTotal(cartItems) {
    if (!Array.isArray(cartItems)) {
      throw new Error('购物车商品列表必须是数组')
    }
    
    return cartItems.reduce((total, item) => {
      const subtotal = this.calculateSubtotal(item.price || 0, item.quantity || 0)
      return PrecisionCalculator.add(total, subtotal)
    }, 0)
  }
  
  /**
   * 计算购物车商品总数量
   * @param {Array} cartItems - 购物车商品列表
   * @returns {number} 总数量
   */
  static calculateTotalQuantity(cartItems) {
    if (!Array.isArray(cartItems)) {
      throw new Error('购物车商品列表必须是数组')
    }
    
    return cartItems.reduce((total, item) => {
      return PrecisionCalculator.add(total, item.quantity || 0)
    }, 0)
  }
  
  /**
   * 计算购物车商品种类数
   * @param {Array} cartItems - 购物车商品列表
   * @returns {number} 商品种类数
   */
  static calculateItemCount(cartItems) {
    if (!Array.isArray(cartItems)) {
      return 0
    }
    return cartItems.length
  }
  
  /**
   * 计算平均单价
   * @param {Array} cartItems - 购物车商品列表
   * @returns {number} 平均单价
   */
  static calculateAveragePrice(cartItems) {
    if (!Array.isArray(cartItems) || cartItems.length === 0) {
      return 0
    }
    
    const totalAmount = this.calculateTotal(cartItems)
    const totalQuantity = this.calculateTotalQuantity(cartItems)
    
    if (totalQuantity === 0) {
      return 0
    }
    
    return PrecisionCalculator.divide(totalAmount, totalQuantity)
  }
}

/**
 * 价格计算工具类
 */
export class PriceCalculator {
  
  /**
   * 计算含税价格
   * @param {number} price - 不含税价格
   * @param {number} taxRate - 税率（如0.13表示13%）
   * @returns {number} 含税价格
   */
  static calculatePriceWithTax(price, taxRate) {
    if (price < 0) {
      throw new Error('价格不能为负数')
    }
    if (taxRate < 0 || taxRate > 1) {
      throw new Error('税率必须在0-1之间')
    }
    
    const taxAmount = PrecisionCalculator.multiply(price, taxRate)
    return PrecisionCalculator.add(price, taxAmount)
  }
  
  /**
   * 计算不含税价格
   * @param {number} priceWithTax - 含税价格
   * @param {number} taxRate - 税率（如0.13表示13%）
   * @returns {number} 不含税价格
   */
  static calculatePriceWithoutTax(priceWithTax, taxRate) {
    if (priceWithTax < 0) {
      throw new Error('价格不能为负数')
    }
    if (taxRate < 0 || taxRate > 1) {
      throw new Error('税率必须在0-1之间')
    }
    
    return PrecisionCalculator.divide(priceWithTax, PrecisionCalculator.add(1, taxRate))
  }
  
  /**
   * 计算税额
   * @param {number} price - 不含税价格
   * @param {number} taxRate - 税率
   * @returns {number} 税额
   */
  static calculateTaxAmount(price, taxRate) {
    if (price < 0) {
      throw new Error('价格不能为负数')
    }
    if (taxRate < 0 || taxRate > 1) {
      throw new Error('税率必须在0-1之间')
    }
    
    return PrecisionCalculator.multiply(price, taxRate)
  }
  
  /**
   * 计算计重商品价格
   * @param {number} unitPrice - 单价（每公斤/每斤）
   * @param {number} weight - 重量
   * @returns {number} 总价格
   */
  static calculateWeightPrice(unitPrice, weight) {
    if (unitPrice < 0 || weight < 0) {
      throw new Error('单价和重量不能为负数')
    }
    
    return PrecisionCalculator.multiply(unitPrice, weight)
  }
}

/**
 * 折扣计算工具类
 */
export class DiscountCalculator {
  
  /**
   * 计算百分比折扣
   * @param {number} originalAmount - 原始金额
   * @param {number} discountRate - 折扣率（如0.1表示10%折扣）
   * @returns {number} 折扣金额
   */
  static calculatePercentageDiscount(originalAmount, discountRate) {
    if (originalAmount < 0) {
      throw new Error('原始金额不能为负数')
    }
    if (discountRate < 0 || discountRate > 1) {
      throw new Error('折扣率必须在0-1之间')
    }
    
    return PrecisionCalculator.multiply(originalAmount, discountRate)
  }
  
  /**
   * 计算固定金额折扣
   * @param {number} originalAmount - 原始金额
   * @param {number} discountAmount - 折扣金额
   * @returns {number} 实际折扣金额（不能超过原始金额）
   */
  static calculateFixedDiscount(originalAmount, discountAmount) {
    if (originalAmount < 0 || discountAmount < 0) {
      throw new Error('金额不能为负数')
    }
    
    // 折扣金额不能超过原始金额
    return Math.min(discountAmount, originalAmount)
  }
  
  /**
   * 计算会员折扣
   * @param {number} originalAmount - 原始金额
   * @param {number} memberDiscountRate - 会员折扣率
   * @param {number} minAmount - 最低消费金额（享受折扣的门槛）
   * @returns {number} 折扣金额
   */
  static calculateMemberDiscount(originalAmount, memberDiscountRate, minAmount = 0) {
    if (originalAmount < 0 || minAmount < 0) {
      throw new Error('金额不能为负数')
    }
    if (memberDiscountRate < 0 || memberDiscountRate > 1) {
      throw new Error('会员折扣率必须在0-1之间')
    }
    
    // 如果未达到最低消费金额，不享受折扣
    if (originalAmount < minAmount) {
      return 0
    }
    
    return this.calculatePercentageDiscount(originalAmount, memberDiscountRate)
  }
  
  /**
   * 计算满减折扣
   * @param {number} originalAmount - 原始金额
   * @param {Array} discountRules - 满减规则数组 [{minAmount: 100, discountAmount: 10}]
   * @returns {number} 折扣金额
   */
  static calculateFullReductionDiscount(originalAmount, discountRules) {
    if (originalAmount < 0) {
      throw new Error('原始金额不能为负数')
    }
    if (!Array.isArray(discountRules)) {
      throw new Error('折扣规则必须是数组')
    }
    
    // 按最低消费金额降序排列，优先匹配最大的满减规则
    const sortedRules = discountRules
      .filter(rule => rule.minAmount <= originalAmount)
      .sort((a, b) => b.minAmount - a.minAmount)
    
    if (sortedRules.length === 0) {
      return 0
    }
    
    // 返回最大的满减金额
    return sortedRules[0].discountAmount || 0
  }
  
  /**
   * 计算阶梯折扣
   * @param {number} originalAmount - 原始金额
   * @param {Array} discountTiers - 阶梯折扣规则 [{minAmount: 0, maxAmount: 100, discountRate: 0.05}]
   * @returns {number} 折扣金额
   */
  static calculateTieredDiscount(originalAmount, discountTiers) {
    if (originalAmount < 0) {
      throw new Error('原始金额不能为负数')
    }
    if (!Array.isArray(discountTiers)) {
      throw new Error('阶梯折扣规则必须是数组')
    }
    
    let totalDiscount = 0
    let remainingAmount = originalAmount
    
    // 按最低金额升序排列
    const sortedTiers = discountTiers.sort((a, b) => a.minAmount - b.minAmount)
    
    for (const tier of sortedTiers) {
      if (remainingAmount <= 0) break
      
      const tierMin = tier.minAmount || 0
      const tierMax = tier.maxAmount || Infinity
      const discountRate = tier.discountRate || 0
      
      if (originalAmount > tierMin) {
        const applicableAmount = Math.min(
          remainingAmount,
          Math.min(tierMax, originalAmount) - tierMin
        )
        
        if (applicableAmount > 0) {
          const tierDiscount = this.calculatePercentageDiscount(applicableAmount, discountRate)
          totalDiscount = PrecisionCalculator.add(totalDiscount, tierDiscount)
          remainingAmount = PrecisionCalculator.subtract(remainingAmount, applicableAmount)
        }
      }
    }
    
    return totalDiscount
  }
}

/**
 * 积分计算工具类
 */
export class PointsCalculator {
  
  /**
   * 计算消费获得的积分
   * @param {number} amount - 消费金额
   * @param {number} pointsRate - 积分比例（每元获得多少积分）
   * @returns {number} 获得的积分数
   */
  static calculateEarnedPoints(amount, pointsRate = 1) {
    if (amount < 0) {
      throw new Error('消费金额不能为负数')
    }
    if (pointsRate < 0) {
      throw new Error('积分比例不能为负数')
    }
    
    return Math.floor(PrecisionCalculator.multiply(amount, pointsRate))
  }
  
  /**
   * 计算积分抵扣金额
   * @param {number} points - 使用的积分数
   * @param {number} exchangeRate - 兑换比例（多少积分兑换1元）
   * @returns {number} 抵扣金额
   */
  static calculatePointsDeduction(points, exchangeRate = NUMERIC_CONSTANTS.POINTS_EXCHANGE_RATE) {
    if (points < 0) {
      throw new Error('积分数不能为负数')
    }
    if (exchangeRate <= 0) {
      throw new Error('兑换比例必须大于0')
    }
    
    return PrecisionCalculator.divide(points, exchangeRate)
  }
  
  /**
   * 计算最大可用积分
   * @param {number} availablePoints - 可用积分总数
   * @param {number} orderAmount - 订单金额
   * @param {number} maxDeductionRate - 最大抵扣比例（如0.5表示最多抵扣50%）
   * @param {number} exchangeRate - 兑换比例
   * @returns {number} 最大可用积分数
   */
  static calculateMaxUsablePoints(availablePoints, orderAmount, maxDeductionRate = 1, exchangeRate = NUMERIC_CONSTANTS.POINTS_EXCHANGE_RATE) {
    if (availablePoints < 0 || orderAmount < 0) {
      throw new Error('积分和订单金额不能为负数')
    }
    if (maxDeductionRate < 0 || maxDeductionRate > 1) {
      throw new Error('最大抵扣比例必须在0-1之间')
    }
    
    // 计算最大可抵扣金额
    const maxDeductionAmount = PrecisionCalculator.multiply(orderAmount, maxDeductionRate)
    
    // 计算达到最大抵扣金额需要的积分数
    const maxDeductionPoints = PrecisionCalculator.multiply(maxDeductionAmount, exchangeRate)
    
    // 返回可用积分和最大抵扣积分的较小值
    return Math.min(availablePoints, maxDeductionPoints)
  }
}

/**
 * 找零计算工具类
 */
export class ChangeCalculator {
  
  /**
   * 计算找零金额
   * @param {number} receivedAmount - 实收金额
   * @param {number} payableAmount - 应付金额
   * @returns {number} 找零金额
   */
  static calculateChange(receivedAmount, payableAmount) {
    if (receivedAmount < 0 || payableAmount < 0) {
      throw new Error('金额不能为负数')
    }
    
    const change = PrecisionCalculator.subtract(receivedAmount, payableAmount)
    return Math.max(0, change) // 找零不能为负数
  }
  
  /**
   * 计算找零面额分布
   * @param {number} changeAmount - 找零金额
   * @param {Array} denominations - 面额数组（从大到小排列）
   * @returns {Object} 面额分布 {denomination: count}
   */
  static calculateChangeDenominations(changeAmount, denominations = [100, 50, 20, 10, 5, 1, 0.5, 0.1]) {
    if (changeAmount < 0) {
      throw new Error('找零金额不能为负数')
    }
    
    const result = {}
    let remainingAmount = changeAmount
    
    // 按面额从大到小分配
    for (const denomination of denominations) {
      if (remainingAmount >= denomination) {
        const count = Math.floor(remainingAmount / denomination)
        result[denomination] = count
        remainingAmount = PrecisionCalculator.subtract(
          remainingAmount, 
          PrecisionCalculator.multiply(denomination, count)
        )
      }
    }
    
    return result
  }
}

/**
 * 统计计算工具类
 */
export class StatisticsCalculator {
  
  /**
   * 计算销售统计
   * @param {Array} orders - 订单列表
   * @returns {Object} 销售统计信息
   */
  static calculateSalesStatistics(orders) {
    if (!Array.isArray(orders)) {
      throw new Error('订单列表必须是数组')
    }
    
    const stats = {
      totalOrders: orders.length,
      totalAmount: 0,
      totalQuantity: 0,
      averageOrderAmount: 0,
      maxOrderAmount: 0,
      minOrderAmount: orders.length > 0 ? Infinity : 0
    }
    
    for (const order of orders) {
      const orderAmount = order.finalAmount || 0
      const orderQuantity = order.items ? order.items.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0
      
      stats.totalAmount = PrecisionCalculator.add(stats.totalAmount, orderAmount)
      stats.totalQuantity = PrecisionCalculator.add(stats.totalQuantity, orderQuantity)
      stats.maxOrderAmount = Math.max(stats.maxOrderAmount, orderAmount)
      stats.minOrderAmount = Math.min(stats.minOrderAmount, orderAmount)
    }
    
    if (stats.totalOrders > 0) {
      stats.averageOrderAmount = PrecisionCalculator.divide(stats.totalAmount, stats.totalOrders)
    }
    
    if (stats.minOrderAmount === Infinity) {
      stats.minOrderAmount = 0
    }
    
    return stats
  }
  
  /**
   * 计算商品销售排行
   * @param {Array} orders - 订单列表
   * @param {number} topN - 返回前N名
   * @returns {Array} 商品销售排行
   */
  static calculateProductRanking(orders, topN = 10) {
    if (!Array.isArray(orders)) {
      throw new Error('订单列表必须是数组')
    }
    
    const productStats = new Map()
    
    for (const order of orders) {
      if (order.items && Array.isArray(order.items)) {
        for (const item of order.items) {
          const productId = item.productId || item.id
          if (productId) {
            const existing = productStats.get(productId) || {
              productId,
              productName: item.productName || item.name,
              totalQuantity: 0,
              totalAmount: 0,
              orderCount: 0
            }
            
            existing.totalQuantity = PrecisionCalculator.add(existing.totalQuantity, item.quantity || 0)
            existing.totalAmount = PrecisionCalculator.add(existing.totalAmount, item.subtotal || 0)
            existing.orderCount += 1
            
            productStats.set(productId, existing)
          }
        }
      }
    }
    
    // 按销售金额排序
    return Array.from(productStats.values())
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, topN)
  }
}