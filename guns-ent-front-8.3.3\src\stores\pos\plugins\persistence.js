/**
 * POS状态持久化插件
 * 
 * 提供状态的自动保存、恢复、加密等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { watch } from 'vue'

/**
 * 状态持久化插件
 */
export class PersistencePlugin {
  constructor(options = {}) {
    this.options = {
      // 存储键前缀
      keyPrefix: 'pos_store_',
      // 是否启用加密
      enableEncryption: false,
      // 数据过期时间（毫秒）
      expireTime: 24 * 60 * 60 * 1000, // 24小时
      // 是否启用压缩
      enableCompression: true,
      // 存储引擎 ('localStorage' | 'sessionStorage' | 'indexedDB')
      storageEngine: 'localStorage',
      // 需要持久化的状态路径
      persistPaths: [],
      // 需要排除的状态路径
      excludePaths: [],
      // 自动保存间隔（毫秒）
      autoSaveInterval: 30000, // 30秒
      // 是否启用自动保存
      enableAutoSave: true,
      // 序列化器
      serializer: {
        serialize: JSON.stringify,
        deserialize: JSON.parse
      },
      ...options
    }
    
    this.storage = this.getStorageEngine()
    this.autoSaveTimer = null
    this.watchers = new Map()
  }
  
  /**
   * 获取存储引擎
   * @returns {Storage} 存储引擎
   */
  getStorageEngine() {
    switch (this.options.storageEngine) {
      case 'sessionStorage':
        return sessionStorage
      case 'localStorage':
      default:
        return localStorage
    }
  }
  
  /**
   * 生成存储键
   * @param {string} storeId - 存储ID
   * @returns {string} 存储键
   */
  generateKey(storeId) {
    return `${this.options.keyPrefix}${storeId}`
  }
  
  /**
   * 压缩数据
   * @param {string} data - 要压缩的数据
   * @returns {string} 压缩后的数据
   */
  compressData(data) {
    if (!this.options.enableCompression) {
      return data
    }
    
    try {
      // 简单的压缩：移除多余空格
      return data.replace(/\s+/g, ' ').trim()
    } catch (error) {
      console.warn('数据压缩失败:', error)
      return data
    }
  }
  
  /**
   * 解压数据
   * @param {string} compressedData - 压缩的数据
   * @returns {string} 解压后的数据
   */
  decompressData(compressedData) {
    // 简单实现，实际项目中可以使用更复杂的压缩算法
    return compressedData
  }
  
  /**
   * 加密数据
   * @param {string} data - 要加密的数据
   * @returns {string} 加密后的数据
   */
  encryptData(data) {
    if (!this.options.enableEncryption) {
      return data
    }
    
    try {
      // 简单的Base64编码（实际项目中应使用更安全的加密方法）
      return btoa(unescape(encodeURIComponent(data)))
    } catch (error) {
      console.warn('数据加密失败:', error)
      return data
    }
  }
  
  /**
   * 解密数据
   * @param {string} encryptedData - 加密的数据
   * @returns {string} 解密后的数据
   */
  decryptData(encryptedData) {
    if (!this.options.enableEncryption) {
      return encryptedData
    }
    
    try {
      return decodeURIComponent(escape(atob(encryptedData)))
    } catch (error) {
      console.warn('数据解密失败:', error)
      return encryptedData
    }
  }
  
  /**
   * 序列化状态数据
   * @param {Object} state - 状态数据
   * @returns {string} 序列化后的数据
   */
  serializeState(state) {
    try {
      const filteredState = this.filterState(state)
      const serialized = this.options.serializer.serialize(filteredState)
      const compressed = this.compressData(serialized)
      const encrypted = this.encryptData(compressed)
      
      return encrypted
    } catch (error) {
      console.error('序列化状态失败:', error)
      return null
    }
  }
  
  /**
   * 反序列化状态数据
   * @param {string} data - 序列化的数据
   * @returns {Object} 状态数据
   */
  deserializeState(data) {
    try {
      const decrypted = this.decryptData(data)
      const decompressed = this.decompressData(decrypted)
      const deserialized = this.options.serializer.deserialize(decompressed)
      
      return deserialized
    } catch (error) {
      console.error('反序列化状态失败:', error)
      return null
    }
  }
  
  /**
   * 过滤状态数据
   * @param {Object} state - 原始状态
   * @returns {Object} 过滤后的状态
   */
  filterState(state) {
    const filtered = {}
    
    for (const [key, value] of Object.entries(state)) {
      // 检查是否在排除列表中
      if (this.options.excludePaths.includes(key)) {
        continue
      }
      
      // 检查是否在包含列表中（如果指定了包含列表）
      if (this.options.persistPaths.length > 0 && !this.options.persistPaths.includes(key)) {
        continue
      }
      
      // 排除函数和Symbol
      if (typeof value === 'function' || typeof value === 'symbol') {
        continue
      }
      
      filtered[key] = value
    }
    
    return filtered
  }
  
  /**
   * 保存状态到存储
   * @param {string} storeId - 存储ID
   * @param {Object} state - 状态数据
   * @returns {boolean} 是否保存成功
   */
  saveState(storeId, state) {
    try {
      const key = this.generateKey(storeId)
      const serializedData = this.serializeState(state)
      
      if (!serializedData) {
        return false
      }
      
      const stateWithMeta = {
        data: serializedData,
        timestamp: Date.now(),
        version: '1.0',
        storeId: storeId
      }
      
      this.storage.setItem(key, JSON.stringify(stateWithMeta))
      
      console.log(`✅ 状态已保存: ${storeId}`)
      return true
      
    } catch (error) {
      console.error(`❌ 保存状态失败: ${storeId}`, error)
      return false
    }
  }
  
  /**
   * 从存储加载状态
   * @param {string} storeId - 存储ID
   * @returns {Object|null} 状态数据
   */
  loadState(storeId) {
    try {
      const key = this.generateKey(storeId)
      const storedData = this.storage.getItem(key)
      
      if (!storedData) {
        return null
      }
      
      const stateWithMeta = JSON.parse(storedData)
      
      // 检查数据是否过期
      if (this.options.expireTime > 0) {
        const age = Date.now() - stateWithMeta.timestamp
        if (age > this.options.expireTime) {
          console.warn(`状态数据已过期: ${storeId}`)
          this.removeState(storeId)
          return null
        }
      }
      
      const state = this.deserializeState(stateWithMeta.data)
      
      if (state) {
        console.log(`✅ 状态已加载: ${storeId}`)
      }
      
      return state
      
    } catch (error) {
      console.error(`❌ 加载状态失败: ${storeId}`, error)
      return null
    }
  }
  
  /**
   * 移除存储的状态
   * @param {string} storeId - 存储ID
   */
  removeState(storeId) {
    try {
      const key = this.generateKey(storeId)
      this.storage.removeItem(key)
      console.log(`🗑️ 状态已移除: ${storeId}`)
    } catch (error) {
      console.error(`❌ 移除状态失败: ${storeId}`, error)
    }
  }
  
  /**
   * 清除所有存储的状态
   */
  clearAllStates() {
    try {
      const keys = []
      
      // 收集所有相关的键
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(this.options.keyPrefix)) {
          keys.push(key)
        }
      }
      
      // 删除所有相关的键
      keys.forEach(key => {
        this.storage.removeItem(key)
      })
      
      console.log(`🧹 已清除所有状态数据 (${keys.length}个)`)
    } catch (error) {
      console.error('清除所有状态失败:', error)
    }
  }
  
  /**
   * 获取存储统计信息
   * @returns {Object} 统计信息
   */
  getStorageStats() {
    const stats = {
      totalKeys: 0,
      totalSize: 0,
      stores: []
    }
    
    try {
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(this.options.keyPrefix)) {
          const data = this.storage.getItem(key)
          const size = data ? data.length : 0
          
          stats.totalKeys++
          stats.totalSize += size
          
          const storeId = key.replace(this.options.keyPrefix, '')
          stats.stores.push({
            storeId,
            key,
            size,
            sizeFormatted: this.formatBytes(size)
          })
        }
      }
      
      stats.totalSizeFormatted = this.formatBytes(stats.totalSize)
    } catch (error) {
      console.error('获取存储统计失败:', error)
    }
    
    return stats
  }
  
  /**
   * 格式化字节数
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的字符串
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  /**
   * 设置状态监听器
   * @param {string} storeId - 存储ID
   * @param {Object} store - Pinia store实例
   */
  setupWatcher(storeId, store) {
    if (this.watchers.has(storeId)) {
      return // 已经设置过监听器
    }
    
    // 监听状态变化
    const unwatch = watch(
      () => store.$state,
      (newState) => {
        // 防抖保存
        this.debouncedSave(storeId, newState)
      },
      { 
        deep: true,
        flush: 'post' // 在DOM更新后执行
      }
    )
    
    this.watchers.set(storeId, unwatch)
    console.log(`👁️ 已设置状态监听器: ${storeId}`)
  }
  
  /**
   * 防抖保存
   * @param {string} storeId - 存储ID
   * @param {Object} state - 状态数据
   */
  debouncedSave(storeId, state) {
    // 清除之前的定时器
    if (this.saveTimers && this.saveTimers[storeId]) {
      clearTimeout(this.saveTimers[storeId])
    }
    
    if (!this.saveTimers) {
      this.saveTimers = {}
    }
    
    // 设置新的定时器
    this.saveTimers[storeId] = setTimeout(() => {
      this.saveState(storeId, state)
      delete this.saveTimers[storeId]
    }, 1000) // 1秒防抖
  }
  
  /**
   * 启动自动保存
   * @param {Map} stores - 存储映射
   */
  startAutoSave(stores) {
    if (!this.options.enableAutoSave || this.autoSaveTimer) {
      return
    }
    
    this.autoSaveTimer = setInterval(() => {
      stores.forEach((store, storeId) => {
        this.saveState(storeId, store.$state)
      })
    }, this.options.autoSaveInterval)
    
    console.log('🔄 自动保存已启动')
  }
  
  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
      console.log('⏹️ 自动保存已停止')
    }
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    // 停止自动保存
    this.stopAutoSave()
    
    // 清理监听器
    this.watchers.forEach(unwatch => {
      unwatch()
    })
    this.watchers.clear()
    
    // 清理保存定时器
    if (this.saveTimers) {
      Object.values(this.saveTimers).forEach(timer => {
        clearTimeout(timer)
      })
      this.saveTimers = {}
    }
    
    console.log('🧹 持久化插件已清理')
  }
}

/**
 * 创建持久化插件的Pinia插件
 * @param {Object} options - 插件选项
 * @returns {Function} Pinia插件函数
 */
export function createPersistencePlugin(options = {}) {
  const persistence = new PersistencePlugin(options)
  const stores = new Map()
  
  return ({ store }) => {
    const storeId = store.$id
    stores.set(storeId, store)
    
    // 加载保存的状态
    const savedState = persistence.loadState(storeId)
    if (savedState) {
      store.$patch(savedState)
    }
    
    // 设置状态监听器
    persistence.setupWatcher(storeId, store)
    
    // 启动自动保存（只启动一次）
    if (stores.size === 1) {
      persistence.startAutoSave(stores)
    }
    
    // 添加插件方法到store
    store.$persistence = {
      save: () => persistence.saveState(storeId, store.$state),
      load: () => {
        const state = persistence.loadState(storeId)
        if (state) {
          store.$patch(state)
        }
        return state
      },
      remove: () => persistence.removeState(storeId),
      getStats: () => persistence.getStorageStats()
    }
  }
}

// 默认导出
export default createPersistencePlugin