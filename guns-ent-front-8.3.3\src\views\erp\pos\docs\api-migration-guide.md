# POS模块API迁移指南

## 概述

本文档提供了从原始POS模块API到重构后模块化API的迁移指南。重构后的API保持了向后兼容性，同时提供了更好的模块化结构和错误处理机制。

## API结构变化

### 重构前（原始结构）
```javascript
// 所有API都在一个大文件中
import { posApi } from '@/api/pos'

// 调用方式
posApi.checkInventory(productId, quantity)
posApi.processCashPayment(paymentData)
posApi.searchMember(keyword)
```

### 重构后（模块化结构）
```javascript
// 按功能模块分类导入
import { CartApi, PaymentApi, MemberApi, ProductApi, OrderApi } from '@/views/erp/pos/api'

// 或者单独导入
import { CartApi } from '@/views/erp/pos/api/cart'
import { PaymentApi } from '@/views/erp/pos/api/payment'

// 调用方式保持一致
CartApi.checkInventory(productId, quantity)
PaymentApi.processCashPayment(paymentData)
MemberApi.searchMember(keyword)
```

## 迁移步骤

### 1. 更新导入语句

#### 旧的导入方式
```javascript
import { posApi } from '@/api/pos'
```

#### 新的导入方式
```javascript
// 方式1：统一导入所有API
import { CartApi, PaymentApi, MemberApi, ProductApi, OrderApi } from '@/views/erp/pos/api'

// 方式2：按需导入特定API
import { CartApi } from '@/views/erp/pos/api/cart'
import { PaymentApi } from '@/views/erp/pos/api/payment'

// 方式3：使用别名导入（保持兼容性）
import * as PosApi from '@/views/erp/pos/api'
```

### 2. 更新API调用

#### 购物车相关API迁移

| 功能 | 旧调用方式 | 新调用方式 | 状态 |
|------|------------|------------|------|
| 检查库存 | `posApi.checkInventory(id, qty)` | `CartApi.checkInventory(id, qty)` | ✅ 兼容 |
| 获取商品详情 | `posApi.getProductDetail(params)` | `CartApi.getProductDetail(params)` | ✅ 兼容 |
| 搜索商品 | `posApi.searchProducts(keyword)` | `CartApi.searchProducts({keyword})` | ⚠️ 参数格式变化 |
| 保存挂单 | `posApi.saveCart(data)` | `CartApi.saveCartState(data)` | ✅ 兼容 |
| 恢复挂单 | `posApi.restoreCart(id)` | `CartApi.restoreCartState(id)` | ✅ 兼容 |

#### 支付相关API迁移

| 功能 | 旧调用方式 | 新调用方式 | 状态 |
|------|------------|------------|------|
| 现金支付 | `posApi.cashPayment(data)` | `PaymentApi.processCashPayment(data)` | ✅ 兼容 |
| 扫码支付 | `posApi.qrPayment(data)` | `PaymentApi.processQrCodePayment(data)` | ✅ 兼容 |
| 查询支付状态 | `posApi.queryPayment(id)` | `PaymentApi.queryQrCodePaymentStatus({paymentId: id})` | ⚠️ 参数格式变化 |
| 申请退款 | `posApi.refund(data)` | `PaymentApi.requestRefund(data)` | ✅ 兼容 |

#### 会员相关API迁移

| 功能 | 旧调用方式 | 新调用方式 | 状态 |
|------|------------|------------|------|
| 搜索会员 | `posApi.searchMember(keyword)` | `MemberApi.searchMember(keyword)` | ✅ 兼容 |
| 获取会员信息 | `posApi.getMember(id)` | `MemberApi.getMemberInfo(id)` | ✅ 兼容 |
| 会员积分查询 | `posApi.getMemberPoints(id)` | `MemberApi.getMemberPoints(id)` | ✅ 兼容 |

### 3. 错误处理迁移

#### 旧的错误处理方式
```javascript
try {
  const result = await posApi.checkInventory(productId, quantity)
  if (result.success) {
    // 处理成功逻辑
  } else {
    // 手动处理错误
    this.$message.error(result.message)
  }
} catch (error) {
  // 手动处理异常
  console.error('API调用失败:', error)
  this.$message.error('网络错误，请重试')
}
```

#### 新的错误处理方式
```javascript
try {
  // 错误处理已内置，无需手动处理
  const result = await CartApi.checkInventory(productId, quantity)
  // 直接处理成功逻辑
  console.log('库存检查结果:', result)
} catch (error) {
  // 错误已自动显示给用户，只需处理业务逻辑
  console.log('库存不足，禁用添加按钮')
}
```

## 兼容性说明

### 完全兼容的API

以下API调用方式完全兼容，只需更新导入语句：

```javascript
// 购物车API
CartApi.checkInventory(productId, quantity)
CartApi.getProductDetail(params)
CartApi.saveCartState(cartData)
CartApi.restoreCartState(suspendId)

// 支付API
PaymentApi.processCashPayment(paymentData)
PaymentApi.processQrCodePayment(paymentData)
PaymentApi.requestRefund(refundData)

// 会员API
MemberApi.searchMember(keyword)
MemberApi.getMemberInfo(memberId)
```

### 需要调整的API

以下API的参数格式有所变化，需要相应调整：

#### 1. 商品搜索API
```javascript
// 旧方式
posApi.searchProducts('商品名称')

// 新方式
CartApi.searchProducts({
  keyword: '商品名称',
  onlyInStock: true,  // 可选参数
  limit: 20          // 可选参数
})
```

#### 2. 支付状态查询API
```javascript
// 旧方式
posApi.queryPayment(paymentId)

// 新方式
PaymentApi.queryQrCodePaymentStatus({
  paymentId: paymentId,
  outTradeNo: tradeNo  // 可选参数
})
```

#### 3. 订单历史查询API
```javascript
// 旧方式
posApi.getOrders(cashierId, startDate, endDate)

// 新方式
OrderApi.getOrderHistory({
  cashierId: cashierId,
  startTime: startDate,
  endTime: endDate,
  pageSize: 20,  // 可选参数
  current: 1     // 可选参数
})
```

## 新增功能

重构后的API提供了一些新的功能和改进：

### 1. 自动错误处理
```javascript
// 自动显示错误消息，自动重试机制
const result = await CartApi.checkInventory(productId, quantity)
```

### 2. 性能监控
```javascript
// 自动记录API调用性能
const result = await PaymentApi.processCashPayment(paymentData)
// 性能数据会自动记录到监控系统
```

### 3. 批量操作支持
```javascript
// 批量检查库存
const results = await CartApi.batchCheckInventory([
  { productId: 'P001', quantity: 5 },
  { productId: 'P002', quantity: 3 }
])

// 批量处理支付
const result = await PaymentApi.batchProcessPayments({
  payments: paymentList,
  batchId: 'BATCH001'
})
```

### 4. 增强的配置选项
```javascript
// 自定义错误处理行为
const result = await CartApi.checkInventory(productId, quantity, {
  showMessage: false,      // 不显示错误消息
  retryOptions: {          // 自定义重试配置
    maxRetries: 5,
    retryDelay: 2000
  }
})
```

## 迁移检查清单

### 代码迁移检查
- [ ] 更新所有API导入语句
- [ ] 检查API调用方式是否需要调整
- [ ] 移除手动错误处理代码（如果使用自动错误处理）
- [ ] 更新单元测试中的API调用
- [ ] 验证所有API功能正常工作

### 功能验证检查
- [ ] 购物车功能：添加商品、修改数量、删除商品
- [ ] 支付功能：现金支付、扫码支付、退款
- [ ] 会员功能：搜索会员、应用折扣、积分抵扣
- [ ] 订单功能：创建订单、挂单恢复、订单查询
- [ ] 商品功能：商品搜索、分类浏览、库存检查

### 性能验证检查
- [ ] API响应时间是否在可接受范围内
- [ ] 错误处理是否及时响应
- [ ] 批量操作是否提升了效率
- [ ] 内存使用是否正常

## 常见问题解答

### Q1: 迁移后原有的API调用还能正常工作吗？
A1: 大部分API调用保持完全兼容，只需更新导入语句。少数API的参数格式有调整，需要按照迁移指南进行修改。

### Q2: 如何处理迁移过程中的错误？
A2: 重构后的API提供了更好的错误处理机制。如果遇到错误，请检查：
- 导入语句是否正确
- API调用参数是否符合新格式
- 网络连接是否正常

### Q3: 新的API是否支持自定义配置？
A3: 是的，新的API支持更多的配置选项，包括错误处理行为、重试机制、性能监控等。

### Q4: 如何验证迁移是否成功？
A4: 可以运行兼容性测试套件：
```bash
npm run test:compatibility
```

### Q5: 迁移后性能是否有提升？
A5: 重构后的API在以下方面有性能提升：
- 更好的错误处理减少了不必要的重试
- 批量操作减少了网络请求次数
- 性能监控帮助识别和优化慢查询

## 技术支持

如果在迁移过程中遇到问题，请：

1. 查看本文档的常见问题解答部分
2. 运行兼容性测试确认问题范围
3. 查看控制台错误信息和网络请求日志
4. 联系开发团队获取技术支持

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2025-01-02 | 初始版本，完整的API迁移指南 |
| 1.0.1 | 2025-01-02 | 添加兼容性测试说明 |
| 1.0.2 | 2025-01-02 | 补充常见问题解答 |