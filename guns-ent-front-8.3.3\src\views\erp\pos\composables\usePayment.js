/**
 * 支付组合式函数
 * 
 * 封装支付流程、支付方式选择、支付结果处理等逻辑
 * 集成支付API和错误处理机制
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, computed, reactive } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { usePosStore } from '@/stores/pos'
import { PaymentApi } from '../api/payment'
import { PaymentValidator, ChangeCalculator } from '../utils'
import { PAYMENT_METHODS, PAYMENT_STATUS } from '../utils/constants'

/**
 * 支付组合式函数
 * @returns {Object} 支付相关的状态和方法
 */
export function usePayment() {
  const store = usePosStore()
  
  // ==================== 响应式状态 ====================
  
  // 支付状态
  const paymentStatus = ref(PAYMENT_STATUS.UNPAID)
  
  // 当前选择的支付方式
  const selectedPaymentMethod = ref('')
  
  // 支付金额
  const paymentAmount = ref(0)
  
  // 实收金额（现金支付时使用）
  const receivedAmount = ref(0)
  
  // 找零金额
  const changeAmount = computed(() => {
    if (selectedPaymentMethod.value === PAYMENT_METHODS.CASH) {
      return ChangeCalculator.calculateChange(receivedAmount.value, paymentAmount.value)
    }
    return 0
  })
  
  // 支付结果
  const paymentResult = reactive({
    success: false,
    paymentId: '',
    transactionId: '',
    message: '',
    timestamp: null
  })
  
  // 加载状态
  const loading = ref(false)
  
  // 支付配置
  const paymentConfig = ref({
    enabledMethods: [],
    limits: {},
    settings: {}
  })
  
  // 可用的支付方式
  const availablePaymentMethods = computed(() => {
    return paymentConfig.value.enabledMethods.map(method => ({
      value: method,
      label: getPaymentMethodLabel(method),
      icon: getPaymentMethodIcon(method),
      enabled: true,
      limit: paymentConfig.value.limits[method]
    }))
  })
  
  // 是否可以支付
  const canPay = computed(() => {
    return paymentAmount.value > 0 && 
           selectedPaymentMethod.value && 
           paymentStatus.value === PAYMENT_STATUS.UNPAID &&
           !loading.value
  })
  
  // ==================== 核心方法 ====================
  
  /**
   * 初始化支付
   * @param {number} amount - 支付金额
   * @param {Object} options - 支付选项
   */
  const initializePayment = async (amount, options = {}) => {
    try {
      // 验证支付金额
      const amountValidation = PaymentValidator.validatePaymentAmount(amount)
      if (!amountValidation.isValid) {
        message.error(amountValidation.message)
        return false
      }
      
      paymentAmount.value = amount
      paymentStatus.value = PAYMENT_STATUS.UNPAID
      selectedPaymentMethod.value = ''
      receivedAmount.value = 0
      
      // 重置支付结果
      Object.assign(paymentResult, {
        success: false,
        paymentId: '',
        transactionId: '',
        message: '',
        timestamp: null
      })
      
      // 加载支付配置
      await loadPaymentConfig()
      
      return true
    } catch (error) {
      console.error('初始化支付失败:', error)
      message.error('初始化支付失败，请重试')
      return false
    }
  }
  
  /**
   * 选择支付方式
   * @param {string} method - 支付方式
   */
  const selectPaymentMethod = (method) => {
    if (!paymentConfig.value.enabledMethods.includes(method)) {
      message.error('该支付方式不可用')
      return false
    }
    
    const limit = paymentConfig.value.limits[method]
    if (limit && limit.max && paymentAmount.value > limit.max) {
      message.error(`${getPaymentMethodLabel(method)}单笔限额为${limit.max}元`)
      return false
    }
    
    selectedPaymentMethod.value = method
    
    // 现金支付时，默认实收金额等于应付金额
    if (method === PAYMENT_METHODS.CASH) {
      receivedAmount.value = paymentAmount.value
    }
    
    return true
  }
  
  /**
   * 处理现金支付
   * @param {Object} params - 支付参数
   * @returns {Promise<boolean>} 支付是否成功
   */
  const processCashPayment = async (params = {}) => {
    try {
      loading.value = true
      paymentStatus.value = PAYMENT_STATUS.PROCESSING
      
      const paymentData = {
        orderId: params.orderId || store.currentOrderId,
        paymentAmount: paymentAmount.value,
        receivedAmount: receivedAmount.value,
        changeAmount: changeAmount.value,
        cashierId: params.cashierId || store.currentUser?.id
      }
      
      // 验证现金支付数据
      const validation = PaymentValidator.validateCashPayment(paymentData)
      if (!validation.isValid) {
        message.error(validation.message)
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        return false
      }
      
      const result = await PaymentApi.processCashPayment(paymentData)
      
      if (result.success) {
        // 更新支付结果
        Object.assign(paymentResult, {
          success: true,
          paymentId: result.paymentId,
          transactionId: result.transactionId,
          message: '现金支付成功',
          timestamp: new Date().toISOString()
        })
        
        paymentStatus.value = PAYMENT_STATUS.PAID
        message.success('现金支付成功')
        return true
      } else {
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        message.error(result.message || '现金支付失败')
        return false
      }
    } catch (error) {
      console.error('现金支付失败:', error)
      paymentStatus.value = PAYMENT_STATUS.UNPAID
      message.error('现金支付失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 处理扫码支付
   * @param {Object} params - 支付参数
   * @returns {Promise<boolean>} 支付是否成功
   */
  const processQrCodePayment = async (params = {}) => {
    try {
      loading.value = true
      paymentStatus.value = PAYMENT_STATUS.PROCESSING
      
      const paymentData = {
        orderId: params.orderId || store.currentOrderId,
        paymentAmount: paymentAmount.value,
        paymentMethod: selectedPaymentMethod.value,
        qrCode: params.qrCode || '',
        cashierId: params.cashierId || store.currentUser?.id
      }
      
      // 验证扫码支付数据
      const validation = PaymentValidator.validateQrCodePayment(paymentData)
      if (!validation.isValid) {
        message.error(validation.message)
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        return false
      }
      
      const result = await PaymentApi.processQrCodePayment(paymentData)
      
      if (result.success) {
        // 开始轮询支付状态
        const paymentSuccess = await pollPaymentStatus(result.paymentId)
        
        if (paymentSuccess) {
          Object.assign(paymentResult, {
            success: true,
            paymentId: result.paymentId,
            transactionId: result.transactionId,
            message: `${getPaymentMethodLabel(selectedPaymentMethod.value)}支付成功`,
            timestamp: new Date().toISOString()
          })
          
          paymentStatus.value = PAYMENT_STATUS.PAID
          message.success(`${getPaymentMethodLabel(selectedPaymentMethod.value)}支付成功`)
          return true
        } else {
          paymentStatus.value = PAYMENT_STATUS.UNPAID
          message.error('支付失败或已取消')
          return false
        }
      } else {
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        message.error(result.message || '发起支付失败')
        return false
      }
    } catch (error) {
      console.error('扫码支付失败:', error)
      paymentStatus.value = PAYMENT_STATUS.UNPAID
      message.error('扫码支付失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 处理会员卡支付
   * @param {Object} params - 支付参数
   * @returns {Promise<boolean>} 支付是否成功
   */
  const processMemberCardPayment = async (params = {}) => {
    try {
      loading.value = true
      paymentStatus.value = PAYMENT_STATUS.PROCESSING
      
      const paymentData = {
        orderId: params.orderId || store.currentOrderId,
        paymentAmount: paymentAmount.value,
        memberId: params.memberId,
        memberCardNo: params.memberCardNo,
        cashierId: params.cashierId || store.currentUser?.id
      }
      
      if (!paymentData.memberId || !paymentData.memberCardNo) {
        message.error('请先选择会员')
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        return false
      }
      
      const result = await PaymentApi.processMemberCardPayment(paymentData)
      
      if (result.success) {
        Object.assign(paymentResult, {
          success: true,
          paymentId: result.paymentId,
          transactionId: result.transactionId,
          message: '会员卡支付成功',
          timestamp: new Date().toISOString()
        })
        
        paymentStatus.value = PAYMENT_STATUS.PAID
        message.success(`会员卡支付成功，余额：${result.remainingBalance}元`)
        return true
      } else {
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        message.error(result.message || '会员卡支付失败')
        return false
      }
    } catch (error) {
      console.error('会员卡支付失败:', error)
      paymentStatus.value = PAYMENT_STATUS.UNPAID
      message.error('会员卡支付失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 处理组合支付
   * @param {Array} paymentMethods - 支付方式列表 [{method, amount}]
   * @param {Object} params - 支付参数
   * @returns {Promise<boolean>} 支付是否成功
   */
  const processComboPayment = async (paymentMethods, params = {}) => {
    try {
      loading.value = true
      paymentStatus.value = PAYMENT_STATUS.PROCESSING
      
      // 验证组合支付金额
      const totalAmount = paymentMethods.reduce((sum, pm) => sum + pm.amount, 0)
      if (Math.abs(totalAmount - paymentAmount.value) > 0.01) {
        message.error('组合支付金额不匹配')
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        return false
      }
      
      const paymentData = {
        orderId: params.orderId || store.currentOrderId,
        totalAmount: paymentAmount.value,
        paymentMethods: paymentMethods,
        cashierId: params.cashierId || store.currentUser?.id
      }
      
      const result = await PaymentApi.processComboPayment(paymentData)
      
      if (result.success) {
        Object.assign(paymentResult, {
          success: true,
          paymentId: result.paymentId,
          transactionId: result.transactionId,
          message: '组合支付成功',
          timestamp: new Date().toISOString()
        })
        
        paymentStatus.value = PAYMENT_STATUS.PAID
        message.success('组合支付成功')
        return true
      } else {
        paymentStatus.value = PAYMENT_STATUS.UNPAID
        message.error(result.message || '组合支付失败')
        return false
      }
    } catch (error) {
      console.error('组合支付失败:', error)
      paymentStatus.value = PAYMENT_STATUS.UNPAID
      message.error('组合支付失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 轮询支付状态
   * @param {string} paymentId - 支付ID
   * @param {number} maxAttempts - 最大轮询次数
   * @returns {Promise<boolean>} 支付是否成功
   */
  const pollPaymentStatus = async (paymentId, maxAttempts = 30) => {
    let attempts = 0
    
    return new Promise((resolve) => {
      const poll = async () => {
        try {
          attempts++
          
          const status = await PaymentApi.queryQrCodePaymentStatus({
            paymentId,
            outTradeNo: `${store.currentOrderId}_${paymentId}`
          })
          
          if (status.status === 'SUCCESS') {
            resolve(true)
            return
          }
          
          if (status.status === 'FAILED' || status.status === 'CANCELLED') {
            resolve(false)
            return
          }
          
          if (attempts >= maxAttempts) {
            message.warning('支付超时，请检查支付状态')
            resolve(false)
            return
          }
          
          // 继续轮询
          setTimeout(poll, 2000)
        } catch (error) {
          console.error('查询支付状态失败:', error)
          if (attempts >= maxAttempts) {
            resolve(false)
          } else {
            setTimeout(poll, 2000)
          }
        }
      }
      
      poll()
    })
  }
  
  /**
   * 取消支付
   * @param {string} reason - 取消原因
   * @returns {Promise<boolean>} 是否取消成功
   */
  const cancelPayment = async (reason = '用户取消') => {
    try {
      if (paymentResult.paymentId) {
        await PaymentApi.cancelPayment({
          paymentId: paymentResult.paymentId,
          cancelReason: reason,
          cancelBy: store.currentUser?.id
        })
      }
      
      // 重置支付状态
      paymentStatus.value = PAYMENT_STATUS.UNPAID
      selectedPaymentMethod.value = ''
      receivedAmount.value = 0
      
      Object.assign(paymentResult, {
        success: false,
        paymentId: '',
        transactionId: '',
        message: '',
        timestamp: null
      })
      
      message.info('支付已取消')
      return true
    } catch (error) {
      console.error('取消支付失败:', error)
      message.error('取消支付失败')
      return false
    }
  }
  
  /**
   * 申请退款
   * @param {Object} params - 退款参数
   * @returns {Promise<boolean>} 是否申请成功
   */
  const requestRefund = async (params) => {
    try {
      loading.value = true
      
      const refundData = {
        paymentId: params.paymentId || paymentResult.paymentId,
        refundAmount: params.refundAmount || paymentAmount.value,
        refundReason: params.refundReason || '客户要求退款',
        refundBy: params.refundBy || store.currentUser?.id
      }
      
      const result = await PaymentApi.requestRefund(refundData)
      
      if (result.success) {
        message.success('退款申请已提交')
        return true
      } else {
        message.error(result.message || '退款申请失败')
        return false
      }
    } catch (error) {
      console.error('申请退款失败:', error)
      message.error('申请退款失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 加载支付配置
   */
  const loadPaymentConfig = async () => {
    try {
      const config = await PaymentApi.getPaymentConfig({
        storeId: store.currentStore?.id
      })
      
      paymentConfig.value = {
        enabledMethods: config.enabledMethods || Object.values(PAYMENT_METHODS),
        limits: config.limits || {},
        settings: config.settings || {}
      }
    } catch (error) {
      console.error('加载支付配置失败:', error)
      // 使用默认配置
      paymentConfig.value = {
        enabledMethods: Object.values(PAYMENT_METHODS),
        limits: {},
        settings: {}
      }
    }
  }
  
  /**
   * 验证支付密码
   * @param {string} password - 支付密码
   * @returns {Promise<boolean>} 验证是否成功
   */
  const validatePaymentPassword = async (password) => {
    try {
      const result = await PaymentApi.validatePaymentPassword({
        password,
        cashierId: store.currentUser?.id
      })
      
      return result.valid
    } catch (error) {
      console.error('验证支付密码失败:', error)
      return false
    }
  }
  
  /**
   * 显示支付确认对话框
   * @param {Object} options - 对话框选项
   * @returns {Promise<boolean>} 用户是否确认
   */
  const showPaymentConfirmDialog = (options = {}) => {
    return new Promise((resolve) => {
      Modal.confirm({
        title: options.title || '确认支付',
        content: options.content || `确认支付 ${paymentAmount.value} 元？`,
        okText: '确认支付',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })
  }
  
  // ==================== 工具方法 ====================
  
  /**
   * 获取支付方式标签
   * @param {string} method - 支付方式
   * @returns {string} 标签
   */
  const getPaymentMethodLabel = (method) => {
    const labels = {
      [PAYMENT_METHODS.CASH]: '现金',
      [PAYMENT_METHODS.WECHAT]: '微信支付',
      [PAYMENT_METHODS.ALIPAY]: '支付宝',
      [PAYMENT_METHODS.MEMBER]: '会员卡',
      [PAYMENT_METHODS.CARD]: '银行卡',
      [PAYMENT_METHODS.POINTS]: '积分支付'
    }
    return labels[method] || method
  }
  
  /**
   * 获取支付方式图标
   * @param {string} method - 支付方式
   * @returns {string} 图标名称
   */
  const getPaymentMethodIcon = (method) => {
    const icons = {
      [PAYMENT_METHODS.CASH]: 'money-collect',
      [PAYMENT_METHODS.WECHAT]: 'wechat',
      [PAYMENT_METHODS.ALIPAY]: 'alipay',
      [PAYMENT_METHODS.MEMBER]: 'credit-card',
      [PAYMENT_METHODS.CARD]: 'bank',
      [PAYMENT_METHODS.POINTS]: 'gift'
    }
    return icons[method] || 'pay-circle'
  }
  
  /**
   * 格式化找零面额分布
   * @param {number} amount - 找零金额
   * @returns {Object} 面额分布
   */
  const formatChangeDenominations = (amount) => {
    return ChangeCalculator.calculateChangeDenominations(amount)
  }
  
  // ==================== 返回接口 ====================
  
  return {
    // 状态
    paymentStatus,
    selectedPaymentMethod,
    paymentAmount,
    receivedAmount,
    changeAmount,
    paymentResult,
    loading,
    paymentConfig,
    availablePaymentMethods,
    canPay,
    
    // 方法
    initializePayment,
    selectPaymentMethod,
    processCashPayment,
    processQrCodePayment,
    processMemberCardPayment,
    processComboPayment,
    cancelPayment,
    requestRefund,
    validatePaymentPassword,
    showPaymentConfirmDialog,
    
    // 工具方法
    getPaymentMethodLabel,
    getPaymentMethodIcon,
    formatChangeDenominations
  }
}