/**
 * 会员相关API接口
 * 
 * 提供会员查询、积分处理、折扣计算等功能的API封装
 * 从原始pos.js中抽离出会员相关的API接口
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import Request from '@/utils/request/request-util'
import { PosErrorHandler } from '../utils/error-handler'
import { PerformanceMonitor } from '../utils/performance-monitor'
import { MEMBER_LEVELS } from '../utils/constants'

/**
 * 会员API类
 */
export class MemberApi {
  
  /**
   * 创建带错误处理和性能监控的API包装器
   * @param {Function} apiFunction - 原始API函数
   * @param {Object} options - 配置选项
   * @returns {Function} 包装后的API函数
   */
  static createApiWrapper(apiFunction, options = {}) {
    const {
      context = 'Member API调用',
      showMessage = true,
      showNotification = false,
      retryOptions = { maxRetries: 2, retryDelay: 1000 }
    } = options
    
    // 包装性能监控
    const monitoredFunction = PerformanceMonitor.measureApiCall(context, apiFunction)
    
    // 包装错误处理
    return PosErrorHandler.wrapApiCall(monitoredFunction, {
      showMessage,
      showNotification,
      context,
      retryOptions
    })
  }
  
  /**
   * 根据会员卡号查询会员信息
   * @param {string} cardNo - 会员卡号
   * @returns {Promise<Object>} 会员信息
   */
  static async getMemberByCardNo(cardNo) {
    const apiCall = () => Request.get('/erp/member/getByCardNo', { cardNo })
    
    return this.createApiWrapper(apiCall, {
      context: '根据卡号查询会员',
      showMessage: true,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 根据手机号查询会员信息
   * @param {string} phone - 手机号
   * @returns {Promise<Object>} 会员信息
   */
  static async getMemberByPhone(phone) {
    const apiCall = () => Request.get('/erp/member/getByPhone', { phone })
    
    return this.createApiWrapper(apiCall, {
      context: '根据手机号查询会员',
      showMessage: true,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 根据身份证号查询会员信息
   * @param {string} idCard - 身份证号
   * @returns {Promise<Object>} 会员信息
   */
  static async getMemberByIdCard(idCard) {
    const apiCall = () => Request.get('/erp/member/getByIdCard', { idCard })
    
    return this.createApiWrapper(apiCall, {
      context: '根据身份证查询会员',
      showMessage: true,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 获取会员详细信息
   * @param {number} memberId - 会员ID
   * @returns {Promise<Object>} 会员详细信息
   */
  static async getMemberDetail(memberId) {
    const apiCall = () => Request.get('/erp/member/detail', { memberId })
    
    return this.createApiWrapper(apiCall, {
      context: '获取会员详细信息',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 搜索会员
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词（姓名、手机号、卡号）
   * @param {string} params.level - 会员等级
   * @param {string} params.status - 会员状态
   * @param {number} params.limit - 返回数量限制
   * @returns {Promise<Array>} 会员列表
   */
  static async searchMembers(params = {}) {
    const apiCall = () => Request.get('/erp/member/search', {
      ...params,
      limit: params.limit || 20
    })
    
    return this.createApiWrapper(apiCall, {
      context: '搜索会员',
      showMessage: false,
      retryOptions: { maxRetries: 1, retryDelay: 300 }
    })()
  }
  
  /**
   * 验证会员密码
   * @param {Object} params - 验证参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.password - 密码
   * @returns {Promise<Object>} 验证结果
   */
  static async validateMemberPassword(params) {
    const apiCall = () => Request.post('/erp/member/validatePassword', params)
    
    return this.createApiWrapper(apiCall, {
      context: '验证会员密码',
      showMessage: false,
      retryOptions: { maxRetries: 0 } // 密码验证不重试
    })()
  }
  
  /**
   * 计算会员折扣
   * @param {Object} params - 计算参数
   * @param {number} params.memberId - 会员ID
   * @param {number} params.totalAmount - 订单总金额
   * @param {Array} params.items - 商品列表
   * @param {string} params.promotionCode - 促销代码
   * @returns {Promise<Object>} 折扣信息
   */
  static async calculateMemberDiscount(params) {
    const apiCall = () => Request.post('/erp/member/calculateDiscount', params)
    
    return this.createApiWrapper(apiCall, {
      context: '计算会员折扣',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 300 }
    })()
  }
  
  /**
   * 计算积分抵扣
   * @param {Object} params - 计算参数
   * @param {number} params.memberId - 会员ID
   * @param {number} params.points - 使用积分数
   * @param {number} params.totalAmount - 订单总金额
   * @param {number} params.maxDeductionRate - 最大抵扣比例
   * @returns {Promise<Object>} 抵扣信息
   */
  static async calculatePointsDeduction(params) {
    const apiCall = () => Request.post('/erp/member/calculatePointsDeduction', params)
    
    return this.createApiWrapper(apiCall, {
      context: '计算积分抵扣',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 300 }
    })()
  }
  
  /**
   * 获取会员可用积分
   * @param {number} memberId - 会员ID
   * @returns {Promise<Object>} 积分信息
   */
  static async getMemberPoints(memberId) {
    const apiCall = () => Request.get('/erp/member/points', { memberId })
    
    return this.createApiWrapper(apiCall, {
      context: '获取会员积分',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 为会员增加积分
   * @param {Object} params - 积分参数
   * @param {number} params.memberId - 会员ID
   * @param {number} params.points - 增加的积分数
   * @param {string} params.orderId - 关联订单ID
   * @param {string} params.source - 积分来源
   * @param {string} params.remark - 备注
   * @returns {Promise<Object>} 操作结果
   */
  static async addMemberPoints(params) {
    const apiCall = () => Request.post('/erp/member/addPoints', {
      ...params,
      operateTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '增加会员积分',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 扣减会员积分
   * @param {Object} params - 积分参数
   * @param {number} params.memberId - 会员ID
   * @param {number} params.points - 扣减的积分数
   * @param {string} params.orderId - 关联订单ID
   * @param {string} params.reason - 扣减原因
   * @param {string} params.remark - 备注
   * @returns {Promise<Object>} 操作结果
   */
  static async deductMemberPoints(params) {
    const apiCall = () => Request.post('/erp/member/deductPoints', {
      ...params,
      operateTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '扣减会员积分',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 获取会员积分明细
   * @param {Object} params - 查询参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @param {string} params.type - 积分类型（EARN/DEDUCT）
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页大小
   * @returns {Promise<Object>} 积分明细分页数据
   */
  static async getMemberPointsHistory(params = {}) {
    const apiCall = () => Request.get('/erp/member/pointsHistory', {
      ...params,
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20
    })
    
    return this.createApiWrapper(apiCall, {
      context: '获取积分明细',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 获取会员余额
   * @param {number} memberId - 会员ID
   * @returns {Promise<Object>} 余额信息
   */
  static async getMemberBalance(memberId) {
    const apiCall = () => Request.get('/erp/member/balance', { memberId })
    
    return this.createApiWrapper(apiCall, {
      context: '获取会员余额',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 会员余额充值
   * @param {Object} params - 充值参数
   * @param {number} params.memberId - 会员ID
   * @param {number} params.amount - 充值金额
   * @param {string} params.paymentMethod - 支付方式
   * @param {string} params.operatorId - 操作员ID
   * @param {string} params.remark - 备注
   * @returns {Promise<Object>} 充值结果
   */
  static async rechargeMemberBalance(params) {
    const apiCall = () => Request.post('/erp/member/recharge', {
      ...params,
      rechargeTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '会员余额充值',
      showMessage: true,
      showNotification: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 扣减会员余额
   * @param {Object} params - 扣减参数
   * @param {number} params.memberId - 会员ID
   * @param {number} params.amount - 扣减金额
   * @param {string} params.orderId - 关联订单ID
   * @param {string} params.reason - 扣减原因
   * @param {string} params.operatorId - 操作员ID
   * @returns {Promise<Object>} 扣减结果
   */
  static async deductMemberBalance(params) {
    const apiCall = () => Request.post('/erp/member/deductBalance', {
      ...params,
      operateTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '扣减会员余额',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 获取会员余额明细
   * @param {Object} params - 查询参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @param {string} params.type - 余额变动类型
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页大小
   * @returns {Promise<Object>} 余额明细分页数据
   */
  static async getMemberBalanceHistory(params = {}) {
    const apiCall = () => Request.get('/erp/member/balanceHistory', {
      ...params,
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20
    })
    
    return this.createApiWrapper(apiCall, {
      context: '获取余额明细',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 升级会员等级
   * @param {Object} params - 升级参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.newLevel - 新等级
   * @param {string} params.reason - 升级原因
   * @param {string} params.operatorId - 操作员ID
   * @returns {Promise<Object>} 升级结果
   */
  static async upgradeMemberLevel(params) {
    const apiCall = () => Request.post('/erp/member/upgradeLevel', {
      ...params,
      upgradeTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '升级会员等级',
      showMessage: true,
      showNotification: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 获取会员等级规则
   * @returns {Promise<Array>} 等级规则列表
   */
  static async getMemberLevelRules() {
    const apiCall = () => Request.get('/erp/member/levelRules')
    
    return this.createApiWrapper(apiCall, {
      context: '获取会员等级规则',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 获取会员消费记录
   * @param {Object} params - 查询参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页大小
   * @returns {Promise<Object>} 消费记录分页数据
   */
  static async getMemberConsumptionHistory(params = {}) {
    const apiCall = () => Request.get('/erp/member/consumptionHistory', {
      ...params,
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20
    })
    
    return this.createApiWrapper(apiCall, {
      context: '获取消费记录',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 获取会员统计信息
   * @param {number} memberId - 会员ID
   * @returns {Promise<Object>} 统计信息
   */
  static async getMemberStatistics(memberId) {
    const apiCall = () => Request.get('/erp/member/statistics', { memberId })
    
    return this.createApiWrapper(apiCall, {
      context: '获取会员统计',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 冻结会员账户
   * @param {Object} params - 冻结参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.reason - 冻结原因
   * @param {string} params.operatorId - 操作员ID
   * @returns {Promise<Object>} 冻结结果
   */
  static async freezeMember(params) {
    const apiCall = () => Request.post('/erp/member/freeze', {
      ...params,
      freezeTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '冻结会员账户',
      showMessage: true,
      showNotification: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 解冻会员账户
   * @param {Object} params - 解冻参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.reason - 解冻原因
   * @param {string} params.operatorId - 操作员ID
   * @returns {Promise<Object>} 解冻结果
   */
  static async unfreezeMember(params) {
    const apiCall = () => Request.post('/erp/member/unfreeze', {
      ...params,
      unfreezeTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '解冻会员账户',
      showMessage: true,
      showNotification: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 获取会员优惠券
   * @param {Object} params - 查询参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.status - 优惠券状态
   * @param {boolean} params.available - 是否只返回可用的
   * @returns {Promise<Array>} 优惠券列表
   */
  static async getMemberCoupons(params = {}) {
    const apiCall = () => Request.get('/erp/member/coupons', params)
    
    return this.createApiWrapper(apiCall, {
      context: '获取会员优惠券',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 发放优惠券给会员
   * @param {Object} params - 发放参数
   * @param {number} params.memberId - 会员ID
   * @param {string} params.couponTemplateId - 优惠券模板ID
   * @param {number} params.quantity - 发放数量
   * @param {string} params.reason - 发放原因
   * @param {string} params.operatorId - 操作员ID
   * @returns {Promise<Object>} 发放结果
   */
  static async issueCouponToMember(params) {
    const apiCall = () => Request.post('/erp/member/issueCoupon', {
      ...params,
      issueTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '发放优惠券',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
}