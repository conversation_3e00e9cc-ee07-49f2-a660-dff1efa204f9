import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { EnhancedPersistencePlugin } from '../enhanced-persistence'
import * as persistenceConfig from '../../config/persistence'

// Mock localStorage and sessionStorage
const createMockStorage = () => {
  const storage = new Map()
  return {
    getItem: vi.fn((key) => storage.get(key) || null),
    setItem: vi.fn((key, value) => storage.set(key, value)),
    removeItem: vi.fn((key) => storage.delete(key)),
    clear: vi.fn(() => storage.clear()),
    get length() { return storage.size },
    key: vi.fn((index) => Array.from(storage.keys())[index] || null)
  }
}

const mockLocalStorage = createMockStorage()
const mockSessionStorage = createMockStorage()

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })
Object.defineProperty(window, 'sessionStorage', { value: mockSessionStorage })

// Mock persistence config
vi.mock('../../config/persistence', () => ({
  getPersistenceConfig: vi.fn((storeId) => ({
    keyPrefix: `test_${storeId}_`,
    enableEncryption: false,
    enableCompression: true,
    expireTime: 60 * 60 * 1000, // 1 hour
    storageEngine: 'localStorage',
    autoSaveInterval: 1000,
    enableAutoSave: true,
    persistPaths: [],
    excludePaths: ['tempData'],
    sensitiveFields: ['password', 'token']
  })),
  maskSensitiveData: vi.fn((data) => ({ ...data, password: '***' })),
  generateChecksum: vi.fn(() => 'test_checksum'),
  verifyChecksum: vi.fn(() => true),
  cleanupExpiredData: vi.fn(() => 0),
  checkStorageQuota: vi.fn(() => ({
    usage: 1000,
    available: 9000,
    percentage: 0.1,
    isWarning: false,
    needsCleanup: false
  })),
  autoCleanupStorage: vi.fn(() => true),
  securityConfig: {
    enableIntegrityCheck: true,
    enableAutoCleanup: true,
    autoCleanupInterval: 60000,
    encryptionKey: 'test_key'
  },
  performanceConfig: {
    batchSave: {
      enabled: true,
      batchSize: 10,
      batchInterval: 5000
    },
    compression: {
      enabled: true,
      threshold: 100
    },
    cache: {
      enabled: true,
      maxSize: 50,
      ttl: 300000
    },
    quota: {
      enabled: true,
      maxSize: 10000,
      warningThreshold: 0.8,
      cleanupThreshold: 0.9
    }
  }
}))

describe('EnhancedPersistencePlugin', () => {
  let plugin
  
  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    plugin = new EnhancedPersistencePlugin({
      enableBatchSave: true,
      enableQuotaManagement: true,
      enableAutoCleanup: true
    })
  })
  
  afterEach(() => {
    plugin.cleanup()
    vi.useRealTimers()
    vi.restoreAllMocks()
  })

  describe('初始化', () => {
    it('应该正确初始化插件', () => {
      expect(plugin.stores).toBeDefined()
      expect(plugin.watchers).toBeDefined()
      expect(plugin.batchSaveQueue).toBeDefined()
      expect(plugin.cache).toBeDefined()
    })

    it('应该启动批量保存定时器', () => {
      expect(plugin.batchSaveTimer).toBeTruthy()
    })
  })

  describe('数据序列化', () => {
    it('应该正确序列化状态数据', () => {
      const storeId = 'test-store'
      const state = {
        data: 'test data',
        count: 42,
        tempData: 'should be excluded'
      }
      
      const result = plugin.serializeState(storeId, state)
      
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
      expect(result.originalSize).toBeGreaterThan(0)
      expect(result.finalSize).toBeGreaterThan(0)
    })

    it('应该正确反序列化状态数据', () => {
      const storeId = 'test-store'
      const state = { data: 'test data', count: 42 }
      
      // 先序列化
      const serializeResult = plugin.serializeState(storeId, state)
      expect(serializeResult.success).toBe(true)
      
      // 再反序列化
      const deserializeResult = plugin.deserializeState(storeId, serializeResult.data)
      
      expect(deserializeResult.success).toBe(true)
      expect(deserializeResult.data).toEqual(expect.objectContaining({
        data: 'test data',
        count: 42
      }))
    })

    it('应该处理序列化错误', () => {
      const storeId = 'test-store'
      const circularState = {}
      circularState.self = circularState // 创建循环引用
      
      const result = plugin.serializeState(storeId, circularState)
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })

  describe('数据压缩', () => {
    it('应该压缩大数据', () => {
      const largeData = 'x'.repeat(1000) // 创建大于阈值的数据
      const compressed = plugin.compressData(largeData, { enableCompression: true })
      
      expect(compressed.length).toBeLessThanOrEqual(largeData.length)
    })

    it('应该跳过小数据的压缩', () => {
      const smallData = 'small'
      const compressed = plugin.compressData(smallData, { enableCompression: true })
      
      expect(compressed).toBe(smallData)
    })
  })

  describe('数据加密', () => {
    it('应该加密数据', () => {
      const data = 'sensitive data'
      const encrypted = plugin.encryptData(data, { enableEncryption: true })
      
      expect(encrypted).not.toBe(data)
      expect(encrypted.length).toBeGreaterThan(0)
    })

    it('应该解密数据', () => {
      const data = 'sensitive data'
      const encrypted = plugin.encryptData(data, { enableEncryption: true })
      const decrypted = plugin.decryptData(encrypted, { enableEncryption: true })
      
      expect(decrypted).toBe(data)
    })

    it('应该跳过未启用加密的数据', () => {
      const data = 'normal data'
      const result = plugin.encryptData(data, { enableEncryption: false })
      
      expect(result).toBe(data)
    })
  })

  describe('状态保存和加载', () => {
    it('应该保存状态到存储', () => {
      const storeId = 'test-store'
      const state = { data: 'test', count: 1 }
      
      const result = plugin.saveState(storeId, state, true) // 立即保存
      
      expect(result).toBe(true)
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('应该从存储加载状态', () => {
      const storeId = 'test-store'
      const state = { data: 'test', count: 1 }
      
      // 先保存
      plugin.saveState(storeId, state, true)
      
      // 再加载
      const loadedState = plugin.loadState(storeId)
      
      expect(loadedState).toEqual(expect.objectContaining(state))
    })

    it('应该处理不存在的状态', () => {
      const storeId = 'nonexistent-store'
      
      const loadedState = plugin.loadState(storeId)
      
      expect(loadedState).toBe(null)
    })
  })

  describe('批量保存', () => {
    it('应该添加状态到批量保存队列', () => {
      const storeId = 'test-store'
      const state = { data: 'test' }
      
      plugin.addToBatchSaveQueue(storeId, state)
      
      expect(plugin.batchSaveQueue.has(storeId)).toBe(true)
      expect(plugin.batchSaveQueue.get(storeId)).toHaveLength(1)
    })

    it('应该执行批量保存', () => {
      const storeId = 'test-store'
      const state = { data: 'test' }
      
      plugin.addToBatchSaveQueue(storeId, state)
      plugin.executeBatchSave()
      
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
      expect(plugin.batchSaveQueue.size).toBe(0)
    })

    it('应该定期执行批量保存', () => {
      const storeId = 'test-store'
      const state = { data: 'test' }
      
      plugin.addToBatchSaveQueue(storeId, state)
      
      // 快进时间触发批量保存
      vi.advanceTimersByTime(5000)
      
      expect(mockLocalStorage.setItem).toHaveBeenCalled()
    })

    it('应该限制批量保存队列大小', () => {
      const storeId = 'test-store'
      
      // 添加超过限制的状态
      for (let i = 0; i < 15; i++) {
        plugin.addToBatchSaveQueue(storeId, { data: `test${i}` })
      }
      
      const queue = plugin.batchSaveQueue.get(storeId)
      expect(queue.length).toBeLessThanOrEqual(10) // 应该被限制在10个以内
    })
  })

  describe('缓存管理', () => {
    it('应该更新缓存', () => {
      const storeId = 'test-store'
      const data = { test: 'data' }
      
      plugin.updateCache(storeId, data)
      
      expect(plugin.cache.has(storeId)).toBe(true)
    })

    it('应该从缓存获取数据', () => {
      const storeId = 'test-store'
      const data = { test: 'data' }
      
      plugin.updateCache(storeId, data)
      const cached = plugin.getFromCache(storeId)
      
      expect(cached).toEqual(data)
    })

    it('应该处理过期的缓存', () => {
      const storeId = 'test-store'
      const data = { test: 'data' }
      
      plugin.updateCache(storeId, data)
      
      // 快进时间使缓存过期
      vi.advanceTimersByTime(400000) // 超过TTL
      
      const cached = plugin.getFromCache(storeId)
      expect(cached).toBe(null)
    })

    it('应该限制缓存大小', () => {
      // 添加超过限制的缓存项
      for (let i = 0; i < 60; i++) {
        plugin.updateCache(`store${i}`, { data: i })
      }
      
      expect(plugin.cache.size).toBeLessThanOrEqual(50)
    })
  })

  describe('自动清理', () => {
    it('应该执行自动清理', () => {
      plugin.performAutoCleanup()
      
      expect(persistenceConfig.cleanupExpiredData).toHaveBeenCalled()
    })

    it('应该定期执行自动清理', () => {
      // 快进时间触发自动清理
      vi.advanceTimersByTime(60000)
      
      expect(persistenceConfig.cleanupExpiredData).toHaveBeenCalled()
    })

    it('应该清理过期缓存', () => {
      const storeId = 'test-store'
      plugin.updateCache(storeId, { test: 'data' })
      
      // 手动设置过期时间
      const cached = plugin.cache.get(storeId)
      cached.timestamp = Date.now() - 400000 // 设置为过期
      
      plugin.cleanupCache()
      
      expect(plugin.cache.has(storeId)).toBe(false)
    })
  })

  describe('配额管理', () => {
    it('应该执行配额检查', () => {
      plugin.performQuotaCheck()
      
      expect(persistenceConfig.checkStorageQuota).toHaveBeenCalled()
    })

    it('应该在配额不足时自动清理', () => {
      // Mock配额不足的情况
      persistenceConfig.checkStorageQuota.mockReturnValue({
        usage: 9500,
        available: 500,
        percentage: 0.95,
        isWarning: true,
        needsCleanup: true
      })
      
      plugin.performQuotaCheck()
      
      expect(persistenceConfig.autoCleanupStorage).toHaveBeenCalled()
    })
  })

  describe('状态过滤', () => {
    it('应该根据配置过滤状态', () => {
      const state = {
        data: 'keep this',
        tempData: 'exclude this',
        count: 42,
        func: () => {}, // 应该被排除
        symbol: Symbol('test') // 应该被排除
      }
      
      const config = {
        excludePaths: ['tempData'],
        persistPaths: []
      }
      
      const filtered = plugin.filterState(state, config)
      
      expect(filtered.data).toBe('keep this')
      expect(filtered.count).toBe(42)
      expect(filtered.tempData).toBeUndefined()
      expect(filtered.func).toBeUndefined()
      expect(filtered.symbol).toBeUndefined()
    })

    it('应该只保留指定的路径', () => {
      const state = {
        data: 'keep this',
        otherData: 'exclude this',
        count: 42
      }
      
      const config = {
        excludePaths: [],
        persistPaths: ['data', 'count']
      }
      
      const filtered = plugin.filterState(state, config)
      
      expect(filtered.data).toBe('keep this')
      expect(filtered.count).toBe(42)
      expect(filtered.otherData).toBeUndefined()
    })
  })

  describe('错误处理', () => {
    it('应该处理存储错误', () => {
      mockLocalStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })
      
      const storeId = 'test-store'
      const state = { data: 'test' }
      
      const result = plugin.saveState(storeId, state, true)
      
      expect(result).toBe(false)
    })

    it('应该处理损坏的数据', () => {
      const storeId = 'test-store'
      
      // Mock损坏的数据
      mockLocalStorage.getItem.mockReturnValue('invalid json data')
      
      const loadedState = plugin.loadState(storeId)
      
      expect(loadedState).toBe(null)
    })
  })

  describe('统计信息', () => {
    it('应该返回正确的统计信息', () => {
      plugin.stores.set('store1', {})
      plugin.stores.set('store2', {})
      plugin.updateCache('cache1', { data: 'test' })
      
      const stats = plugin.getStatistics()
      
      expect(stats.stores).toBe(2)
      expect(stats.cache.size).toBe(1)
      expect(stats.localStorage).toBeDefined()
      expect(stats.sessionStorage).toBeDefined()
    })
  })

  describe('资源清理', () => {
    it('应该正确清理所有资源', () => {
      // 添加一些资源
      plugin.stores.set('store1', {})
      plugin.updateCache('cache1', { data: 'test' })
      plugin.addToBatchSaveQueue('store1', { data: 'test' })
      
      plugin.cleanup()
      
      expect(plugin.cache.size).toBe(0)
      expect(plugin.batchSaveQueue.size).toBe(0)
      expect(plugin.batchSaveTimer).toBe(null)
    })
  })
})