/**
 * 错误处理器单元测试
 * 
 * 测试错误处理功能的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { PosErrorHandler } from '../error-handler'
import { PosErrorTypes, ErrorMessages } from '../error-types'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  notification: {
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock console
const mockConsole = {
  error: vi.fn(),
  warn: vi.fn(),
  log: vi.fn()
}
global.console = mockConsole

describe('PosErrorHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('wrapApiCall', () => {
    it('应该成功包装API调用', async () => {
      const mockApiFunction = vi.fn().mockResolvedValue({ success: true, data: 'test' })
      const wrappedFunction = PosErrorHandler.wrapApiCall(mockApiFunction)
      
      const result = await wrappedFunction('param1', 'param2')
      
      expect(result).toEqual({ success: true, data: 'test' })
      expect(mockApiFunction).toHaveBeenCalledWith('param1', 'param2')
    })
    
    it('应该处理API调用错误', async () => {
      const mockError = new Error('API调用失败')
      const mockApiFunction = vi.fn().mockRejectedValue(mockError)
      const wrappedFunction = PosErrorHandler.wrapApiCall(mockApiFunction, {
        showMessage: true,
        context: '测试操作'
      })
      
      await expect(wrappedFunction()).rejects.toThrow('API调用失败')
      expect(mockApiFunction).toHaveBeenCalled()
    })
    
    it('应该支持自定义错误处理选项', async () => {
      const mockError = new Error('网络错误')
      mockError.code = PosErrorTypes.NETWORK_ERROR
      
      const mockApiFunction = vi.fn().mockRejectedValue(mockError)
      const wrappedFunction = PosErrorHandler.wrapApiCall(mockApiFunction, {
        showMessage: false,
        showNotification: true,
        context: '网络请求'
      })
      
      await expect(wrappedFunction()).rejects.toThrow('网络错误')
    })
  })
  
  describe('handleError', () => {
    it('应该正确处理已知错误类型', () => {
      const { message } = require('ant-design-vue')
      
      const error = new Error('库存不足')
      error.code = PosErrorTypes.INSUFFICIENT_INVENTORY
      
      PosErrorHandler.handleError(error, {
        showMessage: true,
        context: '添加商品'
      })
      
      expect(message.error).toHaveBeenCalledWith(ErrorMessages[PosErrorTypes.INSUFFICIENT_INVENTORY])
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[POS Error] 添加商品:',
        expect.objectContaining({
          message: '库存不足',
          timestamp: expect.any(String)
        })
      )
    })
    
    it('应该处理未知错误类型', () => {
      const { message } = require('ant-design-vue')
      
      const error = new Error('未知错误')
      
      PosErrorHandler.handleError(error, {
        showMessage: true,
        context: '未知操作'
      })
      
      expect(message.error).toHaveBeenCalledWith('未知错误')
    })
    
    it('应该支持通知显示', () => {
      const { notification } = require('ant-design-vue')
      
      const error = new Error('支付失败')
      error.code = PosErrorTypes.PAYMENT_FAILED
      
      PosErrorHandler.handleError(error, {
        showMessage: false,
        showNotification: true,
        context: '支付处理'
      })
      
      expect(notification.error).toHaveBeenCalledWith({
        message: '支付处理失败',
        description: ErrorMessages[PosErrorTypes.PAYMENT_FAILED],
        duration: 5
      })
    })
    
    it('应该处理空错误对象', () => {
      const { message } = require('ant-design-vue')
      
      PosErrorHandler.handleError(null, {
        showMessage: true,
        context: '空错误测试'
      })
      
      expect(message.error).toHaveBeenCalledWith('操作失败，请重试')
    })
  })
  
  describe('getErrorType', () => {
    it('应该根据错误代码返回错误类型', () => {
      const error = new Error('测试错误')
      error.code = PosErrorTypes.NETWORK_ERROR
      
      const errorType = PosErrorHandler.getErrorType(error)
      
      expect(errorType).toBe(PosErrorTypes.NETWORK_ERROR)
    })
    
    it('应该根据错误消息推断错误类型', () => {
      const networkError = new Error('网络连接失败')
      const inventoryError = new Error('商品库存不足')
      
      expect(PosErrorHandler.getErrorType(networkError)).toBe(PosErrorTypes.NETWORK_ERROR)
      expect(PosErrorHandler.getErrorType(inventoryError)).toBe(PosErrorTypes.INSUFFICIENT_INVENTORY)
    })
    
    it('应该返回未知错误类型', () => {
      const error = new Error('完全未知的错误')
      
      const errorType = PosErrorHandler.getErrorType(error)
      
      expect(errorType).toBe('UNKNOWN_ERROR')
    })
  })
  
  describe('getErrorMessage', () => {
    it('应该返回预定义的错误消息', () => {
      const message = PosErrorHandler.getErrorMessage(PosErrorTypes.NETWORK_ERROR, {})
      
      expect(message).toBe(ErrorMessages[PosErrorTypes.NETWORK_ERROR])
    })
    
    it('应该返回错误对象的消息', () => {
      const error = { message: '自定义错误消息' }
      const message = PosErrorHandler.getErrorMessage('UNKNOWN_ERROR', error)
      
      expect(message).toBe('自定义错误消息')
    })
    
    it('应该返回默认错误消息', () => {
      const message = PosErrorHandler.getErrorMessage('UNKNOWN_ERROR', {})
      
      expect(message).toBe('操作失败，请重试')
    })
  })
  
  describe('logError', () => {
    it('应该记录错误日志', () => {
      const error = new Error('测试错误')
      error.stack = 'Error stack trace'
      
      PosErrorHandler.logError(error, '测试上下文')
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[POS Error] 测试上下文:',
        expect.objectContaining({
          message: '测试错误',
          stack: 'Error stack trace',
          timestamp: expect.any(String)
        })
      )
    })
    
    it('应该处理没有堆栈信息的错误', () => {
      const error = new Error('简单错误')
      
      PosErrorHandler.logError(error, '简单测试')
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[POS Error] 简单测试:',
        expect.objectContaining({
          message: '简单错误',
          timestamp: expect.any(String)
        })
      )
    })
  })
  
  describe('createErrorHandler', () => {
    it('应该创建自定义错误处理器', () => {
      const customHandler = PosErrorHandler.createErrorHandler({
        showMessage: false,
        showNotification: true,
        context: '自定义处理器'
      })
      
      expect(typeof customHandler).toBe('function')
      
      const error = new Error('自定义错误')
      customHandler(error)
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[POS Error] 自定义处理器:',
        expect.objectContaining({
          message: '自定义错误'
        })
      )
    })
  })
  
  describe('isRetryableError', () => {
    it('应该识别可重试的错误', () => {
      const networkError = new Error('网络错误')
      networkError.code = PosErrorTypes.NETWORK_ERROR
      
      const timeoutError = new Error('超时错误')
      timeoutError.code = PosErrorTypes.TIMEOUT_ERROR
      
      const businessError = new Error('业务错误')
      businessError.code = PosErrorTypes.INVALID_MEMBER
      
      expect(PosErrorHandler.isRetryableError(networkError)).toBe(true)
      expect(PosErrorHandler.isRetryableError(timeoutError)).toBe(true)
      expect(PosErrorHandler.isRetryableError(businessError)).toBe(false)
    })
    
    it('应该根据HTTP状态码判断', () => {
      const serverError = new Error('服务器错误')
      serverError.status = 500
      
      const clientError = new Error('客户端错误')
      clientError.status = 400
      
      expect(PosErrorHandler.isRetryableError(serverError)).toBe(true)
      expect(PosErrorHandler.isRetryableError(clientError)).toBe(false)
    })
  })
  
  describe('formatErrorForUser', () => {
    it('应该格式化用户友好的错误消息', () => {
      const error = new Error('Internal server error')
      error.code = PosErrorTypes.NETWORK_ERROR
      
      const userMessage = PosErrorHandler.formatErrorForUser(error)
      
      expect(userMessage).toBe(ErrorMessages[PosErrorTypes.NETWORK_ERROR])
    })
    
    it('应该处理技术性错误消息', () => {
      const error = new Error('SQLException: Connection timeout')
      
      const userMessage = PosErrorHandler.formatErrorForUser(error)
      
      expect(userMessage).toBe('系统暂时不可用，请稍后重试')
    })
  })
  
  describe('getErrorSeverity', () => {
    it('应该返回正确的错误严重级别', () => {
      const criticalError = new Error('系统崩溃')
      criticalError.code = PosErrorTypes.SYSTEM_MAINTENANCE
      
      const warningError = new Error('库存不足')
      warningError.code = PosErrorTypes.INSUFFICIENT_INVENTORY
      
      const infoError = new Error('会员信息无效')
      infoError.code = PosErrorTypes.INVALID_MEMBER
      
      expect(PosErrorHandler.getErrorSeverity(criticalError)).toBe('critical')
      expect(PosErrorHandler.getErrorSeverity(warningError)).toBe('warning')
      expect(PosErrorHandler.getErrorSeverity(infoError)).toBe('info')
    })
  })
  
  describe('collectErrorMetrics', () => {
    it('应该收集错误指标', () => {
      const error = new Error('测试错误')
      error.code = PosErrorTypes.NETWORK_ERROR
      
      const metrics = PosErrorHandler.collectErrorMetrics(error, '测试操作')
      
      expect(metrics).toEqual({
        errorType: PosErrorTypes.NETWORK_ERROR,
        context: '测试操作',
        severity: expect.any(String),
        timestamp: expect.any(Number),
        userAgent: expect.any(String),
        url: expect.any(String)
      })
    })
  })
  
  describe('reportError', () => {
    it('应该上报错误到监控系统', async () => {
      // Mock fetch
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })
      
      const error = new Error('需要上报的错误')
      error.code = PosErrorTypes.PAYMENT_FAILED
      
      const result = await PosErrorHandler.reportError(error, '支付处理')
      
      expect(result).toBe(true)
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/errors/report',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: expect.any(String)
        })
      )
    })
    
    it('应该处理上报失败的情况', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('上报失败'))
      
      const error = new Error('测试错误')
      const result = await PosErrorHandler.reportError(error, '测试')
      
      expect(result).toBe(false)
      expect(mockConsole.error).toHaveBeenCalledWith(
        '错误上报失败:',
        expect.any(Error)
      )
    })
  })
  
  describe('clearErrorHistory', () => {
    it('应该清除错误历史记录', () => {
      // 先添加一些错误记录
      const error1 = new Error('错误1')
      const error2 = new Error('错误2')
      
      PosErrorHandler.handleError(error1, { context: '测试1' })
      PosErrorHandler.handleError(error2, { context: '测试2' })
      
      // 清除历史记录
      PosErrorHandler.clearErrorHistory()
      
      const history = PosErrorHandler.getErrorHistory()
      expect(history).toHaveLength(0)
    })
  })
  
  describe('getErrorHistory', () => {
    it('应该返回错误历史记录', () => {
      // 清除之前的记录
      PosErrorHandler.clearErrorHistory()
      
      const error = new Error('历史错误')
      PosErrorHandler.handleError(error, { context: '历史测试' })
      
      const history = PosErrorHandler.getErrorHistory()
      
      expect(history).toHaveLength(1)
      expect(history[0]).toEqual(
        expect.objectContaining({
          message: '历史错误',
          context: '历史测试',
          timestamp: expect.any(Number)
        })
      )
    })
    
    it('应该限制历史记录数量', () => {
      PosErrorHandler.clearErrorHistory()
      
      // 添加超过限制的错误记录
      for (let i = 0; i < 150; i++) {
        const error = new Error(`错误${i}`)
        PosErrorHandler.handleError(error, { context: `测试${i}` })
      }
      
      const history = PosErrorHandler.getErrorHistory()
      expect(history.length).toBeLessThanOrEqual(100) // 假设限制为100条
    })
  })
})