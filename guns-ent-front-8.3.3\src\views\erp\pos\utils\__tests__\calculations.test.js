import { describe, it, expect } from 'vitest'
import {
  calculateItemTotal,
  calculateCartTotal,
  calculateDiscount,
  calculateTax,
  calculateChange,
  calculateMemberDiscount,
  calculatePointsDeduction,
  roundToDecimal,
  formatCurrency,
  validateAmount,
  calculatePercentage,
  applyPercentageDiscount,
  calculateWeightedPrice,
  calculateBulkDiscount,
  calculateFinalAmount
} from '../calculations'

describe('calculations', () => {
  describe('基础计算', () => {
    it('应该正确计算商品总价', () => {
      expect(calculateItemTotal(10.5, 3)).toBe(31.5)
      expect(calculateItemTotal(0, 5)).toBe(0)
      expect(calculateItemTotal(10, 0)).toBe(0)
    })

    it('应该处理小数精度问题', () => {
      expect(calculateItemTotal(0.1, 3)).toBe(0.3)
      expect(calculateItemTotal(0.07, 3)).toBe(0.21)
    })

    it('应该处理负数', () => {
      expect(calculateItemTotal(-10, 2)).toBe(0)
      expect(calculateItemTotal(10, -2)).toBe(0)
    })
  })

  describe('购物车总计算', () => {
    const mockCartItems = [
      { unitPrice: 10.5, quantity: 2, totalPrice: 21 },
      { unitPrice: 15.0, quantity: 1, totalPrice: 15 },
      { unitPrice: 8.75, quantity: 3, totalPrice: 26.25 }
    ]

    it('应该正确计算购物车总金额', () => {
      const result = calculateCartTotal(mockCartItems)
      
      expect(result.subtotal).toBe(62.25)
      expect(result.itemCount).toBe(6)
      expect(result.averagePrice).toBeCloseTo(10.375, 2)
    })

    it('应该处理空购物车', () => {
      const result = calculateCartTotal([])
      
      expect(result.subtotal).toBe(0)
      expect(result.itemCount).toBe(0)
      expect(result.averagePrice).toBe(0)
    })

    it('应该处理无效的商品数据', () => {
      const invalidItems = [
        { unitPrice: null, quantity: 2 },
        { unitPrice: 10, quantity: null },
        { unitPrice: 'invalid', quantity: 1 }
      ]
      
      const result = calculateCartTotal(invalidItems)
      
      expect(result.subtotal).toBe(0)
      expect(result.itemCount).toBe(0)
    })
  })

  describe('折扣计算', () => {
    it('应该正确计算固定金额折扣', () => {
      const result = calculateDiscount(100, { type: 'fixed', value: 10 })
      
      expect(result.discountAmount).toBe(10)
      expect(result.finalAmount).toBe(90)
      expect(result.discountRate).toBe(10)
    })

    it('应该正确计算百分比折扣', () => {
      const result = calculateDiscount(100, { type: 'percentage', value: 15 })
      
      expect(result.discountAmount).toBe(15)
      expect(result.finalAmount).toBe(85)
      expect(result.discountRate).toBe(15)
    })

    it('应该处理超过原价的折扣', () => {
      const result = calculateDiscount(50, { type: 'fixed', value: 60 })
      
      expect(result.discountAmount).toBe(50)
      expect(result.finalAmount).toBe(0)
    })

    it('应该处理无效的折扣配置', () => {
      const result = calculateDiscount(100, { type: 'invalid', value: 10 })
      
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })

    it('应该处理负数折扣', () => {
      const result = calculateDiscount(100, { type: 'fixed', value: -10 })
      
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })
  })

  describe('税费计算', () => {
    it('应该正确计算税费', () => {
      const result = calculateTax(100, 0.13) // 13%税率
      
      expect(result.taxAmount).toBeCloseTo(13, 2)
      expect(result.totalWithTax).toBeCloseTo(113, 2)
      expect(result.taxRate).toBe(13)
    })

    it('应该处理零税率', () => {
      const result = calculateTax(100, 0)
      
      expect(result.taxAmount).toBe(0)
      expect(result.totalWithTax).toBe(100)
    })

    it('应该处理负数税率', () => {
      const result = calculateTax(100, -0.1)
      
      expect(result.taxAmount).toBe(0)
      expect(result.totalWithTax).toBe(100)
    })
  })

  describe('找零计算', () => {
    it('应该正确计算找零', () => {
      const result = calculateChange(100, 80)
      
      expect(result.changeAmount).toBe(20)
      expect(result.isExact).toBe(false)
      expect(result.isInsufficient).toBe(false)
    })

    it('应该处理刚好付款', () => {
      const result = calculateChange(100, 100)
      
      expect(result.changeAmount).toBe(0)
      expect(result.isExact).toBe(true)
      expect(result.isInsufficient).toBe(false)
    })

    it('应该处理付款不足', () => {
      const result = calculateChange(80, 100)
      
      expect(result.changeAmount).toBe(0)
      expect(result.isExact).toBe(false)
      expect(result.isInsufficient).toBe(true)
      expect(result.shortfall).toBe(20)
    })
  })

  describe('会员折扣计算', () => {
    it('应该正确计算会员折扣', () => {
      const memberInfo = {
        discountRate: 10, // 9折
        maxDiscountAmount: 50
      }
      
      const result = calculateMemberDiscount(100, memberInfo)
      
      expect(result.discountAmount).toBe(10)
      expect(result.finalAmount).toBe(90)
      expect(result.discountRate).toBe(10)
    })

    it('应该应用最大折扣限制', () => {
      const memberInfo = {
        discountRate: 20, // 8折
        maxDiscountAmount: 15
      }
      
      const result = calculateMemberDiscount(100, memberInfo)
      
      expect(result.discountAmount).toBe(15) // 限制在15元
      expect(result.finalAmount).toBe(85)
    })

    it('应该处理没有折扣的会员', () => {
      const memberInfo = {
        discountRate: 0
      }
      
      const result = calculateMemberDiscount(100, memberInfo)
      
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })

    it('应该处理无效的会员信息', () => {
      const result = calculateMemberDiscount(100, null)
      
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })
  })

  describe('积分抵扣计算', () => {
    it('应该正确计算积分抵扣', () => {
      const result = calculatePointsDeduction(1000, 100) // 1000积分，100积分=1元
      
      expect(result.deductionAmount).toBe(10)
      expect(result.usedPoints).toBe(1000)
      expect(result.exchangeRate).toBe(100)
    })

    it('应该处理零积分', () => {
      const result = calculatePointsDeduction(0, 100)
      
      expect(result.deductionAmount).toBe(0)
      expect(result.usedPoints).toBe(0)
    })

    it('应该处理无效的兑换比例', () => {
      const result = calculatePointsDeduction(1000, 0)
      
      expect(result.deductionAmount).toBe(0)
      expect(result.usedPoints).toBe(1000)
    })
  })

  describe('数值处理', () => {
    it('应该正确四舍五入到指定小数位', () => {
      expect(roundToDecimal(3.14159, 2)).toBe(3.14)
      expect(roundToDecimal(3.14159, 3)).toBe(3.142)
      expect(roundToDecimal(3.14159, 0)).toBe(3)
    })

    it('应该格式化货币显示', () => {
      expect(formatCurrency(1234.56)).toBe('¥1,234.56')
      expect(formatCurrency(0)).toBe('¥0.00')
      expect(formatCurrency(1000000)).toBe('¥1,000,000.00')
    })

    it('应该验证金额有效性', () => {
      expect(validateAmount(100)).toBe(true)
      expect(validateAmount(0)).toBe(true)
      expect(validateAmount(-10)).toBe(false)
      expect(validateAmount('invalid')).toBe(false)
      expect(validateAmount(null)).toBe(false)
      expect(validateAmount(undefined)).toBe(false)
    })
  })

  describe('百分比计算', () => {
    it('应该正确计算百分比', () => {
      expect(calculatePercentage(25, 100)).toBe(25)
      expect(calculatePercentage(1, 3)).toBeCloseTo(33.33, 2)
      expect(calculatePercentage(0, 100)).toBe(0)
    })

    it('应该处理除零情况', () => {
      expect(calculatePercentage(25, 0)).toBe(0)
    })

    it('应该应用百分比折扣', () => {
      const result = applyPercentageDiscount(100, 15)
      
      expect(result.discountAmount).toBe(15)
      expect(result.finalAmount).toBe(85)
    })
  })

  describe('加权价格计算', () => {
    it('应该正确计算加权平均价格', () => {
      const items = [
        { price: 10, weight: 2 },
        { price: 20, weight: 3 },
        { price: 30, weight: 1 }
      ]
      
      const result = calculateWeightedPrice(items)
      
      // (10*2 + 20*3 + 30*1) / (2+3+1) = 110/6 ≈ 18.33
      expect(result).toBeCloseTo(18.33, 2)
    })

    it('应该处理空数组', () => {
      const result = calculateWeightedPrice([])
      
      expect(result).toBe(0)
    })

    it('应该处理零权重', () => {
      const items = [
        { price: 10, weight: 0 },
        { price: 20, weight: 0 }
      ]
      
      const result = calculateWeightedPrice(items)
      
      expect(result).toBe(0)
    })
  })

  describe('批量折扣计算', () => {
    const discountRules = [
      { minQuantity: 10, discountRate: 5 },
      { minQuantity: 20, discountRate: 10 },
      { minQuantity: 50, discountRate: 15 }
    ]

    it('应该应用正确的批量折扣', () => {
      const result = calculateBulkDiscount(100, 25, discountRules)
      
      expect(result.applicableRule.discountRate).toBe(10)
      expect(result.discountAmount).toBe(10)
      expect(result.finalAmount).toBe(90)
    })

    it('应该选择最高的适用折扣', () => {
      const result = calculateBulkDiscount(100, 60, discountRules)
      
      expect(result.applicableRule.discountRate).toBe(15)
      expect(result.discountAmount).toBe(15)
    })

    it('应该处理不满足条件的情况', () => {
      const result = calculateBulkDiscount(100, 5, discountRules)
      
      expect(result.applicableRule).toBe(null)
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })
  })

  describe('最终金额计算', () => {
    it('应该正确计算最终金额', () => {
      const params = {
        subtotal: 100,
        discount: 10,
        tax: 13,
        pointsDeduction: 5,
        memberDiscount: 8
      }
      
      const result = calculateFinalAmount(params)
      
      // 100 - 10 - 8 - 5 + 13 = 90
      expect(result.finalAmount).toBe(90)
      expect(result.totalDiscount).toBe(23) // 10 + 8 + 5
      expect(result.breakdown).toEqual({
        subtotal: 100,
        totalDiscount: 23,
        taxAmount: 13,
        finalAmount: 90
      })
    })

    it('应该处理负数最终金额', () => {
      const params = {
        subtotal: 50,
        discount: 30,
        memberDiscount: 25,
        pointsDeduction: 10
      }
      
      const result = calculateFinalAmount(params)
      
      expect(result.finalAmount).toBe(0) // 不能为负数
    })

    it('应该处理缺少参数', () => {
      const params = {
        subtotal: 100
      }
      
      const result = calculateFinalAmount(params)
      
      expect(result.finalAmount).toBe(100)
      expect(result.totalDiscount).toBe(0)
    })
  })

  describe('边界情况', () => {
    it('应该处理极大数值', () => {
      const result = calculateItemTotal(999999.99, 999)
      
      expect(result).toBeCloseTo(*********.01, 2)
    })

    it('应该处理极小数值', () => {
      const result = calculateItemTotal(0.01, 1)
      
      expect(result).toBe(0.01)
    })

    it('应该处理浮点数精度问题', () => {
      const result = calculateItemTotal(0.1, 3)
      
      expect(result).toBeCloseTo(0.3, 10)
    })
  })

  describe('错误处理', () => {
    it('应该处理无效输入', () => {
      expect(calculateItemTotal('invalid', 2)).toBe(0)
      expect(calculateItemTotal(10, 'invalid')).toBe(0)
      expect(calculateItemTotal(null, 2)).toBe(0)
      expect(calculateItemTotal(10, null)).toBe(0)
    })

    it('应该处理NaN和Infinity', () => {
      expect(calculateItemTotal(NaN, 2)).toBe(0)
      expect(calculateItemTotal(10, NaN)).toBe(0)
      expect(calculateItemTotal(Infinity, 2)).toBe(0)
      expect(calculateItemTotal(10, Infinity)).toBe(0)
    })
  })
})