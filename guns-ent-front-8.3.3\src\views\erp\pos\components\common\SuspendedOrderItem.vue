<template>
  <div class="order-item" @click="handleResumeOrder">
    <div class="order-header">
      <div class="order-no">
        <icon-font iconClass="icon-order" />
        {{ order.suspendNo }}
      </div>
      <div class="order-time">
        {{ formatTime(order.suspendTime) }}
      </div>
      <a-button 
        type="text" 
        size="small" 
        danger
        @click.stop="handleDeleteOrder"
        class="delete-btn"
      >
        <template #icon>
          <close-outlined />
        </template>
      </a-button>
    </div>

    <div class="order-content">
      <OrderSummary
        :itemCount="order.orderData.cartItems.length"
        :amount="order.orderData.finalAmount"
        :member="order.orderData.currentMember"
        mini
      />

      <!-- 商品列表预览 -->
      <div class="order-items-preview">
        <div 
          v-for="(item, index) in order.orderData.cartItems.slice(0, 3)"
          :key="index"
          class="item-preview"
        >
          <span class="item-name">{{ item.productName }}</span>
          <span class="item-qty">×{{ item.quantity }}</span>
        </div>
        <div 
          v-if="order.orderData.cartItems.length > 3"
          class="more-items"
        >
          还有{{ order.orderData.cartItems.length - 3 }}件商品...
        </div>
      </div>
    </div>

    <!-- 订单操作按钮 -->
    <div class="order-actions">
      <a-button 
        type="primary" 
        size="small"
        @click.stop="handleResumeOrder"
        class="resume-btn"
      >
        <template #icon>
          <play-circle-outlined />
        </template>
        恢复订单
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { CloseOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'
import OrderSummary from './OrderSummary.vue'

// 定义组件名称
defineOptions({
  name: 'SuspendedOrderItem'
})

// 定义属性
const props = defineProps({
  order: {
    type: Object,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['resume', 'delete'])

/**
 * 格式化时间显示
 */
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 小于1小时显示分钟
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于24小时显示小时
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 超过24小时显示日期时间
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 处理恢复订单
 */
const handleResumeOrder = () => {
  emit('resume', props.order.suspendId)
}

/**
 * 处理删除订单
 */
const handleDeleteOrder = () => {
  emit('delete', props.order.suspendId)
}
</script>

<style scoped>
.order-item {
  background: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.order-item:hover {
  background: #f0f9ff;
  border-color: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
}

.order-no {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.order-time {
  font-size: 12px;
  color: #8c8c8c;
}

.delete-btn {
  color: #ff4d4f;
  padding: 4px;
}

.delete-btn:hover:not(:disabled) {
  color: #ff7875;
  background: #fff2f0;
}

.order-content {
  padding: 8px 16px 12px;
}

.order-items-preview {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.item-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #595959;
  margin-bottom: 2px;
}

.item-preview:last-child {
  margin-bottom: 0;
}

.item-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.item-qty {
  font-weight: 500;
  color: #262626;
}

.more-items {
  font-size: 11px;
  color: #8c8c8c;
  text-align: center;
  margin-top: 4px;
  font-style: italic;
}

.order-actions {
  padding: 0 16px 12px;
  display: flex;
  justify-content: flex-end;
}

.resume-btn {
  background: #52c41a;
  border-color: #52c41a;
}

.resume-btn:hover:not(:disabled) {
  background: #73d13d;
  border-color: #73d13d;
}
</style>