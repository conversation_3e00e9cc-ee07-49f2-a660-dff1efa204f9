/**
 * POS主页面业务逻辑组合式函数
 * 
 * 整合所有POS相关的业务逻辑，为主页面提供统一的接口
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { usePosStore } from '@/stores/pos'
import { useUserStore } from '@/store/modules/user'
import { useCart } from './useCart'
import { usePayment } from './usePayment'
import { useMember } from './useMember'
import { useOrder } from './useOrder'
import { useDataRecovery } from './useDataRecovery'
import { AmountFormatter } from '../utils/formatter'

export function usePos() {
  // 路由和状态管理
  const router = useRouter()
  const posStore = usePosStore()
  const userStore = useUserStore()
  
  // 使用其他composables
  const { 
    addItem: addCartItem,
    updateQuantity: updateCartQuantity,
    removeItem: removeCartItem,
    clearCart,
    recalculateTotal
  } = useCart()
  
  const {
    processPayment,
    resetPaymentStatus
  } = usePayment()
  
  const {
    selectMember,
    clearMember
  } = useMember()
  
  const {
    suspendOrder,
    resumeOrder,
    deleteOrder
  } = useOrder()
  
  // 暂时禁用数据备份功能，避免控制台错误
  /*
  const {
    saveCartState,
    restoreCartState
  } = useDataRecovery()
  */
  
  // 响应式状态
  const isFullscreen = ref(false)
  const showHelpModal = ref(false)
  const showPaymentPanel = ref(false)
  const showSuspendedOrdersDrawer = ref(false)
  const currentTime = ref('')
  const currentDate = ref('')
  const timeInterval = ref(null)
  
  // 计算属性
  const currentUser = computed(() => userStore.userInfo || {})
  const cartSummary = computed(() => posStore.getCartSummary())
  const hasMember = computed(() => posStore.hasMember)
  const currentMember = computed(() => posStore.currentMember)
  const suspendedOrdersCount = computed(() => posStore.suspendedOrders.length)
  const hasCartItems = computed(() => posStore.hasCartItems)
  const canCheckout = computed(() => posStore.canCheckout)
  
  // 订单信息（用于支付）
  const orderInfo = computed(() => ({
    itemCount: cartSummary.value.itemCount,
    totalAmount: cartSummary.value.totalAmount,
    discountAmount: cartSummary.value.discountAmount,
    pointsDeductionAmount: cartSummary.value.pointsDeductionAmount,
    finalAmount: cartSummary.value.finalAmount,
    items: posStore.cartItems,
    member: currentMember.value
  }))
  
  // ==================== 基础功能方法 ====================
  
  /**
   * 更新当前时间
   */
  const updateTime = () => {
    const now = new Date()
    currentTime.value = now.toLocaleTimeString('zh-CN', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    currentDate.value = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }
  
  /**
   * 切换全屏模式
   */
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      isFullscreen.value = true
    } else {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
  
  /**
   * 显示帮助
   */
  const showHelp = () => {
    showHelpModal.value = true
  }
  
  /**
   * 重置所有状态
   */
  const resetAll = () => {
    posStore.resetAllState()
    resetPaymentStatus()
    message.success('已重置所有状态')
  }
  
  /**
   * 退出登录
   */
  const logout = () => {
    // 保存当前状态到本地存储
    // saveCartState(cartSummary.value) // 暂时禁用数据备份
    
    // 清除用户信息
    userStore.logout()
    
    // 跳转到登录页
    router.push('/login')
  }
  
  /**
   * 格式化金额显示
   */
  const formatAmount = (amount) => {
    return AmountFormatter.formatCurrency(amount || 0)
  }
  
  // ==================== 商品相关事件处理 ====================
  
  /**
   * 处理商品添加
   */
  const handleProductAdd = async (product) => {
    try {
      await addCartItem(product, 1)
      message.success(`已添加 ${product.name}`)
    } catch (error) {
      console.error('添加商品失败:', error)
      message.error('添加商品失败: ' + error.message)
    }
  }
  
  /**
   * 处理商品选择
   */
  const handleProductSelect = (product) => {
    // 可以在这里添加商品选择的特殊逻辑
    handleProductAdd(product)
  }
  
  // ==================== 购物车相关事件处理 ====================
  
  /**
   * 处理购物车变化
   */
  const handleCartChange = (cartData) => {
    const { type, itemId, quantity, item } = cartData
    
    try {
      switch (type) {
        case 'update':
          updateCartQuantity(itemId, quantity)
          break
        case 'remove':
          removeCartItem(itemId)
          message.success(`已移除 ${item?.name || '商品'}`)
          break
        case 'add':
          addCartItem(item, quantity)
          break
      }
    } catch (error) {
      console.error('购物车操作失败:', error)
      message.error('操作失败: ' + error.message)
    }
  }
  
  /**
   * 处理购物车清空
   */
  const handleCartClear = () => {
    clearCart()
    message.success('已清空购物车')
  }
  
  /**
   * 处理结账
   */
  const handleCheckout = () => {
    if (!canCheckout.value) {
      message.warning('购物车为空或正在处理中，无法结账')
      return
    }
    
    if (!hasCartItems.value) {
      message.warning('购物车为空，请先添加商品')
      return
    }
    
    showPaymentPanel.value = true
  }
  
  // ==================== 挂单相关事件处理 ====================
  
  /**
   * 显示挂单列表
   */
  const handleShowSuspendedOrders = () => {
    showSuspendedOrdersDrawer.value = true
  }
  
  /**
   * 处理会员管理
   */
  const handleMemberManagement = () => {
    // 可以打开会员管理界面或执行其他操作
    message.info('会员管理功能')
  }
  
  /**
   * 处理订单挂起
   */
  const handleOrderSuspended = async (orderData) => {
    try {
      await suspendOrder(orderData)
      message.success('订单已挂起')
      clearCart()
    } catch (error) {
      console.error('挂起订单失败:', error)
      message.error('挂起订单失败: ' + error.message)
    }
  }
  
  /**
   * 处理订单恢复
   */
  const handleOrderResumed = async (suspendId) => {
    try {
      const orderData = await resumeOrder(suspendId)
      message.success('订单已恢复')
      showSuspendedOrdersDrawer.value = false
      
      // 恢复购物车状态
      if (orderData && orderData.cartItems) {
        // 这里可以恢复购物车数据
        posStore.restoreCartFromData(orderData)
      }
    } catch (error) {
      console.error('恢复订单失败:', error)
      message.error('恢复订单失败: ' + error.message)
    }
  }
  
  /**
   * 处理订单删除
   */
  const handleOrderDeleted = async (suspendId) => {
    try {
      await deleteOrder(suspendId)
      message.success('订单已删除')
    } catch (error) {
      console.error('删除订单失败:', error)
      message.error('删除订单失败: ' + error.message)
    }
  }
  
  // ==================== 支付相关事件处理 ====================
  
  /**
   * 处理支付成功
   */
  const handlePaymentSuccess = async (paymentData) => {
    try {
      message.success('支付成功！')
      
      // 清空购物车
      clearCart()
      
      // 清除会员信息（可选）
      // clearMember()
      
      // 关闭支付面板
      showPaymentPanel.value = false
      
      // 重置支付状态
      resetPaymentStatus()
      
      console.log('支付成功:', paymentData)
    } catch (error) {
      console.error('处理支付成功失败:', error)
      message.error('处理支付结果失败')
    }
  }
  
  /**
   * 处理支付取消
   */
  const handlePaymentCancel = () => {
    showPaymentPanel.value = false
    resetPaymentStatus()
    message.info('已取消支付')
  }
  
  // ==================== 生命周期 ====================
  
  /**
   * 组件挂载
   */
  onMounted(() => {
    // 初始化时间显示
    updateTime()
    timeInterval.value = setInterval(updateTime, 1000)
    
    // 恢复购物车状态
    // const savedCart = restoreCartState() // 暂时禁用数据备份
    // if (savedCart && savedCart.items && savedCart.items.length > 0) {
    //   message.info('已恢复上次的购物车状态')
    // }
    
    // 监听全屏变化
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement
    }
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    
    // 清理函数
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  })
  
  /**
   * 组件卸载
   */
  onUnmounted(() => {
    // 清理定时器
    if (timeInterval.value) {
      clearInterval(timeInterval.value)
    }
    
    // 保存当前状态
    // saveCartState(cartSummary.value) // 暂时禁用数据备份
  })
  
  // 返回所有需要的状态和方法
  return {
    // 状态
    currentUser,
    currentTime,
    currentDate,
    isFullscreen,
    showHelpModal,
    showPaymentPanel,
    showSuspendedOrdersDrawer,
    cartSummary,
    hasMember,
    currentMember,
    suspendedOrdersCount,
    hasCartItems,
    canCheckout,
    orderInfo,
    
    // 基础方法
    toggleFullscreen,
    showHelp,
    resetAll,
    logout,
    formatAmount,
    
    // 事件处理方法
    handleProductAdd,
    handleProductSelect,
    handleCartChange,
    handleCartClear,
    handleCheckout,
    handleShowSuspendedOrders,
    handleMemberManagement,
    handleOrderSuspended,
    handleOrderResumed,
    handleOrderDeleted,
    handlePaymentSuccess,
    handlePaymentCancel
  }
}