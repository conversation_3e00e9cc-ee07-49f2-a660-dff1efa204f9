<!--\n  商品网格组件\n  \n  使用虚拟滚动显示大量商品，支持商品选择和添加到购物车\n  \n  <AUTHOR>  @since 2025/01/02\n-->\n<template>\n  <div class=\"product-grid\">\n    <!-- 网格头部工具栏 -->\n    <div class=\"grid-toolbar\" v-if=\"showToolbar\">\n      <div class=\"toolbar-left\">\n        <div class=\"view-mode-switcher\">\n          <a-radio-group \n            v-model:value=\"viewMode\" \n            size=\"small\"\n            @change=\"handleViewModeChange\"\n          >\n            <a-radio-button value=\"grid\">\n              <template #icon>\n                <icon-font iconClass=\"icon-grid\" />\n              </template>\n              网格\n            </a-radio-button>\n            <a-radio-button value=\"list\">\n              <template #icon>\n                <icon-font iconClass=\"icon-list\" />\n              </template>\n              列表\n            </a-radio-button>\n          </a-radio-group>\n        </div>\n        \n        <div class=\"grid-size-slider\" v-if=\"viewMode === 'grid'\">\n          <span class=\"slider-label\">大小:</span>\n          <a-slider\n            v-model:value=\"gridSize\"\n            :min=\"120\"\n            :max=\"200\"\n            :step=\"20\"\n            :tooltip-formatter=\"(value) => `${value}px`\"\n            @change=\"handleGridSizeChange\"\n            class=\"size-slider\"\n          />\n        </div>\n      </div>\n      \n      <div class=\"toolbar-right\">\n        <div class=\"product-count\">\n          共 <strong>{{ totalProducts }}</strong> 个商品\n        </div>\n        \n        <a-button \n          type=\"text\" \n          size=\"small\"\n          @click=\"refreshProducts\"\n          :loading=\"refreshing\"\n          class=\"refresh-btn\"\n        >\n          <template #icon>\n            <reload-outlined />\n          </template>\n        </a-button>\n      </div>\n    </div>\n    \n    <!-- 商品网格容器 -->\n    <div class=\"grid-container\" :class=\"{ 'list-mode': viewMode === 'list' }\">\n      <!-- 虚拟滚动列表 -->\n      <a-virtual-list\n        :data=\"products\"\n        :height=\"containerHeight\"\n        :item-height=\"itemHeight\"\n        :item-key=\"'productId'\"\n        @scroll=\"handleScroll\"\n        class=\"virtual-list\"\n      >\n        <template #item=\"{ item, index }\">\n          <div \n            class=\"product-item-wrapper\"\n            :style=\"getItemStyle(index)\"\n          >\n            <ProductCard\n              :product=\"item\"\n              :view-mode=\"viewMode\"\n              :card-size=\"gridSize\"\n              :selected=\"selectedProducts.includes(item.productId)\"\n              :loading=\"item.loading\"\n              @click=\"handleProductClick\"\n              @add-to-cart=\"handleAddToCart\"\n              @quick-view=\"handleQuickView\"\n            />\n          </div>\n        </template>\n      </a-virtual-list>\n      \n      <!-- 加载更多指示器 -->\n      <div class=\"load-more\" v-if=\"hasMore && !loading\">\n        <a-button \n          type=\"link\"\n          @click=\"loadMore\"\n          :loading=\"loadingMore\"\n        >\n          加载更多商品\n        </a-button>\n      </div>\n    </div>\n    \n    <!-- 空状态 -->\n    <div class=\"empty-state\" v-if=\"!loading && products.length === 0\">\n      <div class=\"empty-icon\">\n        <icon-font iconClass=\"icon-empty-box\" />\n      </div>\n      <div class=\"empty-title\">暂无商品</div>\n      <div class=\"empty-description\">\n        <template v-if=\"hasFilters\">\n          当前筛选条件下没有找到商品，请尝试调整筛选条件\n        </template>\n        <template v-else>\n          该分类下暂无商品，请选择其他分类或联系管理员添加商品\n        </template>\n      </div>\n      <div class=\"empty-actions\">\n        <a-button \n          v-if=\"hasFilters\"\n          type=\"primary\"\n          @click=\"clearFilters\"\n        >\n          清除筛选条件\n        </a-button>\n        <a-button \n          @click=\"refreshProducts\"\n          :loading=\"refreshing\"\n        >\n          刷新商品\n        </a-button>\n      </div>\n    </div>\n    \n    <!-- 加载状态 -->\n    <div class=\"loading-state\" v-if=\"loading\">\n      <a-spin size=\"large\" />\n      <div class=\"loading-text\">正在加载商品...</div>\n    </div>\n    \n    <!-- 商品快速预览弹窗 -->\n    <a-modal\n      v-model:open=\"showQuickView\"\n      title=\"商品详情\"\n      width=\"600px\"\n      :footer=\"null\"\n      class=\"quick-view-modal\"\n    >\n      <ProductQuickView\n        v-if=\"quickViewProduct\"\n        :product=\"quickViewProduct\"\n        @add-to-cart=\"handleAddToCart\"\n        @close=\"closeQuickView\"\n      />\n    </a-modal>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'\nimport { message } from 'ant-design-vue'\nimport { ReloadOutlined } from '@ant-design/icons-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\nimport ProductCard from './ProductCard.vue'\nimport ProductQuickView from './ProductQuickView.vue'\nimport { useProduct } from '../../composables/useProduct'\nimport { GRID_VIEW_MODES, GRID_SIZES } from '../../utils/constants'\n\n// 定义组件名称\ndefineOptions({\n  name: 'ProductGrid'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 分类ID\n  categoryId: {\n    type: String,\n    default: null\n  },\n  // 搜索关键词\n  searchKeyword: {\n    type: String,\n    default: ''\n  },\n  // 价格过滤\n  priceFilter: {\n    type: String,\n    default: ''\n  },\n  // 库存过滤\n  stockFilter: {\n    type: String,\n    default: ''\n  },\n  // 排序方式\n  sortBy: {\n    type: String,\n    default: 'default'\n  },\n  // 是否显示工具栏\n  showToolbar: {\n    type: Boolean,\n    default: true\n  },\n  // 容器高度\n  containerHeight: {\n    type: Number,\n    default: 600\n  },\n  // 每页商品数量\n  pageSize: {\n    type: Number,\n    default: 50\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'product-click',\n  'product-add',\n  'product-select',\n  'view-mode-change',\n  'clear-filters'\n])\n\n// 使用商品业务逻辑\nconst {\n  products,\n  totalProducts,\n  loading,\n  hasMore,\n  loadProducts,\n  loadMoreProducts,\n  refreshProducts: refreshProductsData\n} = useProduct()\n\n// 响应式状态\nconst viewMode = ref('grid')\nconst gridSize = ref(160)\nconst selectedProducts = ref([])\nconst loadingMore = ref(false)\nconst refreshing = ref(false)\nconst showQuickView = ref(false)\nconst quickViewProduct = ref(null)\nconst containerRef = ref(null)\n\n// ==================== 计算属性 ====================\n\n/**\n * 项目高度（用于虚拟滚动）\n */\nconst itemHeight = computed(() => {\n  if (viewMode.value === 'list') {\n    return 80 // 列表模式固定高度\n  }\n  return gridSize.value + 40 // 网格模式：卡片高度 + 间距\n})\n\n/**\n * 是否有筛选条件\n */\nconst hasFilters = computed(() => {\n  return !!(props.searchKeyword || props.priceFilter || props.stockFilter || \n           (props.categoryId && props.categoryId !== 'all'))\n})\n\n/**\n * 每行显示的商品数量（网格模式）\n */\nconst itemsPerRow = computed(() => {\n  if (viewMode.value === 'list') return 1\n  \n  // 根据容器宽度和卡片大小计算\n  const containerWidth = 1200 // 假设容器宽度\n  const cardWidth = gridSize.value + 16 // 卡片宽度 + 间距\n  return Math.floor(containerWidth / cardWidth) || 1\n})\n\n// ==================== 方法 ====================\n\n/**\n * 获取项目样式\n * @param {number} index - 项目索引\n * @returns {Object} 样式对象\n */\nconst getItemStyle = (index) => {\n  if (viewMode.value === 'list') {\n    return {\n      width: '100%',\n      marginBottom: '8px'\n    }\n  }\n  \n  // 网格模式布局\n  const row = Math.floor(index / itemsPerRow.value)\n  const col = index % itemsPerRow.value\n  \n  return {\n    width: `${gridSize.value}px`,\n    height: `${gridSize.value}px`,\n    margin: '8px'\n  }\n}\n\n/**\n * 处理视图模式变化\n * @param {Event} e - 事件对象\n */\nconst handleViewModeChange = (e) => {\n  const mode = e.target.value\n  emit('view-mode-change', mode)\n}\n\n/**\n * 处理网格大小变化\n * @param {number} size - 新大小\n */\nconst handleGridSizeChange = (size) => {\n  // 保存用户偏好到本地存储\n  try {\n    localStorage.setItem('pos_grid_size', size.toString())\n  } catch (error) {\n    console.warn('保存网格大小偏好失败:', error)\n  }\n}\n\n/**\n * 处理商品点击\n * @param {Object} product - 商品信息\n */\nconst handleProductClick = (product) => {\n  // 切换选中状态\n  const index = selectedProducts.value.indexOf(product.productId)\n  if (index > -1) {\n    selectedProducts.value.splice(index, 1)\n  } else {\n    selectedProducts.value.push(product.productId)\n  }\n  \n  emit('product-click', product)\n  emit('product-select', {\n    product,\n    selected: selectedProducts.value.includes(product.productId)\n  })\n}\n\n/**\n * 处理添加到购物车\n * @param {Object} product - 商品信息\n */\nconst handleAddToCart = (product) => {\n  emit('product-add', product)\n  message.success(`已添加 ${product.productName} 到购物车`)\n}\n\n/**\n * 处理快速预览\n * @param {Object} product - 商品信息\n */\nconst handleQuickView = (product) => {\n  quickViewProduct.value = product\n  showQuickView.value = true\n}\n\n/**\n * 关闭快速预览\n */\nconst closeQuickView = () => {\n  showQuickView.value = false\n  quickViewProduct.value = null\n}\n\n/**\n * 处理滚动事件\n * @param {Event} e - 滚动事件\n */\nconst handleScroll = (e) => {\n  // 可以在这里处理滚动相关的逻辑，如懒加载图片等\n}\n\n/**\n * 加载更多商品\n */\nconst loadMore = async () => {\n  if (loadingMore.value || !hasMore.value) return\n  \n  loadingMore.value = true\n  try {\n    await loadMoreProducts()\n  } catch (error) {\n    console.error('加载更多商品失败:', error)\n    message.error('加载更多商品失败')\n  } finally {\n    loadingMore.value = false\n  }\n}\n\n/**\n * 刷新商品列表\n */\nconst refreshProducts = async () => {\n  refreshing.value = true\n  try {\n    await refreshProductsData()\n    message.success('商品列表已刷新')\n  } catch (error) {\n    console.error('刷新商品失败:', error)\n    message.error('刷新商品失败')\n  } finally {\n    refreshing.value = false\n  }\n}\n\n/**\n * 清除筛选条件\n */\nconst clearFilters = () => {\n  emit('clear-filters')\n}\n\n/**\n * 加载用户偏好设置\n */\nconst loadUserPreferences = () => {\n  try {\n    // 加载网格大小偏好\n    const savedGridSize = localStorage.getItem('pos_grid_size')\n    if (savedGridSize) {\n      gridSize.value = parseInt(savedGridSize, 10)\n    }\n    \n    // 加载视图模式偏好\n    const savedViewMode = localStorage.getItem('pos_view_mode')\n    if (savedViewMode && GRID_VIEW_MODES.includes(savedViewMode)) {\n      viewMode.value = savedViewMode\n    }\n  } catch (error) {\n    console.warn('加载用户偏好失败:', error)\n  }\n}\n\n/**\n * 保存用户偏好设置\n */\nconst saveUserPreferences = () => {\n  try {\n    localStorage.setItem('pos_view_mode', viewMode.value)\n    localStorage.setItem('pos_grid_size', gridSize.value.toString())\n  } catch (error) {\n    console.warn('保存用户偏好失败:', error)\n  }\n}\n\n// ==================== 生命周期 ====================\n\nonMounted(() => {\n  loadUserPreferences()\n  \n  // 初始加载商品\n  loadProducts({\n    categoryId: props.categoryId,\n    keyword: props.searchKeyword,\n    priceFilter: props.priceFilter,\n    stockFilter: props.stockFilter,\n    sortBy: props.sortBy,\n    pageSize: props.pageSize\n  })\n})\n\nonUnmounted(() => {\n  saveUserPreferences()\n})\n\n// ==================== 监听器 ====================\n\n// 监听筛选条件变化\nwatch(\n  () => [props.categoryId, props.searchKeyword, props.priceFilter, props.stockFilter, props.sortBy],\n  () => {\n    loadProducts({\n      categoryId: props.categoryId,\n      keyword: props.searchKeyword,\n      priceFilter: props.priceFilter,\n      stockFilter: props.stockFilter,\n      sortBy: props.sortBy,\n      pageSize: props.pageSize\n    })\n  },\n  { deep: true }\n)\n\n// 监听视图模式变化\nwatch(viewMode, (newMode) => {\n  saveUserPreferences()\n})\n\n// 暴露方法给父组件\ndefineExpose({\n  refreshProducts,\n  loadMore,\n  clearSelection: () => {\n    selectedProducts.value = []\n  },\n  selectAll: () => {\n    selectedProducts.value = products.value.map(p => p.productId)\n  },\n  getSelectedProducts: () => {\n    return products.value.filter(p => selectedProducts.value.includes(p.productId))\n  }\n})\n</script>\n\n<style scoped>\n.product-grid {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background: #fff;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n/* 网格工具栏 */\n.grid-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n  flex-shrink: 0;\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.toolbar-right {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.view-mode-switcher {\n  display: flex;\n  align-items: center;\n}\n\n.grid-size-slider {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  min-width: 120px;\n}\n\n.slider-label {\n  font-size: 12px;\n  color: #595959;\n  white-space: nowrap;\n}\n\n.size-slider {\n  width: 80px;\n}\n\n.product-count {\n  font-size: 13px;\n  color: #595959;\n}\n\n.product-count strong {\n  color: #1890ff;\n  font-weight: 600;\n}\n\n.refresh-btn {\n  color: #666;\n}\n\n.refresh-btn:hover {\n  color: #1890ff;\n}\n\n/* 网格容器 */\n.grid-container {\n  flex: 1;\n  overflow: hidden;\n  position: relative;\n}\n\n.virtual-list {\n  height: 100%;\n  padding: 8px;\n}\n\n.product-item-wrapper {\n  display: inline-block;\n  vertical-align: top;\n}\n\n.grid-container.list-mode .product-item-wrapper {\n  display: block;\n  width: 100% !important;\n}\n\n/* 加载更多 */\n.load-more {\n  text-align: center;\n  padding: 20px;\n  border-top: 1px solid #f0f0f0;\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  padding: 40px 20px;\n  text-align: center;\n}\n\n.empty-icon {\n  font-size: 64px;\n  color: #d9d9d9;\n  margin-bottom: 16px;\n}\n\n.empty-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 8px;\n}\n\n.empty-description {\n  font-size: 14px;\n  color: #8c8c8c;\n  line-height: 1.5;\n  margin-bottom: 24px;\n  max-width: 400px;\n}\n\n.empty-actions {\n  display: flex;\n  gap: 12px;\n}\n\n/* 加载状态 */\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 400px;\n  padding: 40px 20px;\n}\n\n.loading-text {\n  margin-top: 16px;\n  font-size: 14px;\n  color: #8c8c8c;\n}\n\n/* 快速预览弹窗 */\n.quick-view-modal :deep(.ant-modal-content) {\n  border-radius: 12px;\n}\n\n.quick-view-modal :deep(.ant-modal-header) {\n  border-bottom: 1px solid #f0f0f0;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .grid-size-slider {\n    min-width: 100px;\n  }\n  \n  .size-slider {\n    width: 60px;\n  }\n}\n\n@media (max-width: 768px) {\n  .grid-toolbar {\n    flex-direction: column;\n    gap: 12px;\n    align-items: stretch;\n  }\n  \n  .toolbar-left,\n  .toolbar-right {\n    justify-content: space-between;\n  }\n  \n  .grid-size-slider {\n    min-width: auto;\n  }\n  \n  .virtual-list {\n    padding: 4px;\n  }\n  \n  .empty-state {\n    height: 300px;\n    padding: 20px;\n  }\n  \n  .empty-icon {\n    font-size: 48px;\n  }\n  \n  .empty-actions {\n    flex-direction: column;\n    width: 100%;\n    max-width: 200px;\n  }\n}\n\n@media (max-width: 480px) {\n  .toolbar-left {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .view-mode-switcher {\n    width: 100%;\n    justify-content: center;\n  }\n  \n  .grid-size-slider {\n    width: 100%;\n  }\n  \n  .size-slider {\n    flex: 1;\n  }\n}\n\n/* 虚拟滚动优化 */\n.virtual-list :deep(.ant-virtual-list-holder) {\n  overflow-x: hidden;\n}\n\n.virtual-list :deep(.ant-virtual-list-holder-inner) {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n}\n\n.grid-container.list-mode .virtual-list :deep(.ant-virtual-list-holder-inner) {\n  display: block;\n}\n\n/* 滚动条样式 */\n.virtual-list :deep(.ant-virtual-list-scrollbar) {\n  width: 6px;\n}\n\n.virtual-list :deep(.ant-virtual-list-scrollbar-thumb) {\n  background: #d9d9d9;\n  border-radius: 3px;\n}\n\n.virtual-list :deep(.ant-virtual-list-scrollbar-thumb:hover) {\n  background: #bfbfbf;\n}\n\n/* 动画效果 */\n.product-item-wrapper {\n  transition: all 0.3s ease;\n}\n\n.empty-state {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .product-grid {\n    border: 2px solid #000;\n  }\n  \n  .grid-toolbar {\n    border-bottom: 2px solid #000;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .product-item-wrapper {\n    transition: none;\n  }\n  \n  .empty-state {\n    animation: none;\n  }\n}\n</style>"