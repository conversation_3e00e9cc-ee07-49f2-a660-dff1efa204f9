/**
 * 懒加载工具函数
 * 
 * 提供组件、模块、CSS等资源的懒加载功能，优化应用性能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineAsyncComponent, ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 懒加载管理器
 */
export class LazyLoader {
  
  // 已加载的模块缓存
  static loadedModules = new Map()
  
  // 加载中的模块
  static loadingModules = new Map()
  
  // 加载统计
  static loadStats = {
    total: 0,
    success: 0,
    failed: 0,
    cached: 0
  }
  
  /**
   * 懒加载Vue组件
   * @param {Function} loader - 组件加载函数
   * @param {Object} options - 加载选项
   * @returns {Object} 异步组件
   */
  static lazyComponent(loader, options = {}) {
    const {
      loadingComponent = null,
      errorComponent = null,
      delay = 200,
      timeout = 10000,
      suspensible = false,
      onError = null
    } = options
    
    return defineAsyncComponent({
      loader: async () => {
        const startTime = performance.now()
        
        try {
          this.loadStats.total++
          
          // 检查缓存
          const cacheKey = loader.toString()
          if (this.loadedModules.has(cacheKey)) {
            this.loadStats.cached++
            return this.loadedModules.get(cacheKey)
          }
          
          // 检查是否正在加载
          if (this.loadingModules.has(cacheKey)) {
            return await this.loadingModules.get(cacheKey)
          }
          
          // 开始加载
          const loadPromise = loader()
          this.loadingModules.set(cacheKey, loadPromise)
          
          const component = await loadPromise
          
          // 缓存组件
          this.loadedModules.set(cacheKey, component)
          this.loadingModules.delete(cacheKey)
          
          this.loadStats.success++
          
          const loadTime = performance.now() - startTime
          console.log(`✅ 组件懒加载成功: ${loadTime.toFixed(2)}ms`)
          
          return component
          
        } catch (error) {
          this.loadStats.failed++
          this.loadingModules.delete(cacheKey)
          
          const loadTime = performance.now() - startTime
          console.error(`❌ 组件懒加载失败: ${loadTime.toFixed(2)}ms`, error)
          
          if (onError) {
            onError(error)
          }
          
          throw error
        }
      },
      loadingComponent,
      errorComponent,
      delay,
      timeout,
      suspensible
    })
  }
  
  /**
   * 懒加载JavaScript模块
   * @param {Function} loader - 模块加载函数
   * @param {Object} options - 加载选项
   * @returns {Promise} 模块Promise
   */
  static async lazyModule(loader, options = {}) {
    const {
      cache = true,
      timeout = 10000,
      retry = 3,
      retryDelay = 1000
    } = options
    
    const cacheKey = loader.toString()
    
    // 检查缓存
    if (cache && this.loadedModules.has(cacheKey)) {
      this.loadStats.cached++
      return this.loadedModules.get(cacheKey)
    }
    
    // 检查是否正在加载
    if (this.loadingModules.has(cacheKey)) {
      return await this.loadingModules.get(cacheKey)
    }
    
    const startTime = performance.now()
    let lastError = null
    
    // 重试加载
    for (let attempt = 1; attempt <= retry; attempt++) {
      try {
        this.loadStats.total++
        
        const loadPromise = Promise.race([
          loader(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Module load timeout')), timeout)
          )
        ])
        
        if (attempt === 1) {
          this.loadingModules.set(cacheKey, loadPromise)
        }
        
        const module = await loadPromise
        
        // 缓存模块
        if (cache) {
          this.loadedModules.set(cacheKey, module)
        }
        this.loadingModules.delete(cacheKey)
        
        this.loadStats.success++
        
        const loadTime = performance.now() - startTime
        console.log(`✅ 模块懒加载成功: ${loadTime.toFixed(2)}ms (尝试 ${attempt}/${retry})`)
        
        return module
        
      } catch (error) {
        lastError = error
        this.loadingModules.delete(cacheKey)
        
        if (attempt < retry) {
          console.warn(`⚠️ 模块加载失败，${retryDelay}ms后重试 (${attempt}/${retry}):`, error.message)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
        }
      }
    }
    
    this.loadStats.failed++
    
    const loadTime = performance.now() - startTime
    console.error(`❌ 模块懒加载失败: ${loadTime.toFixed(2)}ms (${retry}次尝试)`, lastError)
    
    throw lastError
  }
  
  /**
   * 懒加载CSS样式
   * @param {string} href - CSS文件路径
   * @param {Object} options - 加载选项
   * @returns {Promise} 加载Promise
   */
  static async lazyCSS(href, options = {}) {
    const {
      media = 'all',
      cache = true,
      timeout = 5000
    } = options
    
    // 检查是否已加载
    if (cache && document.querySelector(`link[href="${href}"]`)) {
      this.loadStats.cached++
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      const startTime = performance.now()
      
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = href
      link.media = media
      
      const cleanup = () => {
        clearTimeout(timeoutId)
        link.removeEventListener('load', onLoad)
        link.removeEventListener('error', onError)
      }
      
      const onLoad = () => {
        cleanup()
        this.loadStats.success++
        
        const loadTime = performance.now() - startTime
        console.log(`✅ CSS懒加载成功: ${href} (${loadTime.toFixed(2)}ms)`)
        
        resolve()
      }
      
      const onError = () => {
        cleanup()
        document.head.removeChild(link)
        this.loadStats.failed++
        
        const loadTime = performance.now() - startTime
        console.error(`❌ CSS懒加载失败: ${href} (${loadTime.toFixed(2)}ms)`)
        
        reject(new Error(`Failed to load CSS: ${href}`))
      }
      
      const timeoutId = setTimeout(() => {
        cleanup()
        document.head.removeChild(link)
        this.loadStats.failed++
        reject(new Error(`CSS load timeout: ${href}`))
      }, timeout)
      
      link.addEventListener('load', onLoad)
      link.addEventListener('error', onError)
      
      this.loadStats.total++
      document.head.appendChild(link)
    })
  }
  
  /**
   * 预加载资源
   * @param {Array} resources - 资源列表
   * @param {Object} options - 预加载选项
   * @returns {Promise} 预加载Promise
   */
  static async preload(resources, options = {}) {
    const {
      priority = 'low',
      concurrent = 3,
      onProgress = null
    } = options
    
    const results = []
    let completed = 0
    
    // 分批并发加载
    for (let i = 0; i < resources.length; i += concurrent) {
      const batch = resources.slice(i, i + concurrent)
      
      const batchPromises = batch.map(async (resource) => {
        try {
          let result
          
          if (typeof resource === 'function') {
            // 函数形式的资源加载器
            result = await this.lazyModule(resource, { cache: true })
          } else if (typeof resource === 'string') {
            if (resource.endsWith('.css')) {
              // CSS文件
              result = await this.lazyCSS(resource)
            } else {
              // 其他资源
              result = await fetch(resource, { priority })
            }
          } else if (resource.type === 'component') {
            // 组件预加载
            result = await resource.loader()
          }
          
          completed++
          
          if (onProgress) {
            onProgress(completed, resources.length)
          }
          
          return { resource, result, status: 'success' }
          
        } catch (error) {
          completed++
          
          if (onProgress) {
            onProgress(completed, resources.length)
          }
          
          return { resource, error, status: 'failed' }
        }
      })
      
      const batchResults = await Promise.allSettled(batchPromises)
      results.push(...batchResults.map(r => r.value))
    }
    
    const successful = results.filter(r => r.status === 'success').length
    const failed = results.filter(r => r.status === 'failed').length
    
    console.log(`📦 预加载完成: ${successful}个成功, ${failed}个失败`)
    
    return results
  }
  
  /**
   * 智能预加载（基于用户行为）
   * @param {Object} config - 预加载配置
   */
  static setupIntelligentPreload(config = {}) {
    const {
      idleTimeout = 2000,
      intersectionThreshold = 0.1,
      hoverDelay = 100
    } = config
    
    // 空闲时预加载
    if ('requestIdleCallback' in window) {
      const idlePreload = () => {
        requestIdleCallback((deadline) => {
          if (deadline.timeRemaining() > 0 && config.idleResources) {
            this.preload(config.idleResources, { priority: 'low' })
          }
        })
      }
      
      setTimeout(idlePreload, idleTimeout)
    }
    
    // 可视区域预加载
    if ('IntersectionObserver' in window && config.intersectionResources) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const resource = entry.target.dataset.preload
            if (resource && config.intersectionResources[resource]) {
              this.preload([config.intersectionResources[resource]])
              observer.unobserve(entry.target)
            }
          }
        })
      }, { threshold: intersectionThreshold })
      
      // 观察带有data-preload属性的元素
      document.querySelectorAll('[data-preload]').forEach(el => {
        observer.observe(el)
      })
    }
    
    // 鼠标悬停预加载
    if (config.hoverResources) {
      document.addEventListener('mouseover', (event) => {
        const target = event.target.closest('[data-hover-preload]')
        if (target) {
          const resource = target.dataset.hoverPreload
          if (config.hoverResources[resource]) {
            setTimeout(() => {
              this.preload([config.hoverResources[resource]])
            }, hoverDelay)
          }
        }
      })
    }
  }
  
  /**
   * 获取加载统计
   * @returns {Object} 加载统计信息
   */
  static getLoadStats() {
    return {
      ...this.loadStats,
      cacheSize: this.loadedModules.size,
      loadingCount: this.loadingModules.size,
      successRate: this.loadStats.total > 0 
        ? (this.loadStats.success / this.loadStats.total * 100).toFixed(2) + '%'
        : '0%'
    }
  }
  
  /**
   * 清除缓存
   * @param {string} pattern - 清除模式（可选）
   */
  static clearCache(pattern = null) {
    if (pattern) {
      // 按模式清除
      const regex = new RegExp(pattern)
      for (const [key] of this.loadedModules) {
        if (regex.test(key)) {
          this.loadedModules.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      this.loadedModules.clear()
      this.loadingModules.clear()
    }
    
    console.log('🧹 懒加载缓存已清除')
  }
  
  /**
   * 获取缓存信息
   * @returns {Object} 缓存信息
   */
  static getCacheInfo() {
    const cacheKeys = Array.from(this.loadedModules.keys())
    const loadingKeys = Array.from(this.loadingModules.keys())
    
    return {
      cached: cacheKeys.length,
      loading: loadingKeys.length,
      cacheKeys: cacheKeys.slice(0, 10), // 只显示前10个
      loadingKeys: loadingKeys.slice(0, 10)
    }
  }
}

/**
 * 创建懒加载组件的便捷函数
 * @param {Function} loader - 组件加载函数
 * @param {Object} options - 选项
 * @returns {Object} 懒加载组件
 */
export function createLazyComponent(loader, options = {}) {
  return LazyLoader.lazyComponent(loader, {
    loadingComponent: {
      template: `
        <div class="lazy-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>
      `,
      style: `
        .lazy-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #666;
        }
        .loading-spinner {
          width: 20px;
          height: 20px;
          border: 2px solid #f3f3f3;
          border-top: 2px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 8px;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .loading-text {
          font-size: 12px;
        }
      `
    },
    errorComponent: {
      template: `
        <div class="lazy-error">
          <div class="error-icon">⚠️</div>
          <div class="error-text">组件加载失败</div>
          <button class="retry-button" @click="$emit('retry')">重试</button>
        </div>
      `,
      style: `
        .lazy-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          color: #ff4d4f;
          text-align: center;
        }
        .error-icon {
          font-size: 24px;
          margin-bottom: 8px;
        }
        .error-text {
          font-size: 14px;
          margin-bottom: 12px;
        }
        .retry-button {
          padding: 4px 12px;
          border: 1px solid #ff4d4f;
          background: transparent;
          color: #ff4d4f;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }
        .retry-button:hover {
          background: #ff4d4f;
          color: white;
        }
      `
    },
    ...options
  })
}

/**
 * 路由懒加载辅助函数
 * @param {Function} loader - 路由组件加载函数
 * @returns {Function} 路由组件加载器
 */
export function lazyRoute(loader) {
  return () => LazyLoader.lazyModule(loader, {
    cache: true,
    retry: 2,
    timeout: 8000
  })
}

/**
 * 条件懒加载
 * @param {Function} condition - 条件函数
 * @param {Function} loader - 加载函数
 * @param {Function} fallback - 降级函数
 * @returns {Promise} 加载结果
 */
export async function conditionalLazy(condition, loader, fallback = null) {
  if (await condition()) {
    return LazyLoader.lazyModule(loader)
  } else if (fallback) {
    return LazyLoader.lazyModule(fallback)
  } else {
    throw new Error('Condition not met and no fallback provided')
  }
}

/**
 * 批量懒加载
 * @param {Array} loaders - 加载器数组
 * @param {Object} options - 选项
 * @returns {Promise} 批量加载结果
 */
export async function batchLazy(loaders, options = {}) {
  const {
    concurrent = 3,
    failFast = false
  } = options
  
  if (failFast) {
    // 快速失败模式
    const results = []
    for (let i = 0; i < loaders.length; i += concurrent) {
      const batch = loaders.slice(i, i + concurrent)
      const batchResults = await Promise.all(
        batch.map(loader => LazyLoader.lazyModule(loader))
      )
      results.push(...batchResults)
    }
    return results
  } else {
    // 容错模式
    return LazyLoader.preload(loaders, { concurrent })
  }
}

// 默认导出
export default LazyLoader