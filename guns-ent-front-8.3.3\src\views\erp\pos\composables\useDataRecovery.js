/**
 * 数据恢复组合式函数
 * 
 * 提供完整的数据备份和恢复功能，包括购物车、订单、会员等状态
 * 确保系统异常时用户数据不丢失
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { STORAGE_KEYS, NUMERIC_CONSTANTS } from '../utils/constants'

/**
 * 数据恢复组合式函数
 * @param {Object} options - 配置选项
 * @returns {Object} 数据恢复相关的方法和状态
 */
export function useDataRecovery(options = {}) {
  // 配置选项
  const config = ref({
    enableAutoBackup: true,
    backupInterval: 30000, // 30秒
    maxBackupCount: 10,
    enableCrashDetection: true,
    enableRecoveryPrompt: true,
    compressionEnabled: true,
    encryptionEnabled: false,
    ...options
  })
  
  // 恢复状态
  const recoveryState = ref({
    isRecovering: false,
    hasRecoverableData: false,
    lastBackupTime: null,
    backupCount: 0,
    recoveryHistory: []
  })
  
  // 备份数据类型
  const backupTypes = {
    CART: 'cart',
    ORDER: 'order',
    MEMBER: 'member',
    PAYMENT: 'payment',
    SETTINGS: 'settings',
    SESSION: 'session'
  }
  
  // 自动备份定时器
  let autoBackupTimer = null
  let crashDetectionTimer = null
  
  // 计算属性
  const canRecover = computed(() => {
    return recoveryState.value.hasRecoverableData && !recoveryState.value.isRecovering
  })
  
  const backupStatus = computed(() => {
    const { lastBackupTime, backupCount } = recoveryState.value
    const timeSinceBackup = lastBackupTime ? Date.now() - lastBackupTime : null
    
    return {
      lastBackupTime,
      backupCount,
      timeSinceBackup,
      isStale: timeSinceBackup && timeSinceBackup > config.value.backupInterval * 2
    }
  })
  
  /**
   * 生成备份键名
   * @param {string} type - 数据类型
   * @param {number} index - 备份索引
   * @returns {string} 备份键名
   */
  const generateBackupKey = (type, index = 0) => {
    return `${STORAGE_KEYS.DATA_BACKUP_PREFIX}_${type}_${index}`
  }
  
  /**
   * 压缩数据
   * @param {Object} data - 要压缩的数据
   * @returns {string} 压缩后的数据
   */
  const compressData = (data) => {
    if (!config.value.compressionEnabled) {
      return JSON.stringify(data)
    }
    
    try {
      // 简单的压缩：移除空格和重复字符
      const jsonString = JSON.stringify(data)
      return jsonString.replace(/\\s+/g, ' ').trim()
    } catch (error) {
      console.warn('数据压缩失败:', error)
      return JSON.stringify(data)
    }
  }
  
  /**
   * 解压数据
   * @param {string} compressedData - 压缩的数据
   * @returns {Object} 解压后的数据
   */
  const decompressData = (compressedData) => {
    try {
      return JSON.parse(compressedData)
    } catch (error) {
      console.warn('数据解压失败:', error)
      return null
    }
  }
  
  /**
   * 加密数据（简单实现）
   * @param {string} data - 要加密的数据
   * @returns {string} 加密后的数据
   */
  const encryptData = (data) => {
    if (!config.value.encryptionEnabled) {
      return data
    }
    
    // 简单的Base64编码（实际项目中应使用更安全的加密方法）
    try {
      return btoa(data)
    } catch (error) {
      console.warn('数据加密失败:', error)
      return data
    }
  }
  
  /**
   * 解密数据
   * @param {string} encryptedData - 加密的数据
   * @returns {string} 解密后的数据
   */
  const decryptData = (encryptedData) => {
    if (!config.value.encryptionEnabled) {
      return encryptedData
    }
    
    try {
      return atob(encryptedData)
    } catch (error) {
      console.warn('数据解密失败:', error)
      return encryptedData
    }
  }
  
  /**
   * 保存数据到本地存储
   * @param {string} type - 数据类型
   * @param {Object} data - 要保存的数据
   * @param {Object} options - 保存选项
   * @returns {Promise<boolean>} 是否保存成功
   */
  const saveData = async (type, data, options = {}) => {
    try {
      const {
        skipValidation = false,
        metadata = {}
      } = options
      
      // 数据验证
      if (!skipValidation && !validateData(type, data)) {
        throw new Error(`数据验证失败: ${type}`)
      }
      
      // 创建备份数据结构
      const backupData = {
        type,
        data,
        timestamp: Date.now(),
        version: '2.0',
        checksum: generateChecksum(data),
        metadata: {
          userAgent: navigator.userAgent,
          url: window.location.href,
          sessionId: getSessionId(),
          ...metadata
        }
      }
      
      // 压缩和加密
      const compressedData = compressData(backupData)
      const encryptedData = encryptData(compressedData)
      
      // 轮转备份（保持最大备份数量）
      await rotateBackups(type)
      
      // 保存到localStorage
      const backupKey = generateBackupKey(type, 0)
      localStorage.setItem(backupKey, encryptedData)
      
      // 更新状态
      recoveryState.value.lastBackupTime = Date.now()
      recoveryState.value.backupCount++
      recoveryState.value.hasRecoverableData = true
      
      console.log(`✅ 数据备份成功: ${type}`)
      return true
      
    } catch (error) {
      console.error(`❌ 数据备份失败: ${type}`, error)
      return false
    }
  }
  
  /**
   * 从本地存储恢复数据
   * @param {string} type - 数据类型
   * @param {Object} options - 恢复选项
   * @returns {Promise<Object|null>} 恢复的数据或null
   */
  const restoreData = async (type, options = {}) => {
    try {
      const {
        backupIndex = 0,
        skipValidation = false,
        maxAge = null
      } = options
      
      const backupKey = generateBackupKey(type, backupIndex)
      const encryptedData = localStorage.getItem(backupKey)
      
      if (!encryptedData) {
        console.warn(`没有找到备份数据: ${type}`)
        return null
      }
      
      // 解密和解压
      const compressedData = decryptData(encryptedData)
      const backupData = decompressData(compressedData)
      
      if (!backupData) {
        throw new Error('数据解析失败')
      }
      
      // 检查数据年龄
      if (maxAge && Date.now() - backupData.timestamp > maxAge) {
        console.warn(`备份数据已过期: ${type}`)
        return null
      }
      
      // 验证数据完整性
      if (!skipValidation) {
        const currentChecksum = generateChecksum(backupData.data)
        if (currentChecksum !== backupData.checksum) {
          throw new Error('数据校验失败')
        }
      }
      
      // 记录恢复历史
      recoveryState.value.recoveryHistory.unshift({
        type,
        timestamp: Date.now(),
        backupTimestamp: backupData.timestamp,
        success: true
      })
      
      // 限制历史记录数量
      if (recoveryState.value.recoveryHistory.length > 50) {
        recoveryState.value.recoveryHistory = recoveryState.value.recoveryHistory.slice(0, 50)
      }
      
      console.log(`✅ 数据恢复成功: ${type}`)
      return backupData.data
      
    } catch (error) {
      console.error(`❌ 数据恢复失败: ${type}`, error)
      
      // 记录失败的恢复尝试
      recoveryState.value.recoveryHistory.unshift({
        type,
        timestamp: Date.now(),
        success: false,
        error: error.message
      })
      
      return null
    }
  }
  
  /**
   * 轮转备份（保持指定数量的备份）
   * @param {string} type - 数据类型
   */
  const rotateBackups = async (type) => {
    try {
      const maxCount = config.value.maxBackupCount
      
      // 将现有备份向后移动
      for (let i = maxCount - 1; i > 0; i--) {
        const currentKey = generateBackupKey(type, i - 1)
        const nextKey = generateBackupKey(type, i)
        
        const data = localStorage.getItem(currentKey)
        if (data) {
          localStorage.setItem(nextKey, data)
        }
      }
      
      // 删除超出限制的备份
      const oldKey = generateBackupKey(type, maxCount)
      localStorage.removeItem(oldKey)
      
    } catch (error) {
      console.warn('备份轮转失败:', error)
    }
  }
  
  /**
   * 验证数据
   * @param {string} type - 数据类型
   * @param {Object} data - 要验证的数据
   * @returns {boolean} 是否有效
   */
  const validateData = (type, data) => {
    if (!data || typeof data !== 'object') {
      return false
    }
    
    switch (type) {
      case backupTypes.CART:
        return Array.isArray(data.items) && typeof data.total === 'number'
        
      case backupTypes.ORDER:
        return data.orderId && Array.isArray(data.items)
        
      case backupTypes.MEMBER:
        return data.memberId || data.memberInfo
        
      case backupTypes.PAYMENT:
        return data.method && typeof data.amount === 'number'
        
      case backupTypes.SETTINGS:
        return typeof data === 'object'
        
      case backupTypes.SESSION:
        return data.sessionId && data.timestamp
        
      default:
        return true
    }
  }
  
  /**
   * 生成数据校验和
   * @param {Object} data - 数据
   * @returns {string} 校验和
   */
  const generateChecksum = (data) => {
    const str = JSON.stringify(data)
    let hash = 0
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return hash.toString(36)
  }
  
  /**
   * 获取会话ID
   * @returns {string} 会话ID
   */
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('pos_session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('pos_session_id', sessionId)
    }
    return sessionId
  }
  
  /**
   * 检查是否有可恢复的数据
   * @returns {Promise<Object>} 可恢复的数据信息
   */
  const checkRecoverableData = async () => {
    const recoverableData = {}
    
    for (const type of Object.values(backupTypes)) {
      const backupKey = generateBackupKey(type, 0)
      const data = localStorage.getItem(backupKey)
      
      if (data) {
        try {
          const compressedData = decryptData(data)
          const backupData = decompressData(compressedData)
          
          if (backupData && backupData.timestamp) {
            recoverableData[type] = {
              timestamp: backupData.timestamp,
              age: Date.now() - backupData.timestamp,
              size: data.length
            }
          }
        } catch (error) {
          console.warn(`检查备份数据失败: ${type}`, error)
        }
      }
    }
    
    recoveryState.value.hasRecoverableData = Object.keys(recoverableData).length > 0
    
    return recoverableData
  }
  
  /**
   * 显示恢复提示
   * @param {Object} recoverableData - 可恢复的数据
   */
  const showRecoveryPrompt = (recoverableData) => {
    if (!config.value.enableRecoveryPrompt) {
      return
    }
    
    const dataTypes = Object.keys(recoverableData)
    if (dataTypes.length === 0) {
      return
    }
    
    const typeNames = {
      [backupTypes.CART]: '购物车',
      [backupTypes.ORDER]: '订单',
      [backupTypes.MEMBER]: '会员信息',
      [backupTypes.PAYMENT]: '支付信息',
      [backupTypes.SETTINGS]: '设置',
      [backupTypes.SESSION]: '会话'
    }
    
    const dataList = dataTypes.map(type => {
      const info = recoverableData[type]
      const name = typeNames[type] || type
      const time = new Date(info.timestamp).toLocaleString()
      return `${name} (${time})`
    }).join('\\n')
    
    Modal.confirm({
      title: '发现可恢复的数据',
      content: `检测到以下数据可以恢复：\\n\\n${dataList}\\n\\n是否要恢复这些数据？`,
      okText: '恢复',
      cancelText: '忽略',
      onOk: async () => {
        await performBatchRecovery(dataTypes)
      },
      onCancel: () => {
        message.info('已忽略数据恢复')
      }
    })
  }
  
  /**
   * 执行批量恢复
   * @param {Array} types - 要恢复的数据类型
   */
  const performBatchRecovery = async (types) => {
    recoveryState.value.isRecovering = true
    
    try {
      const results = []
      
      for (const type of types) {
        const data = await restoreData(type)
        results.push({ type, data, success: !!data })
      }
      
      const successful = results.filter(r => r.success).length
      const failed = results.length - successful
      
      if (successful > 0) {
        message.success(`成功恢复 ${successful} 项数据`)
      }
      
      if (failed > 0) {
        message.warning(`${failed} 项数据恢复失败`)
      }
      
      return results
      
    } catch (error) {
      console.error('批量恢复失败:', error)
      message.error('数据恢复失败')
      return []
    } finally {
      recoveryState.value.isRecovering = false
    }
  }
  
  /**
   * 设置自动备份
   * @param {Function} dataProvider - 数据提供函数
   */
  const setupAutoBackup = (dataProvider) => {
    if (!config.value.enableAutoBackup || autoBackupTimer) {
      return
    }
    
    autoBackupTimer = setInterval(async () => {
      try {
        const data = await dataProvider()
        
        if (data) {
          // 根据数据类型进行备份
          if (data.cart) {
            await saveData(backupTypes.CART, data.cart)
          }
          if (data.order) {
            await saveData(backupTypes.ORDER, data.order)
          }
          if (data.member) {
            await saveData(backupTypes.MEMBER, data.member)
          }
          if (data.payment) {
            await saveData(backupTypes.PAYMENT, data.payment)
          }
          if (data.settings) {
            await saveData(backupTypes.SETTINGS, data.settings)
          }
        }
      } catch (error) {
        console.warn('自动备份失败:', error)
      }
    }, config.value.backupInterval)
    
    console.log('🔄 自动备份已启用')
  }
  
  /**
   * 停止自动备份
   */
  const stopAutoBackup = () => {
    if (autoBackupTimer) {
      clearInterval(autoBackupTimer)
      autoBackupTimer = null
      console.log('⏹️ 自动备份已停止')
    }
  }
  
  /**
   * 设置崩溃检测
   */
  const setupCrashDetection = () => {
    if (!config.value.enableCrashDetection) {
      return
    }
    
    // 设置心跳检测
    const heartbeatKey = 'pos_heartbeat'
    const updateHeartbeat = () => {
      localStorage.setItem(heartbeatKey, Date.now().toString())
    }
    
    // 初始心跳
    updateHeartbeat()
    
    // 定期更新心跳
    crashDetectionTimer = setInterval(updateHeartbeat, 5000)
    
    // 页面加载时检查是否有异常退出
    const lastHeartbeat = localStorage.getItem(heartbeatKey)
    if (lastHeartbeat) {
      const timeSinceHeartbeat = Date.now() - parseInt(lastHeartbeat)
      
      // 如果超过30秒没有心跳，认为是异常退出
      if (timeSinceHeartbeat > 30000) {
        console.warn('检测到异常退出，检查可恢复数据')
        
        setTimeout(async () => {
          const recoverableData = await checkRecoverableData()
          if (Object.keys(recoverableData).length > 0) {
            showRecoveryPrompt(recoverableData)
          }
        }, 1000)
      }
    }
    
    // 页面卸载时清除心跳
    window.addEventListener('beforeunload', () => {
      localStorage.removeItem(heartbeatKey)
    })
  }
  
  /**
   * 清除所有备份数据
   * @param {string} type - 数据类型（可选）
   */
  const clearAllBackups = (type = null) => {
    try {
      if (type) {
        // 清除指定类型的备份
        for (let i = 0; i < config.value.maxBackupCount; i++) {
          const key = generateBackupKey(type, i)
          localStorage.removeItem(key)
        }
        console.log(`🧹 已清除 ${type} 类型的所有备份`)
      } else {
        // 清除所有备份
        for (const backupType of Object.values(backupTypes)) {
          for (let i = 0; i < config.value.maxBackupCount; i++) {
            const key = generateBackupKey(backupType, i)
            localStorage.removeItem(key)
          }
        }
        
        // 重置状态
        recoveryState.value.hasRecoverableData = false
        recoveryState.value.backupCount = 0
        recoveryState.value.lastBackupTime = null
        
        console.log('🧹 已清除所有备份数据')
      }
    } catch (error) {
      console.error('清除备份数据失败:', error)
    }
  }
  
  /**
   * 获取备份统计信息
   * @returns {Object} 统计信息
   */
  const getBackupStatistics = () => {
    const stats = {
      totalBackups: 0,
      totalSize: 0,
      typeStats: {},
      oldestBackup: null,
      newestBackup: null
    }
    
    for (const type of Object.values(backupTypes)) {
      const typeStats = {
        count: 0,
        size: 0,
        timestamps: []
      }
      
      for (let i = 0; i < config.value.maxBackupCount; i++) {
        const key = generateBackupKey(type, i)
        const data = localStorage.getItem(key)
        
        if (data) {
          typeStats.count++
          typeStats.size += data.length
          
          try {
            const compressedData = decryptData(data)
            const backupData = decompressData(compressedData)
            if (backupData && backupData.timestamp) {
              typeStats.timestamps.push(backupData.timestamp)
            }
          } catch (error) {
            // 忽略解析错误
          }
        }
      }
      
      if (typeStats.count > 0) {
        stats.typeStats[type] = typeStats
        stats.totalBackups += typeStats.count
        stats.totalSize += typeStats.size
        
        const minTimestamp = Math.min(...typeStats.timestamps)
        const maxTimestamp = Math.max(...typeStats.timestamps)
        
        if (!stats.oldestBackup || minTimestamp < stats.oldestBackup) {
          stats.oldestBackup = minTimestamp
        }
        
        if (!stats.newestBackup || maxTimestamp > stats.newestBackup) {
          stats.newestBackup = maxTimestamp
        }
      }
    }
    
    return stats
  }
  
  /**
   * 导出备份数据
   * @param {string} format - 导出格式 ('json' | 'csv')
   * @returns {string} 导出的数据
   */
  const exportBackupData = (format = 'json') => {
    const allBackups = {}
    
    for (const type of Object.values(backupTypes)) {
      const typeBackups = []
      
      for (let i = 0; i < config.value.maxBackupCount; i++) {
        const key = generateBackupKey(type, i)
        const data = localStorage.getItem(key)
        
        if (data) {
          try {
            const compressedData = decryptData(data)
            const backupData = decompressData(compressedData)
            if (backupData) {
              typeBackups.push(backupData)
            }
          } catch (error) {
            console.warn(`导出备份数据失败: ${key}`, error)
          }
        }
      }
      
      if (typeBackups.length > 0) {
        allBackups[type] = typeBackups
      }
    }
    
    if (format === 'csv') {
      const csvData = []
      csvData.push('Type,Timestamp,Size,Checksum')
      
      for (const [type, backups] of Object.entries(allBackups)) {
        backups.forEach(backup => {
          const size = JSON.stringify(backup.data).length
          csvData.push(`${type},${new Date(backup.timestamp).toISOString()},${size},${backup.checksum}`)
        })
      }
      
      return csvData.join('\\n')
    }
    
    return JSON.stringify({
      exportTime: new Date().toISOString(),
      backups: allBackups,
      statistics: getBackupStatistics()
    }, null, 2)
  }
  
  // 生命周期钩子
  onMounted(async () => {
    // 检查可恢复数据
    await checkRecoverableData()
    
    // 设置崩溃检测
    setupCrashDetection()
  })
  
  onUnmounted(() => {
    // 清理定时器
    stopAutoBackup()
    
    if (crashDetectionTimer) {
      clearInterval(crashDetectionTimer)
      crashDetectionTimer = null
    }
  })
  
  return {
    // 状态
    recoveryState,
    config,
    canRecover,
    backupStatus,
    backupTypes,
    
    // 方法
    saveData,
    restoreData,
    checkRecoverableData,
    showRecoveryPrompt,
    performBatchRecovery,
    setupAutoBackup,
    stopAutoBackup,
    clearAllBackups,
    getBackupStatistics,
    exportBackupData,
    
    // 兼容性方法（保持向后兼容）
    saveCartState: (cartData) => saveData(backupTypes.CART, cartData),
    restoreCartState: () => restoreData(backupTypes.CART),
    clearBackup: () => clearAllBackups(backupTypes.CART)
  }
}