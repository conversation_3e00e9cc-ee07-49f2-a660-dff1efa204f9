<template>
<!-- 功能栏 -->
  <div class="toolbar-panel">
    <!-- 垂直排列的功能按钮组 -->
    <div class="function-buttons">
      <!-- 挂单列表按钮 -->
      <div class="function-button-item">
        <div class="function-card suspend-list-card" @click="handleShowSuspendedOrders">
          <div class="card-content">
            <div class="card-title">挂单列表</div>
          </div>
          <div class="card-badge" v-if="suspendedOrdersCount > 0">
            {{ suspendedOrdersCount }}
          </div>
        </div>
      </div>

      <!-- 会员管理按钮 -->
      <div class="function-button-item">
        <div class="function-card member-card" @click="handleMemberManagement">
          <div class="card-content">
            <div class="card-title">会员管理</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePosStore } from '@/stores/pos'

// 导入样式文件
import '../styles/common.css'
import '../styles/toolbar.css'

// 定义组件名称
defineOptions({
  name: 'ToolbarPanel'
})

// 定义Props
const props = defineProps({
  suspendedOrdersCount: {
    type: Number,
    default: 0
  }
})

// 定义事件
const emit = defineEmits([
  'showSuspendedOrders',
  'memberManagement'
])

// 使用POS状态管理
const posStore = usePosStore()

/**
 * 显示挂单列表
 */
const handleShowSuspendedOrders = () => {
  emit('showSuspendedOrders')
}

/**
 * 会员管理
 */
const handleMemberManagement = () => {
  emit('memberManagement')
}
</script>

<style scoped>
.toolbar-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  /* 新增：最大宽度限制，防止溢出 */
  max-width: 100%;
  overflow: hidden;
}

.function-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  /* 新增：最大宽度限制，防止溢出 */
  max-width: 100%;
  overflow: hidden;
}

.function-button-item {
  position: relative;
  /* 新增：防止子元素溢出 */
  max-width: 100%;
}

.function-button-item.checkout-button {
  margin-top: auto; /* 将结账按钮推到底部 */
}

.function-btn {
  height: 70px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  /* 新增：宽度自适应父容器 */
  width: 100%;
  min-width: 0;
  box-sizing: border-box;
  /* 优化内边距，防止撑宽 */
  padding-left: 0;
  padding-right: 0;
}

.function-btn .btn-text {
  font-size: 14px;
  line-height: 1.2;
  font-weight: 500;
}

/* 挂单列表按钮 */
.suspend-list-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #fff;
}

.suspend-list-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: #fff;
}



/* 会员管理按钮 */
.member-btn {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  border: none;
  color: #2d3436;
}

.member-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #81ecec 0%, #fd79a8 100%);
  color: #2d3436;
}

/* 结账按钮 */
.checkout-btn {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  border: none;
  color: #fff;
  height: 90px;
  font-size: 18px;
  padding: 12px 16px;
}

.checkout-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #00a085 0%, #00b7b3 100%);
  color: #fff;
}

.checkout-btn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.checkout-btn .btn-text {
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-panel {
    padding: 8px;
    max-width: 100vw;
  }

  .function-btn {
    height: 50px;
    font-size: 12px;
    min-width: 0;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }

  .function-btn .btn-text {
    font-size: 11px;
  }

  .checkout-btn {
    height: 60px;
    font-size: 14px;
  }

  .checkout-btn .btn-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .toolbar-panel {
    padding: 6px;
    gap: 8px;
  }

  .function-btn {
    height: 45px;
    font-size: 11px;
    padding: 6px;
    min-width: 60px;
  }

  .function-btn .btn-text {
    font-size: 10px;
  }

  .checkout-btn {
    height: 50px;
    font-size: 12px;
  }

  .checkout-btn .btn-text {
    font-size: 11px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .function-btn:hover:not(:disabled) {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .function-btn {
    transition: none;
  }

  .function-btn:hover:not(:disabled) {
    transform: none;
  }
}
</style>
