import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useCartStore } from '../cart'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('useCartStore', () => {
  let pinia
  let cartStore
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    cartStore = useCartStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(cartStore.cartItems).toEqual([])
      expect(cartStore.totalAmount).toBe(0)
      expect(cartStore.discountAmount).toBe(0)
      expect(cartStore.finalAmount).toBe(0)
      expect(cartStore.isBatchUpdating).toBe(false)
    })

    it('应该有正确的计算属性初始值', () => {
      expect(cartStore.cartItemCount).toBe(0)
      expect(cartStore.hasCartItems).toBe(false)
      expect(cartStore.cartSummary.hasItems).toBe(false)
    })
  })

  describe('添加商品', () => {
    const mockProduct = {
      productId: 1,
      productName: '测试商品',
      productCode: 'TEST001',
      price: 10.50,
      stock: 100,
      unit: '个'
    }

    it('应该能够添加新商品', () => {
      const result = cartStore.addToCart(mockProduct, 2)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(1)
      expect(cartStore.cartItems[0]).toEqual(expect.objectContaining({
        productId: 1,
        productName: '测试商品',
        quantity: 2,
        unitPrice: 10.50,
        totalPrice: 21.00
      }))
      expect(cartStore.totalAmount).toBe(21.00)
    })

    it('应该能够增加已存在商品的数量', () => {
      cartStore.addToCart(mockProduct, 1)
      cartStore.addToCart(mockProduct, 2)
      
      expect(cartStore.cartItems).toHaveLength(1)
      expect(cartStore.cartItems[0].quantity).toBe(3)
      expect(cartStore.cartItems[0].totalPrice).toBe(31.50)
      expect(cartStore.totalAmount).toBe(31.50)
    })

    it('应该验证商品数据', () => {
      const invalidProduct = {
        productId: null,
        productName: '',
        price: 0
      }
      
      const result = cartStore.addToCart(invalidProduct)
      
      expect(result).toBe(false)
      expect(cartStore.cartItems).toHaveLength(0)
    })

    it('应该检查库存', () => {
      const lowStockProduct = {
        ...mockProduct,
        stock: 1
      }
      
      const result = cartStore.addToCart(lowStockProduct, 5)
      
      expect(result).toBe(false)
      expect(cartStore.cartItems).toHaveLength(0)
    })

    it('应该处理没有库存信息的商品', () => {
      const noStockProduct = {
        ...mockProduct
      }
      delete noStockProduct.stock
      
      const result = cartStore.addToCart(noStockProduct, 1)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(1)
    })

    it('应该使用不同的价格字段', () => {
      const productWithRetailPrice = {
        productId: 2,
        productName: '零售价商品',
        retailPrice: 15.00,
        stock: 100
      }
      
      cartStore.addToCart(productWithRetailPrice, 1)
      
      expect(cartStore.cartItems[0].unitPrice).toBe(15.00)
      expect(cartStore.cartItems[0].totalPrice).toBe(15.00)
    })
  })

  describe('移除商品', () => {
    beforeEach(() => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      cartStore.addToCart(mockProduct, 2)
    })

    it('应该能够移除商品', () => {
      const result = cartStore.removeFromCart(1)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(0)
      expect(cartStore.totalAmount).toBe(0)
    })

    it('应该处理不存在的商品ID', () => {
      const result = cartStore.removeFromCart(999)
      
      expect(result).toBe(false)
      expect(cartStore.cartItems).toHaveLength(1)
    })
  })

  describe('更新商品数量', () => {
    beforeEach(() => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      cartStore.addToCart(mockProduct, 2)
    })

    it('应该能够更新商品数量', () => {
      const result = cartStore.updateQuantity(1, 5)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems[0].quantity).toBe(5)
      expect(cartStore.cartItems[0].totalPrice).toBe(50.00)
      expect(cartStore.totalAmount).toBe(50.00)
    })

    it('应该在数量为0时移除商品', () => {
      const result = cartStore.updateQuantity(1, 0)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(0)
    })

    it('应该在数量为负数时移除商品', () => {
      const result = cartStore.updateQuantity(1, -1)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(0)
    })

    it('应该处理不存在的商品ID', () => {
      const result = cartStore.updateQuantity(999, 3)
      
      expect(result).toBe(false)
      expect(cartStore.cartItems).toHaveLength(1)
    })
  })

  describe('批量更新', () => {
    beforeEach(() => {
      const products = [
        { productId: 1, productName: '商品1', price: 10.00, stock: 100 },
        { productId: 2, productName: '商品2', price: 20.00, stock: 100 },
        { productId: 3, productName: '商品3', price: 30.00, stock: 100 }
      ]
      
      products.forEach(product => cartStore.addToCart(product, 1))
    })

    it('应该能够批量更新商品数量', () => {
      const updates = [
        { productId: 1, quantity: 3 },
        { productId: 2, quantity: 2 },
        { productId: 3, quantity: 0 } // 应该被移除
      ]
      
      const result = cartStore.batchUpdateQuantities(updates)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(2)
      expect(cartStore.cartItems.find(item => item.productId === 1).quantity).toBe(3)
      expect(cartStore.cartItems.find(item => item.productId === 2).quantity).toBe(2)
      expect(cartStore.cartItems.find(item => item.productId === 3)).toBeUndefined()
      expect(cartStore.totalAmount).toBe(70.00) // 3*10 + 2*20
    })

    it('应该处理空的更新数组', () => {
      const result = cartStore.batchUpdateQuantities([])
      
      expect(result).toBe(false)
      expect(cartStore.cartItems).toHaveLength(3)
    })

    it('应该处理批量更新中的错误', () => {
      // Mock console.error to avoid test output pollution
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟批量更新过程中的错误
      const originalFind = cartStore.cartItems.find
      cartStore.cartItems.find = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const updates = [{ productId: 1, quantity: 3 }]
      const result = cartStore.batchUpdateQuantities(updates)
      
      expect(result).toBe(false)
      
      // 恢复原始方法
      cartStore.cartItems.find = originalFind
      consoleSpy.mockRestore()
    })
  })

  describe('清空购物车', () => {
    beforeEach(() => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      cartStore.addToCart(mockProduct, 2)
      cartStore.setDiscountAmount(5.00)
    })

    it('应该能够清空购物车', () => {
      cartStore.clearCart()
      
      expect(cartStore.cartItems).toHaveLength(0)
      expect(cartStore.totalAmount).toBe(0)
      expect(cartStore.discountAmount).toBe(0)
      expect(cartStore.finalAmount).toBe(0)
    })
  })

  describe('折扣管理', () => {
    beforeEach(() => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      cartStore.addToCart(mockProduct, 5) // 总价50.00
    })

    it('应该能够设置折扣金额', () => {
      cartStore.setDiscountAmount(10.00)
      
      expect(cartStore.discountAmount).toBe(10.00)
      expect(cartStore.finalAmount).toBe(40.00)
    })

    it('应该处理负数折扣', () => {
      cartStore.setDiscountAmount(-5.00)
      
      expect(cartStore.discountAmount).toBe(0)
      expect(cartStore.finalAmount).toBe(50.00)
    })

    it('应该在设置折扣后重新计算总额', () => {
      cartStore.setDiscountAmount(15.00)
      
      expect(cartStore.finalAmount).toBe(35.00)
      
      // 添加更多商品
      const anotherProduct = {
        productId: 2,
        productName: '另一个商品',
        price: 20.00,
        stock: 100
      }
      cartStore.addToCart(anotherProduct, 1)
      
      expect(cartStore.totalAmount).toBe(70.00)
      expect(cartStore.finalAmount).toBe(55.00) // 70 - 15
    })
  })

  describe('购物车快照', () => {
    beforeEach(() => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      cartStore.addToCart(mockProduct, 2)
      cartStore.setDiscountAmount(5.00)
    })

    it('应该能够获取购物车快照', () => {
      const snapshot = cartStore.getCartSnapshot()
      
      expect(snapshot).toEqual({
        cartItems: expect.any(Array),
        totalAmount: 20.00,
        discountAmount: 5.00,
        finalAmount: 15.00,
        timestamp: expect.any(Number)
      })
      expect(snapshot.cartItems).toHaveLength(1)
    })

    it('应该能够从快照恢复购物车', () => {
      const snapshot = cartStore.getCartSnapshot()
      
      // 清空购物车
      cartStore.clearCart()
      expect(cartStore.cartItems).toHaveLength(0)
      
      // 从快照恢复
      const result = cartStore.restoreFromSnapshot(snapshot)
      
      expect(result).toBe(true)
      expect(cartStore.cartItems).toHaveLength(1)
      expect(cartStore.totalAmount).toBe(20.00)
      expect(cartStore.discountAmount).toBe(5.00)
      expect(cartStore.finalAmount).toBe(15.00)
    })

    it('应该处理无效的快照', () => {
      const result = cartStore.restoreFromSnapshot(null)
      
      expect(result).toBe(false)
    })

    it('应该处理格式错误的快照', () => {
      const invalidSnapshot = {
        cartItems: 'not an array',
        totalAmount: 'not a number'
      }
      
      const result = cartStore.restoreFromSnapshot(invalidSnapshot)
      
      expect(result).toBe(false)
    })
  })

  describe('数据验证', () => {
    beforeEach(() => {
      const products = [
        { productId: 1, productName: '商品1', price: 10.00, stock: 100 },
        { productId: 2, productName: '商品2', price: 20.00, stock: 100 }
      ]
      
      products.forEach(product => cartStore.addToCart(product, 2))
    })

    it('应该能够验证购物车数据', () => {
      const validation = cartStore.validateCart()
      
      expect(validation.isValid).toBe(true)
      expect(validation.issues).toHaveLength(0)
    })

    it('应该检测数据问题', () => {
      // 手动破坏数据
      cartStore.cartItems[0].productId = null
      cartStore.cartItems[0].totalPrice = 999 // 错误的总价
      
      const validation = cartStore.validateCart()
      
      expect(validation.isValid).toBe(false)
      expect(validation.issues.length).toBeGreaterThan(0)
    })

    it('应该能够修复购物车数据', () => {
      // 手动破坏数据
      cartStore.cartItems[0].totalPrice = 999
      cartStore.cartItems[1].quantity = 0
      
      const result = cartStore.repairCart()
      
      expect(result).toBe(true)
      expect(cartStore.cartItems[0].totalPrice).toBe(20.00) // 10 * 2
      expect(cartStore.cartItems[1].quantity).toBe(1) // 修复为1
    })
  })

  describe('计算属性', () => {
    it('应该正确计算商品总数量', () => {
      const products = [
        { productId: 1, productName: '商品1', price: 10.00, stock: 100 },
        { productId: 2, productName: '商品2', price: 20.00, stock: 100 }
      ]
      
      cartStore.addToCart(products[0], 3)
      cartStore.addToCart(products[1], 2)
      
      expect(cartStore.cartItemCount).toBe(5)
    })

    it('应该正确计算购物车摘要', () => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      
      cartStore.addToCart(mockProduct, 3)
      cartStore.setDiscountAmount(5.00)
      
      const summary = cartStore.cartSummary
      
      expect(summary).toEqual({
        itemCount: 3,
        totalAmount: '30.00',
        discountAmount: '5.00',
        finalAmount: '25.00',
        hasItems: true
      })
    })
  })

  describe('工具方法', () => {
    it('应该正确格式化金额', () => {
      expect(cartStore.formatAmount(10)).toBe('10.00')
      expect(cartStore.formatAmount(10.5)).toBe('10.50')
      expect(cartStore.formatAmount(10.555)).toBe('10.56')
      expect(cartStore.formatAmount('invalid')).toBe('0.00')
      expect(cartStore.formatAmount(null)).toBe('0.00')
    })

    it('应该能够获取特定商品', () => {
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      
      cartStore.addToCart(mockProduct, 2)
      
      const item = cartStore.getCartItem(1)
      expect(item).toBeTruthy()
      expect(item.productId).toBe(1)
      
      const nonExistentItem = cartStore.getCartItem(999)
      expect(nonExistentItem).toBe(null)
    })
  })

  describe('错误处理', () => {
    it('应该处理添加商品时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟错误
      const originalPush = cartStore.cartItems.push
      cartStore.cartItems.push = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      
      const result = cartStore.addToCart(mockProduct, 1)
      
      expect(result).toBe(false)
      
      // 恢复原始方法
      cartStore.cartItems.push = originalPush
      consoleSpy.mockRestore()
    })

    it('应该处理计算总额时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 添加正常商品
      const mockProduct = {
        productId: 1,
        productName: '测试商品',
        price: 10.00,
        stock: 100
      }
      cartStore.addToCart(mockProduct, 1)
      
      // 破坏数据导致计算错误
      cartStore.cartItems[0].totalPrice = undefined
      
      // 调用计算方法
      cartStore.calculateTotal()
      
      // 应该不会抛出错误，而是优雅处理
      expect(cartStore.totalAmount).toBe(0) // 因为totalPrice是undefined
      
      consoleSpy.mockRestore()
    })
  })
})