/**
 * POS模块Composables统一导出
 * 
 * 提供可复用的业务逻辑组合式函数，遵循Vue 3 Composition API最佳实践
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

// 主页面业务逻辑
export { usePos } from './usePos'

// 错误处理逻辑
export { useErrorHandler } from './useErrorHandler'

// 性能监控逻辑
export { usePerformanceMonitor } from './usePerformanceMonitor'

// 懒加载逻辑
export { useLazyLoad } from './useLazyLoad'

// 购物车业务逻辑
export { useCart } from './useCart'

// 支付业务逻辑
export { usePayment } from './usePayment'

// 会员业务逻辑
export { useMember } from './useMember'

// 订单业务逻辑
export { useOrder } from './useOrder'

// 商品业务逻辑
export { useProduct } from './useProduct'

// 键盘快捷键逻辑
export { useKeyboard } from './useKeyboard'

// 数据恢复逻辑
export { useDataRecovery } from './useDataRecovery'

