/**
 * 购物车组合式函数
 * 
 * 封装购物车的添加、删除、更新、计算等业务逻辑
 * 集成工具函数和API调用，提供完整的购物车管理功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { usePosStore } from '@/stores/pos'
import { CartApi } from '../api/cart'
import { CartCalculator, CartValidator } from '../utils'
import { NUMERIC_CONSTANTS } from '../utils/constants'
import { useDataRecovery } from './useDataRecovery'

/**
 * 购物车组合式函数
 * @returns {Object} 购物车相关的状态和方法
 */
export function useCart() {
  const store = usePosStore()
  const { saveCartState: saveBackup, restoreCartState: restoreBackup } = useDataRecovery()
  
  // ==================== 响应式状态 ====================
  
  // 购物车商品列表
  const items = computed(() => store.cartItems || [])
  
  // 购物车总金额计算
  const cartTotal = computed(() => {
    try {
      const totalAmount = CartCalculator.calculateTotal(items.value)
      const discountAmount = store.discountAmount || 0
      const finalAmount = Math.max(0, totalAmount - discountAmount)
      
      return {
        totalAmount: Number(totalAmount.toFixed(2)),
        discountAmount: Number(discountAmount.toFixed(2)),
        finalAmount: Number(finalAmount.toFixed(2)),
        itemCount: CartCalculator.calculateItemCount(items.value),
        totalQuantity: CartCalculator.calculateTotalQuantity(items.value)
      }
    } catch (error) {
      console.error('计算购物车总金额失败:', error)
      return {
        totalAmount: 0,
        discountAmount: 0,
        finalAmount: 0,
        itemCount: 0,
        totalQuantity: 0
      }
    }
  })
  
  // 购物车是否为空
  const isEmpty = computed(() => items.value.length === 0)
  
  // 是否可以结账
  const canCheckout = computed(() => {
    return !isEmpty.value && cartTotal.value.finalAmount > 0
  })
  
  // 购物车摘要信息
  const cartSummary = computed(() => ({
    itemCount: cartTotal.value.itemCount,
    totalQuantity: cartTotal.value.totalQuantity,
    totalAmount: cartTotal.value.totalAmount,
    discountAmount: cartTotal.value.discountAmount,
    finalAmount: cartTotal.value.finalAmount,
    averagePrice: items.value.length > 0 ? CartCalculator.calculateAveragePrice(items.value) : 0
  }))
  
  // 加载状态
  const loading = ref(false)
  
  // ==================== 核心方法 ====================
  
  /**
   * 添加商品到购物车
   * @param {Object} product - 商品信息
   * @param {number} quantity - 数量，默认1
   * @returns {Promise<boolean>} 是否添加成功
   */
  const addItem = async (product, quantity = 1) => {
    try {
      loading.value = true
      
      // 确保商品ID为字符串类型，适配后端返回的数据结构
      const normalizedProduct = {
        ...product,
        id: String(product.productId || product.id || ''),
        name: product.productName || product.name || '未知商品',
        price: product.retailPrice || product.price || 0,
        stock: product.stockQuantity || product.stock || 0
      }
      
      // 验证商品信息
      const productValidation = CartValidator.validateProduct(normalizedProduct)
      if (!productValidation.isValid) {
        message.error(productValidation.message)
        return false
      }
      
      // 验证数量
      const quantityValidation = CartValidator.validateQuantity(quantity)
      if (!quantityValidation.isValid) {
        message.error(quantityValidation.message)
        return false
      }
      
      // 检查购物车商品数量限制
      if (items.value.length >= NUMERIC_CONSTANTS.MAX_CART_ITEMS) {
        message.error(`购物车商品数量不能超过${NUMERIC_CONSTANTS.MAX_CART_ITEMS}件`)
        return false
      }
      
      // 检查库存（使用商品本身的库存信息）
      if (normalizedProduct.stock < quantity) {
        message.error(`商品"${normalizedProduct.name}"库存不足，当前库存：${normalizedProduct.stock}`)
        return false
      }
      
      // 检查是否已存在相同商品
      const existingItemIndex = items.value.findIndex(item => item.id === normalizedProduct.id)
      
      if (existingItemIndex !== -1) {
        // 更新现有商品数量
        const existingItem = items.value[existingItemIndex]
        const newQuantity = existingItem.quantity + quantity
        
        // 再次检查库存（使用商品本身的库存信息）
        if (normalizedProduct.stock < newQuantity) {
          message.error(`商品"${normalizedProduct.name}"库存不足，当前库存：${normalizedProduct.stock}，已添加：${existingItem.quantity}`)
          return false
        }
        
        return await updateQuantity(normalizedProduct.id, newQuantity)
      } else {
        // 添加新商品
        const cartItem = {
          id: normalizedProduct.id,
          name: normalizedProduct.name,
          barcode: normalizedProduct.barcode || '',
          price: normalizedProduct.price,
          quantity: quantity,
          subtotal: CartCalculator.calculateSubtotal(normalizedProduct.price, quantity),
          unit: normalizedProduct.unit || '件',
          categoryId: normalizedProduct.categoryId || '',
          categoryName: normalizedProduct.categoryName || '',
          specifications: normalizedProduct.specification || normalizedProduct.specifications || '',
          image: normalizedProduct.imageUrl || normalizedProduct.image || '',
          pricingType: normalizedProduct.pricingType || 'NORMAL',
          addedAt: new Date().toISOString()
        }
        
        // 验证购物车项
        const itemValidation = CartValidator.validateCartItem(cartItem)
        if (!itemValidation.isValid) {
          message.error(itemValidation.message)
          return false
        }
        
        // 添加到store（使用原始商品信息和数量）
        const success = store.addToCart(normalizedProduct, quantity)
        if (!success) {
          return false
        }
        
        // 自动保存备份
        await saveCartBackup()
        
        return true
      }
    } catch (error) {
      console.error('添加商品到购物车失败:', error)
      message.error('添加商品失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 更新商品数量
   * @param {string} itemId - 商品ID
   * @param {number} quantity - 新数量
   * @returns {Promise<boolean>} 是否更新成功
   */
  const updateQuantity = async (itemId, quantity) => {
    try {
      loading.value = true
      
      // 确保itemId为字符串类型
      const normalizedItemId = String(itemId || '')
      
      // 验证数量
      const quantityValidation = CartValidator.validateQuantity(quantity)
      if (!quantityValidation.isValid) {
        message.error(quantityValidation.message)
        return false
      }
      
      const item = items.value.find(item => item.id === normalizedItemId)
      if (!item) {
        message.error('商品不存在')
        return false
      }
      
      // 如果数量为0，删除商品
      if (quantity === 0) {
        return await removeItem(normalizedItemId)
      }
      
      // 检查库存（暂时跳过库存检查，因为没有直接的商品库存信息）
      // 在实际应用中，这里应该从商品信息中获取库存数量进行检查
      // if (product.stockQuantity < quantity) {
      //   message.error(`商品"${item.name}"库存不足，当前库存：${product.stockQuantity}`)
      //   return false
      // }
      
      // 更新store中的商品数量
      store.updateQuantity(normalizedItemId, quantity)
      
      // 自动保存备份
      await saveCartBackup()
      
      return true
    } catch (error) {
      console.error('更新商品数量失败:', error)
      message.error('更新数量失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 移除商品
   * @param {string} itemId - 商品ID
   * @returns {Promise<boolean>} 是否移除成功
   */
  const removeItem = async (itemId) => {
    try {
      // 确保itemId为字符串类型
      const normalizedItemId = String(itemId || '')
      
      const item = items.value.find(item => item.id === normalizedItemId)
      if (!item) {
        message.error('商品不存在')
        return false
      }
      
      // 从store中移除
      store.removeFromCart(normalizedItemId)
      
      // 自动保存备份
      await saveCartBackup()
      
      message.success(`已移除"${item.name}"`)
      return true
    } catch (error) {
      console.error('移除商品失败:', error)
      message.error('移除商品失败，请重试')
      return false
    }
  }
  
  /**
   * 清空购物车
   * @returns {Promise<boolean>} 是否清空成功
   */
  const clearCart = async () => {
    try {
      if (isEmpty.value) {
        message.info('购物车已经是空的')
        return true
      }
      
      store.clearCart()
      
      // 清除备份
      await saveCartBackup()
      
      message.success('购物车已清空')
      return true
    } catch (error) {
      console.error('清空购物车失败:', error)
      message.error('清空购物车失败，请重试')
      return false
    }
  }
  
  /**
   * 批量添加商品
   * @param {Array} products - 商品列表 [{product, quantity}]
   * @returns {Promise<Object>} 添加结果 {success: number, failed: number, errors: Array}
   */
  const batchAddItems = async (products) => {
    const result = {
      success: 0,
      failed: 0,
      errors: []
    }
    
    try {
      loading.value = true
      
      // 逐个添加商品（直接使用商品本身的库存信息）
      for (const { product, quantity = 1 } of products) {
        try {
          const success = await addItem(product, quantity)
          if (success) {
            result.success++
          } else {
            const productName = product.name || product.productName || '未知商品'
            result.failed++
            result.errors.push(`添加商品"${productName}"失败`)
          }
        } catch (error) {
          const productName = product.name || product.productName || '未知商品'
          result.failed++
          result.errors.push(`添加商品"${productName}"失败: ${error.message}`)
        }
      }
      
      // 显示结果消息
      if (result.success > 0) {
        message.success(`成功添加${result.success}件商品`)
      }
      if (result.failed > 0) {
        message.warning(`${result.failed}件商品添加失败`)
      }
      
      return result
    } catch (error) {
      console.error('批量添加商品失败:', error)
      message.error('批量添加商品失败，请重试')
      return {
        success: 0,
        failed: products.length,
        errors: [error.message]
      }
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 验证购物车
   * @returns {Promise<Object>} 验证结果
   */
  const validateCart = async () => {
    try {
      // 客户端验证
      const clientValidation = CartValidator.validateCart(items.value)
      if (!clientValidation.isValid) {
        return clientValidation
      }
      
      // 暂时跳过服务端验证，只使用客户端验证
      return clientValidation
    } catch (error) {
      console.error('验证购物车失败:', error)
      return {
        isValid: false,
        message: '验证购物车失败，请重试'
      }
    }
  }
  
  /**
   * 应用优惠券
   * @param {string} couponCode - 优惠券代码
   * @param {Object} member - 会员信息
   * @returns {Promise<boolean>} 是否应用成功
   */
  const applyCoupon = async (couponCode, member = null) => {
    try {
      loading.value = true
      
      if (isEmpty.value) {
        message.error('购物车为空，无法使用优惠券')
        return false
      }
      
      // 暂时禁用优惠券功能，避免调用不存在的接口
      message.info('优惠券功能暂未开放')
      return false
    } catch (error) {
      console.error('应用优惠券失败:', error)
      message.error('应用优惠券失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 移除优惠券
   * @param {string} couponId - 优惠券ID
   * @returns {Promise<boolean>} 是否移除成功
   */
  const removeCoupon = async (couponId) => {
    try {
      loading.value = true
      
      // 暂时禁用优惠券功能，避免调用不存在的接口
      message.info('优惠券功能暂未开放')
      return false
    } catch (error) {
      console.error('移除优惠券失败:', error)
      message.error('移除优惠券失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 保存购物车备份
   */
  const saveCartBackup = async () => {
    // 完全禁用数据备份功能，避免控制台错误
    console.log('数据备份功能已禁用')
    return true
  }
  
  /**
   * 恢复购物车备份
   */
  const restoreCartBackup = async () => {
    // 完全禁用数据恢复功能，避免控制台错误
    console.log('数据恢复功能已禁用')
    return false
  }
  
  /**
   * 根据条码添加商品
   * @param {string} barcode - 商品条码
   * @param {number} quantity - 数量，默认1
   * @returns {Promise<boolean>} 是否添加成功
   */
  const addItemByBarcode = async (barcode, quantity = 1) => {
    try {
      loading.value = true
      
      if (!barcode || barcode.trim() === '') {
        message.error('请输入商品条码')
        return false
      }
      
      // 暂时禁用条码扫描功能，避免调用可能不存在的接口
      message.info('条码扫描功能暂未开放')
      return false
    } catch (error) {
      console.error('根据条码添加商品失败:', error)
      message.error('商品条码无效或商品不存在')
      return false
    } finally {
      loading.value = false
    }
  }
  
  // ==================== 监听器 ====================
  
  // 监听购物车变化，自动保存备份
  // 暂时注释掉数据备份功能，避免控制台错误
  /*
  watch(
    () => items.value,
    async () => {
      await nextTick()
      await saveCartBackup()
    },
    { deep: true }
  )
  */
  
  // ==================== 返回接口 ====================
  
  return {
    // 状态
    items,
    cartTotal,
    cartSummary,
    isEmpty,
    canCheckout,
    loading,
    
    // 方法
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    batchAddItems,
    validateCart,
    applyCoupon,
    removeCoupon,
    addItemByBarcode,
    saveCartBackup,
    restoreCartBackup
  }
}