import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import ToolbarPanel from '../ToolbarPanel.vue'

// Mock FunctionButton组件
vi.mock('../common/FunctionButton.vue', () => ({
  default: {
    name: 'FunctionButton',
    template: '<div class="function-button-mock" @click="$emit(\'click\')">{{ title }}</div>',
    props: ['title', 'icon', 'type', 'badge'],
    emits: ['click']
  }
}))

describe('ToolbarPanel', () => {
  let wrapper

  const createWrapper = (props = {}) => {
    return mount(ToolbarPanel, {
      props: {
        suspendedOrdersCount: 0,
        ...props
      },
      global: {
        stubs: {
          'icon-font': true
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染工具栏面板', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.toolbar-panel').exists()).toBe(true)
      expect(wrapper.find('.function-buttons').exists()).toBe(true)
    })

    it('应该渲染挂单列表和会员管理按钮', () => {
      wrapper = createWrapper()
      
      const functionButtons = wrapper.findAllComponents({ name: 'FunctionButton' })
      expect(functionButtons).toHaveLength(2)
      
      // 检查按钮属性
      expect(functionButtons[0].props('title')).toBe('挂单列表')
      expect(functionButtons[0].props('type')).toBe('primary')
      expect(functionButtons[1].props('title')).toBe('会员管理')
      expect(functionButtons[1].props('type')).toBe('default')
    })

    it('应该正确传递挂单数量徽章', () => {
      wrapper = createWrapper({ suspendedOrdersCount: 5 })
      
      const suspendButton = wrapper.findAllComponents({ name: 'FunctionButton' })[0]
      expect(suspendButton.props('badge')).toBe(5)
    })
  })

  describe('事件处理', () => {
    it('应该在点击挂单列表按钮时触发showSuspendedOrders事件', async () => {
      wrapper = createWrapper()
      
      const suspendButton = wrapper.findAllComponents({ name: 'FunctionButton' })[0]
      await suspendButton.vm.$emit('click')
      
      expect(wrapper.emitted('showSuspendedOrders')).toBeTruthy()
    })

    it('应该在点击会员管理按钮时触发memberManagement事件', async () => {
      wrapper = createWrapper()
      
      const memberButton = wrapper.findAllComponents({ name: 'FunctionButton' })[1]
      await memberButton.vm.$emit('click')
      
      expect(wrapper.emitted('memberManagement')).toBeTruthy()
    })
  })

  describe('响应式设计', () => {
    it('应该在不同屏幕尺寸下正确显示', () => {
      wrapper = createWrapper()
      
      const toolbarPanel = wrapper.find('.toolbar-panel')
      expect(toolbarPanel.classes()).toContain('toolbar-panel')
      
      // 检查CSS类是否正确应用
      const functionButtons = wrapper.find('.function-buttons')
      expect(functionButtons.exists()).toBe(true)
    })
  })

  describe('属性验证', () => {
    it('应该正确处理suspendedOrdersCount属性', () => {
      wrapper = createWrapper({ suspendedOrdersCount: 10 })
      
      expect(wrapper.props('suspendedOrdersCount')).toBe(10)
    })

    it('应该为suspendedOrdersCount提供默认值', () => {
      wrapper = createWrapper()
      
      expect(wrapper.props('suspendedOrdersCount')).toBe(0)
    })
  })
})