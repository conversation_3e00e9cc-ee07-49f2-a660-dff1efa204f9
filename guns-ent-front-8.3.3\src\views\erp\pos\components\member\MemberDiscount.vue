<template>
  <div class="member-discount-panel">
    <!-- 会员折扣信息 -->
    <div class="member-discount" v-if="discountRate > 0">
      <div class="discount-info">
        <icon-font iconClass="icon-discount" />
        <span class="discount-text">
          享受 {{ (discountRate * 100).toFixed(1) }}% 会员折扣
        </span>
      </div>
    </div>

    <!-- 积分抵扣设置 -->
    <div class="points-deduction" v-if="canUsePoints">
      <div class="deduction-header">
        <span class="deduction-label">积分抵扣</span>
        <a-switch 
          v-model:checked="usePointsDeduction" 
          @change="handlePointsDeductionChange"
          size="small"
        />
      </div>
      <div class="deduction-details" v-if="usePointsDeduction">
        <div class="deduction-input">
          <a-input-number
            v-model:value="pointsToDeduct"
            :min="0"
            :max="maxDeductiblePoints"
            :step="100"
            size="small"
            @change="calculatePointsDeduction"
            class="points-input"
          />
          <span class="points-unit">积分</span>
        </div>
        <div class="deduction-amount">
          可抵扣: ￥{{ formatAmount(pointsDeductionAmount) }}
        </div>
        <div class="deduction-rate">
          ({{ pointsExchangeRate }}积分 = ￥1)
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { AmountFormatter } from '../../utils/formatter'

// 定义组件名称
defineOptions({
  name: 'MemberDiscount'
})

// 定义属性
const props = defineProps({
  member: {
    type: Object,
    required: true
  },
  discountRate: {
    type: Number,
    default: 0
  },
  finalAmount: {
    type: Number,
    default: 0
  },
  pointsExchangeRate: {
    type: Number,
    default: 100
  }
})

// 定义事件
const emit = defineEmits(['pointsDeductionChange'])

// 响应式数据
const usePointsDeduction = ref(false)
const pointsToDeduct = ref(0)
const pointsDeductionAmount = ref(0)

// 计算属性
const canUsePoints = computed(() => {
  return props.member && 
         props.member.points > 0 && 
         props.finalAmount > 0
})

const maxDeductiblePoints = computed(() => {
  if (!props.member) return 0
  
  const maxByPoints = props.member.points || 0
  const maxByAmount = Math.floor(props.finalAmount * props.pointsExchangeRate)
  
  return Math.min(maxByPoints, maxByAmount)
})

/**
 * 格式化金额显示
 */
const formatAmount = (amount) => {
  return AmountFormatter.formatCurrency(amount, { showSymbol: false })
}

/**
 * 处理积分抵扣开关变化
 */
const handlePointsDeductionChange = (checked) => {
  if (checked) {
    // 默认使用最大可抵扣积分
    pointsToDeduct.value = Math.min(maxDeductiblePoints.value, 1000)
    calculatePointsDeduction()
  } else {
    pointsToDeduct.value = 0
    pointsDeductionAmount.value = 0
    emit('pointsDeductionChange', {
      points: 0,
      amount: 0
    })
  }
}

/**
 * 计算积分抵扣
 */
const calculatePointsDeduction = () => {
  if (!usePointsDeduction.value || pointsToDeduct.value <= 0) {
    pointsDeductionAmount.value = 0
    emit('pointsDeductionChange', {
      points: 0,
      amount: 0
    })
    return
  }
  
  const deductionAmount = pointsToDeduct.value / props.pointsExchangeRate
  pointsDeductionAmount.value = deductionAmount
  
  emit('pointsDeductionChange', {
    points: pointsToDeduct.value,
    amount: deductionAmount
  })
}

/**
 * 重置积分抵扣
 */
const resetPointsDeduction = () => {
  usePointsDeduction.value = false
  pointsToDeduct.value = 0
  pointsDeductionAmount.value = 0
}

// 监听会员变化，重置积分抵扣
watch(
  () => props.member,
  () => {
    resetPointsDeduction()
  }
)

// 暴露方法给父组件
defineExpose({
  resetPointsDeduction
})
</script>

<style scoped>
.member-discount-panel {
  padding: 16px 0;
}

.member-discount {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.discount-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #fff;
}

.points-deduction {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 12px;
  color: #fff;
}

.deduction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.deduction-label {
  font-size: 14px;
  font-weight: 500;
}

.deduction-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.deduction-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.points-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
}

.points-unit {
  font-size: 12px;
  opacity: 0.8;
}

.deduction-amount {
  font-size: 13px;
  font-weight: 500;
  color: #52c41a;
}

.deduction-rate {
  font-size: 11px;
  opacity: 0.7;
}
</style>