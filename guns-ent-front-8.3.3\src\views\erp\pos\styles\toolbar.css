/* 工具栏面板样式 */
.toolbar-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  overflow: hidden;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.function-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
}

.function-button-item {
  position: relative;
  max-width: 100%;
}

/* 功能卡片样式 - 简约设计 */
.function-card {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-height: 60px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #495057;
}

.function-card:hover:not(.disabled) {
  background: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 挂单列表卡片 */
.suspend-list-card {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.suspend-list-card:hover:not(.disabled) {
  background: #e9ecef;
  border-color: #dee2e6;
}

/* 会员管理卡片 */
.member-card {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.member-card:hover:not(.disabled) {
  background: #e9ecef;
  border-color: #dee2e6;
}

/* 卡片内容 */
.card-content {
  flex: 1;
  min-width: 0;
  text-align: center;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #495057;
}

/* 卡片徽章 */
.card-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #dc3545;
  color: #fff;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  min-width: 16px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(220, 53, 69, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-panel {
    padding: 10px;
  }

  .function-card {
    padding: 10px 14px;
    min-height: 50px;
  }

  .card-title {
    font-size: 13px;
  }

  .card-badge {
    top: 6px;
    right: 6px;
    padding: 1px 5px;
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .toolbar-panel {
    padding: 8px;
  }

  .function-buttons {
    gap: 10px;
  }

  .function-card {
    padding: 8px 12px;
    min-height: 45px;
  }

  .card-title {
    font-size: 12px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .function-card:hover {
    transform: none;
    box-shadow: none;
  }

  .function-card:active {
    transform: scale(0.98);
    background: #e9ecef;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .function-card {
    transition: none;
  }

  .function-card:hover {
    transform: none;
  }
}
