import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useOrderStore } from '../order'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
})

describe('useOrderStore', () => {
  let pinia
  let orderStore
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    orderStore = useOrderStore()
    vi.clearAllMocks()
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    vi.useRealTimers()
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(orderStore.suspendedOrders).toEqual([])
      expect(orderStore.orderHistory).toEqual([])
      expect(orderStore.suspendCounter).toBe(1)
      expect(orderStore.currentOrder).toBe(null)
    })

    it('应该有正确的计算属性初始值', () => {
      expect(orderStore.hasSuspendedOrders).toBe(false)
      expect(orderStore.suspendedOrderCount).toBe(0)
      expect(orderStore.orderHistoryCount).toBe(0)
      expect(orderStore.todayOrderCount).toBe(0)
      expect(orderStore.todaySalesAmount).toBe(0)
      expect(orderStore.expiredSuspendedOrders).toEqual([])
    })

    it('应该有默认的订单配置', () => {
      expect(orderStore.orderConfig.maxSuspendedOrders).toBe(50)
      expect(orderStore.orderConfig.maxOrderHistory).toBe(100)
      expect(orderStore.orderConfig.suspendExpireHours).toBe(24)
      expect(orderStore.orderConfig.autoCleanup).toBe(true)
    })
  })

  describe('创建订单', () => {
    const mockOrderData = {
      cartItems: [
        { productId: 1, productName: '商品1', quantity: 2, totalPrice: 20 }
      ],
      totalAmount: 20,
      discountAmount: 0,
      finalAmount: 20
    }

    it('应该能够创建新订单', () => {
      const order = orderStore.createOrder(mockOrderData)
      
      expect(order).toBeTruthy()
      expect(order.orderId).toBeTruthy()
      expect(order.orderNo).toMatch(/^ORDER-/)
      expect(order.status).toBe('pending')
      expect(order.cartItems).toEqual(mockOrderData.cartItems)
      expect(orderStore.currentOrder).toBe(order)
    })

    it('应该验证订单数据', () => {
      const invalidOrderData = {
        cartItems: [],
        totalAmount: 0
      }
      
      const order = orderStore.createOrder(invalidOrderData)
      
      expect(order).toBe(null)
      expect(orderStore.currentOrder).toBe(null)
    })

    it('应该处理缺少商品的订单', () => {
      const orderDataWithoutItems = {
        totalAmount: 20
      }
      
      const order = orderStore.createOrder(orderDataWithoutItems)
      
      expect(order).toBe(null)
    })

    it('应该处理无效金额的订单', () => {
      const orderDataWithInvalidAmount = {
        cartItems: [{ productId: 1, productName: '商品1' }],
        totalAmount: -10
      }
      
      const order = orderStore.createOrder(orderDataWithInvalidAmount)
      
      expect(order).toBe(null)
    })
  })

  describe('完成订单', () => {
    beforeEach(() => {
      const mockOrderData = {
        cartItems: [{ productId: 1, productName: '商品1', quantity: 1, totalPrice: 10 }],
        totalAmount: 10,
        finalAmount: 10
      }
      orderStore.createOrder(mockOrderData)
    })

    it('应该能够完成订单', () => {
      const paymentResult = {
        paymentMethod: 'CASH',
        amount: 10,
        transactionId: 'TXN123'
      }
      
      const result = orderStore.completeOrder(paymentResult)
      
      expect(result).toBe(true)
      expect(orderStore.currentOrder).toBe(null)
      expect(orderStore.orderHistory).toHaveLength(1)
      expect(orderStore.orderHistory[0].status).toBe('completed')
      expect(orderStore.orderHistory[0].paymentResult).toEqual(paymentResult)
    })

    it('应该在没有当前订单时拒绝完成', () => {
      orderStore.currentOrder = null
      
      const result = orderStore.completeOrder({})
      
      expect(result).toBe(false)
    })

    it('应该限制历史记录数量', () => {
      // 创建大量订单历史
      for (let i = 0; i < 105; i++) {
        orderStore.orderHistory.push({
          orderId: i,
          orderNo: `ORDER${i}`,
          status: 'completed',
          finalAmount: 10
        })
      }
      
      orderStore.completeOrder({ amount: 10 })
      
      expect(orderStore.orderHistory.length).toBeLessThanOrEqual(100)
    })
  })

  describe('取消订单', () => {
    beforeEach(() => {
      const mockOrderData = {
        cartItems: [{ productId: 1, productName: '商品1', quantity: 1, totalPrice: 10 }],
        totalAmount: 10,
        finalAmount: 10
      }
      orderStore.createOrder(mockOrderData)
    })

    it('应该能够取消订单', () => {
      const result = orderStore.cancelOrder('用户取消')
      
      expect(result).toBe(true)
      expect(orderStore.currentOrder).toBe(null)
      expect(orderStore.orderHistory).toHaveLength(1)
      expect(orderStore.orderHistory[0].status).toBe('cancelled')
      expect(orderStore.orderHistory[0].cancelReason).toBe('用户取消')
    })

    it('应该在没有当前订单时拒绝取消', () => {
      orderStore.currentOrder = null
      
      const result = orderStore.cancelOrder('测试取消')
      
      expect(result).toBe(false)
    })
  })

  describe('挂单管理', () => {
    const mockOrderData = {
      cartItems: [
        { productId: 1, productName: '商品1', quantity: 2, totalPrice: 20 }
      ],
      totalAmount: 20,
      discountAmount: 0,
      finalAmount: 20
    }

    it('应该能够挂起订单', () => {
      const result = orderStore.suspendCurrentOrder(mockOrderData, '临时挂单')
      
      expect(result).toBe(true)
      expect(orderStore.suspendedOrders).toHaveLength(1)
      expect(orderStore.suspendedOrders[0].remark).toBe('临时挂单')
      expect(orderStore.suspendedOrders[0].suspendNo).toMatch(/^SUSPEND-/)
      expect(orderStore.suspendedOrders[0].orderData).toEqual(expect.objectContaining(mockOrderData))
      expect(orderStore.suspendCounter).toBe(2)
    })

    it('应该验证挂单数据', () => {
      const invalidOrderData = {
        cartItems: [],
        totalAmount: 0
      }
      
      const result = orderStore.suspendCurrentOrder(invalidOrderData)
      
      expect(result).toBe(false)
      expect(orderStore.suspendedOrders).toHaveLength(0)
    })

    it('应该限制挂单数量', () => {
      // 设置较小的限制用于测试
      orderStore.setOrderConfig({ maxSuspendedOrders: 2 })
      
      // 添加2个挂单
      orderStore.suspendCurrentOrder(mockOrderData, '挂单1')
      orderStore.suspendCurrentOrder(mockOrderData, '挂单2')
      
      // 第3个应该被拒绝
      const result = orderStore.suspendCurrentOrder(mockOrderData, '挂单3')
      
      expect(result).toBe(false)
      expect(orderStore.suspendedOrders).toHaveLength(2)
    })

    it('应该能够恢复挂单', () => {
      orderStore.suspendCurrentOrder(mockOrderData, '测试挂单')
      const suspendId = orderStore.suspendedOrders[0].suspendId
      
      const restoredData = orderStore.resumeSuspendedOrder(suspendId)
      
      expect(restoredData).toBeTruthy()
      expect(restoredData.cartItems).toEqual(mockOrderData.cartItems)
      expect(orderStore.suspendedOrders).toHaveLength(0)
    })

    it('应该处理不存在的挂单', () => {
      const restoredData = orderStore.resumeSuspendedOrder(999)
      
      expect(restoredData).toBe(null)
    })

    it('应该处理过期的挂单', () => {
      orderStore.suspendCurrentOrder(mockOrderData, '过期挂单')
      
      // 手动设置挂单为过期状态
      const suspendedOrder = orderStore.suspendedOrders[0]
      suspendedOrder.suspendTime = new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString() // 25小时前
      
      const restoredData = orderStore.resumeSuspendedOrder(suspendedOrder.suspendId)
      
      expect(restoredData).toBe(null)
      expect(orderStore.suspendedOrders).toHaveLength(0) // 应该被自动删除
    })

    it('应该能够删除挂单', () => {
      orderStore.suspendCurrentOrder(mockOrderData, '待删除挂单')
      const suspendId = orderStore.suspendedOrders[0].suspendId
      
      const result = orderStore.deleteSuspendedOrder(suspendId)
      
      expect(result).toBe(true)
      expect(orderStore.suspendedOrders).toHaveLength(0)
    })

    it('应该能够批量删除挂单', () => {
      orderStore.suspendCurrentOrder(mockOrderData, '挂单1')
      orderStore.suspendCurrentOrder(mockOrderData, '挂单2')
      orderStore.suspendCurrentOrder(mockOrderData, '挂单3')
      
      const suspendIds = orderStore.suspendedOrders.map(order => order.suspendId)
      const deletedCount = orderStore.batchDeleteSuspendedOrders(suspendIds.slice(0, 2))
      
      expect(deletedCount).toBe(2)
      expect(orderStore.suspendedOrders).toHaveLength(1)
    })

    it('应该能够清理过期挂单', () => {
      // 添加正常挂单
      orderStore.suspendCurrentOrder(mockOrderData, '正常挂单')
      
      // 添加过期挂单
      orderStore.suspendCurrentOrder(mockOrderData, '过期挂单')
      const expiredOrder = orderStore.suspendedOrders[1]
      expiredOrder.suspendTime = new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString()
      
      const clearedCount = orderStore.clearExpiredSuspendedOrders()
      
      expect(clearedCount).toBe(1)
      expect(orderStore.suspendedOrders).toHaveLength(1)
      expect(orderStore.suspendedOrders[0].remark).toBe('正常挂单')
    })
  })

  describe('订单查询', () => {
    beforeEach(() => {
      // 添加一些测试订单
      const orders = [
        {
          orderId: 1,
          orderNo: 'ORDER001',
          status: 'completed',
          finalAmount: 100,
          createTime: new Date().toISOString()
        },
        {
          orderId: 2,
          orderNo: 'ORDER002',
          status: 'cancelled',
          finalAmount: 50,
          createTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 昨天
        }
      ]
      
      orderStore.orderHistory.push(...orders)
    })

    it('应该能够根据订单号查询订单', () => {
      const order = orderStore.getOrderByNo('ORDER001')
      
      expect(order).toBeTruthy()
      expect(order.orderId).toBe(1)
      expect(order.status).toBe('completed')
    })

    it('应该处理不存在的订单号', () => {
      const order = orderStore.getOrderByNo('NONEXISTENT')
      
      expect(order).toBe(null)
    })

    it('应该能够搜索订单', () => {
      const results = orderStore.searchOrders({
        status: 'completed'
      })
      
      expect(results).toHaveLength(1)
      expect(results[0].orderNo).toBe('ORDER001')
    })

    it('应该能够按订单号搜索', () => {
      const results = orderStore.searchOrders({
        orderNo: '001'
      })
      
      expect(results).toHaveLength(1)
      expect(results[0].orderNo).toBe('ORDER001')
    })

    it('应该能够按金额范围搜索', () => {
      const results = orderStore.searchOrders({
        minAmount: 80,
        maxAmount: 120
      })
      
      expect(results).toHaveLength(1)
      expect(results[0].finalAmount).toBe(100)
    })

    it('应该能够按日期范围搜索', () => {
      const today = new Date()
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
      
      const results = orderStore.searchOrders({
        startDate: yesterday.toISOString(),
        endDate: today.toISOString()
      })
      
      expect(results).toHaveLength(2)
    })
  })

  describe('统计信息', () => {
    beforeEach(() => {
      const today = new Date().toDateString()
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()
      
      const orders = [
        {
          orderId: 1,
          status: 'completed',
          finalAmount: 100,
          createTime: new Date(today + ' 10:00:00').toISOString()
        },
        {
          orderId: 2,
          status: 'completed',
          finalAmount: 50,
          createTime: new Date(today + ' 11:00:00').toISOString()
        },
        {
          orderId: 3,
          status: 'cancelled',
          finalAmount: 30,
          createTime: new Date(today + ' 12:00:00').toISOString()
        },
        {
          orderId: 4,
          status: 'completed',
          finalAmount: 80,
          createTime: new Date(yesterday + ' 10:00:00').toISOString()
        }
      ]
      
      orderStore.orderHistory.push(...orders)
      
      // 添加挂单
      orderStore.suspendedOrders.push({
        suspendId: 1,
        suspendTime: new Date().toISOString()
      })
    })

    it('应该正确计算今日订单数量', () => {
      expect(orderStore.todayOrderCount).toBe(3)
    })

    it('应该正确计算今日销售额', () => {
      expect(orderStore.todaySalesAmount).toBe(150) // 100 + 50，不包括取消的订单
    })

    it('应该能够获取订单统计信息', () => {
      const stats = orderStore.getOrderStatistics({ dateRange: 'today' })
      
      expect(stats.totalOrders).toBe(3)
      expect(stats.completedOrders).toBe(2)
      expect(stats.cancelledOrders).toBe(1)
      expect(stats.totalSales).toBe(150)
      expect(stats.averageOrderValue).toBe(75) // 150 / 2
      expect(stats.suspendedOrders).toBe(1)
    })

    it('应该能够获取周统计', () => {
      const stats = orderStore.getOrderStatistics({ dateRange: 'week' })
      
      expect(stats.totalOrders).toBe(4)
      expect(stats.completedOrders).toBe(3)
      expect(stats.totalSales).toBe(230) // 100 + 50 + 80
    })
  })

  describe('本地存储', () => {
    const mockOrderData = {
      cartItems: [{ productId: 1, productName: '商品1', quantity: 1, totalPrice: 10 }],
      totalAmount: 10,
      finalAmount: 10
    }

    it('应该保存挂单到本地存储', () => {
      orderStore.suspendCurrentOrder(mockOrderData, '测试挂单')
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'pos_suspended_orders',
        expect.any(String)
      )
    })

    it('应该保存订单历史到本地存储', () => {
      const order = orderStore.createOrder(mockOrderData)
      orderStore.completeOrder({ amount: 10 })
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'pos_order_history',
        expect.any(String)
      )
    })

    it('应该从本地存储加载挂单', () => {
      const mockData = {
        suspendedOrders: [{
          suspendId: 1,
          suspendNo: 'SUSPEND-123',
          remark: '测试挂单'
        }],
        suspendCounter: 2
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockData))
      
      orderStore.loadSuspendedOrdersFromLocal()
      
      expect(orderStore.suspendedOrders).toHaveLength(1)
      expect(orderStore.suspendCounter).toBe(2)
    })

    it('应该从本地存储加载订单历史', () => {
      const mockData = {
        orderHistory: [{
          orderId: 1,
          orderNo: 'ORDER001',
          status: 'completed'
        }]
      }
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockData))
      
      orderStore.loadOrderHistoryFromLocal()
      
      expect(orderStore.orderHistory).toHaveLength(1)
    })

    it('应该处理损坏的本地存储数据', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      mockLocalStorage.getItem.mockReturnValue('invalid json')
      
      orderStore.loadSuspendedOrdersFromLocal()
      
      expect(orderStore.suspendedOrders).toEqual([])
      expect(orderStore.suspendCounter).toBe(1)
      
      consoleSpy.mockRestore()
    })
  })

  describe('配置管理', () => {
    it('应该能够设置订单配置', () => {
      const newConfig = {
        maxSuspendedOrders: 20,
        suspendExpireHours: 12
      }
      
      orderStore.setOrderConfig(newConfig)
      
      expect(orderStore.orderConfig.maxSuspendedOrders).toBe(20)
      expect(orderStore.orderConfig.suspendExpireHours).toBe(12)
      expect(orderStore.orderConfig.maxOrderHistory).toBe(100) // 保持原值
    })
  })

  describe('状态重置', () => {
    beforeEach(() => {
      const mockOrderData = {
        cartItems: [{ productId: 1, productName: '商品1', quantity: 1, totalPrice: 10 }],
        totalAmount: 10,
        finalAmount: 10
      }
      
      orderStore.createOrder(mockOrderData)
      orderStore.suspendCurrentOrder(mockOrderData, '测试挂单')
    })

    it('应该能够重置订单状态', () => {
      orderStore.resetOrderState()
      
      expect(orderStore.currentOrder).toBe(null)
      expect(orderStore.suspendedOrders).toEqual([])
      expect(orderStore.suspendCounter).toBe(1)
      // 历史记录不应该被清除
      expect(orderStore.orderHistory.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('初始化', () => {
    it('应该能够初始化订单存储', () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
      
      orderStore.initializeOrderStore()
      
      expect(consoleSpy).toHaveBeenCalledWith('订单存储初始化完成')
      
      consoleSpy.mockRestore()
    })
  })

  describe('错误处理', () => {
    it('应该处理挂单时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟错误
      const originalPush = orderStore.suspendedOrders.push
      orderStore.suspendedOrders.push = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const mockOrderData = {
        cartItems: [{ productId: 1, productName: '商品1', quantity: 1, totalPrice: 10 }],
        totalAmount: 10,
        finalAmount: 10
      }
      
      const result = orderStore.suspendCurrentOrder(mockOrderData, '测试挂单')
      
      expect(result).toBe(false)
      
      // 恢复原始方法
      orderStore.suspendedOrders.push = originalPush
      consoleSpy.mockRestore()
    })

    it('应该处理完成订单时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      const mockOrderData = {
        cartItems: [{ productId: 1, productName: '商品1', quantity: 1, totalPrice: 10 }],
        totalAmount: 10,
        finalAmount: 10
      }
      
      orderStore.createOrder(mockOrderData)
      
      // 模拟错误
      const originalUnshift = orderStore.orderHistory.unshift
      orderStore.orderHistory.unshift = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const result = orderStore.completeOrder({ amount: 10 })
      
      expect(result).toBe(false)
      
      // 恢复原始方法
      orderStore.orderHistory.unshift = originalUnshift
      consoleSpy.mockRestore()
    })

    it('应该处理搜索订单时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟错误
      const originalFilter = orderStore.orderHistory.filter
      orderStore.orderHistory.filter = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const results = orderStore.searchOrders({ status: 'completed' })
      
      expect(results).toEqual([])
      
      // 恢复原始方法
      orderStore.orderHistory.filter = originalFilter
      consoleSpy.mockRestore()
    })
  })
})