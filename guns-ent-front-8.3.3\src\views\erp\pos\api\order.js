/**
 * 订单相关API接口
 * 
 * 提供订单创建、查询、状态管理等功能的API封装
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import Request from '@/utils/request/request-util'
import { PosErrorHandler } from '../utils/error-handler'

/**
 * 订单API类
 */
export class OrderApi {
  
  /**
   * 创建POS订单
   * @param {Object} orderData - 订单数据
   * @param {number} orderData.memberId - 会员ID（可选）
   * @param {number} orderData.totalAmount - 订单总金额
   * @param {number} orderData.discountAmount - 折扣金额
   * @param {number} orderData.finalAmount - 实付金额
   * @param {Array} orderData.orderItems - 订单项列表
   * @param {string} orderData.remark - 备注
   * @returns {Promise<Object>} 创建的订单信息
   */
  static async createOrder(orderData) {
    const apiCall = () => Request.post('/erp/pos/order/create', orderData)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '创建订单',
      showNotification: true, // 订单创建失败显示通知
      retryOptions: { maxRetries: 1 } // 订单创建谨慎重试
    })()
  }
  
  /**
   * 根据订单ID查询订单详情
   * @param {Object} params - 查询参数
   * @param {number} params.orderId - 订单ID
   * @returns {Promise<Object>} 订单详情
   */
  static async getOrderDetail(params) {
    const apiCall = () => Request.get('/erp/pos/order/detail', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '获取订单详情',
      showMessage: false
    })()
  }
  
  /**
   * 根据订单号查询订单详情
   * @param {Object} params - 查询参数
   * @param {string} params.orderNo - 订单号
   * @returns {Promise<Object>} 订单详情
   */
  static async getOrderDetailByNo(params) {
    const apiCall = () => Request.get('/erp/pos/order/detailByNo', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '根据订单号获取详情',
      showMessage: false
    })()
  }
  
  /**
   * 更新订单状态
   * @param {Object} params - 更新参数
   * @param {number} params.orderId - 订单ID
   * @param {string} params.orderStatus - 订单状态
   * @returns {Promise<Object>} 更新结果
   */
  static async updateOrderStatus(params) {
    const apiCall = () => Request.post('/erp/pos/order/updateStatus', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '更新订单状态',
      showMessage: true
    })()
  }
  
  /**
   * 取消订单
   * @param {Object} params - 取消参数
   * @param {number} params.orderId - 订单ID
   * @param {string} params.remark - 取消原因
   * @returns {Promise<Object>} 取消结果
   */
  static async cancelOrder(params) {
    const apiCall = () => Request.post('/erp/pos/order/cancel', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '取消订单',
      showMessage: true,
      showNotification: true
    })()
  }
  
  /**
   * 查询收银员今日订单
   * @param {Object} params - 查询参数
   * @param {number} params.cashierId - 收银员ID
   * @returns {Promise<Array>} 今日订单列表
   */
  static async getTodayOrdersByCashier(params) {
    const apiCall = () => Request.get('/erp/pos/order/todayOrders', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '获取今日订单',
      showMessage: false
    })()
  }
  
  /**
   * 分页查询订单列表
   * @param {Object} params - 查询参数
   * @param {number} params.pageNo - 页码
   * @param {number} params.pageSize - 每页大小
   * @param {string} params.orderStatus - 订单状态
   * @param {string} params.paymentStatus - 支付状态
   * @param {number} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 订单分页数据
   */
  static async getOrderPage(params = {}) {
    const apiCall = () => Request.get('/erp/pos/order/page', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '查询订单列表',
      showMessage: false
    })()
  }
}