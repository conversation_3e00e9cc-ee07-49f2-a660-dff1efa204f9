/**
 * 计算工具函数单元测试
 * 
 * 测试购物车金额计算、折扣计算、税费计算等核心计算逻辑
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect } from 'vitest'
import {
  CartCalculator,
  PriceCalculator,
  DiscountCalculator,
  PointsCalculator,
  ChangeCalculator,
  StatisticsCalculator
} from '../calculator'

describe('CartCalculator', () => {
  describe('calculateSubtotal', () => {
    it('应该正确计算商品小计', () => {
      expect(CartCalculator.calculateSubtotal(10.5, 2)).toBe(21)
      expect(CartCalculator.calculateSubtotal(9.99, 3)).toBe(29.97)
      expect(CartCalculator.calculateSubtotal(0, 5)).toBe(0)
    })
    
    it('应该在价格或数量为负数时抛出错误', () => {
      expect(() => CartCalculator.calculateSubtotal(-10, 2)).toThrow('价格和数量不能为负数')
      expect(() => CartCalculator.calculateSubtotal(10, -2)).toThrow('价格和数量不能为负数')
    })
  })
  
  describe('calculateTotal', () => {
    it('应该正确计算购物车总金额', () => {
      const cartItems = [
        { price: 10, quantity: 2 },
        { price: 5.5, quantity: 3 },
        { price: 8.8, quantity: 1 }
      ]
      expect(CartCalculator.calculateTotal(cartItems)).toBe(45.3)
    })
    
    it('应该处理空购物车', () => {
      expect(CartCalculator.calculateTotal([])).toBe(0)
    })
    
    it('应该在参数不是数组时抛出错误', () => {
      expect(() => CartCalculator.calculateTotal(null)).toThrow('购物车商品列表必须是数组')
      expect(() => CartCalculator.calculateTotal('invalid')).toThrow('购物车商品列表必须是数组')
    })
  })
  
  describe('calculateTotalQuantity', () => {
    it('应该正确计算总数量', () => {
      const cartItems = [
        { quantity: 2 },
        { quantity: 3.5 },
        { quantity: 1 }
      ]
      expect(CartCalculator.calculateTotalQuantity(cartItems)).toBe(6.5)
    })
  })
  
  describe('calculateItemCount', () => {
    it('应该正确计算商品种类数', () => {
      const cartItems = [
        { id: 1, quantity: 2 },
        { id: 2, quantity: 3 },
        { id: 3, quantity: 1 }
      ]
      expect(CartCalculator.calculateItemCount(cartItems)).toBe(3)
    })
  })
  
  describe('calculateAveragePrice', () => {
    it('应该正确计算平均单价', () => {
      const cartItems = [
        { price: 10, quantity: 2 },
        { price: 20, quantity: 1 }
      ]
      expect(CartCalculator.calculateAveragePrice(cartItems)).toBe(13.33)
    })
    
    it('应该处理空购物车', () => {
      expect(CartCalculator.calculateAveragePrice([])).toBe(0)
    })
  })
})

describe('PriceCalculator', () => {
  describe('calculatePriceWithTax', () => {
    it('应该正确计算含税价格', () => {
      expect(PriceCalculator.calculatePriceWithTax(100, 0.13)).toBe(113)
      expect(PriceCalculator.calculatePriceWithTax(50.5, 0.06)).toBe(53.53)
    })
    
    it('应该在价格为负数时抛出错误', () => {
      expect(() => PriceCalculator.calculatePriceWithTax(-100, 0.13)).toThrow('价格不能为负数')
    })
    
    it('应该在税率超出范围时抛出错误', () => {
      expect(() => PriceCalculator.calculatePriceWithTax(100, -0.1)).toThrow('税率必须在0-1之间')
      expect(() => PriceCalculator.calculatePriceWithTax(100, 1.5)).toThrow('税率必须在0-1之间')
    })
  })
  
  describe('calculatePriceWithoutTax', () => {
    it('应该正确计算不含税价格', () => {
      expect(PriceCalculator.calculatePriceWithoutTax(113, 0.13)).toBe(100)
      expect(PriceCalculator.calculatePriceWithoutTax(106, 0.06)).toBe(100)
    })
  })
  
  describe('calculateWeightPrice', () => {
    it('应该正确计算计重商品价格', () => {
      expect(PriceCalculator.calculateWeightPrice(12.5, 2.3)).toBe(28.75)
      expect(PriceCalculator.calculateWeightPrice(8, 0.5)).toBe(4)
    })
    
    it('应该在单价或重量为负数时抛出错误', () => {
      expect(() => PriceCalculator.calculateWeightPrice(-12.5, 2.3)).toThrow('单价和重量不能为负数')
      expect(() => PriceCalculator.calculateWeightPrice(12.5, -2.3)).toThrow('单价和重量不能为负数')
    })
  })
})

describe('DiscountCalculator', () => {
  describe('calculatePercentageDiscount', () => {
    it('应该正确计算百分比折扣', () => {
      expect(DiscountCalculator.calculatePercentageDiscount(100, 0.1)).toBe(10)
      expect(DiscountCalculator.calculatePercentageDiscount(50.5, 0.15)).toBe(7.58)
    })
    
    it('应该在原始金额为负数时抛出错误', () => {
      expect(() => DiscountCalculator.calculatePercentageDiscount(-100, 0.1)).toThrow('原始金额不能为负数')
    })
    
    it('应该在折扣率超出范围时抛出错误', () => {
      expect(() => DiscountCalculator.calculatePercentageDiscount(100, -0.1)).toThrow('折扣率必须在0-1之间')
      expect(() => DiscountCalculator.calculatePercentageDiscount(100, 1.5)).toThrow('折扣率必须在0-1之间')
    })
  })
  
  describe('calculateFixedDiscount', () => {
    it('应该正确计算固定金额折扣', () => {
      expect(DiscountCalculator.calculateFixedDiscount(100, 10)).toBe(10)
      expect(DiscountCalculator.calculateFixedDiscount(50, 80)).toBe(50) // 不能超过原始金额
    })
  })
  
  describe('calculateMemberDiscount', () => {
    it('应该正确计算会员折扣', () => {
      expect(DiscountCalculator.calculateMemberDiscount(100, 0.1, 50)).toBe(10)
      expect(DiscountCalculator.calculateMemberDiscount(30, 0.1, 50)).toBe(0) // 未达到最低消费
    })
  })
  
  describe('calculateFullReductionDiscount', () => {
    it('应该正确计算满减折扣', () => {
      const rules = [
        { minAmount: 100, discountAmount: 10 },
        { minAmount: 200, discountAmount: 25 },
        { minAmount: 300, discountAmount: 50 }
      ]
      
      expect(DiscountCalculator.calculateFullReductionDiscount(150, rules)).toBe(10)
      expect(DiscountCalculator.calculateFullReductionDiscount(250, rules)).toBe(25)
      expect(DiscountCalculator.calculateFullReductionDiscount(350, rules)).toBe(50)
      expect(DiscountCalculator.calculateFullReductionDiscount(50, rules)).toBe(0)
    })
  })
})

describe('PointsCalculator', () => {
  describe('calculateEarnedPoints', () => {
    it('应该正确计算获得的积分', () => {
      expect(PointsCalculator.calculateEarnedPoints(100, 1)).toBe(100)
      expect(PointsCalculator.calculateEarnedPoints(99.9, 1)).toBe(99) // 向下取整
      expect(PointsCalculator.calculateEarnedPoints(100, 2)).toBe(200)
    })
    
    it('应该在消费金额为负数时抛出错误', () => {
      expect(() => PointsCalculator.calculateEarnedPoints(-100, 1)).toThrow('消费金额不能为负数')
    })
  })
  
  describe('calculatePointsDeduction', () => {
    it('应该正确计算积分抵扣金额', () => {
      expect(PointsCalculator.calculatePointsDeduction(100, 100)).toBe(1)
      expect(PointsCalculator.calculatePointsDeduction(500, 100)).toBe(5)
    })
    
    it('应该在积分数为负数时抛出错误', () => {
      expect(() => PointsCalculator.calculatePointsDeduction(-100, 100)).toThrow('积分数不能为负数')
    })
  })
  
  describe('calculateMaxUsablePoints', () => {
    it('应该正确计算最大可用积分', () => {
      expect(PointsCalculator.calculateMaxUsablePoints(1000, 100, 0.5, 100)).toBe(5000) // 最多抵扣50%
      expect(PointsCalculator.calculateMaxUsablePoints(500, 100, 1, 100)).toBe(500) // 积分不足
    })
  })
})

describe('ChangeCalculator', () => {
  describe('calculateChange', () => {
    it('应该正确计算找零金额', () => {
      expect(ChangeCalculator.calculateChange(100, 85.5)).toBe(14.5)
      expect(ChangeCalculator.calculateChange(50, 60)).toBe(0) // 找零不能为负数
    })
    
    it('应该在金额为负数时抛出错误', () => {
      expect(() => ChangeCalculator.calculateChange(-100, 50)).toThrow('金额不能为负数')
      expect(() => ChangeCalculator.calculateChange(100, -50)).toThrow('金额不能为负数')
    })
  })
  
  describe('calculateChangeDenominations', () => {
    it('应该正确计算找零面额分布', () => {
      const result = ChangeCalculator.calculateChangeDenominations(186.6)
      expect(result[100]).toBe(1)
      expect(result[50]).toBe(1)
      expect(result[20]).toBe(1)
      expect(result[10]).toBe(1)
      expect(result[5]).toBe(1)
      expect(result[1]).toBe(1)
      expect(result[0.5]).toBe(1)
      expect(result[0.1]).toBe(1)
    })
  })
})

describe('StatisticsCalculator', () => {
  describe('calculateSalesStatistics', () => {
    it('应该正确计算销售统计', () => {
      const orders = [
        { finalAmount: 100, items: [{ quantity: 2 }, { quantity: 3 }] },
        { finalAmount: 200, items: [{ quantity: 1 }] },
        { finalAmount: 50, items: [{ quantity: 4 }] }
      ]
      
      const stats = StatisticsCalculator.calculateSalesStatistics(orders)
      
      expect(stats.totalOrders).toBe(3)
      expect(stats.totalAmount).toBe(350)
      expect(stats.totalQuantity).toBe(10)
      expect(stats.averageOrderAmount).toBe(116.67)
      expect(stats.maxOrderAmount).toBe(200)
      expect(stats.minOrderAmount).toBe(50)
    })
    
    it('应该处理空订单列表', () => {
      const stats = StatisticsCalculator.calculateSalesStatistics([])
      
      expect(stats.totalOrders).toBe(0)
      expect(stats.totalAmount).toBe(0)
      expect(stats.averageOrderAmount).toBe(0)
    })
  })
  
  describe('calculateProductRanking', () => {
    it('应该正确计算商品销售排行', () => {
      const orders = [
        {
          items: [
            { productId: 1, productName: '商品A', quantity: 2, subtotal: 20 },
            { productId: 2, productName: '商品B', quantity: 1, subtotal: 15 }
          ]
        },
        {
          items: [
            { productId: 1, productName: '商品A', quantity: 3, subtotal: 30 },
            { productId: 3, productName: '商品C', quantity: 1, subtotal: 25 }
          ]
        }
      ]
      
      const ranking = StatisticsCalculator.calculateProductRanking(orders, 3)
      
      expect(ranking).toHaveLength(3)
      expect(ranking[0].productId).toBe(1) // 商品A销售金额最高
      expect(ranking[0].totalAmount).toBe(50)
      expect(ranking[0].totalQuantity).toBe(5)
    })
  })
})