/**
 * 购物车API单元测试
 * 
 * 测试购物车相关API接口的调用和错误处理
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { CartApi } from '../cart'
import Request from '@/utils/request/request-util'

// Mock Request模块
vi.mock('@/utils/request/request-util', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn()
  }
}))

// Mock 错误处理器和性能监控器
vi.mock('../../utils/error-handler', () => ({
  PosErrorHandler: {
    wrapApiCall: vi.fn((fn) => fn)
  }
}))

vi.mock('../../utils/performance-monitor', () => ({
  PerformanceMonitor: {
    measureApiCall: vi.fn((name, fn) => fn)
  }
}))

describe('CartApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('checkInventory', () => {
    it('应该成功检查商品库存', async () => {
      const mockResponse = { 
        available: true, 
        stock: 100,
        productId: 'P001'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.checkInventory('P001', 5)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/product/checkStock', {
        productId: 'P001',
        quantity: 5
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理库存不足的情况', async () => {
      const mockError = new Error('库存不足')
      Request.post.mockRejectedValue(mockError)
      
      await expect(CartApi.checkInventory('P001', 100)).rejects.toThrow('库存不足')
    })
  })
  
  describe('batchCheckInventory', () => {
    it('应该成功批量检查库存', async () => {
      const items = [
        { productId: 'P001', quantity: 2 },
        { productId: 'P002', quantity: 3 }
      ]
      const mockResponse = [
        { productId: 'P001', available: true, stock: 10 },
        { productId: 'P002', available: false, stock: 1 }
      ]
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.batchCheckInventory(items)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/product/batchCheckStock', {
        items
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getProductDetail', () => {
    it('应该成功获取商品详情', async () => {
      const mockProduct = {
        id: 'P001',
        name: '苹果',
        price: 5.5,
        stock: 100,
        unit: '斤'
      }
      Request.get.mockResolvedValue(mockProduct)
      
      const result = await CartApi.getProductDetail({ productId: 'P001' })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/detail', {
        productId: 'P001'
      })
      expect(result).toEqual(mockProduct)
    })
  })
  
  describe('getProductByBarcode', () => {
    it('应该成功根据条形码获取商品', async () => {
      const mockProduct = {
        id: 'P001',
        name: '苹果',
        barcode: '1234567890123',
        price: 5.5
      }
      Request.get.mockResolvedValue(mockProduct)
      
      const result = await CartApi.getProductByBarcode('1234567890123')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/barcode', {
        barcode: '1234567890123'
      })
      expect(result).toEqual(mockProduct)
    })
    
    it('应该处理条形码不存在的情况', async () => {
      const mockError = new Error('商品不存在')
      Request.get.mockRejectedValue(mockError)
      
      await expect(CartApi.getProductByBarcode('invalid')).rejects.toThrow('商品不存在')
    })
  })
  
  describe('getProductsBatch', () => {
    it('应该成功批量获取商品信息', async () => {
      const productIds = ['P001', 'P002', 'P003']
      const mockProducts = [
        { id: 'P001', name: '苹果', price: 5.5 },
        { id: 'P002', name: '香蕉', price: 3.0 },
        { id: 'P003', name: '橙子', price: 4.0 }
      ]
      Request.post.mockResolvedValue(mockProducts)
      
      const result = await CartApi.getProductsBatch(productIds)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/product/batch', {
        productIds
      })
      expect(result).toEqual(mockProducts)
    })
  })
  
  describe('searchProducts', () => {
    it('应该成功搜索商品', async () => {
      const mockProducts = [
        { id: 'P001', name: '红苹果', price: 5.5 },
        { id: 'P002', name: '青苹果', price: 4.5 }
      ]
      Request.get.mockResolvedValue(mockProducts)
      
      const result = await CartApi.searchProducts({
        keyword: '苹果',
        onlyInStock: true
      })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/products/search', {
        keyword: '苹果',
        onlyInStock: true,
        limit: 20
      })
      expect(result).toEqual(mockProducts)
    })
    
    it('应该使用默认的limit参数', async () => {
      Request.get.mockResolvedValue([])
      
      await CartApi.searchProducts({ keyword: '测试' })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/products/search', {
        keyword: '测试',
        limit: 20
      })
    })
  })
  
  describe('saveCartState', () => {
    it('应该成功保存购物车状态', async () => {
      const cartData = {
        items: [
          { id: 'P001', name: '苹果', quantity: 2, price: 5.5 }
        ],
        totalAmount: 11,
        discountAmount: 0,
        remark: '测试挂单'
      }
      const mockResponse = {
        suspendId: 'SUSPEND001',
        success: true
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.saveCartState(cartData)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/suspend', {
        ...cartData,
        suspendTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('restoreCartState', () => {
    it('应该成功恢复购物车状态', async () => {
      const mockCartData = {
        suspendId: 'SUSPEND001',
        items: [
          { id: 'P001', name: '苹果', quantity: 2, price: 5.5 }
        ],
        totalAmount: 11,
        suspendTime: '2025-01-02T10:00:00Z'
      }
      Request.get.mockResolvedValue(mockCartData)
      
      const result = await CartApi.restoreCartState('SUSPEND001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/cart/restore/SUSPEND001')
      expect(result).toEqual(mockCartData)
    })
  })
  
  describe('getSuspendedCarts', () => {
    it('应该成功获取挂单列表', async () => {
      const mockSuspendedCarts = [
        {
          suspendId: 'SUSPEND001',
          itemCount: 3,
          totalAmount: 25.5,
          suspendTime: '2025-01-02T10:00:00Z'
        },
        {
          suspendId: 'SUSPEND002',
          itemCount: 1,
          totalAmount: 8.0,
          suspendTime: '2025-01-02T11:00:00Z'
        }
      ]
      Request.get.mockResolvedValue(mockSuspendedCarts)
      
      const result = await CartApi.getSuspendedCarts({ cashierId: 1 })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/cart/suspended', {
        cashierId: 1,
        limit: 50
      })
      expect(result).toEqual(mockSuspendedCarts)
    })
  })
  
  describe('deleteSuspendedCart', () => {
    it('应该成功删除挂单', async () => {
      const mockResponse = { success: true }
      Request.delete.mockResolvedValue(mockResponse)
      
      const result = await CartApi.deleteSuspendedCart('SUSPEND001')
      
      expect(Request.delete).toHaveBeenCalledWith('/erp/pos/cart/suspended/SUSPEND001')
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('validateCart', () => {
    it('应该成功验证购物车', async () => {
      const cartData = {
        items: [
          { id: 'P001', name: '苹果', quantity: 2, price: 5.5 }
        ]
      }
      const mockResponse = {
        valid: true,
        errors: []
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.validateCart(cartData)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/validate', cartData)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理验证失败的情况', async () => {
      const cartData = {
        items: []
      }
      const mockResponse = {
        valid: false,
        errors: ['购物车不能为空']
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.validateCart(cartData)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('购物车不能为空')
    })
  })
  
  describe('calculateCartTotal', () => {
    it('应该成功计算购物车总金额', async () => {
      const cartData = {
        items: [
          { id: 'P001', name: '苹果', quantity: 2, price: 5.5 },
          { id: 'P002', name: '香蕉', quantity: 3, price: 3.0 }
        ],
        member: { id: 'M001', discountRate: 0.1 }
      }
      const mockResponse = {
        subtotal: 20,
        discountAmount: 2,
        finalAmount: 18,
        breakdown: {
          memberDiscount: 2,
          couponDiscount: 0
        }
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.calculateCartTotal(cartData)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/calculate', cartData)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('applyCoupon', () => {
    it('应该成功应用优惠券', async () => {
      const params = {
        items: [
          { id: 'P001', name: '苹果', quantity: 2, price: 5.5 }
        ],
        couponCode: 'DISCOUNT10',
        member: { id: 'M001' }
      }
      const mockResponse = {
        success: true,
        discountAmount: 1.1,
        couponInfo: {
          code: 'DISCOUNT10',
          name: '10%折扣券',
          discountType: 'PERCENTAGE',
          discountValue: 0.1
        }
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.applyCoupon(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/applyCoupon', params)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理无效优惠券的情况', async () => {
      const mockError = new Error('优惠券无效或已过期')
      Request.post.mockRejectedValue(mockError)
      
      const params = {
        items: [],
        couponCode: 'INVALID',
        member: null
      }
      
      await expect(CartApi.applyCoupon(params)).rejects.toThrow('优惠券无效或已过期')
    })
  })
  
  describe('removeCoupon', () => {
    it('应该成功移除优惠券', async () => {
      const params = {
        items: [
          { id: 'P001', name: '苹果', quantity: 2, price: 5.5 }
        ],
        couponId: 'COUPON001'
      }
      const mockResponse = {
        success: true,
        removedDiscount: 1.1
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.removeCoupon(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/removeCoupon', params)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('clearExpiredCarts', () => {
    it('应该成功清理过期挂单', async () => {
      const mockResponse = {
        success: true,
        clearedCount: 5
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await CartApi.clearExpiredCarts(48)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/clearExpired', {
        expireHours: 48
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该使用默认的过期时间', async () => {
      Request.post.mockResolvedValue({ success: true, clearedCount: 0 })
      
      await CartApi.clearExpiredCarts()
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/cart/clearExpired', {
        expireHours: 24
      })
    })
  })
})