import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { usePerformanceMonitor } from '../usePerformanceMonitor'

// Mock PerformanceMonitor
vi.mock('../../utils/performance-monitor', () => ({
  PerformanceMonitor: {
    init: vi.fn(),
    recordMetric: vi.fn()
  }
}))

// Mock performance API
global.performance = {
  now: vi.fn(() => Date.now()),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024 // 2GB
  }
}

// Mock nextTick
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    nextTick: vi.fn(fn => Promise.resolve().then(fn))
  }
})

describe('usePerformanceMonitor', () => {
  let performanceMonitor
  
  beforeEach(() => {
    vi.clearAllMocks()
    performanceMonitor = usePerformanceMonitor('TestComponent')
  })
  
  afterEach(() => {
    performanceMonitor.stopMonitoring()
    vi.restoreAllMocks()
  })

  describe('基础功能', () => {
    it('应该返回所有必要的状态和方法', () => {
      expect(performanceMonitor.isMonitoring).toBeDefined()
      expect(performanceMonitor.performanceData).toBeDefined()
      expect(performanceMonitor.performanceStats).toBeDefined()
      expect(performanceMonitor.performanceWarnings).toBeDefined()
      expect(performanceMonitor.hasPerformanceIssues).toBeDefined()
      expect(performanceMonitor.monitorConfig).toBeDefined()
      
      expect(performanceMonitor.startMonitoring).toBeDefined()
      expect(performanceMonitor.stopMonitoring).toBeDefined()
      expect(performanceMonitor.recordRenderTime).toBeDefined()
      expect(performanceMonitor.recordApiCall).toBeDefined()
      expect(performanceMonitor.measureOperation).toBeDefined()
      expect(performanceMonitor.clearPerformanceWarnings).toBeDefined()
      expect(performanceMonitor.getPerformanceReport).toBeDefined()
      expect(performanceMonitor.exportPerformanceData).toBeDefined()
      expect(performanceMonitor.updateMonitorConfig).toBeDefined()
      expect(performanceMonitor.getPerformanceSuggestions).toBeDefined()
    })

    it('应该正确初始化状态', () => {
      expect(performanceMonitor.isMonitoring.value).toBe(false)
      expect(performanceMonitor.performanceData.value.renderTimes).toEqual([])
      expect(performanceMonitor.performanceData.value.memoryUsage).toEqual([])
      expect(performanceMonitor.performanceData.value.userInteractions).toEqual([])
      expect(performanceMonitor.performanceData.value.apiCalls).toEqual([])
      expect(performanceMonitor.performanceWarnings.value).toEqual([])
      expect(performanceMonitor.hasPerformanceIssues.value).toBe(false)
    })
  })

  describe('监控控制', () => {
    it('应该能够开始监控', () => {
      performanceMonitor.startMonitoring()
      
      expect(performanceMonitor.isMonitoring.value).toBe(true)
    })

    it('应该能够停止监控', () => {
      performanceMonitor.startMonitoring()
      performanceMonitor.stopMonitoring()
      
      expect(performanceMonitor.isMonitoring.value).toBe(false)
    })

    it('应该防止重复开始监控', () => {
      performanceMonitor.startMonitoring()
      const firstState = performanceMonitor.isMonitoring.value
      
      performanceMonitor.startMonitoring()
      
      expect(performanceMonitor.isMonitoring.value).toBe(firstState)
    })
  })

  describe('渲染性能监控', () => {
    it('应该正确记录渲染时间', () => {
      const renderTime = 25.5
      
      performanceMonitor.recordRenderTime(renderTime)
      
      expect(performanceMonitor.performanceData.value.renderTimes).toHaveLength(1)
      expect(performanceMonitor.performanceData.value.renderTimes[0].time).toBe(renderTime)
      expect(performanceMonitor.performanceStats.value.totalRenders).toBe(1)
      expect(performanceMonitor.performanceStats.value.averageRenderTime).toBe(renderTime)
    })

    it('应该在渲染时间过长时添加警告', () => {
      const slowRenderTime = 50 // 超过默认阈值16ms
      
      performanceMonitor.recordRenderTime(slowRenderTime)
      
      expect(performanceMonitor.performanceWarnings.value).toHaveLength(1)
      expect(performanceMonitor.performanceWarnings.value[0].type).toBe('slow_render')
      expect(performanceMonitor.hasPerformanceIssues.value).toBe(true)
    })

    it('应该正确计算渲染统计', () => {
      const renderTimes = [10, 20, 30, 40, 50]
      
      renderTimes.forEach(time => {
        performanceMonitor.recordRenderTime(time)
      })
      
      expect(performanceMonitor.performanceStats.value.totalRenders).toBe(5)
      expect(performanceMonitor.performanceStats.value.averageRenderTime).toBe(30)
      expect(performanceMonitor.performanceStats.value.maxRenderTime).toBe(50)
      expect(performanceMonitor.performanceStats.value.minRenderTime).toBe(10)
    })

    it('应该限制渲染时间数据量', () => {
      // 添加超过限制的数据
      for (let i = 0; i < 150; i++) {
        performanceMonitor.recordRenderTime(i)
      }
      
      expect(performanceMonitor.performanceData.value.renderTimes.length).toBeLessThanOrEqual(100)
    })
  })

  describe('API性能监控', () => {
    it('应该正确记录API调用', () => {
      const apiCall = {
        name: 'testApi',
        duration: 150,
        status: 'success'
      }
      
      performanceMonitor.recordApiCall(apiCall)
      
      expect(performanceMonitor.performanceData.value.apiCalls).toHaveLength(1)
      expect(performanceMonitor.performanceData.value.apiCalls[0].name).toBe('testApi')
      expect(performanceMonitor.performanceData.value.apiCalls[0].duration).toBe(150)
    })

    it('应该在API调用缓慢时添加警告', () => {
      const slowApiCall = {
        name: 'slowApi',
        duration: 200, // 超过默认阈值100ms
        status: 'success'
      }
      
      performanceMonitor.recordApiCall(slowApiCall)
      
      expect(performanceMonitor.performanceWarnings.value).toHaveLength(1)
      expect(performanceMonitor.performanceWarnings.value[0].type).toBe('slow_api')
      expect(performanceMonitor.performanceStats.value.slowOperations).toBe(1)
    })

    it('应该正确测量操作性能', async () => {
      const testOperation = vi.fn().mockResolvedValue('success')
      
      const result = await performanceMonitor.measureOperation('testOp', testOperation)
      
      expect(result).toBe('success')
      expect(testOperation).toHaveBeenCalled()
      expect(performanceMonitor.performanceData.value.apiCalls).toHaveLength(1)
      expect(performanceMonitor.performanceData.value.apiCalls[0].name).toBe('testOp')
      expect(performanceMonitor.performanceData.value.apiCalls[0].status).toBe('success')
    })

    it('应该正确处理操作失败', async () => {
      const failingOperation = vi.fn().mockRejectedValue(new Error('Test error'))
      
      await expect(performanceMonitor.measureOperation('failOp', failingOperation))
        .rejects.toThrow('Test error')
      
      expect(performanceMonitor.performanceData.value.apiCalls).toHaveLength(1)
      expect(performanceMonitor.performanceData.value.apiCalls[0].status).toBe('error')
      expect(performanceMonitor.performanceData.value.apiCalls[0].error).toBe('Test error')
    })
  })

  describe('内存监控', () => {
    it('应该正确记录内存使用', () => {
      const memoryInfo = {
        used: 50,
        total: 100,
        limit: 2000,
        timestamp: Date.now()
      }
      
      // 直接调用内部方法进行测试
      performanceMonitor.recordMemoryUsage(memoryInfo)
      
      expect(performanceMonitor.performanceData.value.memoryUsage).toHaveLength(1)
      expect(performanceMonitor.performanceData.value.memoryUsage[0].used).toBe(50)
    })

    it('应该正确更新内存趋势', () => {
      // 添加内存使用数据模拟上升趋势
      const memoryData = [
        { used: 30, timestamp: Date.now() - 2000 },
        { used: 35, timestamp: Date.now() - 1000 },
        { used: 45, timestamp: Date.now() } // 增长15MB，超过5MB阈值
      ]
      
      memoryData.forEach(data => {
        performanceMonitor.recordMemoryUsage(data)
      })
      
      expect(performanceMonitor.performanceStats.value.memoryTrend).toBe('increasing')
    })
  })

  describe('性能警告管理', () => {
    it('应该能够清除特定警告', () => {
      // 添加一些警告
      performanceMonitor.recordRenderTime(50) // 会产生警告
      performanceMonitor.recordRenderTime(60) // 会产生警告
      
      expect(performanceMonitor.performanceWarnings.value).toHaveLength(2)
      
      const firstWarningId = performanceMonitor.performanceWarnings.value[0].id
      performanceMonitor.clearPerformanceWarnings(firstWarningId)
      
      expect(performanceMonitor.performanceWarnings.value).toHaveLength(1)
    })

    it('应该能够清除所有警告', () => {
      // 添加一些警告
      performanceMonitor.recordRenderTime(50)
      performanceMonitor.recordRenderTime(60)
      
      expect(performanceMonitor.performanceWarnings.value).toHaveLength(2)
      
      performanceMonitor.clearPerformanceWarnings()
      
      expect(performanceMonitor.performanceWarnings.value).toHaveLength(0)
      expect(performanceMonitor.hasPerformanceIssues.value).toBe(false)
    })

    it('应该限制警告数量', () => {
      // 添加超过限制的警告
      for (let i = 0; i < 60; i++) {
        performanceMonitor.recordRenderTime(50) // 每次都会产生警告
      }
      
      expect(performanceMonitor.performanceWarnings.value.length).toBeLessThanOrEqual(50)
    })
  })

  describe('配置管理', () => {
    it('应该能够更新监控配置', () => {
      const newConfig = {
        renderTimeThreshold: 20,
        memoryThreshold: 60
      }
      
      performanceMonitor.updateMonitorConfig(newConfig)
      
      expect(performanceMonitor.monitorConfig.value.renderTimeThreshold).toBe(20)
      expect(performanceMonitor.monitorConfig.value.memoryThreshold).toBe(60)
    })

    it('应该在配置更新时重启监控', () => {
      performanceMonitor.startMonitoring()
      const stopSpy = vi.spyOn(performanceMonitor, 'stopMonitoring')
      const startSpy = vi.spyOn(performanceMonitor, 'startMonitoring')
      
      performanceMonitor.updateMonitorConfig({ renderTimeThreshold: 20 })
      
      expect(stopSpy).toHaveBeenCalled()
      expect(startSpy).toHaveBeenCalled()
    })
  })

  describe('性能报告', () => {
    it('应该生成正确的性能报告', () => {
      // 添加一些测试数据
      performanceMonitor.recordRenderTime(25)
      performanceMonitor.recordApiCall({ name: 'test', duration: 100, status: 'success' })
      
      const report = performanceMonitor.getPerformanceReport()
      
      expect(report.component).toBe('TestComponent')
      expect(report.timestamp).toBeDefined()
      expect(report.stats).toBeDefined()
      expect(report.data).toBeDefined()
      expect(report.config).toBeDefined()
      expect(report.data.renderTimes).toHaveLength(1)
      expect(report.data.apiCalls).toHaveLength(1)
    })

    it('应该能够导出性能数据', () => {
      performanceMonitor.recordRenderTime(25)
      
      const jsonData = performanceMonitor.exportPerformanceData('json')
      const csvData = performanceMonitor.exportPerformanceData('csv')
      
      expect(jsonData).toContain('TestComponent')
      expect(csvData).toContain('Render Times')
      expect(csvData).toContain('25')
    })
  })

  describe('性能建议', () => {
    it('应该为慢渲染提供建议', () => {
      // 添加多个慢渲染
      for (let i = 0; i < 5; i++) {
        performanceMonitor.recordRenderTime(50)
      }
      
      const suggestions = performanceMonitor.getPerformanceSuggestions()
      
      expect(suggestions).toHaveLength(1)
      expect(suggestions[0].type).toBe('render')
      expect(suggestions[0].priority).toBe('high')
    })

    it('应该为内存问题提供建议', () => {
      // 模拟内存上升趋势
      const memoryData = [
        { used: 30, timestamp: Date.now() - 2000 },
        { used: 40, timestamp: Date.now() - 1000 },
        { used: 55, timestamp: Date.now() }
      ]
      
      memoryData.forEach(data => {
        performanceMonitor.recordMemoryUsage(data)
      })
      
      const suggestions = performanceMonitor.getPerformanceSuggestions()
      
      expect(suggestions.some(s => s.type === 'memory')).toBe(true)
    })

    it('应该为慢API提供建议', () => {
      // 添加多个慢API调用
      for (let i = 0; i < 6; i++) {
        performanceMonitor.recordApiCall({
          name: `slowApi${i}`,
          duration: 200,
          status: 'success'
        })
      }
      
      const suggestions = performanceMonitor.getPerformanceSuggestions()
      
      expect(suggestions.some(s => s.type === 'api')).toBe(true)
    })
  })
})