/**
 * POS状态管理统一入口
 * 
 * 整合所有POS相关的状态管理模块，提供统一的接口和批量更新机制
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useCartStore } from './cart'
import { useMemberStore } from './member'
import { usePaymentStore } from './payment'
import { useOrderStore } from './order'

export const usePosStore = defineStore('pos', () => {
  // ==================== 子模块状态 ====================
  
  const cartStore = useCartStore()
  const memberStore = useMemberStore()
  const paymentStore = usePaymentStore()
  const orderStore = useOrderStore()
  
  // ==================== 全局状态 ====================
  
  // 商品和分类状态
  const categories = ref([])
  const selectedCategory = ref(null)
  const products = ref([])
  const searchKeyword = ref('')
  
  // 系统状态
  const isInitialized = ref(false)
  const isLoading = ref(false)
  const lastUpdateTime = ref(null)
  
  // 批量更新控制
  const isBatchUpdating = ref(false)
  const batchUpdateQueue = ref([])
  
  // ==================== 计算属性 ====================
  
  // 是否可以结算（购物车不为空且不在支付处理中）
  const canCheckout = computed(() => {
    return cartStore.hasCartItems && !paymentStore.isPaymentProcessing
  })
  
  // 订单总览信息
  const orderSummary = computed(() => {
    const cartSummary = cartStore.cartSummary
    const memberInfo = memberStore.memberDiscountInfo
    const pointsInfo = memberStore.pointsDeductionInfo
    
    // 计算最终金额
    let finalAmount = cartStore.totalAmount
    
    // 应用会员折扣
    if (memberInfo.hasDiscount) {
      finalAmount -= memberInfo.discountAmount
    }
    
    // 应用积分抵扣
    if (pointsInfo.hasDeduction) {
      finalAmount -= pointsInfo.deductionAmount
    }
    
    finalAmount = Math.max(0, finalAmount)
    
    return {
      ...cartSummary,
      memberDiscount: memberInfo.discountAmount,
      pointsDeduction: pointsInfo.deductionAmount,
      finalAmount: finalAmount.toFixed(2),
      hasMember: memberStore.hasMember,
      memberName: memberStore.memberDisplayName,
      paymentMethod: paymentStore.currentPaymentMethod?.name || '',
      canCheckout: canCheckout.value
    }
  })
  
  // 系统状态概览
  const systemStatus = computed(() => ({
    initialized: isInitialized.value,
    loading: isLoading.value,
    cartItems: cartStore.cartItemCount,
    suspendedOrders: orderStore.suspendedOrderCount,
    todayOrders: orderStore.todayOrderCount,
    todaySales: orderStore.todaySalesAmount,
    paymentStatus: paymentStore.paymentStatus,
    lastUpdate: lastUpdateTime.value
  }))
  
  // ==================== 批量更新机制 ====================
  
  /**
   * 开始批量更新
   */
  const startBatchUpdate = () => {
    isBatchUpdating.value = true
    batchUpdateQueue.value = []
  }
  
  /**
   * 添加更新操作到队列
   * @param {Function} updateFn - 更新函数
   * @param {string} description - 操作描述
   */
  const addUpdateToQueue = (updateFn, description = '') => {
    if (isBatchUpdating.value) {
      batchUpdateQueue.value.push({ updateFn, description })
    } else {
      updateFn()
    }
  }
  
  /**
   * 执行批量更新
   */
  const executeBatchUpdate = async () => {
    if (!isBatchUpdating.value) return
    
    try {
      isLoading.value = true
      
      // 执行所有更新操作
      for (const { updateFn, description } of batchUpdateQueue.value) {
        try {
          await updateFn()
        } catch (error) {
          console.error(`批量更新操作失败 [${description}]:`, error)
        }
      }
      
      // 触发相关计算
      cartStore.calculateTotal()
      lastUpdateTime.value = Date.now()
      
    } catch (error) {
      console.error('批量更新失败:', error)
      message.error('批量更新失败')
    } finally {
      isBatchUpdating.value = false
      batchUpdateQueue.value = []
      isLoading.value = false
    }
  }
  
  // ==================== 业务逻辑方法 ====================
  
  /**
   * 添加商品到购物车（集成会员折扣计算）
   * @param {Object} product - 商品信息
   * @param {number} quantity - 数量
   * @returns {boolean} 是否添加成功
   */
  const addToCart = async (product, quantity = 1) => {
    try {
      const success = cartStore.addToCart(product, quantity)
      
      if (success && memberStore.hasMember) {
        // 重新计算会员折扣
        const discountResult = memberStore.applyMemberDiscount(cartStore.totalAmount)
        if (discountResult.success) {
          cartStore.setDiscountAmount(discountResult.discountAmount)
        }
      }
      
      return success
    } catch (error) {
      console.error('添加商品到购物车失败:', error)
      return false
    }
  }
  
  /**
   * 设置会员（自动应用折扣）
   * @param {Object} member - 会员信息
   * @returns {boolean} 是否设置成功
   */
  const setMember = async (member) => {
    try {
      const success = memberStore.setCurrentMember(member)
      
      if (success && cartStore.hasCartItems) {
        // 自动应用会员折扣
        const discountResult = memberStore.applyMemberDiscount(cartStore.totalAmount)
        if (discountResult.success) {
          cartStore.setDiscountAmount(discountResult.discountAmount)
        }
      }
      
      return success
    } catch (error) {
      console.error('设置会员失败:', error)
      return false
    }
  }
  
  /**
   * 清除会员（清除相关折扣）
   */
  const clearMember = () => {
    try {
      memberStore.clearCurrentMember()
      cartStore.setDiscountAmount(0)
    } catch (error) {
      console.error('清除会员失败:', error)
    }
  }
  
  /**
   * 开始结算流程
   * @param {string} paymentMethod - 支付方式
   * @returns {boolean} 是否开始成功
   */
  const startCheckout = async (paymentMethod) => {
    try {
      if (!canCheckout.value) {
        message.warning('当前无法结算')
        return false
      }
      
      // 设置支付方式
      const paymentSuccess = paymentStore.setPaymentMethod(paymentMethod)
      if (!paymentSuccess) {
        return false
      }
      
      // 创建订单
      const orderData = {
        cartItems: cartStore.getCartSnapshot().cartItems,
        totalAmount: cartStore.totalAmount,
        discountAmount: cartStore.discountAmount,
        finalAmount: parseFloat(orderSummary.value.finalAmount),
        memberInfo: memberStore.currentMember,
        memberDiscount: memberStore.memberDiscountInfo,
        pointsDeduction: memberStore.pointsDeductionInfo
      }
      
      const order = orderStore.createOrder(orderData)
      if (!order) {
        return false
      }
      
      // 开始支付
      return paymentStore.startPayment({
        orderId: order.orderId,
        orderNo: order.orderNo,
        amount: parseFloat(orderSummary.value.finalAmount)
      })
      
    } catch (error) {
      console.error('开始结算失败:', error)
      message.error('开始结算失败')
      return false
    }
  }
  
  /**
   * 完成订单
   * @param {Object} paymentResult - 支付结果
   * @returns {boolean} 是否完成成功
   */
  const completeOrder = async (paymentResult) => {
    try {
      // 标记支付成功
      paymentStore.paymentSuccess(paymentResult)
      
      // 完成订单
      const orderSuccess = orderStore.completeOrder(paymentResult)
      if (!orderSuccess) {
        return false
      }
      
      // 清理状态
      cartStore.clearCart()
      memberStore.clearCurrentMember()
      paymentStore.resetPaymentStatus()
      
      message.success('订单完成')
      return true
      
    } catch (error) {
      console.error('完成订单失败:', error)
      message.error('完成订单失败')
      return false
    }
  }
  
  /**
   * 挂起当前订单
   * @param {string} remark - 挂单备注
   * @returns {boolean} 是否挂单成功
   */
  const suspendOrder = async (remark = '') => {
    try {
      if (!cartStore.hasCartItems) {
        message.warning('购物车为空，无法挂单')
        return false
      }
      
      const orderData = {
        cartItems: cartStore.getCartSnapshot().cartItems,
        totalAmount: cartStore.totalAmount,
        discountAmount: cartStore.discountAmount,
        finalAmount: parseFloat(orderSummary.value.finalAmount),
        memberInfo: memberStore.getMemberSnapshot(),
        paymentInfo: paymentStore.getPaymentSnapshot()
      }
      
      const success = orderStore.suspendCurrentOrder(orderData, remark)
      
      if (success) {
        // 清理当前状态
        cartStore.clearCart()
        memberStore.clearCurrentMember()
        paymentStore.resetPaymentStatus()
      }
      
      return success
      
    } catch (error) {
      console.error('挂单失败:', error)
      return false
    }
  }
  
  /**
   * 恢复挂单
   * @param {number} suspendId - 挂单ID
   * @returns {boolean} 是否恢复成功
   */
  const resumeOrder = async (suspendId) => {
    try {
      const orderData = orderStore.resumeSuspendedOrder(suspendId)
      if (!orderData) {
        return false
      }
      
      // 恢复购物车
      if (orderData.cartItems) {
        cartStore.restoreFromSnapshot({
          cartItems: orderData.cartItems,
          totalAmount: orderData.totalAmount,
          discountAmount: orderData.discountAmount,
          finalAmount: orderData.finalAmount
        })
      }
      
      // 恢复会员信息
      if (orderData.memberInfo) {
        memberStore.restoreFromSnapshot(orderData.memberInfo)
      }
      
      // 恢复支付信息
      if (orderData.paymentInfo) {
        paymentStore.restoreFromSnapshot(orderData.paymentInfo)
      }
      
      return true
      
    } catch (error) {
      console.error('恢复挂单失败:', error)
      return false
    }
  }
  
  // ==================== 商品和分类管理 ====================
  
  /**
   * 设置商品分类列表
   * @param {Array} categoryList - 分类列表
   */
  const setCategories = (categoryList) => {
    categories.value = categoryList || []
  }
  
  /**
   * 设置当前选中的分类
   * @param {Object} category - 分类对象
   */
  const setSelectedCategory = (category) => {
    selectedCategory.value = category
  }
  
  /**
   * 设置商品列表
   * @param {Array} productList - 商品列表
   */
  const setProducts = (productList) => {
    products.value = productList || []
  }
  
  /**
   * 设置搜索关键词
   * @param {string} keyword - 搜索关键词
   */
  const setSearchKeyword = (keyword) => {
    searchKeyword.value = keyword || ''
  }
  
  // ==================== 数据持久化 ====================
  
  /**
   * 保存当前状态到本地存储
   */
  const saveStateToLocal = () => {
    try {
      const stateData = {
        cart: cartStore.getCartSnapshot(),
        member: memberStore.getMemberSnapshot(),
        payment: paymentStore.getPaymentSnapshot(),
        categories: categories.value,
        selectedCategory: selectedCategory.value,
        searchKeyword: searchKeyword.value,
        timestamp: Date.now()
      }
      
      localStorage.setItem('pos_current_state', JSON.stringify(stateData))
    } catch (error) {
      console.error('保存状态到本地存储失败:', error)
    }
  }
  
  /**
   * 从本地存储恢复状态
   */
  const loadStateFromLocal = () => {
    try {
      const stateDataStr = localStorage.getItem('pos_current_state')
      if (stateDataStr) {
        const stateData = JSON.parse(stateDataStr)
        
        // 检查数据是否过期（超过1小时自动清除）
        const expireTime = Date.now() - (60 * 60 * 1000)
        if (stateData.timestamp && stateData.timestamp > expireTime) {
          // 恢复各模块状态
          if (stateData.cart) {
            cartStore.restoreFromSnapshot(stateData.cart)
          }
          if (stateData.member) {
            memberStore.restoreFromSnapshot(stateData.member)
          }
          if (stateData.payment) {
            paymentStore.restoreFromSnapshot(stateData.payment)
          }
          
          // 恢复其他状态
          categories.value = stateData.categories || []
          selectedCategory.value = stateData.selectedCategory || null
          searchKeyword.value = stateData.searchKeyword || ''
          
          if (cartStore.hasCartItems) {
            message.info('已恢复上次未完成的订单')
          }
        } else {
          // 数据过期，清除本地存储
          localStorage.removeItem('pos_current_state')
        }
      }
    } catch (error) {
      console.error('从本地存储恢复状态失败:', error)
      localStorage.removeItem('pos_current_state')
    }
  }
  
  /**
   * 清除本地存储的状态数据
   */
  const clearStateFromLocal = () => {
    try {
      localStorage.removeItem('pos_current_state')
    } catch (error) {
      console.error('清除本地状态数据失败:', error)
    }
  }
  
  // ==================== 系统管理 ====================
  
  /**
   * 重置所有状态到初始状态
   */
  const resetAllState = () => {
    try {
      startBatchUpdate()
      
      addUpdateToQueue(() => cartStore.clearCart(), '清空购物车')
      addUpdateToQueue(() => memberStore.resetMemberState(), '重置会员状态')
      addUpdateToQueue(() => paymentStore.resetPaymentStatus(), '重置支付状态')
      addUpdateToQueue(() => orderStore.resetOrderState(), '重置订单状态')
      
      addUpdateToQueue(() => {
        categories.value = []
        selectedCategory.value = null
        products.value = []
        searchKeyword.value = ''
      }, '重置商品状态')
      
      addUpdateToQueue(() => clearStateFromLocal(), '清除本地存储')
      
      executeBatchUpdate()
      
      message.success('状态已重置')
    } catch (error) {
      console.error('重置状态失败:', error)
      message.error('重置状态失败')
    }
  }
  
  /**
   * 初始化POS系统
   */
  const initializePosSystem = async () => {
    try {
      isLoading.value = true
      
      // 初始化各个子模块
      orderStore.initializeOrderStore()
      
      // 恢复本地状态
      loadStateFromLocal()
      
      // 设置自动保存
      setupAutoSave()
      
      isInitialized.value = true
      lastUpdateTime.value = Date.now()
      
      console.log('POS系统初始化完成')
      
    } catch (error) {
      console.error('POS系统初始化失败:', error)
      message.error('系统初始化失败')
    } finally {
      isLoading.value = false
    }
  }
  
  /**
   * 设置自动保存
   */
  const setupAutoSave = () => {
    // 监听关键状态变化，自动保存
    watch(
      () => [
        cartStore.cartItems.length,
        cartStore.totalAmount,
        memberStore.currentMember,
        paymentStore.selectedPaymentMethod
      ],
      () => {
        if (isInitialized.value) {
          saveStateToLocal()
        }
      },
      { deep: true }
    )
    
    // 页面卸载时保存状态
    window.addEventListener('beforeunload', () => {
      saveStateToLocal()
    })
  }
  
  // ==================== 返回接口 ====================
  
  return {
    // 子模块
    cartStore,
    memberStore,
    paymentStore,
    orderStore,
    
    // 全局状态
    categories,
    selectedCategory,
    products,
    searchKeyword,
    isInitialized,
    isLoading,
    lastUpdateTime,
    isBatchUpdating,
    
    // 计算属性
    canCheckout,
    orderSummary,
    systemStatus,
    
    // 批量更新
    startBatchUpdate,
    addUpdateToQueue,
    executeBatchUpdate,
    
    // 业务方法
    addToCart,
    setMember,
    clearMember,
    startCheckout,
    completeOrder,
    suspendOrder,
    resumeOrder,
    
    // 商品分类方法
    setCategories,
    setSelectedCategory,
    setProducts,
    setSearchKeyword,
    
    // 数据持久化
    saveStateToLocal,
    loadStateFromLocal,
    clearStateFromLocal,
    
    // 系统管理
    resetAllState,
    initializePosSystem,
    
    // 兼容性方法（保持向后兼容）
    formatAmount: cartStore.formatAmount,
    getCartSummary: () => orderSummary.value
  }
})

// 导出子模块（用于直接访问）
export { useCartStore, useMemberStore, usePaymentStore, useOrderStore }