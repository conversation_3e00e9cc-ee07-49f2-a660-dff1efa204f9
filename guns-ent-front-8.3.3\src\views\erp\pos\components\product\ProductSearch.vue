<!--
  商品搜索组件
  
  提供商品搜索和过滤功能
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="product-search">
    <div class="search-row">
      <!-- 搜索框 -->
      <div class="search-wrapper">
        <a-input
          v-model:value="searchKeyword"
          placeholder="搜索商品名称、编码、条形码"
          class="search-input"
          size="large"
          :loading="searching"
          @press-enter="handleSearch"
          @change="handleSearchChange"
          allowClear
        >
          <template #prefix>
            <search-outlined />
          </template>
          <template #suffix>
            <a-button 
              v-if="showSearchButton"
              type="primary" 
              size="small"
              :loading="searching"
              @click="handleSearch"
              class="search-btn"
            >
              搜索
            </a-button>
          </template>
        </a-input>
      </div>

      <!-- 快捷搜索按钮 -->
      <div class="quick-search" v-if="quickSearchItems.length > 0">
        <a-button
          v-for="item in quickSearchItems"
          :key="item.key"
          size="small"
          :type="searchKeyword === item.keyword ? 'primary' : 'default'"
          @click="handleQuickSearch(item.keyword)"
          class="quick-search-btn"
        >
          {{ item.label }}
        </a-button>
      </div>
    </div>

    <!-- 高级过滤器 -->
    <div class="filter-row" v-if="showFilters">
      <!-- 价格过滤 -->
      <div class="filter-item">
        <label class="filter-label">价格范围:</label>
        <a-select
          v-model:value="priceFilter"
          placeholder="选择价格范围"
          class="filter-select"
          @change="handlePriceFilterChange"
          allowClear
        >
          <a-select-option 
            v-for="option in priceFilterOptions" 
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 库存过滤 -->
      <div class="filter-item">
        <label class="filter-label">库存状态:</label>
        <a-select
          v-model:value="stockFilter"
          placeholder="选择库存状态"
          class="filter-select"
          @change="handleStockFilterChange"
          allowClear
        >
          <a-select-option 
            v-for="option in stockFilterOptions" 
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 排序方式 -->
      <div class="filter-item">
        <label class="filter-label">排序方式:</label>
        <a-select
          v-model:value="sortBy"
          placeholder="选择排序方式"
          class="filter-select"
          @change="handleSortChange"
        >
          <a-select-option 
            v-for="option in sortOptions" 
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </div>

      <!-- 操作按钮 -->
      <div class="filter-actions">
        <a-button 
          @click="handleResetFilters" 
          class="reset-btn" 
          size="small"
          :loading="resetting"
        >
          <template #icon>
            <reload-outlined />
          </template>
          重置
        </a-button>
        
        <a-button 
          @click="toggleFilters" 
          class="toggle-btn" 
          size="small"
          type="text"
        >
          <template #icon>
            <filter-outlined v-if="!filtersExpanded" />
            <up-outlined v-else />
          </template>
          {{ filtersExpanded ? '收起' : '更多筛选' }}
        </a-button>
      </div>
    </div>

    <!-- 搜索结果统计 -->
    <div class="search-stats" v-if="showStats">
      <div class="stats-info">
        <span class="stats-text">
          <template v-if="searchKeyword">
            搜索"<strong>{{ searchKeyword }}</strong>"
          </template>
          <template v-if="hasActiveFilters">
            {{ searchKeyword ? '，' : '' }}已应用 {{ activeFiltersCount }} 个筛选条件
          </template>
          <template v-if="totalResults !== null">
            ，共找到 <strong>{{ totalResults }}</strong> 个商品
          </template>
        </span>
      </div>
      
      <div class="stats-actions">
        <a-button 
          v-if="searchKeyword || hasActiveFilters"
          type="link" 
          size="small"
          @click="handleClearAll"
        >
          清除所有条件
        </a-button>
      </div>
    </div>

    <!-- 搜索历史 -->
    <div class="search-history" v-if="showHistory && searchHistory.length > 0">
      <div class="history-title">
        <icon-font iconClass="icon-history" />
        <span>搜索历史</span>
        <a-button 
          type="text" 
          size="small"
          @click="clearSearchHistory"
          class="clear-history-btn"
        >
          清空
        </a-button>
      </div>
      
      <div class="history-items">
        <a-tag
          v-for="(item, index) in searchHistory"
          :key="index"
          :closable="true"
          @close="removeHistoryItem(index)"
          @click="handleQuickSearch(item)"
          class="history-tag"
        >
          {{ item }}
        </a-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  FilterOutlined, 
  UpOutlined 
} from '@ant-design/icons-vue'
import IconFont from '@/components/common/IconFont/index.vue'
import { PRICE_FILTER_OPTIONS, STOCK_FILTER_OPTIONS, SORT_OPTIONS } from '../../utils/constants'

// 定义组件名称
defineOptions({
  name: 'ProductSearch'
})

// 定义Props
const props = defineProps({
  // 初始搜索关键词
  initialKeyword: {
    type: String,
    default: ''
  },
  // 是否显示搜索按钮
  showSearchButton: {
    type: Boolean,
    default: false
  },
  // 是否显示过滤器
  showFilters: {
    type: Boolean,
    default: true
  },
  // 是否显示统计信息
  showStats: {
    type: Boolean,
    default: true
  },
  // 是否显示搜索历史
  showHistory: {
    type: Boolean,
    default: true
  },
  // 快捷搜索项
  quickSearchItems: {
    type: Array,
    default: () => [
      { key: 'hot', label: '热销商品', keyword: '' },
      { key: 'new', label: '新品上架', keyword: '' },
      { key: 'discount', label: '促销商品', keyword: '' }
    ]
  },
  // 搜索结果总数
  totalResults: {
    type: Number,
    default: null
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'search',
  'filter-change',
  'sort-change',
  'reset',
  'clear-all'
])

// 响应式状态
const searchKeyword = ref(props.initialKeyword)
const priceFilter = ref('')
const stockFilter = ref('')
const sortBy = ref('default')
const searching = ref(false)
const resetting = ref(false)
const filtersExpanded = ref(false)
const searchHistory = ref([])

// 防抖定时器
let searchTimer = null

// ==================== 计算属性 ====================

/**
 * 价格过滤选项
 */
const priceFilterOptions = computed(() => PRICE_FILTER_OPTIONS)

/**
 * 库存过滤选项
 */
const stockFilterOptions = computed(() => STOCK_FILTER_OPTIONS)

/**
 * 排序选项
 */
const sortOptions = computed(() => SORT_OPTIONS)

/**
 * 是否有活跃的过滤条件
 */
const hasActiveFilters = computed(() => {
  return !!(priceFilter.value || stockFilter.value || sortBy.value !== 'default')
})

/**
 * 活跃过滤条件数量
 */
const activeFiltersCount = computed(() => {
  let count = 0
  if (priceFilter.value) count++
  if (stockFilter.value) count++
  if (sortBy.value !== 'default') count++
  return count
})

// ==================== 方法 ====================

/**
 * 处理搜索
 */
const handleSearch = () => {
  const keyword = searchKeyword.value.trim()
  
  if (keyword) {
    // 添加到搜索历史
    addToSearchHistory(keyword)
  }
  
  searching.value = true
  
  emit('search', {
    keyword,
    priceFilter: priceFilter.value,
    stockFilter: stockFilter.value,
    sortBy: sortBy.value
  })
  
  // 模拟搜索延迟
  setTimeout(() => {
    searching.value = false
  }, 500)
}

/**
 * 处理搜索变化（防抖）
 */
const handleSearchChange = () => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(() => {
    handleSearch()
  }, 300)
}

/**
 * 处理快捷搜索
 * @param {string} keyword - 搜索关键词
 */
const handleQuickSearch = (keyword) => {
  searchKeyword.value = keyword
  handleSearch()
}

/**
 * 处理价格过滤变化
 * @param {string} value - 过滤值
 */
const handlePriceFilterChange = (value) => {
  emit('filter-change', {
    type: 'price',
    value,
    filters: {
      keyword: searchKeyword.value,
      priceFilter: value,
      stockFilter: stockFilter.value,
      sortBy: sortBy.value
    }
  })
}

/**
 * 处理库存过滤变化
 * @param {string} value - 过滤值
 */
const handleStockFilterChange = (value) => {
  emit('filter-change', {
    type: 'stock',
    value,
    filters: {
      keyword: searchKeyword.value,
      priceFilter: priceFilter.value,
      stockFilter: value,
      sortBy: sortBy.value
    }
  })
}

/**
 * 处理排序变化
 * @param {string} value - 排序值
 */
const handleSortChange = (value) => {
  emit('sort-change', {
    sortBy: value,
    filters: {
      keyword: searchKeyword.value,
      priceFilter: priceFilter.value,
      stockFilter: stockFilter.value,
      sortBy: value
    }
  })
}

/**
 * 重置所有过滤条件
 */
const handleResetFilters = () => {
  resetting.value = true
  
  searchKeyword.value = ''
  priceFilter.value = ''
  stockFilter.value = ''
  sortBy.value = 'default'
  
  emit('reset')
  
  setTimeout(() => {
    resetting.value = false
    message.success('已重置所有过滤条件')
  }, 300)
}

/**
 * 清除所有条件
 */
const handleClearAll = () => {
  handleResetFilters()
  emit('clear-all')
}

/**
 * 切换过滤器展开状态
 */
const toggleFilters = () => {
  filtersExpanded.value = !filtersExpanded.value
}

/**
 * 添加到搜索历史
 * @param {string} keyword - 搜索关键词
 */
const addToSearchHistory = (keyword) => {
  if (!keyword || searchHistory.value.includes(keyword)) {
    return
  }
  
  searchHistory.value.unshift(keyword)
  
  // 限制历史记录数量
  if (searchHistory.value.length > 10) {
    searchHistory.value = searchHistory.value.slice(0, 10)
  }
  
  // 保存到本地存储
  saveSearchHistory()
}

/**
 * 移除历史记录项
 * @param {number} index - 索引
 */
const removeHistoryItem = (index) => {
  searchHistory.value.splice(index, 1)
  saveSearchHistory()
}

/**
 * 清空搜索历史
 */
const clearSearchHistory = () => {
  searchHistory.value = []
  saveSearchHistory()
  message.success('已清空搜索历史')
}

/**
 * 保存搜索历史到本地存储
 */
const saveSearchHistory = () => {
  try {
    localStorage.setItem('pos_search_history', JSON.stringify(searchHistory.value))
  } catch (error) {
    console.warn('保存搜索历史失败:', error)
  }
}

/**
 * 从本地存储加载搜索历史
 */
const loadSearchHistory = () => {
  try {
    const saved = localStorage.getItem('pos_search_history')
    if (saved) {
      searchHistory.value = JSON.parse(saved)
    }
  } catch (error) {
    console.warn('加载搜索历史失败:', error)
    searchHistory.value = []
  }
}

// ==================== 生命周期 ====================

onMounted(() => {
  loadSearchHistory()
})

// ==================== 监听器 ====================

// 监听初始关键词变化
watch(
  () => props.initialKeyword,
  (newKeyword) => {
    searchKeyword.value = newKeyword
  }
)

// 暴露方法给父组件
defineExpose({
  search: handleSearch,
  reset: handleResetFilters,
  clearAll: handleClearAll,
  setKeyword: (keyword) => {
    searchKeyword.value = keyword
  }
})
</script>

<style scoped>
.product-search {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 搜索行 */
.search-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.search-wrapper {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
}

.search-btn {
  border: none;
  box-shadow: none;
}

/* 快捷搜索 */
.quick-search {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-search-btn {
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
  border-radius: 14px;
}

/* 过滤行 */
.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 13px;
  color: #595959;
  white-space: nowrap;
}

.filter-select {
  width: 120px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.reset-btn {
  color: #666;
  border-color: #d9d9d9;
}

.reset-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.toggle-btn {
  color: #1890ff;
}

/* 搜索统计 */
.search-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 13px;
}

.stats-text {
  color: #595959;
}

.stats-text strong {
  color: #1890ff;
  font-weight: 600;
}

/* 搜索历史 */
.search-history {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.history-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 13px;
  color: #595959;
}

.clear-history-btn {
  margin-left: auto;
  font-size: 12px;
  color: #8c8c8c;
}

.history-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.history-tag:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-search {
    padding: 12px;
  }
  
  .search-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .search-wrapper {
    min-width: auto;
  }
  
  .quick-search {
    justify-content: center;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-item {
    justify-content: space-between;
  }
  
  .filter-select {
    width: 140px;
  }
  
  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }
  
  .search-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .filter-select {
    width: 100%;
  }
  
  .quick-search-btn {
    flex: 1;
    min-width: 80px;
  }
}

/* 动画效果 */
.filter-row {
  transition: all 0.3s ease;
}

.search-stats {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .product-search {
    border: 2px solid #000;
  }
  
  .search-stats {
    border: 1px solid #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .search-stats {
    animation: none;
  }
  
  .filter-row {
    transition: none;
  }
  
  .history-tag {
    transition: none;
  }
}
</style>