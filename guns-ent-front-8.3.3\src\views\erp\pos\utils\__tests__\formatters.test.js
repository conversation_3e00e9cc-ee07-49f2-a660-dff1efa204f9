import { describe, it, expect } from 'vitest'
import {
  formatCurrency,
  formatNumber,
  formatPercentage,
  formatDate,
  formatTime,
  formatDateTime,
  formatDuration,
  formatFileSize,
  formatPhoneNumber,
  formatCardNumber,
  formatOrderNumber,
  formatProductCode,
  formatAddress,
  truncateText,
  capitalizeFirst,
  formatNameDisplay,
  formatQuantity,
  formatWeight,
  formatDiscount,
  formatTaxRate,
  formatMemberLevel,
  formatPaymentMethod,
  formatOrderStatus,
  parseFormattedNumber,
  sanitizeInput,
  maskSensitiveData
} from '../formatters'

describe('formatters', () => {
  describe('货币格式化', () => {
    it('应该正确格式化货币', () => {
      expect(formatCurrency(1234.56)).toBe('¥1,234.56')
      expect(formatCurrency(0)).toBe('¥0.00')
      expect(formatCurrency(1000000)).toBe('¥1,000,000.00')
      expect(formatCurrency(0.5)).toBe('¥0.50')
    })

    it('应该支持不同货币符号', () => {
      expect(formatCurrency(100, '$')).toBe('$100.00')
      expect(formatCurrency(100, '€')).toBe('€100.00')
      expect(formatCurrency(100, '')).toBe('100.00')
    })

    it('应该支持不同小数位数', () => {
      expect(formatCurrency(100, '¥', 0)).toBe('¥100')
      expect(formatCurrency(100.123, '¥', 3)).toBe('¥100.123')
    })

    it('应该处理负数', () => {
      expect(formatCurrency(-100)).toBe('-¥100.00')
    })

    it('应该处理无效输入', () => {
      expect(formatCurrency(null)).toBe('¥0.00')
      expect(formatCurrency(undefined)).toBe('¥0.00')
      expect(formatCurrency('invalid')).toBe('¥0.00')
      expect(formatCurrency(NaN)).toBe('¥0.00')
    })
  })

  describe('数字格式化', () => {
    it('应该正确格式化数字', () => {
      expect(formatNumber(1234.56)).toBe('1,234.56')
      expect(formatNumber(1000000)).toBe('1,000,000')
      expect(formatNumber(0)).toBe('0')
    })

    it('应该支持指定小数位数', () => {
      expect(formatNumber(1234.5678, 2)).toBe('1,234.57')
      expect(formatNumber(1234, 2)).toBe('1,234.00')
      expect(formatNumber(1234.5678, 0)).toBe('1,235')
    })

    it('应该处理负数', () => {
      expect(formatNumber(-1234.56)).toBe('-1,234.56')
    })
  })

  describe('百分比格式化', () => {
    it('应该正确格式化百分比', () => {
      expect(formatPercentage(0.15)).toBe('15.00%')
      expect(formatPercentage(0.5)).toBe('50.00%')
      expect(formatPercentage(1)).toBe('100.00%')
      expect(formatPercentage(0)).toBe('0.00%')
    })

    it('应该支持指定小数位数', () => {
      expect(formatPercentage(0.1234, 1)).toBe('12.3%')
      expect(formatPercentage(0.1234, 0)).toBe('12%')
    })

    it('应该处理大于1的值', () => {
      expect(formatPercentage(1.5)).toBe('150.00%')
    })
  })

  describe('日期时间格式化', () => {
    const testDate = new Date('2023-12-25T15:30:45')

    it('应该正确格式化日期', () => {
      expect(formatDate(testDate)).toBe('2023-12-25')
    })

    it('应该支持自定义日期格式', () => {
      expect(formatDate(testDate, 'YYYY/MM/DD')).toBe('2023/12/25')
      expect(formatDate(testDate, 'MM-DD-YYYY')).toBe('12-25-2023')
    })

    it('应该正确格式化时间', () => {
      expect(formatTime(testDate)).toBe('15:30:45')
    })

    it('应该支持12小时制', () => {
      expect(formatTime(testDate, true)).toBe('3:30:45 PM')
    })

    it('应该正确格式化日期时间', () => {
      expect(formatDateTime(testDate)).toBe('2023-12-25 15:30:45')
    })

    it('应该处理字符串日期', () => {
      expect(formatDate('2023-12-25')).toBe('2023-12-25')
    })

    it('应该处理无效日期', () => {
      expect(formatDate('invalid')).toBe('Invalid Date')
      expect(formatDate(null)).toBe('Invalid Date')
    })
  })

  describe('持续时间格式化', () => {
    it('应该正确格式化秒数', () => {
      expect(formatDuration(65)).toBe('1分5秒')
      expect(formatDuration(3661)).toBe('1小时1分1秒')
      expect(formatDuration(30)).toBe('30秒')
    })

    it('应该处理零值', () => {
      expect(formatDuration(0)).toBe('0秒')
    })

    it('应该处理大数值', () => {
      expect(formatDuration(90061)).toBe('25小时1分1秒')
    })
  })

  describe('文件大小格式化', () => {
    it('应该正确格式化文件大小', () => {
      expect(formatFileSize(1024)).toBe('1.00 KB')
      expect(formatFileSize(1048576)).toBe('1.00 MB')
      expect(formatFileSize(1073741824)).toBe('1.00 GB')
      expect(formatFileSize(500)).toBe('500 B')
    })

    it('应该处理零值', () => {
      expect(formatFileSize(0)).toBe('0 B')
    })

    it('应该支持指定小数位数', () => {
      expect(formatFileSize(1536, 1)).toBe('1.5 KB')
      expect(formatFileSize(1536, 0)).toBe('2 KB')
    })
  })

  describe('电话号码格式化', () => {
    it('应该正确格式化手机号码', () => {
      expect(formatPhoneNumber('***********')).toBe('138-0013-8000')
      expect(formatPhoneNumber('1380013800')).toBe('138-0013-800')
    })

    it('应该处理固定电话', () => {
      expect(formatPhoneNumber('02012345678')).toBe('020-1234-5678')
    })

    it('应该处理无效号码', () => {
      expect(formatPhoneNumber('123')).toBe('123')
      expect(formatPhoneNumber('')).toBe('')
    })
  })

  describe('卡号格式化', () => {
    it('应该正确格式化银行卡号', () => {
      expect(formatCardNumber('1234567890123456')).toBe('1234 5678 9012 3456')
    })

    it('应该处理不同长度的卡号', () => {
      expect(formatCardNumber('123456789012345')).toBe('1234 5678 9012 345')
      expect(formatCardNumber('12345678901234567')).toBe('1234 5678 9012 3456 7')
    })

    it('应该处理短卡号', () => {
      expect(formatCardNumber('1234')).toBe('1234')
    })
  })

  describe('订单号格式化', () => {
    it('应该正确格式化订单号', () => {
      expect(formatOrderNumber('ORDER20231225001')).toBe('ORDER-2023-1225-001')
    })

    it('应该处理不同格式的订单号', () => {
      expect(formatOrderNumber('ORD123456')).toBe('ORD-123456')
      expect(formatOrderNumber('123456')).toBe('123456')
    })
  })

  describe('商品编码格式化', () => {
    it('应该正确格式化商品编码', () => {
      expect(formatProductCode('ABC123456')).toBe('ABC-123456')
      expect(formatProductCode('12345678')).toBe('1234-5678')
    })

    it('应该处理短编码', () => {
      expect(formatProductCode('ABC')).toBe('ABC')
    })
  })

  describe('地址格式化', () => {
    it('应该正确格式化地址', () => {
      const address = {
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        street: '科技园南区',
        detail: '腾讯大厦'
      }
      
      expect(formatAddress(address)).toBe('广东省深圳市南山区科技园南区腾讯大厦')
    })

    it('应该处理缺少字段的地址', () => {
      const address = {
        city: '深圳市',
        detail: '腾讯大厦'
      }
      
      expect(formatAddress(address)).toBe('深圳市腾讯大厦')
    })

    it('应该处理空地址', () => {
      expect(formatAddress({})).toBe('')
      expect(formatAddress(null)).toBe('')
    })
  })

  describe('文本处理', () => {
    it('应该正确截断文本', () => {
      expect(truncateText('这是一个很长的文本内容', 10)).toBe('这是一个很长的文...')
      expect(truncateText('短文本', 10)).toBe('短文本')
    })

    it('应该支持自定义省略符', () => {
      expect(truncateText('长文本内容', 4, '…')).toBe('长文本…')
    })

    it('应该首字母大写', () => {
      expect(capitalizeFirst('hello world')).toBe('Hello world')
      expect(capitalizeFirst('HELLO')).toBe('HELLO')
      expect(capitalizeFirst('')).toBe('')
    })

    it('应该格式化姓名显示', () => {
      expect(formatNameDisplay('张', '三')).toBe('张三')
      expect(formatNameDisplay('', '三')).toBe('三')
      expect(formatNameDisplay('张', '')).toBe('张')
      expect(formatNameDisplay('', '')).toBe('')
    })
  })

  describe('业务格式化', () => {
    it('应该正确格式化数量', () => {
      expect(formatQuantity(1.5, '公斤')).toBe('1.50公斤')
      expect(formatQuantity(10, '个')).toBe('10个')
      expect(formatQuantity(0, '件')).toBe('0件')
    })

    it('应该正确格式化重量', () => {
      expect(formatWeight(1500)).toBe('1.50kg')
      expect(formatWeight(500)).toBe('500g')
      expect(formatWeight(0)).toBe('0g')
    })

    it('应该正确格式化折扣', () => {
      expect(formatDiscount(0.1)).toBe('9折')
      expect(formatDiscount(0.15)).toBe('8.5折')
      expect(formatDiscount(0)).toBe('无折扣')
      expect(formatDiscount(0.5)).toBe('5折')
    })

    it('应该正确格式化税率', () => {
      expect(formatTaxRate(0.13)).toBe('13%')
      expect(formatTaxRate(0.06)).toBe('6%')
      expect(formatTaxRate(0)).toBe('免税')
    })

    it('应该正确格式化会员等级', () => {
      expect(formatMemberLevel(1)).toBe('普通会员')
      expect(formatMemberLevel(2)).toBe('银卡会员')
      expect(formatMemberLevel(3)).toBe('金卡会员')
      expect(formatMemberLevel(4)).toBe('钻石会员')
      expect(formatMemberLevel(5)).toBe('至尊会员')
      expect(formatMemberLevel(99)).toBe('未知等级')
    })

    it('应该正确格式化支付方式', () => {
      expect(formatPaymentMethod('CASH')).toBe('现金支付')
      expect(formatPaymentMethod('ALIPAY')).toBe('支付宝')
      expect(formatPaymentMethod('WECHAT')).toBe('微信支付')
      expect(formatPaymentMethod('CARD')).toBe('银行卡')
      expect(formatPaymentMethod('UNKNOWN')).toBe('其他支付')
    })

    it('应该正确格式化订单状态', () => {
      expect(formatOrderStatus('pending')).toBe('待处理')
      expect(formatOrderStatus('processing')).toBe('处理中')
      expect(formatOrderStatus('completed')).toBe('已完成')
      expect(formatOrderStatus('cancelled')).toBe('已取消')
      expect(formatOrderStatus('refunded')).toBe('已退款')
      expect(formatOrderStatus('unknown')).toBe('未知状态')
    })
  })

  describe('数据解析', () => {
    it('应该正确解析格式化的数字', () => {
      expect(parseFormattedNumber('1,234.56')).toBe(1234.56)
      expect(parseFormattedNumber('¥1,000.00')).toBe(1000)
      expect(parseFormattedNumber('$500.25')).toBe(500.25)
    })

    it('应该处理无效输入', () => {
      expect(parseFormattedNumber('invalid')).toBe(0)
      expect(parseFormattedNumber('')).toBe(0)
      expect(parseFormattedNumber(null)).toBe(0)
    })
  })

  describe('输入清理', () => {
    it('应该清理危险输入', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('alert("xss")')
      expect(sanitizeInput('正常文本')).toBe('正常文本')
      expect(sanitizeInput('SELECT * FROM users')).toBe('SELECT * FROM users')
    })

    it('应该处理空输入', () => {
      expect(sanitizeInput('')).toBe('')
      expect(sanitizeInput(null)).toBe('')
      expect(sanitizeInput(undefined)).toBe('')
    })
  })

  describe('敏感数据脱敏', () => {
    it('应该正确脱敏手机号', () => {
      expect(maskSensitiveData('***********', 'phone')).toBe('138****8000')
    })

    it('应该正确脱敏身份证号', () => {
      expect(maskSensitiveData('******************', 'idCard')).toBe('440123********1234')
    })

    it('应该正确脱敏银行卡号', () => {
      expect(maskSensitiveData('****************', 'bankCard')).toBe('6222********7890')
    })

    it('应该正确脱敏邮箱', () => {
      expect(maskSensitiveData('<EMAIL>', 'email')).toBe('te**@example.com')
    })

    it('应该处理默认脱敏', () => {
      expect(maskSensitiveData('sensitive data')).toBe('sen***data')
    })

    it('应该处理短数据', () => {
      expect(maskSensitiveData('abc', 'phone')).toBe('***')
      expect(maskSensitiveData('', 'phone')).toBe('')
    })
  })

  describe('边界情况', () => {
    it('应该处理极大数值', () => {
      expect(formatCurrency(Number.MAX_SAFE_INTEGER)).toContain('¥')
    })

    it('应该处理极小数值', () => {
      expect(formatCurrency(0.001)).toBe('¥0.00')
    })

    it('应该处理特殊字符', () => {
      expect(sanitizeInput('测试<>&"\'文本')).toBe('测试<>&"\'文本')
    })

    it('应该处理Unicode字符', () => {
      expect(truncateText('🎉🎊🎈', 2)).toBe('🎉🎊...')
    })
  })

  describe('性能测试', () => {
    it('应该快速处理大量数据', () => {
      const start = Date.now()
      
      for (let i = 0; i < 1000; i++) {
        formatCurrency(Math.random() * 10000)
        formatNumber(Math.random() * 1000000)
        formatPercentage(Math.random())
      }
      
      const end = Date.now()
      expect(end - start).toBeLessThan(100) // 应该在100ms内完成
    })
  })

  describe('国际化支持', () => {
    it('应该支持不同语言环境', () => {
      // 这里可以测试不同语言环境下的格式化
      // 实际实现中可能需要i18n库支持
      expect(formatCurrency(1000, '¥')).toBe('¥1,000.00')
      expect(formatCurrency(1000, '$')).toBe('$1,000.00')
    })
  })
})