<template>
  <div class="member-search">
    <div class="search-methods">
      <a-tabs v-model:activeKey="searchMethod" size="small" class="search-tabs">
        <a-tab-pane key="card" tab="会员卡">
          <div class="card-input">
            <a-input-search
              v-model:value="memberCardNo"
              placeholder="请输入会员卡号或扫描会员卡"
              size="large"
              @search="handleCardSearch"
              @pressEnter="handleCardSearch"
              :loading="searching"
              allowClear
              class="card-search-input"
            >
              <template #prefix>
                <icon-font iconClass="icon-card" />
              </template>
              <template #enterButton>
                <a-button type="primary">
                  <template #icon>
                    <search-outlined />
                  </template>
                  查询
                </a-button>
              </template>
            </a-input-search>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="phone" tab="手机号">
          <div class="phone-input">
            <a-input-search
              v-model:value="memberPhone"
              placeholder="请输入会员手机号"
              size="large"
              @search="handlePhoneSearch"
              @pressEnter="handlePhoneSearch"
              :loading="searching"
              allowClear
              class="phone-search-input"
            >
              <template #prefix>
                <mobile-outlined />
              </template>
              <template #enterButton>
                <a-button type="primary">
                  <template #icon>
                    <search-outlined />
                  </template>
                  查询
                </a-button>
              </template>
            </a-input-search>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 搜索结果为空 -->
    <div class="no-member-found" v-if="searchAttempted && !searching">
      <a-empty 
        description="未找到会员信息"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <p class="search-tip">请检查会员卡号或手机号是否正确</p>
      </a-empty>
    </div>

    <!-- 错误提示 -->
    <div class="error-message" v-if="errorMessage">
      <a-alert
        :message="errorMessage"
        type="error"
        show-icon
        closable
        @close="clearError"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { SearchOutlined, MobileOutlined } from '@ant-design/icons-vue'
import { MemberApi } from '../../api'

// 定义组件名称
defineOptions({
  name: 'MemberSearch'
})

// 定义事件
const emit = defineEmits(['memberFound', 'searchError'])

// 响应式数据
const searchMethod = ref('card')
const memberCardNo = ref('')
const memberPhone = ref('')
const searching = ref(false)
const searchAttempted = ref(false)
const errorMessage = ref('')

/**
 * 处理会员卡搜索
 */
const handleCardSearch = async () => {
  if (!memberCardNo.value.trim()) {
    message.warning('请输入会员卡号')
    return
  }
  
  await searchMember('card', memberCardNo.value.trim())
}

/**
 * 处理手机号搜索
 */
const handlePhoneSearch = async () => {
  if (!memberPhone.value.trim()) {
    message.warning('请输入手机号')
    return
  }
  
  await searchMember('phone', memberPhone.value.trim())
}

/**
 * 搜索会员
 */
const searchMember = async (type, value) => {
  try {
    searching.value = true
    searchAttempted.value = true
    errorMessage.value = ''
    
    let member
    if (type === 'card') {
      member = await MemberApi.getMemberByCardNo(value)
    } else {
      member = await MemberApi.getMemberByPhone(value)
    }
    
    if (member) {
      emit('memberFound', member)
      // 清空搜索输入
      memberCardNo.value = ''
      memberPhone.value = ''
      searchAttempted.value = false
    } else {
      message.warning('未找到会员信息')
    }
    
  } catch (error) {
    console.error('搜索会员失败:', error)
    errorMessage.value = error.message || '搜索会员失败，请重试'
    emit('searchError', error)
  } finally {
    searching.value = false
  }
}

/**
 * 清除错误信息
 */
const clearError = () => {
  errorMessage.value = ''
}

/**
 * 重置搜索状态
 */
const resetSearch = () => {
  memberCardNo.value = ''
  memberPhone.value = ''
  searchAttempted.value = false
  errorMessage.value = ''
  searching.value = false
}

// 暴露方法给父组件
defineExpose({
  resetSearch,
  searchMember
})
</script>

<style scoped>
.member-search {
  padding: 16px 0;
}

.search-tabs {
  margin-bottom: 16px;
}

.search-tabs :deep(.ant-tabs-content-holder) {
  padding-top: 12px;
}

.card-input,
.phone-input {
  margin-bottom: 16px;
}

.card-search-input,
.phone-search-input {
  width: 100%;
}

.no-member-found {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.search-tip {
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

.error-message {
  margin-top: 16px;
}
</style>