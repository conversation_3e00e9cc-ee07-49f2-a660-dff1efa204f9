/**
 * API集成测试
 * 
 * 测试各个API模块之间的集成和数据流
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { CartApi } from '../../api/cart'
import { MemberApi } from '../../api/member'
import { PaymentApi } from '../../api/payment'
import { OrderApi } from '../../api/order'
import { ProductApi } from '../../api/product'

// Mock Request
const mockRequest = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn()
}

vi.mock('@/utils/request/request-util', () => ({
  default: mockRequest
}))

describe('API集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('商品-购物车-订单集成流程', () => {
    it('应该完成从商品搜索到订单创建的完整流程', async () => {
      // 1. 搜索商品
      const mockProducts = [
        {
          id: 'P001',
          name: '红苹果',
          price: 5.5,
          stock: 100,
          barcode: '1234567890123'
        }
      ]
      
      mockRequest.get.mockResolvedValueOnce(mockProducts)
      const searchResult = await ProductApi.searchProducts({ keyword: '苹果' })
      
      expect(mockRequest.get).toHaveBeenCalledWith('/erp/pos/product/search', {
        keyword: '苹果',
        limit: 50
      })
      expect(searchResult).toEqual(mockProducts)
      
      // 2. 检查库存
      mockRequest.post.mockResolvedValueOnce({
        available: true,
        stock: 100,
        productId: 'P001'
      })
      
      const stockResult = await CartApi.checkInventory('P001', 2)
      
      expect(mockRequest.post).toHaveBeenCalledWith('/erp/pos/product/checkStock', {
        productId: 'P001',
        quantity: 2
      })
      expect(stockResult.available).toBe(true)
      
      // 3. 验证购物车
      const cartData = {
        items: [
          {
            id: 'P001',
            name: '红苹果',
            price: 5.5,
            quantity: 2,
            subtotal: 11
          }
        ]
      }
      
      mockRequest.post.mockResolvedValueOnce({
        valid: true,
        errors: []
      })
      
      const cartValidation = await CartApi.validateCart(cartData)
      
      expect(mockRequest.post).toHaveBeenCalledWith('/erp/pos/cart/validate', cartData)
      expect(cartValidation.valid).toBe(true)
      
      // 4. 创建订单
      const orderData = {
        items: cartData.items,
        totalAmount: 11,
        discountAmount: 0,
        finalAmount: 11,
        cashierId: 'CASHIER001'
      }
      
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        orderId: 'ORDER001',
        orderNo: 'POS20250102001'
      })
      
      const orderResult = await OrderApi.createOrder(orderData)
      
      expect(mockRequest.post).toHaveBeenCalledWith('/erp/pos/order/create', {
        ...orderData,
        createTime: expect.any(String)
      })
      expect(orderResult.success).toBe(true)
    })
    
    it('应该处理库存不足的情况', async () => {
      // 1. 检查库存 - 库存不足
      mockRequest.post.mockResolvedValueOnce({
        available: false,
        stock: 1,
        productId: 'P001'
      })
      
      const stockResult = await CartApi.checkInventory('P001', 5)
      
      expect(stockResult.available).toBe(false)
      expect(stockResult.stock).toBe(1)
      
      // 2. 由于库存不足，不应该创建订单
      // 这里模拟前端逻辑，不会调用订单创建API
      expect(mockRequest.post).toHaveBeenCalledTimes(1) // 只调用了库存检查
    })
  })
  
  describe('会员-折扣-支付集成流程', () => {
    it('应该完成会员折扣计算和支付的完整流程', async () => {
      // 1. 搜索会员
      const mockMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        discountRate: 0.1,
        points: 1000,
        balance: 500,
        status: 'ACTIVE'
      }
      
      mockRequest.get.mockResolvedValueOnce(mockMember)
      const memberResult = await MemberApi.searchMember({ cardNo: 'VIP123456' })
      
      expect(mockRequest.get).toHaveBeenCalledWith('/erp/pos/member/search', {
        cardNo: 'VIP123456'
      })
      expect(memberResult).toEqual(mockMember)
      
      // 2. 计算购物车总金额（包含会员折扣）
      const cartData = {
        items: [
          { id: 'P001', name: '红苹果', price: 5.5, quantity: 2, subtotal: 11 }
        ],
        member: mockMember
      }
      
      mockRequest.post.mockResolvedValueOnce({
        subtotal: 11,
        discountAmount: 1.1,
        finalAmount: 9.9,
        breakdown: {
          memberDiscount: 1.1,
          couponDiscount: 0
        }
      })
      
      const totalResult = await CartApi.calculateCartTotal(cartData)
      
      expect(mockRequest.post).toHaveBeenCalledWith('/erp/pos/cart/calculate', cartData)
      expect(totalResult.finalAmount).toBe(9.9)
      expect(totalResult.discountAmount).toBe(1.1)
      
      // 3. 创建订单
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        orderId: 'ORDER002',
        orderNo: 'POS20250102002'
      })
      
      const orderData = {
        items: cartData.items,
        member: mockMember,
        totalAmount: 11,
        discountAmount: 1.1,
        finalAmount: 9.9,
        cashierId: 'CASHIER001'
      }
      
      const orderResult = await OrderApi.createOrder(orderData)
      expect(orderResult.success).toBe(true)
      
      // 4. 处理会员卡支付
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        paymentId: 'PAY002',
        remainingBalance: 490.1,
        status: 'SUCCESS'
      })
      
      const paymentResult = await PaymentApi.processMemberCardPayment({
        orderId: 'ORDER002',
        paymentAmount: 9.9,
        memberId: 'M001',
        memberCardNo: 'VIP123456',
        cashierId: 'CASHIER001'
      })
      
      expect(paymentResult.success).toBe(true)
      expect(paymentResult.remainingBalance).toBe(490.1)
      
      // 5. 更新会员积分
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        oldPoints: 1000,
        newPoints: 1009, // 消费9.9元获得9积分
        changePoints: 9
      })
      
      const pointsResult = await MemberApi.updateMemberPoints({
        memberId: 'M001',
        changeType: 'EARN',
        points: 9,
        reason: '消费获得',
        orderId: 'ORDER002'
      })
      
      expect(pointsResult.success).toBe(true)
      expect(pointsResult.newPoints).toBe(1009)
    })
    
    it('应该处理会员余额不足的情况', async () => {
      const mockMember = {
        id: 'M001',
        balance: 5 // 余额不足
      }
      
      // 尝试使用会员卡支付100元
      mockRequest.post.mockRejectedValueOnce(new Error('会员余额不足'))
      
      await expect(PaymentApi.processMemberCardPayment({
        orderId: 'ORDER003',
        paymentAmount: 100,
        memberId: 'M001',
        memberCardNo: 'VIP123456'
      })).rejects.toThrow('会员余额不足')
    })
  })
  
  describe('支付-订单状态集成流程', () => {
    it('应该完成扫码支付状态查询的完整流程', async () => {
      // 1. 发起扫码支付
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        paymentId: 'PAY003',
        qrCodeUrl: 'https://qr.weixin.com/test',
        status: 'PENDING'
      })
      
      const paymentResult = await PaymentApi.processQrCodePayment({
        orderId: 'ORDER004',
        paymentAmount: 50,
        paymentMethod: 'WECHAT',
        cashierId: 'CASHIER001'
      })
      
      expect(paymentResult.status).toBe('PENDING')
      expect(paymentResult.paymentId).toBe('PAY003')
      
      // 2. 查询支付状态 - 仍在等待
      mockRequest.get.mockResolvedValueOnce({
        paymentId: 'PAY003',
        status: 'PENDING'
      })
      
      let statusResult = await PaymentApi.queryQrCodePaymentStatus({
        paymentId: 'PAY003'
      })
      
      expect(statusResult.status).toBe('PENDING')
      
      // 3. 再次查询支付状态 - 支付成功
      mockRequest.get.mockResolvedValueOnce({
        paymentId: 'PAY003',
        status: 'SUCCESS',
        transactionId: 'WX_TXN_001',
        paidAmount: 50,
        paidTime: '2025-01-02T10:30:00Z'
      })
      
      statusResult = await PaymentApi.queryQrCodePaymentStatus({
        paymentId: 'PAY003'
      })
      
      expect(statusResult.status).toBe('SUCCESS')
      expect(statusResult.transactionId).toBe('WX_TXN_001')
      
      // 4. 更新订单状态
      mockRequest.put.mockResolvedValueOnce({
        success: true,
        orderId: 'ORDER004',
        status: 'PAID',
        updatedAt: '2025-01-02T10:30:00Z'
      })
      
      const orderUpdateResult = await OrderApi.updateOrderStatus({
        orderId: 'ORDER004',
        status: 'PAID',
        updatedBy: 'CASHIER001',
        remark: '支付完成'
      })
      
      expect(orderUpdateResult.success).toBe(true)
      expect(orderUpdateResult.status).toBe('PAID')
    })
    
    it('应该处理支付超时的情况', async () => {
      // 1. 发起支付
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        paymentId: 'PAY004',
        status: 'PENDING'
      })
      
      await PaymentApi.processQrCodePayment({
        orderId: 'ORDER005',
        paymentAmount: 30,
        paymentMethod: 'ALIPAY'
      })
      
      // 2. 查询支付状态 - 超时
      mockRequest.get.mockResolvedValueOnce({
        paymentId: 'PAY004',
        status: 'TIMEOUT'
      })
      
      const statusResult = await PaymentApi.queryQrCodePaymentStatus({
        paymentId: 'PAY004'
      })
      
      expect(statusResult.status).toBe('TIMEOUT')
      
      // 3. 取消支付
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        cancelled: true,
        cancelTime: '2025-01-02T10:35:00Z'
      })
      
      const cancelResult = await PaymentApi.cancelPayment({
        paymentId: 'PAY004',
        cancelReason: '支付超时',
        cancelBy: 'CASHIER001'
      })
      
      expect(cancelResult.success).toBe(true)
      expect(cancelResult.cancelled).toBe(true)
    })
  })
  
  describe('批量操作集成测试', () => {
    it('应该完成批量库存检查', async () => {
      const items = [
        { productId: 'P001', quantity: 2 },
        { productId: 'P002', quantity: 3 },
        { productId: 'P003', quantity: 1 }
      ]
      
      mockRequest.post.mockResolvedValueOnce([
        { productId: 'P001', available: true, stock: 10 },
        { productId: 'P002', available: false, stock: 1 },
        { productId: 'P003', available: true, stock: 5 }
      ])
      
      const batchResult = await CartApi.batchCheckInventory(items)
      
      expect(mockRequest.post).toHaveBeenCalledWith('/erp/pos/product/batchCheckStock', {
        items
      })
      
      expect(batchResult).toHaveLength(3)
      expect(batchResult[0].available).toBe(true)
      expect(batchResult[1].available).toBe(false)
      expect(batchResult[2].available).toBe(true)
    })
    
    it('应该完成批量订单更新', async () => {
      const batchParams = {
        orderIds: ['ORDER006', 'ORDER007', 'ORDER008'],
        updates: {
          status: 'COMPLETED',
          updatedBy: 'CASHIER001'
        }
      }
      
      mockRequest.put.mockResolvedValueOnce({
        success: true,
        updatedCount: 3,
        results: [
          { orderId: 'ORDER006', success: true },
          { orderId: 'ORDER007', success: true },
          { orderId: 'ORDER008', success: true }
        ]
      })
      
      const batchResult = await OrderApi.batchUpdateOrders(batchParams)
      
      expect(mockRequest.put).toHaveBeenCalledWith('/erp/pos/order/batchUpdate', {
        ...batchParams,
        updateTime: expect.any(String)
      })
      
      expect(batchResult.success).toBe(true)
      expect(batchResult.updatedCount).toBe(3)
    })
  })
  
  describe('错误处理集成测试', () => {
    it('应该正确处理网络错误', async () => {
      mockRequest.get.mockRejectedValue(new Error('Network Error'))
      
      await expect(ProductApi.getProductList({})).rejects.toThrow('Network Error')
    })
    
    it('应该正确处理业务错误', async () => {
      mockRequest.post.mockRejectedValue({
        code: 'INSUFFICIENT_INVENTORY',
        message: '库存不足'
      })
      
      await expect(CartApi.checkInventory('P001', 100)).rejects.toMatchObject({
        code: 'INSUFFICIENT_INVENTORY',
        message: '库存不足'
      })
    })
    
    it('应该正确处理认证错误', async () => {
      mockRequest.get.mockRejectedValue({
        status: 401,
        message: 'Unauthorized'
      })
      
      await expect(MemberApi.searchMember({ cardNo: 'VIP123456' }))
        .rejects.toMatchObject({
          status: 401,
          message: 'Unauthorized'
        })
    })
  })
  
  describe('数据一致性验证', () => {
    it('应该确保订单金额计算的一致性', async () => {
      // 1. 计算购物车总金额
      const cartData = {
        items: [
          { id: 'P001', price: 10, quantity: 2, subtotal: 20 },
          { id: 'P002', price: 5, quantity: 3, subtotal: 15 }
        ],
        member: { discountRate: 0.1 }
      }
      
      mockRequest.post.mockResolvedValueOnce({
        subtotal: 35,
        discountAmount: 3.5,
        finalAmount: 31.5
      })
      
      const cartTotal = await CartApi.calculateCartTotal(cartData)
      
      // 2. 创建订单时使用相同的金额
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        orderId: 'ORDER009'
      })
      
      const orderData = {
        items: cartData.items,
        member: cartData.member,
        totalAmount: cartTotal.subtotal,
        discountAmount: cartTotal.discountAmount,
        finalAmount: cartTotal.finalAmount
      }
      
      await OrderApi.createOrder(orderData)
      
      // 验证订单创建时使用的金额与购物车计算的金额一致
      expect(mockRequest.post).toHaveBeenLastCalledWith('/erp/pos/order/create', {
        ...orderData,
        createTime: expect.any(String)
      })
    })
    
    it('应该确保支付金额与订单金额一致', async () => {
      const orderAmount = 99.99
      
      // 1. 创建订单
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        orderId: 'ORDER010',
        finalAmount: orderAmount
      })
      
      await OrderApi.createOrder({
        items: [{ id: 'P001', price: 99.99, quantity: 1, subtotal: 99.99 }],
        totalAmount: orderAmount,
        finalAmount: orderAmount
      })
      
      // 2. 支付时使用相同金额
      mockRequest.post.mockResolvedValueOnce({
        success: true,
        paymentId: 'PAY010'
      })
      
      await PaymentApi.processCashPayment({
        orderId: 'ORDER010',
        paymentAmount: orderAmount,
        receivedAmount: 100,
        changeAmount: 0.01
      })
      
      // 验证支付金额与订单金额一致
      expect(mockRequest.post).toHaveBeenLastCalledWith('/erp/pos/payment/cash', {
        orderId: 'ORDER010',
        paymentAmount: orderAmount,
        receivedAmount: 100,
        changeAmount: 0.01,
        paymentMethod: 'CASH',
        paymentTime: expect.any(String)
      })
    })
  })
})