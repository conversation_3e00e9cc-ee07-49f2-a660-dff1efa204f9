import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { usePaymentStore } from '../payment'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    loading: vi.fn(),
    destroy: vi.fn()
  }
}))

describe('usePaymentStore', () => {
  let pinia
  let paymentStore
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    paymentStore = usePaymentStore()
    vi.clearAllMocks()
    vi.useFakeTimers()
  })
  
  afterEach(() => {
    paymentStore.cleanup()
    vi.useRealTimers()
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(paymentStore.paymentStatus).toBe('idle')
      expect(paymentStore.selectedPaymentMethod).toBe('')
      expect(paymentStore.receivedAmount).toBe(0)
      expect(paymentStore.changeAmount).toBe(0)
      expect(paymentStore.paymentResult).toBe(null)
      expect(paymentStore.paymentTimeout).toBe(300)
      expect(paymentStore.paymentStartTime).toBe(null)
    })

    it('应该有正确的计算属性初始值', () => {
      expect(paymentStore.isPaymentProcessing).toBe(false)
      expect(paymentStore.isPaymentSuccess).toBe(false)
      expect(paymentStore.isPaymentFailed).toBe(false)
      expect(paymentStore.canStartPayment).toBe(false)
      expect(paymentStore.currentPaymentMethod).toBe(null)
      expect(paymentStore.isCashPayment).toBe(false)
      expect(paymentStore.paymentRemainingTime).toBe(300)
      expect(paymentStore.paymentProgress).toBe(0)
    })

    it('应该有默认的支付方式配置', () => {
      expect(paymentStore.paymentMethods).toHaveLength(5)
      expect(paymentStore.paymentMethods[0].code).toBe('CASH')
      expect(paymentStore.paymentMethods[0].enabled).toBe(true)
    })
  })

  describe('设置支付方式', () => {
    it('应该能够设置有效的支付方式', () => {
      const result = paymentStore.setPaymentMethod('CASH')
      
      expect(result).toBe(true)
      expect(paymentStore.selectedPaymentMethod).toBe('CASH')
      expect(paymentStore.isCashPayment).toBe(true)
      expect(paymentStore.canStartPayment).toBe(true)
    })

    it('应该能够设置非现金支付方式', () => {
      const result = paymentStore.setPaymentMethod('ALIPAY')
      
      expect(result).toBe(true)
      expect(paymentStore.selectedPaymentMethod).toBe('ALIPAY')
      expect(paymentStore.isCashPayment).toBe(false)
      expect(paymentStore.receivedAmount).toBe(0)
      expect(paymentStore.changeAmount).toBe(0)
    })

    it('应该拒绝无效的支付方式', () => {
      const result = paymentStore.setPaymentMethod('INVALID_METHOD')
      
      expect(result).toBe(false)
      expect(paymentStore.selectedPaymentMethod).toBe('')
    })

    it('应该拒绝被禁用的支付方式', () => {
      // 禁用支付宝
      paymentStore.togglePaymentMethod('ALIPAY', false)
      
      const result = paymentStore.setPaymentMethod('ALIPAY')
      
      expect(result).toBe(false)
      expect(paymentStore.selectedPaymentMethod).toBe('')
    })

    it('应该获取当前支付方式信息', () => {
      paymentStore.setPaymentMethod('WECHAT')
      
      const method = paymentStore.currentPaymentMethod
      
      expect(method).toBeTruthy()
      expect(method.code).toBe('WECHAT')
      expect(method.name).toBe('微信支付')
    })
  })

  describe('现金支付', () => {
    beforeEach(() => {
      paymentStore.setPaymentMethod('CASH')
    })

    it('应该能够设置实收金额', () => {
      const result = paymentStore.setReceivedAmount(100, 80)
      
      expect(result).toBe(true)
      expect(paymentStore.receivedAmount).toBe(100)
      expect(paymentStore.changeAmount).toBe(20)
    })

    it('应该处理实收金额不足的情况', () => {
      const result = paymentStore.setReceivedAmount(50, 80)
      
      expect(result).toBe(true)
      expect(paymentStore.receivedAmount).toBe(50)
      expect(paymentStore.changeAmount).toBe(0)
    })

    it('应该拒绝负数实收金额', () => {
      const result = paymentStore.setReceivedAmount(-10, 80)
      
      expect(result).toBe(false)
    })

    it('应该在非现金支付时拒绝设置实收金额', () => {
      paymentStore.setPaymentMethod('ALIPAY')
      
      const result = paymentStore.setReceivedAmount(100, 80)
      
      expect(result).toBe(false)
    })

    it('应该验证现金支付的有效性', () => {
      paymentStore.setReceivedAmount(100, 80)
      expect(paymentStore.isCashPaymentValid).toBe(true)
      
      paymentStore.setReceivedAmount(50, 80)
      expect(paymentStore.isCashPaymentValid).toBe(false)
    })
  })

  describe('支付流程', () => {
    beforeEach(() => {
      paymentStore.setPaymentMethod('ALIPAY')
    })

    it('应该能够开始支付', () => {
      const paymentData = { amount: 100, orderId: 'ORDER123' }
      const result = paymentStore.startPayment(paymentData)
      
      expect(result).toBe(true)
      expect(paymentStore.paymentStatus).toBe('processing')
      expect(paymentStore.isPaymentProcessing).toBe(true)
      expect(paymentStore.paymentStartTime).toBeTruthy()
    })

    it('应该在没有选择支付方式时拒绝开始支付', () => {
      paymentStore.selectedPaymentMethod = ''
      
      const result = paymentStore.startPayment({ amount: 100 })
      
      expect(result).toBe(false)
      expect(paymentStore.paymentStatus).toBe('idle')
    })

    it('应该在现金支付金额不足时拒绝开始支付', () => {
      paymentStore.setPaymentMethod('CASH')
      paymentStore.setReceivedAmount(50, 100) // 实收不足
      
      const result = paymentStore.startPayment({ amount: 100 })
      
      expect(result).toBe(false)
    })

    it('应该能够处理支付成功', () => {
      paymentStore.startPayment({ amount: 100 })
      
      const paymentResult = {
        transactionId: 'TXN123456',
        amount: 100,
        timestamp: Date.now()
      }
      
      paymentStore.paymentSuccess(paymentResult)
      
      expect(paymentStore.paymentStatus).toBe('success')
      expect(paymentStore.isPaymentSuccess).toBe(true)
      expect(paymentStore.paymentResult.transactionId).toBe('TXN123456')
      expect(paymentStore.paymentResult.success).toBe(true)
    })

    it('应该能够处理支付失败', () => {
      paymentStore.startPayment({ amount: 100 })
      
      paymentStore.paymentFailed('网络连接失败', { code: 'NETWORK_ERROR' })
      
      expect(paymentStore.paymentStatus).toBe('failed')
      expect(paymentStore.isPaymentFailed).toBe(true)
      expect(paymentStore.paymentResult.success).toBe(false)
      expect(paymentStore.paymentResult.errorMessage).toBe('网络连接失败')
    })

    it('应该能够取消支付', () => {
      paymentStore.startPayment({ amount: 100 })
      expect(paymentStore.isPaymentProcessing).toBe(true)
      
      paymentStore.cancelPayment()
      
      expect(paymentStore.paymentStatus).toBe('idle')
      expect(paymentStore.paymentStartTime).toBe(null)
      expect(paymentStore.paymentResult).toBe(null)
    })

    it('应该能够重试支付', () => {
      // 先开始支付并失败
      paymentStore.startPayment({ amount: 100 })
      paymentStore.paymentFailed('支付失败')
      
      // 重试支付
      const result = paymentStore.retryPayment({ amount: 100 })
      
      expect(result).toBe(true)
      expect(paymentStore.paymentStatus).toBe('processing')
      expect(paymentStore.selectedPaymentMethod).toBe('ALIPAY') // 保持原支付方式
    })

    it('应该在支付进行中时拒绝重试', () => {
      paymentStore.startPayment({ amount: 100 })
      
      const result = paymentStore.retryPayment({ amount: 100 })
      
      expect(result).toBe(false)
    })
  })

  describe('支付超时', () => {
    beforeEach(() => {
      paymentStore.setPaymentMethod('ALIPAY')
    })

    it('应该在超时后自动失败', () => {
      paymentStore.startPayment({ amount: 100 })
      
      // 快进时间到超时
      vi.advanceTimersByTime(300000) // 5分钟
      
      expect(paymentStore.paymentStatus).toBe('failed')
      expect(paymentStore.paymentResult.errorMessage).toBe('支付超时，请重试')
    })

    it('应该能够设置支付超时时间', () => {
      const result = paymentStore.setPaymentTimeout(600) // 10分钟
      
      expect(result).toBe(true)
      expect(paymentStore.paymentTimeout).toBe(600)
    })

    it('应该拒绝无效的超时时间', () => {
      const result = paymentStore.setPaymentTimeout(0)
      
      expect(result).toBe(false)
      expect(paymentStore.paymentTimeout).toBe(300) // 保持原值
    })

    it('应该正确计算剩余时间', () => {
      paymentStore.startPayment({ amount: 100 })
      
      // 快进30秒
      vi.advanceTimersByTime(30000)
      
      expect(paymentStore.paymentRemainingTime).toBe(270) // 300 - 30
    })

    it('应该正确计算支付进度', () => {
      paymentStore.startPayment({ amount: 100 })
      
      // 快进60秒
      vi.advanceTimersByTime(60000)
      
      expect(paymentStore.paymentProgress).toBe(20) // 60/300 * 100
    })
  })

  describe('支付方式管理', () => {
    it('应该能够设置支付方式配置', () => {
      const newMethods = [
        { code: 'CASH', name: '现金', enabled: true },
        { code: 'CARD', name: '银行卡', enabled: false }
      ]
      
      paymentStore.setPaymentMethods(newMethods)
      
      expect(paymentStore.paymentMethods).toEqual(newMethods)
    })

    it('应该在设置新配置时清除无效的选择', () => {
      paymentStore.setPaymentMethod('ALIPAY')
      
      const newMethods = [
        { code: 'CASH', name: '现金', enabled: true }
      ]
      
      paymentStore.setPaymentMethods(newMethods)
      
      expect(paymentStore.selectedPaymentMethod).toBe('')
    })

    it('应该能够启用/禁用支付方式', () => {
      paymentStore.setPaymentMethod('ALIPAY')
      
      paymentStore.togglePaymentMethod('ALIPAY', false)
      
      const method = paymentStore.paymentMethods.find(m => m.code === 'ALIPAY')
      expect(method.enabled).toBe(false)
      expect(paymentStore.selectedPaymentMethod).toBe('')
    })

    it('应该处理无效的配置数据', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      paymentStore.setPaymentMethods('invalid data')
      
      // 应该保持原有配置
      expect(paymentStore.paymentMethods).toHaveLength(5)
      
      consoleSpy.mockRestore()
    })
  })

  describe('状态重置', () => {
    beforeEach(() => {
      paymentStore.setPaymentMethod('CASH')
      paymentStore.setReceivedAmount(100, 80)
      paymentStore.startPayment({ amount: 80 })
      paymentStore.paymentSuccess({ transactionId: 'TXN123' })
    })

    it('应该能够重置支付状态', () => {
      paymentStore.resetPaymentStatus()
      
      expect(paymentStore.paymentStatus).toBe('idle')
      expect(paymentStore.selectedPaymentMethod).toBe('')
      expect(paymentStore.receivedAmount).toBe(0)
      expect(paymentStore.changeAmount).toBe(0)
      expect(paymentStore.paymentResult).toBe(null)
      expect(paymentStore.paymentStartTime).toBe(null)
    })
  })

  describe('数据快照', () => {
    beforeEach(() => {
      paymentStore.setPaymentMethod('CASH')
      paymentStore.setReceivedAmount(100, 80)
    })

    it('应该能够获取支付数据快照', () => {
      const snapshot = paymentStore.getPaymentSnapshot()
      
      expect(snapshot).toEqual({
        paymentStatus: 'idle',
        selectedPaymentMethod: 'CASH',
        receivedAmount: 100,
        changeAmount: 20,
        paymentResult: null,
        paymentStartTime: null,
        timestamp: expect.any(Number)
      })
    })

    it('应该能够从快照恢复支付数据', () => {
      const snapshot = paymentStore.getPaymentSnapshot()
      
      // 重置状态
      paymentStore.resetPaymentStatus()
      expect(paymentStore.selectedPaymentMethod).toBe('')
      
      // 从快照恢复
      const result = paymentStore.restoreFromSnapshot(snapshot)
      
      expect(result).toBe(true)
      expect(paymentStore.selectedPaymentMethod).toBe('CASH')
      expect(paymentStore.receivedAmount).toBe(100)
      expect(paymentStore.changeAmount).toBe(20)
    })

    it('应该跳过恢复处理中的支付状态', () => {
      const processingSnapshot = {
        paymentStatus: 'processing',
        selectedPaymentMethod: 'ALIPAY',
        paymentStartTime: Date.now()
      }
      
      const result = paymentStore.restoreFromSnapshot(processingSnapshot)
      
      expect(result).toBe(true)
      expect(paymentStore.paymentStatus).toBe('idle') // 不应该恢复为processing
    })

    it('应该处理无效的快照', () => {
      const result = paymentStore.restoreFromSnapshot(null)
      
      expect(result).toBe(false)
    })
  })

  describe('统计信息', () => {
    beforeEach(() => {
      paymentStore.setPaymentMethod('ALIPAY')
      paymentStore.startPayment({ amount: 100 })
    })

    it('应该能够获取支付统计信息', () => {
      const stats = paymentStore.getPaymentStatistics()
      
      expect(stats).toEqual({
        currentStatus: 'processing',
        selectedMethod: 'ALIPAY',
        isProcessing: true,
        remainingTime: 300,
        progress: 0,
        availableMethods: 5, // 所有方式都启用
        totalMethods: 5
      })
    })

    it('应该正确统计可用支付方式', () => {
      paymentStore.togglePaymentMethod('ALIPAY', false)
      paymentStore.togglePaymentMethod('WECHAT', false)
      
      const stats = paymentStore.getPaymentStatistics()
      
      expect(stats.availableMethods).toBe(3) // 5 - 2
      expect(stats.totalMethods).toBe(5)
    })
  })

  describe('错误处理', () => {
    it('应该处理支付成功时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟错误
      const originalAssign = Object.assign
      Object.assign = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      paymentStore.setPaymentMethod('ALIPAY')
      paymentStore.startPayment({ amount: 100 })
      
      // 不应该抛出错误
      paymentStore.paymentSuccess({ transactionId: 'TXN123' })
      
      // 恢复原始方法
      Object.assign = originalAssign
      consoleSpy.mockRestore()
    })

    it('应该处理支付失败时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      paymentStore.setPaymentMethod('ALIPAY')
      paymentStore.startPayment({ amount: 100 })
      
      // 不应该抛出错误
      paymentStore.paymentFailed('支付失败')
      
      expect(paymentStore.paymentStatus).toBe('failed')
      
      consoleSpy.mockRestore()
    })

    it('应该处理设置支付方式时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟查找方法时的错误
      const originalFind = paymentStore.paymentMethods.find
      paymentStore.paymentMethods.find = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const result = paymentStore.setPaymentMethod('CASH')
      
      expect(result).toBe(false)
      
      // 恢复原始方法
      paymentStore.paymentMethods.find = originalFind
      consoleSpy.mockRestore()
    })
  })

  describe('资源清理', () => {
    it('应该能够清理定时器资源', () => {
      paymentStore.setPaymentMethod('ALIPAY')
      paymentStore.startPayment({ amount: 100 })
      
      // 应该有定时器在运行
      expect(paymentStore.paymentStartTime).toBeTruthy()
      
      paymentStore.cleanup()
      
      // 快进时间，支付不应该超时失败（因为定时器已清理）
      const originalStatus = paymentStore.paymentStatus
      vi.advanceTimersByTime(400000)
      
      expect(paymentStore.paymentStatus).toBe(originalStatus)
    })
  })
})