<template>
  <div class="pos-main" :class="{ 'fullscreen': isFullscreen }">
    <!-- 顶部工具栏 -->
    <div class="pos-toolbar">
      <div class="toolbar-left">
        <div class="pos-logo">
          <icon-font iconClass="icon-pos" />
          <span class="logo-text">POS收银系统2</span>
        </div>
        <div class="cashier-info">
          <span class="cashier-name">收银员: {{ currentUser.realName }}</span>
          <span class="work-time">{{ currentTime }}</span>
        </div>
      </div>
      
      <div class="toolbar-right">
        <a-space>
          <!-- 快捷功能按钮 -->
          <a-tooltip title="快捷键: F1">
            <a-button @click="showHelp" class="toolbar-btn">
              <template #icon>
                <question-circle-outlined />
              </template>
              帮助
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="快捷键: F11">
            <a-button @click="toggleFullscreen" class="toolbar-btn">
              <template #icon>
                <fullscreen-outlined v-if="!isFullscreen" />
                <fullscreen-exit-outlined v-else />
              </template>
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="快捷键: Ctrl+R">
            <a-button @click="resetAll" class="toolbar-btn">
              <template #icon>
                <reload-outlined />
              </template>
              重置
            </a-button>
          </a-tooltip>
          
          <a-button @click="logout" type="primary" danger class="logout-btn">
            <template #icon>
              <logout-outlined />
            </template>
            退出登录
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pos-content">
      <!-- 左侧区域：商品展示区域 (75%宽度) -->
      <div class="pos-left pos-card">
        <ProductDisplayArea
          @productAdd="handleProductAdd"
          @productSelect="handleProductSelect"
        />
      </div>

      <!-- 中间区域：购物车区域 -->
      <div class="pos-center pos-card">
        <!-- 购物车组件 - 已选商品列表 -->
        <div class="cart-section">
          <ShoppingCart
            @cartChange="handleCartChange"
            @cartClear="handleCartClear"
            @checkout="handleCheckout"
          />
        </div>
      </div>

      <!-- 右侧区域：功能操作区域 -->
      <div class="pos-right">
        <ToolbarPanel
          :suspended-orders-count="suspendedOrdersCount"
          @showSuspendedOrders="handleShowSuspendedOrders"
          @memberManagement="handleMemberManagement"
          @checkout="handleCheckout"
        />

        <!-- 挂单管理抽屉 -->
        <OrderSuspend
          v-model:visible="showSuspendedOrdersDrawer"
          @orderSuspended="handleOrderSuspended"
          @orderResumed="handleOrderResumed"
          @orderDeleted="handleOrderDeleted"
        />

        <!-- 支付面板 -->
        <PaymentPanel
          v-model:visible="showPaymentPanel"
          @paymentSuccess="handlePaymentSuccess"
          @paymentFailed="handlePaymentFailed"
          @paymentCancel="handlePaymentCancel"
        />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="pos-statusbar">
      <div class="status-left">
        <span class="status-item">
          <icon-font iconClass="icon-cart" />
          购物车: {{ cartSummary.itemCount }}件
        </span>
        <span class="status-item">
          <icon-font iconClass="icon-money" />
          金额: {{ cartSummary.finalAmount }}
        </span>
        <span class="status-item" v-if="hasMember">
          <icon-font iconClass="icon-member" />
          会员: {{ currentMember.memberName }}
        </span>
      </div>
      
      <div class="status-right">
        <span class="status-item">
          <icon-font iconClass="icon-suspend" />
          挂单: {{ suspendedOrdersCount }}个
        </span>
        <span class="status-item">
          <icon-font iconClass="icon-time" />
          {{ currentDate }}
        </span>
      </div>
    </div>

    <!-- 快捷键帮助对话框 -->
    <a-modal
      v-model:open="showHelpModal"
      title="快捷键帮助"
      :footer="null"
      width="600px"
      class="help-modal"
    >
      <div class="help-content">
        <div class="help-section">
          <h4>基本操作</h4>
          <div class="help-item">
            <span class="key">F1</span>
            <span class="desc">显示帮助</span>
          </div>
          <div class="help-item">
            <span class="key">F11</span>
            <span class="desc">切换全屏</span>
          </div>
          <div class="help-item">
            <span class="key">Ctrl + R</span>
            <span class="desc">重置所有状态</span>
          </div>
          <div class="help-item">
            <span class="key">Esc</span>
            <span class="desc">取消当前操作</span>
          </div>
        </div>
        
        <div class="help-section">
          <h4>购物车操作</h4>
          <div class="help-item">
            <span class="key">Ctrl + A</span>
            <span class="desc">添加商品到购物车</span>
          </div>
          <div class="help-item">
            <span class="key">Ctrl + D</span>
            <span class="desc">删除选中商品</span>
          </div>
          <div class="help-item">
            <span class="key">Ctrl + C</span>
            <span class="desc">清空购物车</span>
          </div>
        </div>
        
        <div class="help-section">
          <h4>订单操作</h4>
          <div class="help-item">
            <span class="key">F2</span>
            <span class="desc">挂起当前订单</span>
          </div>
          <div class="help-item">
            <span class="key">F3</span>
            <span class="desc">恢复挂起订单</span>
          </div>
          <div class="help-item">
            <span class="key">F4</span>
            <span class="desc">开始结账</span>
          </div>
        </div>
        
        <div class="help-section">
          <h4>会员操作</h4>
          <div class="help-item">
            <span class="key">F5</span>
            <span class="desc">会员卡扫描</span>
          </div>
          <div class="help-item">
            <span class="key">F6</span>
            <span class="desc">清除会员信息</span>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 会员管理模态框 -->
    <a-modal
      v-model:open="showMemberModal"
      title="会员管理"
      width="600px"
      :footer="null"
      class="member-modal"
    >
      <MemberPanel
        @memberChange="handleMemberChange"
        @memberSelect="handleMemberSelect"
        @memberClear="handleMemberClear"
      />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { 
  QuestionCircleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ReloadOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'
import { usePosStore } from '@/stores/pos'
import { useUserStore } from '@/store/modules/user'
import { PosApi } from '@/api/erp/pos'
import IconFont from '@/components/common/IconFont/index.vue'

// 导入样式文件
import './styles/common.css'

import ProductDisplayArea from './components/ProductDisplayArea.vue'
import ShoppingCart from './components/ShoppingCart.vue'
import ToolbarPanel from './components/ToolbarPanel.vue'
import MemberPanel from './components/MemberPanel.vue'
import OrderSuspend from './components/OrderSuspend.vue'
import PaymentPanel from './components/PaymentPanel.vue'

// 定义组件名称
defineOptions({
  name: 'POSMain'
})

// 路由和状态管理
const router = useRouter()
const posStore = usePosStore()
const userStore = useUserStore()

// 响应式数据
const isFullscreen = ref(false)
const showHelpModal = ref(false)
const showPaymentPanel = ref(false)
const showSuspendedOrdersDrawer = ref(false)
const showMemberModal = ref(false)
const currentTime = ref('')
const currentDate = ref('')

// 计算属性
const currentUser = computed(() => userStore.userInfo || {})
const cartSummary = computed(() => posStore.getCartSummary())
const hasMember = computed(() => posStore.hasMember)
const currentMember = computed(() => posStore.currentMember)
const suspendedOrdersCount = computed(() => posStore.suspendedOrders.length)
const hasCartItems = computed(() => posStore.hasCartItems)
const canCheckout = computed(() => posStore.canCheckout)

/**
 * 更新当前时间
 */
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 切换全屏模式
 */
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

/**
 * 显示帮助
 */
const showHelp = () => {
  showHelpModal.value = true
}

/**
 * 重置所有状态
 */
const resetAll = () => {
  posStore.resetAllState()
  message.success('已重置所有状态')
}

/**
 * 退出登录
 */
const logout = () => {
  // 保存当前状态到本地存储
  posStore.saveCartToLocal()
  
  // 清除用户信息
  userStore.logout()
  
  // 跳转到登录页
  router.push('/login')
}

// ==================== 事件处理方法 ====================

/**
 * 处理商品添加
 */
const handleProductAdd = (product) => {
  // 业务逻辑
}

/**
 * 处理商品选择
 */
const handleProductSelect = (product) => {
  // 业务逻辑
}

/**
 * 处理会员变化
 */
const handleMemberChange = (member) => {
  // 业务逻辑
}

/**
 * 处理会员选择
 */
const handleMemberSelect = (member) => {
  // 业务逻辑
}

/**
 * 处理会员清除
 */
const handleMemberClear = () => {
  // 业务逻辑
}

/**
 * 处理购物车变化
 */
const handleCartChange = (cartData) => {
  // 业务逻辑
}

/**
 * 处理购物车清空
 */
const handleCartClear = () => {
  // 业务逻辑
}

/**
 * 处理结账
 */
const handleCheckout = () => {
  if (!canCheckout.value) {
    message.warning('购物车为空或正在处理中，无法结账')
    return
  }
  showPaymentPanel.value = true
}

/**
 * 处理挂单操作
 */
const handleSuspendOrder = () => {
  if (!hasCartItems.value) {
    message.warning('购物车为空，无法挂单')
    return
  }

  // 调用POS Store的挂单方法
  const remark = `挂单-${new Date().toLocaleTimeString()}`
  const success = posStore.suspendCurrentOrder(remark)

  if (success) {
    message.success('订单已挂起')
  } else {
    message.error('挂单失败')
  }
}

/**
 * 处理会员管理
 */
const handleMemberManagement = () => {
  showMemberModal.value = true
}

/**
 * 处理显示挂单列表
 */
const handleShowSuspendedOrders = () => {
  showSuspendedOrdersDrawer.value = true
}



/**
 * 处理订单挂起
 */
const handleOrderSuspended = () => {
  // 业务逻辑
}

/**
 * 处理订单恢复
 */
const handleOrderResumed = (suspendId) => {
  // 业务逻辑
}

/**
 * 处理订单删除
 */
const handleOrderDeleted = (suspendId) => {
  // 业务逻辑
}

/**
 * 处理支付成功
 */
const handlePaymentSuccess = (paymentData) => {
  // 业务逻辑
  showPaymentPanel.value = false
}

/**
 * 处理支付失败
 */
const handlePaymentFailed = (error) => {
  // 业务逻辑
}

/**
 * 处理支付取消
 */
const handlePaymentCancel = () => {
  showPaymentPanel.value = false
}

// ==================== 快捷键处理 ====================

/**
 * 处理键盘事件
 */
const handleKeydown = (event) => {
  // 阻止某些默认行为
  if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
    return
  }
  
  if (event.key === 'F1') {
    event.preventDefault()
    showHelp()
    return
  }
  
  if (event.ctrlKey && event.key === 'r') {
    event.preventDefault()
    resetAll()
    return
  }
  
  if (event.key === 'Escape') {
    event.preventDefault()
    if (showPaymentPanel.value) {
      showPaymentPanel.value = false
    } else if (showHelpModal.value) {
      showHelpModal.value = false
    }
    return
  }
  
  if (event.key === 'F2') {
    event.preventDefault()
    handleSuspendOrder()
    return
  }
  
  if (event.key === 'F4') {
    event.preventDefault()
    handleCheckout()
    return
  }
}

// 生命周期钩子
onMounted(() => {
  // 初始化POS Store
  posStore.initializeStore()

  // 开始时间更新
  updateTime()
  const timeInterval = setInterval(updateTime, 1000)

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 监听全屏变化
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })

  // 清理定时器
  onUnmounted(() => {
    clearInterval(timeInterval)
    document.removeEventListener('keydown', handleKeydown)
  })
})
</script>

<style scoped>
.pos-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.pos-main.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

/* 顶部工具栏 */
.pos-toolbar {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.pos-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.logo-text {
  font-size: 16px;
}

.cashier-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cashier-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.work-time {
  font-size: 12px;
  color: #8c8c8c;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-btn {
  border: none;
  box-shadow: none;
}

.logout-btn {
  margin-left: 8px;
}

/* 主要内容区域 */
.pos-content {
  flex: 1;
  display: grid;
  grid-template-columns: 75% 18% 7%;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.pos-left,
.pos-center,
.pos-right {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

/* 左侧区域：商品展示区域 (75%宽度) */
.pos-left {
  min-width: 0;
}





/* 中间区域：购物车区域 */
.pos-center {
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.cart-section {
  flex: 1;
  min-height: 0;
}

/* 右侧区域：功能操作区域 */
.pos-right {
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}



/* 状态栏 */
.pos-statusbar {
  height: 40px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  flex-shrink: 0;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #595959;
}

/* 帮助对话框 */
.help-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.help-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.help-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #fafafa;
}

.help-item:last-child {
  border-bottom: none;
}

.help-item .key {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  color: #262626;
  min-width: 80px;
  text-align: center;
}

.help-item .desc {
  font-size: 12px;
  color: #595959;
  flex: 1;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .pos-content {
    grid-template-columns: 70% 22% 8%;
  }


}

@media (max-width: 1200px) {
  .pos-content {
    grid-template-columns: 65% 25% 10%;
  }

  .member-info-section {
    height: 150px;
  }
}

@media (max-width: 1024px) {
  .pos-content {
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto auto;
  }

  .pos-right {
    grid-column: 1 / -1;
    grid-row: 2;
    flex-direction: row;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .pos-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 12px;
  }

  .pos-left,
  .pos-center,
  .pos-right {
    grid-column: 1;
  }

  .pos-right {
    flex-direction: row;
    height: auto;
    gap: 8px;
  }

  .toolbar-left .cashier-info {
    display: none;
  }

  .status-left,
  .status-right {
    gap: 12px;
  }

  .status-item {
    font-size: 11px;
  }
}

/* 触屏设备优化 */
@media (hover: none) and (pointer: coarse) {
  .toolbar-btn,
  .logout-btn {
    min-height: 40px;
    padding: 8px 12px;
  }

  .status-item {
    padding: 4px 0;
  }
}
</style>
