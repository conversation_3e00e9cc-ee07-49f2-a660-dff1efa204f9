import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import PosIndex from '../index.vue'

// Mock所有子组件
vi.mock('../components/ProductDisplayArea.vue', () => ({
  default: {
    name: 'ProductDisplayArea',
    template: '<div class="product-display-area-mock">ProductDisplayArea</div>',
    emits: ['productAdd', 'productSelect']
  }
}))

vi.mock('../components/ShoppingCart.vue', () => ({
  default: {
    name: 'ShoppingCart',
    template: '<div class="shopping-cart-mock">ShoppingCart</div>',
    emits: ['cartChange', 'cartClear', 'checkout']
  }
}))

vi.mock('../components/ToolbarPanel.vue', () => ({
  default: {
    name: 'ToolbarPanel',
    template: '<div class="toolbar-panel-mock">ToolbarPanel</div>',
    props: ['suspendedOrdersCount'],
    emits: ['showSuspendedOrders', 'memberManagement']
  }
}))

vi.mock('../components/MemberPanel.vue', () => ({
  default: {
    name: 'MemberPanel',
    template: '<div class="member-panel-mock">MemberPanel</div>',
    emits: ['memberChange', 'memberSelect', 'memberClear']
  }
}))

vi.mock('../components/OrderSuspend.vue', () => ({
  default: {
    name: 'OrderSuspend',
    template: '<div class="order-suspend-mock">OrderSuspend</div>',
    props: ['visible'],
    emits: ['update:visible', 'orderSuspended', 'orderResumed', 'orderDeleted']
  }
}))

vi.mock('../components/PaymentPanel.vue', () => ({
  default: {
    name: 'PaymentPanel',
    template: '<div class="payment-panel-mock">PaymentPanel</div>',
    props: ['visible', 'orderInfo', 'memberInfo'],
    emits: ['update:visible', 'paymentSuccess', 'paymentCancel']
  }
}))

// Mock composables
vi.mock('../composables/usePos', () => ({
  usePos: () => ({
    // 状态
    currentUser: { realName: '测试收银员' },
    currentTime: '10:30:00',
    currentDate: '2025-01-02',
    isFullscreen: false,
    showHelpModal: false,
    showPaymentPanel: false,
    showSuspendedOrdersDrawer: false,
    cartSummary: { itemCount: 0, finalAmount: 0 },
    hasMember: false,
    currentMember: null,
    suspendedOrdersCount: 0,
    orderInfo: {},
    
    // 方法
    toggleFullscreen: vi.fn(),
    showHelp: vi.fn(),
    resetAll: vi.fn(),
    logout: vi.fn(),
    formatAmount: vi.fn((amount) => `¥${amount}`),
    
    // 事件处理
    handleProductAdd: vi.fn(),
    handleProductSelect: vi.fn(),
    handleMemberChange: vi.fn(),
    handleMemberSelect: vi.fn(),
    handleMemberClear: vi.fn(),
    handleCartChange: vi.fn(),
    handleCartClear: vi.fn(),
    handleCheckout: vi.fn(),
    handleShowSuspendedOrders: vi.fn(),
    handleMemberManagement: vi.fn(),
    handleOrderSuspended: vi.fn(),
    handleOrderResumed: vi.fn(),
    handleOrderDeleted: vi.fn(),
    handlePaymentSuccess: vi.fn(),
    handlePaymentCancel: vi.fn()
  })
}))

vi.mock('../composables/useKeyboard', () => ({
  useKeyboard: vi.fn()
}))

// Mock IconFont组件
vi.mock('@/components/common/IconFont/index.vue', () => ({
  default: {
    name: 'IconFont',
    template: '<i class="icon-font-mock"></i>',
    props: ['iconClass']
  }
}))

describe('PosIndex', () => {
  let wrapper
  let pinia

  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(PosIndex, {
      props,
      global: {
        plugins: [pinia],
        stubs: {
          'a-space': true,
          'a-tooltip': true,
          'a-button': true,
          'a-modal': true
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染主页面结构', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.pos-main').exists()).toBe(true)
      expect(wrapper.find('.pos-toolbar').exists()).toBe(true)
      expect(wrapper.find('.pos-content').exists()).toBe(true)
      expect(wrapper.find('.pos-statusbar').exists()).toBe(true)
    })

    it('应该渲染所有主要组件', () => {
      wrapper = createWrapper()
      
      expect(wrapper.findComponent({ name: 'ProductDisplayArea' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'ShoppingCart' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'ToolbarPanel' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'MemberPanel' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'OrderSuspend' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'PaymentPanel' }).exists()).toBe(true)
    })

    it('应该正确显示收银员信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('收银员: 测试收银员')
      expect(wrapper.text()).toContain('10:30:00')
    })

    it('应该正确显示状态栏信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('购物车: 0件')
      expect(wrapper.text()).toContain('金额: ¥0')
      expect(wrapper.text()).toContain('挂单: 0个')
      expect(wrapper.text()).toContain('2025-01-02')
    })
  })

  describe('布局结构', () => {
    it('应该有正确的三栏布局', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.pos-left').exists()).toBe(true)
      expect(wrapper.find('.pos-center').exists()).toBe(true)
      expect(wrapper.find('.pos-right').exists()).toBe(true)
    })

    it('应该在中间栏包含会员和购物车区域', () => {
      wrapper = createWrapper()
      
      const centerArea = wrapper.find('.pos-center')
      expect(centerArea.find('.member-section').exists()).toBe(true)
      expect(centerArea.find('.cart-section').exists()).toBe(true)
    })
  })

  describe('组件集成', () => {
    it('应该正确传递props给子组件', () => {
      wrapper = createWrapper()
      
      const toolbarPanel = wrapper.findComponent({ name: 'ToolbarPanel' })
      expect(toolbarPanel.props('suspendedOrdersCount')).toBe(0)
      
      const paymentPanel = wrapper.findComponent({ name: 'PaymentPanel' })
      expect(paymentPanel.props('orderInfo')).toEqual({})
      expect(paymentPanel.props('memberInfo')).toBe(null)
    })

    it('应该正确处理子组件事件', async () => {
      wrapper = createWrapper()
      
      // 测试商品添加事件
      const productDisplay = wrapper.findComponent({ name: 'ProductDisplayArea' })
      await productDisplay.vm.$emit('productAdd', { id: 1, name: '测试商品' })
      
      // 测试购物车变化事件
      const shoppingCart = wrapper.findComponent({ name: 'ShoppingCart' })
      await shoppingCart.vm.$emit('cartChange', { type: 'add', item: { id: 1 } })
      
      // 测试会员选择事件
      const memberPanel = wrapper.findComponent({ name: 'MemberPanel' })
      await memberPanel.vm.$emit('memberSelect', { id: 1, name: '测试会员' })
    })
  })

  describe('响应式设计', () => {
    it('应该有响应式CSS类', () => {
      wrapper = createWrapper()
      
      const posMain = wrapper.find('.pos-main')
      expect(posMain.classes()).toContain('pos-main')
      
      // 检查是否有响应式相关的样式
      const style = wrapper.find('.pos-main').element.style
      expect(wrapper.html()).toContain('pos-content')
    })
  })

  describe('模态框和抽屉', () => {
    it('应该包含帮助模态框', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.help-modal').exists()).toBe(true)
    })

    it('应该包含挂单管理抽屉', () => {
      wrapper = createWrapper()
      
      const orderSuspend = wrapper.findComponent({ name: 'OrderSuspend' })
      expect(orderSuspend.exists()).toBe(true)
    })

    it('应该包含支付面板', () => {
      wrapper = createWrapper()
      
      const paymentPanel = wrapper.findComponent({ name: 'PaymentPanel' })
      expect(paymentPanel.exists()).toBe(true)
    })
  })

  describe('Composables集成', () => {
    it('应该使用usePos composable', () => {
      const { usePos } = require('../composables/usePos')
      wrapper = createWrapper()
      
      expect(usePos).toHaveBeenCalled()
    })

    it('应该使用useKeyboard composable', () => {
      const { useKeyboard } = require('../composables/useKeyboard')
      wrapper = createWrapper()
      
      expect(useKeyboard).toHaveBeenCalled()
    })
  })
})