import{_ as ge,s as ve,r as p,o as _e,k as g,a as v,f as h,w as l,d as e,g as c,h as F,b as V,c as S,F as ye,e as be,t as Y,cj as B,m as xe,l as he,u as Ue,v as Fe,z as Ve,A as Ne,aa as we,y as Ce,al as ke,B as Le,I as Ie,bk as Te,a7 as Oe,G as Ae,H as De}from"./index-18a1ea24.js";import{_ as $e}from"./index-3a0e5c06.js";/* empty css              *//* empty css              *//* empty css              *//* empty css              */import{a as je}from"./FileApi-418f4d35.js";import{S as qe}from"./SysDictTypeApi-1ce2cbe7.js";import"./index-d0cfb2ce.js";import"./index-02bf6f00.js";/* empty css              *//* empty css              *//* empty css              */import"./OrgApi-021dd6dd.js";const Me={key:0,style:{width:"100%",display:"flex","align-items":"center"}},Se={class:"filename"},Ye=["onClick"],Be={__name:"user-form",props:{form:Object,isUpdate:Boolean,superAdminFlag:{type:Boolean,default:!1}},setup(n,{expose:z}){const d=n,E=ve({realName:[{required:!0,message:"\u8BF7\u8F93\u5165\u59D3\u540D",type:"string",trigger:"blur"}],account:[{required:!0,message:"\u8BF7\u8F93\u5165\u8D26\u53F7",type:"string",trigger:"blur"}],sex:[{required:!0,message:"\u8BF7\u9009\u62E9\u6027\u522B",type:"string",trigger:"change"}],superAdminFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u662F\u5426\u662F\u8D85\u7EA7\u7BA1\u7406\u5458",type:"string",trigger:"change"}],password:[{required:!0,message:"\u8BF7\u8F93\u5165\u5BC6\u7801",type:"string",trigger:"blur"}],statusFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u7528\u6237\u72B6\u6001",type:"number",trigger:"change"}]}),N=p(null),w=p(null),y=p(!1),C=p(""),U=p([]),_=p(void 0),k=p({}),T=p([]),R=p({orgName:[{required:!0,message:"\u8BF7\u9009\u62E9\u673A\u6784"}],positionName:[{required:!0,message:"\u8BF7\u9009\u62E9\u804C\u52A1"}],mainFlag:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E3B\u8981\u90E8\u95E8"}]});_e(()=>{H()});const H=async()=>{T.value=await qe.getDictListByParams({dictTypeId:"1722790763315597314"})},O=async()=>{let o=!1;return await N.value.validate(!0)?o=!1:o=!0,o},P=async()=>{if(await O()){let o={mainFlag:d.form.userOrgList.length==0?"Y":"N",statusFlag:1,orgId:"",orgName:"",positionId:"",positionName:""};d.form.userOrgList.push(o)}},K=async o=>{await B.modal.confirm("\u60A8\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u6570\u636E?")==="confirm"&&N.value.remove(o);const u=N.value.getTableData().tableData;d.form.userOrgList=u},G=(o,t)=>{_.value=t,A(o,"orgId","orgName","selectOrgList"),U.value=["dept"],C.value="\u673A\u6784\u9009\u62E9",y.value=!0},X=(o,t)=>{_.value=t,A(o,"positionId","positionName","selectPositionList"),U.value=["position"],C.value="\u804C\u4F4D\u9009\u62E9",y.value=!0},A=(o,t,u,i)=>{o[t]&&o[u]?k.value[i]=[{bizId:o[t],name:o[u]}]:k.value[i]=[]},J=o=>{y.value=!1,U.value[0]=="dept"?D(o,"orgId","orgName","selectOrgList"):U.value[0]=="position"&&D(o,"positionId","positionName","selectPositionList")},D=(o,t,u,i)=>{_.value!=null&&(o[i]&&o[i].length>0?(d.form.userOrgList[_.value][t]=o[i][0].bizId,d.form.userOrgList[_.value][u]=o[i][0].name):(d.form.userOrgList[_.value][t]="",d.form.userOrgList[_.value][u]=""))},Q=(o,t)=>{if(o.mainFlag=="Y")d.form.userOrgList.forEach((u,i)=>{t!=i&&(u.mainFlag="N")});else if(o.mainFlag=="N"&&!d.form.userOrgList.find(u=>u.mainFlag=="Y"))return o.mainFlag="Y",xe.warning("\u5FC5\u987B\u6709\u4E00\u4E2A\u4E3B\u8981\u90E8\u95E8")},W=()=>{let o={certificateType:"",certificateNo:"",issuingAuthority:"",dateIssued:"",dateExpires:null,attachmentId:"",attachmentName:"",attachmentUrl:""};d.form.userCertificateList.push(o)},Z=async o=>{await B.modal.confirm("\u60A8\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u6570\u636E?")==="confirm"&&w.value.remove(o);const u=w.value.getTableData().tableData;d.form.userCertificateList=u},ee=(o,t)=>{const u=new FormData;return u.append("file",o),je.commonUpload("N",u).then(i=>{t.attachmentName=i.data.fileOriginName,t.attachmentId=i.data.fileId,t.attachmentUrl=i.data.fileUrl}),d.form.userCertificateList=[...d.form.userCertificateList],!1},te=o=>{o.attachmentName="",o.attachmentId="",o.attachmentUrl="",d.form.userCertificateList=[...d.form.userCertificateList]},ae=o=>{const{href:t}=router.resolve({path:record.attachmentUrl});window.open(t,"_blank")};return z({validAllEvent:O}),(o,t)=>{const u=he,i=Ue,r=Fe,b=Ve,L=Ne,le=we,oe=Ce,ne=ke,$=g("plus-outlined"),I=Le,m=g("vxe-column"),x=g("vxe-input"),j=g("vxe-switch"),q=Ie,M=g("vxe-table"),ie=g("vxe-option"),se=g("vxe-select"),ue=g("CloudUploadOutlined"),re=Te,de=Oe,me=g("delete-outlined"),fe=Ae,ce=$e,pe=De;return v(),h(pe,{ref:"formRef",model:n.form,rules:E,layout:"vertical"},{default:l(()=>[e(fe,{gutter:20},{default:l(()=>[e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u59D3\u540D:",name:"realName"},{default:l(()=>[e(u,{value:n.form.realName,"onUpdate:value":t[0]||(t[0]=a=>n.form.realName=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u59D3\u540D"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u8D26\u53F7:",name:"account"},{default:l(()=>[e(u,{value:n.form.account,"onUpdate:value":t[1]||(t[1]=a=>n.form.account=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u8D26\u53F7"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u6027\u522B:",name:"sex"},{default:l(()=>[e(L,{value:n.form.sex,"onUpdate:value":t[2]||(t[2]=a=>n.form.sex=a)},{default:l(()=>[e(b,{value:"M"},{default:l(()=>t[12]||(t[12]=[c("\u7537")])),_:1,__:[12]}),e(b,{value:"F"},{default:l(()=>t[13]||(t[13]=[c("\u5973")])),_:1,__:[13]})]),_:1},8,["value"])]),_:1})]),_:1}),d.superAdminFlag?(v(),h(r,{key:0,xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u662F\u5426\u662F\u8D85\u7EA7\u7BA1\u7406\u5458:",name:"superAdminFlag"},{default:l(()=>[e(L,{value:n.form.superAdminFlag,"onUpdate:value":t[3]||(t[3]=a=>n.form.superAdminFlag=a)},{default:l(()=>[e(b,{value:"Y"},{default:l(()=>t[14]||(t[14]=[c("\u662F")])),_:1,__:[14]}),e(b,{value:"N"},{default:l(()=>t[15]||(t[15]=[c("\u5426")])),_:1,__:[15]})]),_:1},8,["value"])]),_:1})]),_:1})):F("",!0),n.isUpdate?F("",!0):(v(),h(r,{key:1,xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u5BC6\u7801:",name:"password"},{default:l(()=>[e(le,{value:n.form.password,"onUpdate:value":t[4]||(t[4]=a=>n.form.password=a),placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801",autocomplete:"new-password"},null,8,["value"])]),_:1})]),_:1})),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u7528\u6237\u72B6\u6001:",name:"statusFlag"},{default:l(()=>[e(L,{value:n.form.statusFlag,"onUpdate:value":t[5]||(t[5]=a=>n.form.statusFlag=a)},{default:l(()=>[e(b,{value:1},{default:l(()=>t[16]||(t[16]=[c("\u542F\u7528")])),_:1,__:[16]}),e(b,{value:2},{default:l(()=>t[17]||(t[17]=[c("\u7981\u7528")])),_:1,__:[17]})]),_:1},8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u7528\u6237\u6392\u5E8F:",name:"userSort"},{default:l(()=>[e(oe,{value:n.form.userSort,"onUpdate:value":t[6]||(t[6]=a=>n.form.userSort=a),placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F","allow-clear":"",autocomplete:"off",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u624B\u673A\u53F7:",name:"phone"},{default:l(()=>[e(u,{value:n.form.phone,"onUpdate:value":t[7]||(t[7]=a=>n.form.phone=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u90AE\u7BB1:",name:"email"},{default:l(()=>[e(u,{value:n.form.email,"onUpdate:value":t[8]||(t[8]=a=>n.form.email=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1",autocomplete:"new-password"},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u51FA\u751F\u65E5\u671F:",name:"birthday"},{default:l(()=>[e(ne,{value:n.form.birthday,"onUpdate:value":t[9]||(t[9]=a=>n.form.birthday=a),"value-format":"YYYY-MM-DD",placeholder:"\u8BF7\u9009\u62E9\u51FA\u751F\u65E5\u671F",style:{width:"100%"}},null,8,["value"])]),_:1})]),_:1}),e(r,{xs:24,sm:24,md:12},{default:l(()=>[e(i,{label:"\u4EBA\u5458\u5DE5\u53F7:",name:"employeeNumber"},{default:l(()=>[e(u,{value:n.form.employeeNumber,"onUpdate:value":t[10]||(t[10]=a=>n.form.employeeNumber=a),"allow-clear":"",placeholder:"\u8BF7\u8F93\u5165\u4EBA\u5458\u5DE5\u53F7"},null,8,["value"])]),_:1})]),_:1}),e(r,{span:24},{default:l(()=>t[18]||(t[18]=[V("div",{class:"card-title"},"\u7EC4\u7EC7\u673A\u6784\u4FE1\u606F",-1)])),_:1,__:[18]}),e(r,{span:24},{default:l(()=>[e(I,{type:"primary",class:"border-radius",onClick:P},{default:l(()=>[e($),t[19]||(t[19]=c(" \u6DFB\u52A0\u673A\u6784"))]),_:1,__:[19]})]),_:1}),e(r,{span:24,class:"marginT10"},{default:l(()=>[e(M,{border:"","show-overflow":"",data:n.form.userOrgList,"row-config":{useKey:!0},"edit-rules":R.value,"column-config":{resizable:!0},"edit-config":{trigger:"click",mode:"cell"},"max-height":"600",ref_key:"xTableRef",ref:N},{default:l(()=>[e(m,{type:"seq",width:"60",title:"\u5E8F\u53F7",align:"center"}),e(m,{field:"orgName",title:"\u673A\u6784\u540D\u79F0","min-width":"150",align:"center"},{default:l(({row:a,rowIndex:s})=>[e(x,{modelValue:a.orgName,"onUpdate:modelValue":f=>a.orgName=f,type:"text",onFocus:f=>G(a,s)},null,8,["modelValue","onUpdate:modelValue","onFocus"])]),_:1}),e(m,{field:"positionName",title:"\u804C\u4F4D",width:"200",align:"center"},{default:l(({row:a,rowIndex:s})=>[e(x,{modelValue:a.positionName,"onUpdate:modelValue":f=>a.positionName=f,type:"text",placeholder:"\u8BF7\u8F93\u5165\u804C\u4F4D",onFocus:f=>X(a,s)},null,8,["modelValue","onUpdate:modelValue","onFocus"])]),_:1}),e(m,{field:"mainFlag",title:"\u4E3B\u8981\u90E8\u95E8",width:"100",align:"center"},{default:l(({row:a,rowIndex:s})=>[e(j,{modelValue:a.mainFlag,"onUpdate:modelValue":f=>a.mainFlag=f,"open-value":"Y","close-value":"N",onChange:f=>Q(a,s)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(m,{field:"statusFlag",title:"\u662F\u5426\u542F\u7528",width:"100",align:"center"},{default:l(({row:a})=>[e(j,{modelValue:a.statusFlag,"onUpdate:modelValue":s=>a.statusFlag=s,"open-value":1,"close-value":2},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(m,{title:"\u64CD\u4F5C",width:"100",align:"center"},{default:l(({row:a})=>[e(q,{iconClass:"icon-opt-shanchu",color:"#60666b","font-size":"24px",onClick:s=>K(a)},null,8,["onClick"])]),_:1})]),_:1},8,["data","edit-rules"])]),_:1}),e(r,{span:24},{default:l(()=>t[20]||(t[20]=[V("div",{class:"card-title"},"\u8BC1\u4E66\u4FE1\u606F",-1)])),_:1,__:[20]}),e(r,{span:24},{default:l(()=>[e(I,{type:"primary",class:"border-radius",onClick:W},{default:l(()=>[e($),t[21]||(t[21]=c(" \u6DFB\u52A0\u8BC1\u4E66"))]),_:1,__:[21]})]),_:1}),e(r,{span:24,style:{"margin-top":"10px"}},{default:l(()=>[e(M,{border:"","show-overflow":"",data:n.form.userCertificateList,"row-config":{useKey:!0},"column-config":{resizable:!0},"max-height":"600",ref_key:"certificateRef",ref:w},{default:l(()=>[e(m,{type:"seq",width:"60",title:"\u5E8F\u53F7",align:"center"}),e(m,{field:"certificateType",title:"\u8BC1\u4E66\u7C7B\u578B",width:"200",align:"center"},{default:l(({row:a})=>[e(se,{modelValue:a.certificateType,"onUpdate:modelValue":s=>a.certificateType=s,transfer:"",placeholder:"\u8BF7\u9009\u62E9\u8BC1\u4E66\u7C7B\u578B"},{default:l(()=>[(v(!0),S(ye,null,be(T.value,(s,f)=>(v(),h(ie,{key:f,value:s.dictId,label:s.dictName},null,8,["value","label"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),e(m,{field:"certificateNo",title:"\u8BC1\u4E66\u7F16\u53F7",width:"200",align:"center"},{default:l(({row:a})=>[e(x,{modelValue:a.certificateNo,"onUpdate:modelValue":s=>a.certificateNo=s,type:"text",placeholder:"\u8BF7\u8F93\u5165\u8BC1\u4E66\u7F16\u53F7"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(m,{field:"issuingAuthority",title:"\u53D1\u8BC1\u673A\u6784\u540D\u79F0",width:"200",align:"center"},{default:l(({row:a})=>[e(x,{modelValue:a.issuingAuthority,"onUpdate:modelValue":s=>a.issuingAuthority=s,type:"text",placeholder:"\u8BF7\u8F93\u5165\u53D1\u8BC1\u673A\u6784\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(m,{field:"dateIssued",title:"\u53D1\u8BC1\u65E5\u671F",width:"150",align:"center"},{default:l(({row:a})=>[e(x,{modelValue:a.dateIssued,"onUpdate:modelValue":s=>a.dateIssued=s,type:"date",valueFormat:"yyyy-MM-dd HH:mm:ss",placeholder:"\u8BF7\u9009\u62E9\u53D1\u8BC1\u65E5\u671F",transfer:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(m,{field:"dateExpires",title:"\u5230\u671F\u65E5\u671F",width:"150",align:"center"},{default:l(({row:a})=>[e(x,{modelValue:a.dateExpires,"onUpdate:modelValue":s=>a.dateExpires=s,valueFormat:"yyyy-MM-dd HH:mm:ss",type:"date",placeholder:"\u8BF7\u9009\u62E9\u5230\u671F\u65E5\u671F",transfer:""},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(m,{field:"attachmentId",title:"\u9644\u4EF6",width:"150",align:"center"},{default:l(({row:a})=>[e(re,{name:"file",multiple:!1,maxCount:1,accept:".jpeg,.jpg,.png,.tif,.jfif,.webp,.pjp,.apng,.pjpeg,.avif,.ico,.tiff,.bmp,.xbm,.jxl,.jpeg,.svgz,.gif,.svg,.pdf",beforeUpload:s=>ee(s,a),showUploadList:!1},{default:l(()=>[a.attachmentId?F("",!0):(v(),h(I,{key:0,type:"primary"},{icon:l(()=>[e(ue)]),default:l(()=>[t[22]||(t[22]=V("span",null,"\u4E0A\u4F20\u6587\u4EF6",-1))]),_:1,__:[22]}))]),_:2},1032,["beforeUpload"]),a.attachmentId?(v(),S("div",Me,[V("span",Se,[V("a",{onClick:s=>ae(a)},[e(de,null,{title:l(()=>[c(Y(a.attachmentName),1)]),default:l(()=>[c(" "+Y(a.attachmentName),1)]),_:2},1024)],8,Ye)]),e(me,{class:"delete",onClick:s=>te(a)},null,8,["onClick"])])):F("",!0)]),_:1}),e(m,{title:"\u64CD\u4F5C",width:"100",align:"center",fixed:"right"},{default:l(({row:a})=>[e(q,{iconClass:"icon-opt-shanchu",color:"#60666b","font-size":"24px",onClick:s=>Z(a)},null,8,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1})]),_:1}),y.value?(v(),h(ce,{key:0,visible:y.value,"onUpdate:visible":t[11]||(t[11]=a=>y.value=a),title:C.value,data:k.value,showTab:U.value,onDone:J},null,8,["visible","title","data","showTab"])):F("",!0)]),_:1},8,["model","rules"])}}},at=ge(Be,[["__scopeId","data-v-e59fe4cd"]]);export{at as default};
