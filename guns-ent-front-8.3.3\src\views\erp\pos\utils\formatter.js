/**
 * POS模块格式化工具函数
 * 
 * 提供金额格式化、日期格式化、商品信息格式化等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { NUMERIC_CONSTANTS } from './constants'

/**
 * 金额格式化工具类
 */
export class AmountFormatter {
  
  /**
   * 格式化货币金额
   * @param {number} amount - 金额
   * @param {Object} options - 格式化选项
   * @param {string} options.currency - 货币符号，默认'¥'
   * @param {number} options.precision - 小数位数，默认2位
   * @param {boolean} options.showSymbol - 是否显示货币符号，默认true
   * @param {boolean} options.showThousandsSeparator - 是否显示千分位分隔符，默认true
   * @returns {string} 格式化后的金额字符串
   */
  static formatCurrency(amount, options = {}) {
    const {
      currency = '¥',
      precision = NUMERIC_CONSTANTS.AMOUNT_PRECISION,
      showSymbol = true,
      showThousandsSeparator = true
    } = options
    
    if (typeof amount !== 'number' || isNaN(amount)) {
      return showSymbol ? `${currency}0.00` : '0.00'
    }
    
    // 四舍五入到指定精度
    const roundedAmount = Math.round(amount * Math.pow(10, precision)) / Math.pow(10, precision)
    
    // 格式化数字
    let formattedNumber = roundedAmount.toFixed(precision)
    
    // 添加千分位分隔符
    if (showThousandsSeparator) {
      const parts = formattedNumber.split('.')
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      formattedNumber = parts.join('.')
    }
    
    // 添加货币符号
    return showSymbol ? `${currency}${formattedNumber}` : formattedNumber
  }
  
  /**
   * 格式化百分比
   * @param {number} value - 数值（如0.15表示15%）
   * @param {Object} options - 格式化选项
   * @param {number} options.precision - 小数位数，默认2位
   * @param {boolean} options.showSymbol - 是否显示%符号，默认true
   * @returns {string} 格式化后的百分比字符串
   */
  static formatPercentage(value, options = {}) {
    const {
      precision = NUMERIC_CONSTANTS.DISCOUNT_PRECISION,
      showSymbol = true
    } = options
    
    if (typeof value !== 'number' || isNaN(value)) {
      return showSymbol ? '0.00%' : '0.00'
    }
    
    const percentage = (value * 100).toFixed(precision)
    return showSymbol ? `${percentage}%` : percentage
  }
  
  /**
   * 格式化数量
   * @param {number} quantity - 数量
   * @param {Object} options - 格式化选项
   * @param {number} options.precision - 小数位数，默认3位
   * @param {string} options.unit - 单位，默认空字符串
   * @param {boolean} options.showUnit - 是否显示单位，默认false
   * @returns {string} 格式化后的数量字符串
   */
  static formatQuantity(quantity, options = {}) {
    const {
      precision = NUMERIC_CONSTANTS.QUANTITY_PRECISION,
      unit = '',
      showUnit = false
    } = options
    
    if (typeof quantity !== 'number' || isNaN(quantity)) {
      return showUnit && unit ? `0${unit}` : '0'
    }
    
    // 移除末尾的0
    const formattedQuantity = parseFloat(quantity.toFixed(precision)).toString()
    
    return showUnit && unit ? `${formattedQuantity}${unit}` : formattedQuantity
  }
  
  /**
   * 格式化折扣金额显示
   * @param {number} originalAmount - 原始金额
   * @param {number} discountAmount - 折扣金额
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的折扣显示字符串
   */
  static formatDiscountDisplay(originalAmount, discountAmount, options = {}) {
    const formattedOriginal = this.formatCurrency(originalAmount, options)
    const formattedDiscount = this.formatCurrency(discountAmount, options)
    const finalAmount = originalAmount - discountAmount
    const formattedFinal = this.formatCurrency(finalAmount, options)
    
    if (discountAmount > 0) {
      return `${formattedOriginal} - ${formattedDiscount} = ${formattedFinal}`
    }
    
    return formattedOriginal
  }
  
  /**
   * 格式化找零显示
   * @param {number} receivedAmount - 实收金额
   * @param {number} payableAmount - 应付金额
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的找零显示字符串
   */
  static formatChangeDisplay(receivedAmount, payableAmount, options = {}) {
    const changeAmount = Math.max(0, receivedAmount - payableAmount)
    const formattedReceived = this.formatCurrency(receivedAmount, options)
    const formattedPayable = this.formatCurrency(payableAmount, options)
    const formattedChange = this.formatCurrency(changeAmount, options)
    
    return `实收: ${formattedReceived} | 应付: ${formattedPayable} | 找零: ${formattedChange}`
  }
}

/**
 * 日期时间格式化工具类
 */
export class DateFormatter {
  
  /**
   * 格式化日期时间
   * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
   * @param {string} format - 格式字符串，默认'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化后的日期时间字符串
   */
  static formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    let dateObj
    
    if (date instanceof Date) {
      dateObj = date
    } else if (typeof date === 'string' || typeof date === 'number') {
      dateObj = new Date(date)
    } else {
      dateObj = new Date()
    }
    
    if (isNaN(dateObj.getTime())) {
      return '无效日期'
    }
    
    const year = dateObj.getFullYear()
    const month = String(dateObj.getMonth() + 1).padStart(2, '0')
    const day = String(dateObj.getDate()).padStart(2, '0')
    const hours = String(dateObj.getHours()).padStart(2, '0')
    const minutes = String(dateObj.getMinutes()).padStart(2, '0')
    const seconds = String(dateObj.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  }
  
  /**
   * 格式化日期
   * @param {Date|string|number} date - 日期
   * @param {string} format - 格式字符串，默认'YYYY-MM-DD'
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date, format = 'YYYY-MM-DD') {
    return this.formatDateTime(date, format)
  }
  
  /**
   * 格式化时间
   * @param {Date|string|number} date - 日期
   * @param {string} format - 格式字符串，默认'HH:mm:ss'
   * @returns {string} 格式化后的时间字符串
   */
  static formatTime(date, format = 'HH:mm:ss') {
    return this.formatDateTime(date, format)
  }
  
  /**
   * 格式化相对时间
   * @param {Date|string|number} date - 日期
   * @returns {string} 相对时间字符串（如"2分钟前"）
   */
  static formatRelativeTime(date) {
    let dateObj
    
    if (date instanceof Date) {
      dateObj = date
    } else if (typeof date === 'string' || typeof date === 'number') {
      dateObj = new Date(date)
    } else {
      return '无效日期'
    }
    
    if (isNaN(dateObj.getTime())) {
      return '无效日期'
    }
    
    const now = new Date()
    const diffMs = now.getTime() - dateObj.getTime()
    const diffSeconds = Math.floor(diffMs / 1000)
    const diffMinutes = Math.floor(diffSeconds / 60)
    const diffHours = Math.floor(diffMinutes / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffSeconds < 60) {
      return '刚刚'
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return this.formatDate(dateObj)
    }
  }
  
  /**
   * 格式化时长
   * @param {number} seconds - 秒数
   * @returns {string} 格式化后的时长字符串
   */
  static formatDuration(seconds) {
    if (typeof seconds !== 'number' || seconds < 0) {
      return '00:00:00'
    }
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`
  }
}

/**
 * 商品信息格式化工具类
 */
export class ProductFormatter {
  
  /**
   * 格式化商品名称显示
   * @param {Object} product - 商品对象
   * @param {Object} options - 格式化选项
   * @param {number} options.maxLength - 最大长度，默认20
   * @param {boolean} options.showSpecs - 是否显示规格，默认true
   * @returns {string} 格式化后的商品名称
   */
  static formatProductName(product, options = {}) {
    const {
      maxLength = 20,
      showSpecs = true
    } = options
    
    if (!product || !product.name) {
      return '未知商品'
    }
    
    let displayName = product.name
    
    // 添加规格信息
    if (showSpecs && product.specifications) {
      displayName += ` (${product.specifications})`
    }
    
    // 截断过长的名称
    if (displayName.length > maxLength) {
      displayName = displayName.substring(0, maxLength - 3) + '...'
    }
    
    return displayName
  }
  
  /**
   * 格式化商品价格显示
   * @param {Object} product - 商品对象
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的价格显示
   */
  static formatProductPrice(product, options = {}) {
    if (!product) {
      return AmountFormatter.formatCurrency(0, options)
    }
    
    const price = product.price || 0
    const unit = product.unit || ''
    
    let priceDisplay = AmountFormatter.formatCurrency(price, options)
    
    if (unit) {
      priceDisplay += `/${unit}`
    }
    
    return priceDisplay
  }
  
  /**
   * 格式化商品库存显示
   * @param {Object} product - 商品对象
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的库存显示
   */
  static formatProductStock(product, options = {}) {
    if (!product) {
      return '库存: 0'
    }
    
    const stock = product.stock || 0
    const unit = product.unit || '件'
    const safetyStock = product.safetyStock || 0
    
    let stockDisplay = `库存: ${AmountFormatter.formatQuantity(stock, { unit, showUnit: true })}`
    
    // 添加库存状态提示
    if (stock <= 0) {
      stockDisplay += ' (缺货)'
    } else if (stock <= safetyStock) {
      stockDisplay += ' (预警)'
    }
    
    return stockDisplay
  }
  
  /**
   * 格式化商品条码显示
   * @param {string} barcode - 商品条码
   * @param {Object} options - 格式化选项
   * @param {boolean} options.showPrefix - 是否显示前缀，默认true
   * @returns {string} 格式化后的条码显示
   */
  static formatBarcode(barcode, options = {}) {
    const { showPrefix = true } = options
    
    if (!barcode) {
      return showPrefix ? '条码: 无' : '无'
    }
    
    // 格式化条码显示（每4位添加一个空格）
    const formattedBarcode = barcode.replace(/(.{4})/g, '$1 ').trim()
    
    return showPrefix ? `条码: ${formattedBarcode}` : formattedBarcode
  }
  
  /**
   * 格式化商品分类路径
   * @param {Array} categoryPath - 分类路径数组
   * @param {string} separator - 分隔符，默认' > '
   * @returns {string} 格式化后的分类路径
   */
  static formatCategoryPath(categoryPath, separator = ' > ') {
    if (!Array.isArray(categoryPath) || categoryPath.length === 0) {
      return '未分类'
    }
    
    return categoryPath
      .filter(category => category && category.name)
      .map(category => category.name)
      .join(separator)
  }
}

/**
 * 订单信息格式化工具类
 */
export class OrderFormatter {
  
  /**
   * 格式化订单号显示
   * @param {string} orderNo - 订单号
   * @param {Object} options - 格式化选项
   * @param {boolean} options.showPrefix - 是否显示前缀，默认true
   * @returns {string} 格式化后的订单号
   */
  static formatOrderNo(orderNo, options = {}) {
    const { showPrefix = true } = options
    
    if (!orderNo) {
      return showPrefix ? '订单号: 无' : '无'
    }
    
    return showPrefix ? `订单号: ${orderNo}` : orderNo
  }
  
  /**
   * 格式化订单状态显示
   * @param {string} status - 订单状态
   * @param {Object} statusOptions - 状态选项配置
   * @returns {Object} 格式化后的状态显示对象
   */
  static formatOrderStatus(status, statusOptions = []) {
    const statusOption = statusOptions.find(option => option.value === status)
    
    if (statusOption) {
      return {
        label: statusOption.label,
        color: statusOption.color,
        value: status
      }
    }
    
    return {
      label: status || '未知状态',
      color: 'default',
      value: status
    }
  }
  
  /**
   * 格式化订单摘要
   * @param {Object} order - 订单对象
   * @returns {string} 订单摘要字符串
   */
  static formatOrderSummary(order) {
    if (!order) {
      return '无效订单'
    }
    
    const orderNo = order.orderNo || '未知'
    const itemCount = order.items ? order.items.length : 0
    const totalAmount = AmountFormatter.formatCurrency(order.finalAmount || 0)
    const createTime = DateFormatter.formatDateTime(order.createdAt)
    
    return `${orderNo} | ${itemCount}件商品 | ${totalAmount} | ${createTime}`
  }
}

/**
 * 会员信息格式化工具类
 */
export class MemberFormatter {
  
  /**
   * 格式化会员卡号显示
   * @param {string} cardNo - 会员卡号
   * @param {Object} options - 格式化选项
   * @param {boolean} options.showPrefix - 是否显示前缀，默认true
   * @param {boolean} options.maskMiddle - 是否遮罩中间部分，默认false
   * @returns {string} 格式化后的会员卡号
   */
  static formatMemberCardNo(cardNo, options = {}) {
    const { showPrefix = true, maskMiddle = false } = options
    
    if (!cardNo) {
      return showPrefix ? '卡号: 无' : '无'
    }
    
    let displayCardNo = cardNo
    
    // 遮罩中间部分
    if (maskMiddle && cardNo.length > 6) {
      const start = cardNo.substring(0, 3)
      const end = cardNo.substring(cardNo.length - 3)
      const middle = '*'.repeat(cardNo.length - 6)
      displayCardNo = start + middle + end
    }
    
    return showPrefix ? `卡号: ${displayCardNo}` : displayCardNo
  }
  
  /**
   * 格式化会员手机号显示
   * @param {string} phone - 手机号
   * @param {Object} options - 格式化选项
   * @param {boolean} options.showPrefix - 是否显示前缀，默认true
   * @param {boolean} options.maskMiddle - 是否遮罩中间部分，默认true
   * @returns {string} 格式化后的手机号
   */
  static formatMemberPhone(phone, options = {}) {
    const { showPrefix = true, maskMiddle = true } = options
    
    if (!phone) {
      return showPrefix ? '手机: 无' : '无'
    }
    
    let displayPhone = phone
    
    // 遮罩中间部分
    if (maskMiddle && phone.length === 11) {
      displayPhone = phone.substring(0, 3) + '****' + phone.substring(7)
    }
    
    return showPrefix ? `手机: ${displayPhone}` : displayPhone
  }
  
  /**
   * 格式化会员等级显示
   * @param {string} level - 会员等级
   * @param {Array} levelOptions - 等级选项配置
   * @returns {Object} 格式化后的等级显示对象
   */
  static formatMemberLevel(level, levelOptions = []) {
    const levelOption = levelOptions.find(option => option.value === level)
    
    if (levelOption) {
      return {
        label: levelOption.label,
        color: levelOption.color,
        value: level
      }
    }
    
    return {
      label: level || '普通会员',
      color: '#666666',
      value: level
    }
  }
  
  /**
   * 格式化会员积分显示
   * @param {number} points - 积分数
   * @param {Object} options - 格式化选项
   * @param {boolean} options.showPrefix - 是否显示前缀，默认true
   * @param {boolean} options.showThousandsSeparator - 是否显示千分位分隔符，默认true
   * @returns {string} 格式化后的积分显示
   */
  static formatMemberPoints(points, options = {}) {
    const { showPrefix = true, showThousandsSeparator = true } = options
    
    if (typeof points !== 'number' || isNaN(points)) {
      return showPrefix ? '积分: 0' : '0'
    }
    
    let formattedPoints = Math.floor(points).toString()
    
    // 添加千分位分隔符
    if (showThousandsSeparator) {
      formattedPoints = formattedPoints.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    }
    
    return showPrefix ? `积分: ${formattedPoints}` : formattedPoints
  }
  
  /**
   * 格式化会员余额显示
   * @param {number} balance - 余额
   * @param {Object} options - 格式化选项
   * @returns {string} 格式化后的余额显示
   */
  static formatMemberBalance(balance, options = {}) {
    const formattedBalance = AmountFormatter.formatCurrency(balance || 0, options)
    return `余额: ${formattedBalance}`
  }
}

/**
 * 通用文本格式化工具类
 */
export class TextFormatter {
  
  /**
   * 截断文本
   * @param {string} text - 原始文本
   * @param {number} maxLength - 最大长度
   * @param {string} suffix - 后缀，默认'...'
   * @returns {string} 截断后的文本
   */
  static truncate(text, maxLength, suffix = '...') {
    if (!text || typeof text !== 'string') {
      return ''
    }
    
    if (text.length <= maxLength) {
      return text
    }
    
    return text.substring(0, maxLength - suffix.length) + suffix
  }
  
  /**
   * 首字母大写
   * @param {string} text - 原始文本
   * @returns {string} 首字母大写的文本
   */
  static capitalize(text) {
    if (!text || typeof text !== 'string') {
      return ''
    }
    
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
  }
  
  /**
   * 驼峰命名转换
   * @param {string} text - 原始文本
   * @param {string} separator - 分隔符，默认'-'
   * @returns {string} 驼峰命名的文本
   */
  static toCamelCase(text, separator = '-') {
    if (!text || typeof text !== 'string') {
      return ''
    }
    
    return text
      .split(separator)
      .map((word, index) => index === 0 ? word.toLowerCase() : this.capitalize(word))
      .join('')
  }
  
  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @param {number} decimals - 小数位数，默认2
   * @returns {string} 格式化后的文件大小
   */
  static formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }
}