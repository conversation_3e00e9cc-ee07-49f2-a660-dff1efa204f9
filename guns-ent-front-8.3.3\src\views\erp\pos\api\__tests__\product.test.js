/**
 * 商品API单元测试
 * 
 * 测试商品相关API接口的调用和错误处理
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ProductApi } from '../product'
import Request from '@/utils/request/request-util'

// Mock Request模块
vi.mock('@/utils/request/request-util', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn()
  }
}))

// Mock 错误处理器和性能监控器
vi.mock('../../utils/error-handler', () => ({
  PosErrorHandler: {
    wrapApiCall: vi.fn((fn) => fn)
  }
}))

vi.mock('../../utils/performance-monitor', () => ({
  PerformanceMonitor: {
    measureApiCall: vi.fn((name, fn) => fn)
  }
}))

describe('ProductApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('getProductList', () => {
    it('应该成功获取商品列表', async () => {
      const queryParams = {
        categoryId: 'CAT001',
        keyword: '苹果',
        onlyInStock: true,
        page: 1,
        limit: 20
      }
      const mockResponse = {
        products: [
          {
            id: 'P001',
            name: '红苹果',
            price: 5.5,
            stock: 100,
            categoryId: 'CAT001',
            categoryName: '水果'
          },
          {
            id: 'P002',
            name: '青苹果',
            price: 4.5,
            stock: 80,
            categoryId: 'CAT001',
            categoryName: '水果'
          }
        ],
        total: 2,
        page: 1,
        limit: 20
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getProductList(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/list', queryParams)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该使用默认查询参数', async () => {
      Request.get.mockResolvedValue({ products: [], total: 0 })
      
      await ProductApi.getProductList({})
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/list', {
        page: 1,
        limit: 20,
        onlyInStock: false
      })
    })
  })
  
  describe('getProductDetail', () => {
    it('应该成功获取商品详情', async () => {
      const mockProduct = {
        id: 'P001',
        name: '红苹果',
        price: 5.5,
        stock: 100,
        unit: '斤',
        barcode: '1234567890123',
        categoryId: 'CAT001',
        categoryName: '水果',
        specifications: '新鲜红苹果，产地山东',
        images: ['image1.jpg', 'image2.jpg'],
        pricingType: 'NORMAL',
        safetyStock: 10,
        status: 'ACTIVE'
      }
      Request.get.mockResolvedValue(mockProduct)
      
      const result = await ProductApi.getProductDetail('P001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/detail/P001')
      expect(result).toEqual(mockProduct)
    })
    
    it('应该处理商品不存在的情况', async () => {
      const mockError = new Error('商品不存在')
      Request.get.mockRejectedValue(mockError)
      
      await expect(ProductApi.getProductDetail('INVALID')).rejects.toThrow('商品不存在')
    })
  })
  
  describe('searchProducts', () => {
    it('应该成功搜索商品', async () => {
      const searchParams = {
        keyword: '苹果',
        searchFields: ['name', 'barcode'],
        categoryId: 'CAT001',
        priceRange: { min: 0, max: 10 },
        onlyInStock: true
      }
      const mockResponse = [
        {
          id: 'P001',
          name: '红苹果',
          price: 5.5,
          stock: 100,
          barcode: '1234567890123'
        },
        {
          id: 'P002',
          name: '青苹果',
          price: 4.5,
          stock: 80,
          barcode: '1234567890124'
        }
      ]
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.searchProducts(searchParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/search', {
        ...searchParams,
        limit: 50
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理空搜索结果', async () => {
      Request.get.mockResolvedValue([])
      
      const result = await ProductApi.searchProducts({ keyword: '不存在的商品' })
      
      expect(result).toEqual([])
    })
  })
  
  describe('getProductByBarcode', () => {
    it('应该成功根据条码获取商品', async () => {
      const mockProduct = {
        id: 'P001',
        name: '红苹果',
        price: 5.5,
        barcode: '1234567890123',
        stock: 100
      }
      Request.get.mockResolvedValue(mockProduct)
      
      const result = await ProductApi.getProductByBarcode('1234567890123')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/barcode/1234567890123')
      expect(result).toEqual(mockProduct)
    })
    
    it('应该处理条码不存在的情况', async () => {
      const mockError = new Error('未找到对应的商品')
      Request.get.mockRejectedValue(mockError)
      
      await expect(ProductApi.getProductByBarcode('INVALID')).rejects.toThrow('未找到对应的商品')
    })
  })
  
  describe('getProductCategories', () => {
    it('应该成功获取商品分类', async () => {
      const mockCategories = [
        {
          id: 'CAT001',
          name: '水果',
          parentId: null,
          level: 1,
          sort: 1,
          productCount: 50,
          children: [
            {
              id: 'CAT001_001',
              name: '苹果',
              parentId: 'CAT001',
              level: 2,
              sort: 1,
              productCount: 20
            }
          ]
        },
        {
          id: 'CAT002',
          name: '蔬菜',
          parentId: null,
          level: 1,
          sort: 2,
          productCount: 30
        }
      ]
      Request.get.mockResolvedValue(mockCategories)
      
      const result = await ProductApi.getProductCategories({ includeProductCount: true })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/categories', {
        includeProductCount: true
      })
      expect(result).toEqual(mockCategories)
    })
  })
  
  describe('getHotProducts', () => {
    it('应该成功获取热销商品', async () => {
      const queryParams = {
        categoryId: 'CAT001',
        days: 7,
        limit: 10
      }
      const mockResponse = [
        {
          id: 'P001',
          name: '红苹果',
          price: 5.5,
          salesCount: 100,
          salesAmount: 550,
          rank: 1
        },
        {
          id: 'P002',
          name: '香蕉',
          price: 3.0,
          salesCount: 80,
          salesAmount: 240,
          rank: 2
        }
      ]
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getHotProducts(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/hot', queryParams)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该使用默认参数', async () => {
      Request.get.mockResolvedValue([])
      
      await ProductApi.getHotProducts({})
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/hot', {
        days: 30,
        limit: 20
      })
    })
  })
  
  describe('getRecommendProducts', () => {
    it('应该成功获取推荐商品', async () => {
      const queryParams = {
        productId: 'P001',
        type: 'similar',
        limit: 5
      }
      const mockResponse = [
        {
          id: 'P002',
          name: '青苹果',
          price: 4.5,
          similarity: 0.85,
          reason: '同类商品'
        },
        {
          id: 'P003',
          name: '红富士苹果',
          price: 6.0,
          similarity: 0.90,
          reason: '相似商品'
        }
      ]
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getRecommendProducts(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/recommend', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('checkProductStock', () => {
    it('应该成功检查商品库存', async () => {
      const stockParams = {
        productId: 'P001',
        quantity: 5,
        storeId: 'STORE001'
      }
      const mockResponse = {
        productId: 'P001',
        available: true,
        currentStock: 100,
        requestedQuantity: 5,
        remainingStock: 95,
        safetyStock: 10,
        stockStatus: 'SUFFICIENT'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.checkProductStock(stockParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/product/checkStock', stockParams)
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理库存不足的情况', async () => {
      const mockResponse = {
        productId: 'P001',
        available: false,
        currentStock: 2,
        requestedQuantity: 5,
        stockStatus: 'INSUFFICIENT'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.checkProductStock({
        productId: 'P001',
        quantity: 5
      })
      
      expect(result.available).toBe(false)
      expect(result.stockStatus).toBe('INSUFFICIENT')
    })
  })
  
  describe('batchCheckStock', () => {
    it('应该成功批量检查库存', async () => {
      const batchParams = {
        items: [
          { productId: 'P001', quantity: 2 },
          { productId: 'P002', quantity: 3 }
        ],
        storeId: 'STORE001'
      }
      const mockResponse = [
        {
          productId: 'P001',
          available: true,
          currentStock: 100,
          requestedQuantity: 2
        },
        {
          productId: 'P002',
          available: false,
          currentStock: 1,
          requestedQuantity: 3
        }
      ]
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.batchCheckStock(batchParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/product/batchCheckStock', batchParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getProductPricing', () => {
    it('应该成功获取商品定价', async () => {
      const pricingParams = {
        productId: 'P001',
        memberId: 'M001',
        quantity: 5
      }
      const mockResponse = {
        productId: 'P001',
        basePrice: 5.5,
        memberPrice: 5.0,
        finalPrice: 5.0,
        discountRate: 0.09,
        discountAmount: 0.5,
        pricingRules: [
          {
            type: 'MEMBER_DISCOUNT',
            description: '会员折扣',
            discountRate: 0.09
          }
        ]
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getProductPricing(pricingParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/pricing', pricingParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getProductImages', () => {
    it('应该成功获取商品图片', async () => {
      const mockImages = [
        {
          id: 'IMG001',
          url: '/images/products/P001_1.jpg',
          type: 'MAIN',
          sort: 1
        },
        {
          id: 'IMG002',
          url: '/images/products/P001_2.jpg',
          type: 'DETAIL',
          sort: 2
        }
      ]
      Request.get.mockResolvedValue(mockImages)
      
      const result = await ProductApi.getProductImages('P001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/images/P001')
      expect(result).toEqual(mockImages)
    })
  })
  
  describe('getProductSpecs', () => {
    it('应该成功获取商品规格', async () => {
      const mockSpecs = [
        {
          id: 'SPEC001',
          name: '规格',
          value: '500g',
          price: 5.5,
          stock: 100,
          barcode: '1234567890123'
        },
        {
          id: 'SPEC002',
          name: '规格',
          value: '1kg',
          price: 10.0,
          stock: 50,
          barcode: '1234567890124'
        }
      ]
      Request.get.mockResolvedValue(mockSpecs)
      
      const result = await ProductApi.getProductSpecs('P001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/specs/P001')
      expect(result).toEqual(mockSpecs)
    })
  })
  
  describe('getProductReviews', () => {
    it('应该成功获取商品评价', async () => {
      const queryParams = {
        productId: 'P001',
        rating: 5,
        page: 1,
        limit: 10
      }
      const mockResponse = {
        reviews: [
          {
            id: 'REV001',
            memberId: 'M001',
            memberName: '张三',
            rating: 5,
            content: '商品很新鲜，质量很好',
            createdAt: '2025-01-01T10:00:00Z'
          }
        ],
        total: 1,
        averageRating: 4.8,
        ratingStats: {
          5: 80,
          4: 15,
          3: 3,
          2: 1,
          1: 1
        }
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getProductReviews(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/reviews', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getProductStatistics', () => {
    it('应该成功获取商品统计', async () => {
      const queryParams = {
        productId: 'P001',
        startTime: '2025-01-01T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z'
      }
      const mockResponse = {
        productId: 'P001',
        salesCount: 100,
        salesAmount: 550,
        viewCount: 500,
        addToCartCount: 150,
        conversionRate: 0.67,
        averageRating: 4.8,
        reviewCount: 20
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getProductStatistics(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/statistics', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('updateProductStock', () => {
    it('应该成功更新商品库存', async () => {
      const updateParams = {
        productId: 'P001',
        changeType: 'INCREASE',
        quantity: 50,
        reason: '进货补充',
        operatorId: 'OPERATOR001'
      }
      const mockResponse = {
        success: true,
        productId: 'P001',
        oldStock: 100,
        newStock: 150,
        changeQuantity: 50,
        updatedAt: '2025-01-02T10:00:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.updateProductStock(updateParams)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/pos/product/updateStock', {
        ...updateParams,
        updateTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getStockAlerts', () => {
    it('应该成功获取库存预警', async () => {
      const queryParams = {
        alertType: 'LOW_STOCK',
        categoryId: 'CAT001',
        limit: 20
      }
      const mockResponse = [
        {
          productId: 'P001',
          productName: '红苹果',
          currentStock: 5,
          safetyStock: 10,
          alertType: 'LOW_STOCK',
          alertLevel: 'WARNING'
        },
        {
          productId: 'P002',
          productName: '香蕉',
          currentStock: 0,
          safetyStock: 20,
          alertType: 'OUT_OF_STOCK',
          alertLevel: 'CRITICAL'
        }
      ]
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await ProductApi.getStockAlerts(queryParams)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/pos/product/stockAlerts', queryParams)
      expect(result).toEqual(mockResponse)
    })
  })
})