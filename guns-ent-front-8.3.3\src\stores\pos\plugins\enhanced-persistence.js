/**
 * 增强的POS状态持久化插件
 * 
 * 集成安全性、性能优化、数据完整性校验等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { watch } from 'vue'
import { 
  getPersistenceConfig,
  maskSensitiveData,
  generateChecksum,
  verifyChecksum,
  cleanupExpiredData,
  checkStorageQuota,
  autoCleanupStorage,
  securityConfig,
  performanceConfig
} from '../config/persistence'

/**
 * 增强的持久化插件类
 */
export class EnhancedPersistencePlugin {
  constructor(options = {}) {
    this.options = {
      // 全局配置覆盖
      globalKeyPrefix: 'pos_enhanced_',
      enableGlobalEncryption: false,
      enableGlobalCompression: true,
      enableBatchSave: true,
      enableQuotaManagement: true,
      enableAutoCleanup: true,
      ...options
    }
    
    // 内部状态
    this.stores = new Map()
    this.watchers = new Map()
    this.saveTimers = new Map()
    this.batchSaveQueue = new Map()
    this.cache = new Map()
    
    // 定时器
    this.batchSaveTimer = null
    this.cleanupTimer = null
    this.quotaCheckTimer = null
    
    // 初始化
    this.initialize()
  }
  
  /**
   * 初始化插件
   */
  initialize() {
    // 启动批量保存
    if (this.options.enableBatchSave && performanceConfig.batchSave.enabled) {
      this.startBatchSave()
    }
    
    // 启动自动清理
    if (this.options.enableAutoCleanup && securityConfig.enableAutoCleanup) {
      this.startAutoCleanup()
    }
    
    // 启动配额检查
    if (this.options.enableQuotaManagement && performanceConfig.quota.enabled) {
      this.startQuotaCheck()
    }
    
    console.log('🚀 增强持久化插件已初始化')
  }
  
  /**
   * 获取存储引擎
   * @param {string} engine - 存储引擎类型
   * @returns {Storage} 存储引擎
   */
  getStorageEngine(engine) {
    switch (engine) {
      case 'sessionStorage':
        return sessionStorage
      case 'localStorage':
      default:
        return localStorage
    }
  }
  
  /**
   * 生成存储键
   * @param {string} storeId - 存储ID
   * @param {Object} config - 配置对象
   * @returns {string} 存储键
   */
  generateKey(storeId, config) {
    const prefix = config.keyPrefix || this.options.globalKeyPrefix
    return `${prefix}${storeId}`
  }
  
  /**
   * 高级数据压缩
   * @param {string} data - 要压缩的数据
   * @param {Object} config - 压缩配置
   * @returns {string} 压缩后的数据
   */
  compressData(data, config) {
    if (!config.enableCompression && !this.options.enableGlobalCompression) {
      return data
    }
    
    try {
      const compressionConfig = performanceConfig.compression
      
      // 只有数据大小超过阈值才压缩
      if (data.length < compressionConfig.threshold) {
        return data
      }
      
      // 简单压缩：移除多余空格和换行
      let compressed = data.replace(/\\s+/g, ' ').trim()
      
      // 进一步压缩：替换常见模式
      const patterns = [
        [/"([^"]+)":/g, '"$1":'], // 移除键值对中的空格
        [/,\\s*}/g, '}'], // 移除对象结尾的空格
        [/,\\s*]/g, ']'], // 移除数组结尾的空格
        [/:\\s*/g, ':'], // 移除冒号后的空格
        [/,\\s*/g, ','] // 移除逗号后的空格
      ]
      
      patterns.forEach(([pattern, replacement]) => {
        compressed = compressed.replace(pattern, replacement)
      })
      
      return compressed
    } catch (error) {
      console.warn('数据压缩失败:', error)
      return data
    }
  }
  
  /**
   * 高级数据加密
   * @param {string} data - 要加密的数据
   * @param {Object} config - 加密配置
   * @returns {string} 加密后的数据
   */
  encryptData(data, config) {
    if (!config.enableEncryption && !this.options.enableGlobalEncryption) {
      return data
    }
    
    try {
      // 简单的XOR加密（实际项目中应使用更安全的加密算法）
      const key = securityConfig.encryptionKey
      let encrypted = ''
      
      for (let i = 0; i < data.length; i++) {
        const charCode = data.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        encrypted += String.fromCharCode(charCode)
      }
      
      // Base64编码
      return btoa(encrypted)
    } catch (error) {
      console.warn('数据加密失败:', error)
      return data
    }
  }
  
  /**
   * 高级数据解密
   * @param {string} encryptedData - 加密的数据
   * @param {Object} config - 解密配置
   * @returns {string} 解密后的数据
   */
  decryptData(encryptedData, config) {
    if (!config.enableEncryption && !this.options.enableGlobalEncryption) {
      return encryptedData
    }
    
    try {
      // Base64解码
      const encrypted = atob(encryptedData)
      
      // XOR解密
      const key = securityConfig.encryptionKey
      let decrypted = ''
      
      for (let i = 0; i < encrypted.length; i++) {
        const charCode = encrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length)
        decrypted += String.fromCharCode(charCode)
      }
      
      return decrypted
    } catch (error) {
      console.warn('数据解密失败:', error)
      return encryptedData
    }
  }
  
  /**
   * 序列化状态数据
   * @param {string} storeId - 存储ID
   * @param {Object} state - 状态数据
   * @returns {Object} 序列化结果
   */
  serializeState(storeId, state) {
    try {
      const config = getPersistenceConfig(storeId)
      
      // 过滤状态数据
      const filteredState = this.filterState(state, config)
      
      // 脱敏处理
      const maskedState = config.sensitiveFields 
        ? maskSensitiveData(filteredState, config.sensitiveFields)
        : filteredState
      
      // 生成校验和
      const checksum = generateChecksum(maskedState)
      
      // 创建数据包
      const dataPackage = {
        data: maskedState,
        checksum: checksum,
        timestamp: Date.now(),
        version: '2.0',
        storeId: storeId,
        config: {
          encrypted: config.enableEncryption,
          compressed: config.enableCompression
        }
      }
      
      // 序列化
      const serialized = JSON.stringify(dataPackage)
      
      // 压缩
      const compressed = this.compressData(serialized, config)
      
      // 加密
      const encrypted = this.encryptData(compressed, config)
      
      return {
        success: true,
        data: encrypted,
        originalSize: serialized.length,
        finalSize: encrypted.length,
        compressionRatio: serialized.length > 0 ? (1 - encrypted.length / serialized.length) : 0
      }
      
    } catch (error) {
      console.error(`序列化状态失败 [${storeId}]:`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 反序列化状态数据
   * @param {string} storeId - 存储ID
   * @param {string} data - 序列化的数据
   * @returns {Object} 反序列化结果
   */
  deserializeState(storeId, data) {
    try {
      const config = getPersistenceConfig(storeId)
      
      // 解密
      const decrypted = this.decryptData(data, config)
      
      // 解压（这里简化处理，实际应该有对应的解压算法）
      const decompressed = decrypted
      
      // 反序列化
      const dataPackage = JSON.parse(decompressed)
      
      // 验证数据包格式
      if (!dataPackage.data || !dataPackage.timestamp) {
        throw new Error('数据包格式无效')
      }
      
      // 检查数据是否过期
      const age = Date.now() - dataPackage.timestamp
      if (age > config.expireTime) {
        throw new Error('数据已过期')
      }
      
      // 验证数据完整性
      if (securityConfig.enableIntegrityCheck && dataPackage.checksum) {
        if (!verifyChecksum(dataPackage.data, dataPackage.checksum)) {
          throw new Error('数据完整性校验失败')
        }
      }
      
      return {
        success: true,
        data: dataPackage.data,
        timestamp: dataPackage.timestamp,
        version: dataPackage.version
      }
      
    } catch (error) {
      console.error(`反序列化状态失败 [${storeId}]:`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }
  
  /**
   * 过滤状态数据
   * @param {Object} state - 原始状态
   * @param {Object} config - 配置对象
   * @returns {Object} 过滤后的状态
   */
  filterState(state, config) {
    const filtered = {}
    
    for (const [key, value] of Object.entries(state)) {
      // 检查排除列表
      if (config.excludePaths && config.excludePaths.includes(key)) {
        continue
      }
      
      // 检查包含列表
      if (config.persistPaths && config.persistPaths.length > 0) {
        if (!config.persistPaths.includes(key)) {
          continue
        }
      }
      
      // 排除函数和Symbol
      if (typeof value === 'function' || typeof value === 'symbol') {
        continue
      }
      
      // 排除undefined
      if (value === undefined) {
        continue
      }
      
      filtered[key] = value
    }
    
    return filtered
  }
  
  /**
   * 保存状态到存储
   * @param {string} storeId - 存储ID
   * @param {Object} state - 状态数据
   * @param {boolean} immediate - 是否立即保存
   * @returns {boolean} 是否保存成功
   */
  saveState(storeId, state, immediate = false) {
    try {
      const config = getPersistenceConfig(storeId)
      
      if (!immediate && this.options.enableBatchSave) {
        // 添加到批量保存队列
        this.addToBatchSaveQueue(storeId, state)
        return true
      }
      
      // 检查存储配额
      const storage = this.getStorageEngine(config.storageEngine)
      if (this.options.enableQuotaManagement) {
        const quotaInfo = checkStorageQuota(storage)
        if (quotaInfo.needsCleanup) {
          autoCleanupStorage(storage, config.keyPrefix)
        }
      }
      
      // 序列化数据
      const serializeResult = this.serializeState(storeId, state)
      if (!serializeResult.success) {
        return false
      }
      
      // 保存到存储
      const key = this.generateKey(storeId, config)
      storage.setItem(key, serializeResult.data)
      
      // 更新缓存
      if (performanceConfig.cache.enabled) {
        this.updateCache(storeId, state)
      }
      
      console.log(`✅ 状态已保存 [${storeId}]: ${serializeResult.originalSize}B → ${serializeResult.finalSize}B (压缩率: ${(serializeResult.compressionRatio * 100).toFixed(1)}%)`)
      
      return true
      
    } catch (error) {
      console.error(`❌ 保存状态失败 [${storeId}]:`, error)
      return false
    }
  }
  
  /**
   * 从存储加载状态
   * @param {string} storeId - 存储ID
   * @returns {Object|null} 状态数据
   */
  loadState(storeId) {
    try {
      // 检查缓存
      if (performanceConfig.cache.enabled) {
        const cached = this.getFromCache(storeId)
        if (cached) {
          console.log(`📦 从缓存加载状态 [${storeId}]`)
          return cached
        }
      }
      
      const config = getPersistenceConfig(storeId)
      const storage = this.getStorageEngine(config.storageEngine)
      const key = this.generateKey(storeId, config)
      
      const storedData = storage.getItem(key)
      if (!storedData) {
        return null
      }
      
      // 反序列化数据
      const deserializeResult = this.deserializeState(storeId, storedData)
      if (!deserializeResult.success) {
        // 数据损坏，删除
        storage.removeItem(key)
        return null
      }
      
      // 更新缓存
      if (performanceConfig.cache.enabled) {
        this.updateCache(storeId, deserializeResult.data)
      }
      
      console.log(`✅ 状态已加载 [${storeId}]`)
      return deserializeResult.data
      
    } catch (error) {
      console.error(`❌ 加载状态失败 [${storeId}]:`, error)
      return null
    }
  }
  
  /**
   * 添加到批量保存队列
   * @param {string} storeId - 存储ID
   * @param {Object} state - 状态数据
   */
  addToBatchSaveQueue(storeId, state) {
    if (!this.batchSaveQueue.has(storeId)) {
      this.batchSaveQueue.set(storeId, [])
    }
    
    const queue = this.batchSaveQueue.get(storeId)
    queue.push({
      state: { ...state },
      timestamp: Date.now()
    })
    
    // 限制队列大小
    const maxSize = performanceConfig.batchSave.batchSize
    if (queue.length > maxSize) {
      queue.splice(0, queue.length - maxSize)
    }
  }
  
  /**
   * 执行批量保存
   */
  executeBatchSave() {
    if (this.batchSaveQueue.size === 0) {
      return
    }
    
    console.log(`🔄 执行批量保存 (${this.batchSaveQueue.size}个存储)`)
    
    for (const [storeId, queue] of this.batchSaveQueue.entries()) {
      if (queue.length > 0) {
        // 取最新的状态
        const latestState = queue[queue.length - 1].state
        this.saveState(storeId, latestState, true)
      }
    }
    
    // 清空队列
    this.batchSaveQueue.clear()
  }
  
  /**
   * 启动批量保存
   */
  startBatchSave() {
    if (this.batchSaveTimer) {
      return
    }
    
    const interval = performanceConfig.batchSave.batchInterval
    this.batchSaveTimer = setInterval(() => {
      this.executeBatchSave()
    }, interval)
    
    console.log(`🔄 批量保存已启动 (间隔: ${interval}ms)`)
  }
  
  /**
   * 停止批量保存
   */
  stopBatchSave() {
    if (this.batchSaveTimer) {
      clearInterval(this.batchSaveTimer)
      this.batchSaveTimer = null
      
      // 执行最后一次保存
      this.executeBatchSave()
      
      console.log('⏹️ 批量保存已停止')
    }
  }
  
  /**
   * 更新缓存
   * @param {string} storeId - 存储ID
   * @param {Object} data - 数据
   */
  updateCache(storeId, data) {
    const cache = performanceConfig.cache
    
    // 检查缓存大小限制
    if (this.cache.size >= cache.maxSize) {
      // 删除最旧的缓存项
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }
    
    this.cache.set(storeId, {
      data: { ...data },
      timestamp: Date.now(),
      ttl: cache.ttl
    })
  }
  
  /**
   * 从缓存获取数据
   * @param {string} storeId - 存储ID
   * @returns {Object|null} 缓存的数据
   */
  getFromCache(storeId) {
    const cached = this.cache.get(storeId)
    if (!cached) {
      return null
    }
    
    // 检查TTL
    const age = Date.now() - cached.timestamp
    if (age > cached.ttl) {
      this.cache.delete(storeId)
      return null
    }
    
    return cached.data
  }
  
  /**
   * 启动自动清理
   */
  startAutoCleanup() {
    if (this.cleanupTimer) {
      return
    }
    
    const interval = securityConfig.autoCleanupInterval
    this.cleanupTimer = setInterval(() => {
      this.performAutoCleanup()
    }, interval)
    
    console.log(`🧹 自动清理已启动 (间隔: ${interval}ms)`)
  }
  
  /**
   * 执行自动清理
   */
  performAutoCleanup() {
    console.log('🧹 执行自动清理...')
    
    let totalCleaned = 0
    
    // 清理localStorage
    totalCleaned += cleanupExpiredData(localStorage, this.options.globalKeyPrefix)
    
    // 清理sessionStorage
    totalCleaned += cleanupExpiredData(sessionStorage, this.options.globalKeyPrefix)
    
    // 清理缓存
    this.cleanupCache()
    
    if (totalCleaned > 0) {
      console.log(`🧹 自动清理完成，清理了 ${totalCleaned} 个过期项`)
    }
  }
  
  /**
   * 清理缓存
   */
  cleanupCache() {
    const now = Date.now()
    const keysToDelete = []
    
    for (const [key, cached] of this.cache.entries()) {
      const age = now - cached.timestamp
      if (age > cached.ttl) {
        keysToDelete.push(key)
      }
    }
    
    keysToDelete.forEach(key => {
      this.cache.delete(key)
    })
    
    if (keysToDelete.length > 0) {
      console.log(`🧹 清理了 ${keysToDelete.length} 个过期缓存项`)
    }
  }
  
  /**
   * 启动配额检查
   */
  startQuotaCheck() {
    if (this.quotaCheckTimer) {
      return
    }
    
    const interval = 5 * 60 * 1000 // 5分钟检查一次
    this.quotaCheckTimer = setInterval(() => {
      this.performQuotaCheck()
    }, interval)
    
    console.log(`📊 配额检查已启动 (间隔: ${interval}ms)`)
  }
  
  /**
   * 执行配额检查
   */
  performQuotaCheck() {
    const localStorageQuota = checkStorageQuota(localStorage)
    const sessionStorageQuota = checkStorageQuota(sessionStorage)
    
    if (localStorageQuota.isWarning) {
      console.warn(`⚠️ localStorage使用率过高: ${(localStorageQuota.percentage * 100).toFixed(1)}%`)
    }
    
    if (sessionStorageQuota.isWarning) {
      console.warn(`⚠️ sessionStorage使用率过高: ${(sessionStorageQuota.percentage * 100).toFixed(1)}%`)
    }
    
    if (localStorageQuota.needsCleanup) {
      autoCleanupStorage(localStorage, this.options.globalKeyPrefix)
    }
    
    if (sessionStorageQuota.needsCleanup) {
      autoCleanupStorage(sessionStorage, this.options.globalKeyPrefix)
    }
  }
  
  /**
   * 设置状态监听器
   * @param {string} storeId - 存储ID
   * @param {Object} store - Pinia store实例
   */
  setupWatcher(storeId, store) {
    if (this.watchers.has(storeId)) {
      return
    }
    
    const config = getPersistenceConfig(storeId)
    
    // 设置防抖保存
    const debouncedSave = this.createDebouncedSave(storeId, config.autoSaveInterval || 1000)
    
    // 监听状态变化
    const unwatch = watch(
      () => store.$state,
      (newState) => {
        if (config.enableAutoSave) {
          debouncedSave(newState)
        }
      },
      { 
        deep: true,
        flush: 'post'
      }
    )
    
    this.watchers.set(storeId, unwatch)
    console.log(`👁️ 已设置状态监听器 [${storeId}]`)
  }
  
  /**
   * 创建防抖保存函数
   * @param {string} storeId - 存储ID
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖函数
   */
  createDebouncedSave(storeId, delay) {
    return (state) => {
      // 清除之前的定时器
      if (this.saveTimers.has(storeId)) {
        clearTimeout(this.saveTimers.get(storeId))
      }
      
      // 设置新的定时器
      const timer = setTimeout(() => {
        this.saveState(storeId, state)
        this.saveTimers.delete(storeId)
      }, delay)
      
      this.saveTimers.set(storeId, timer)
    }
  }
  
  /**
   * 清理资源
   */
  cleanup() {
    // 停止定时器
    this.stopBatchSave()
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    if (this.quotaCheckTimer) {
      clearInterval(this.quotaCheckTimer)
      this.quotaCheckTimer = null
    }
    
    // 清理监听器
    this.watchers.forEach(unwatch => {
      unwatch()
    })
    this.watchers.clear()
    
    // 清理保存定时器
    this.saveTimers.forEach(timer => {
      clearTimeout(timer)
    })
    this.saveTimers.clear()
    
    // 清理缓存
    this.cache.clear()
    
    console.log('🧹 增强持久化插件已清理')
  }
  
  /**
   * 获取插件统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return {
      stores: this.stores.size,
      watchers: this.watchers.size,
      batchSaveQueue: this.batchSaveQueue.size,
      cache: {
        size: this.cache.size,
        maxSize: performanceConfig.cache.maxSize
      },
      localStorage: checkStorageQuota(localStorage),
      sessionStorage: checkStorageQuota(sessionStorage)
    }
  }
}

/**
 * 创建增强持久化插件的Pinia插件
 * @param {Object} options - 插件选项
 * @returns {Function} Pinia插件函数
 */
export function createEnhancedPersistencePlugin(options = {}) {
  const persistence = new EnhancedPersistencePlugin(options)
  
  return ({ store }) => {
    const storeId = store.$id
    persistence.stores.set(storeId, store)
    
    // 加载保存的状态
    const savedState = persistence.loadState(storeId)
    if (savedState) {
      store.$patch(savedState)
    }
    
    // 设置状态监听器
    persistence.setupWatcher(storeId, store)
    
    // 添加插件方法到store
    store.$enhancedPersistence = {
      save: (immediate = false) => persistence.saveState(storeId, store.$state, immediate),
      load: () => {
        const state = persistence.loadState(storeId)
        if (state) {
          store.$patch(state)
        }
        return state
      },
      getStats: () => persistence.getStatistics(),
      cleanup: () => persistence.cleanup()
    }
  }
}

// 默认导出
export default createEnhancedPersistencePlugin