/**
 * 验证工具函数单元测试
 * 
 * 测试商品验证、数量验证、价格验证、会员信息验证等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect } from 'vitest'
import {
  CartValidator,
  PaymentValidator,
  MemberValidator,
  OrderValidator,
  ProductValidator,
  BusinessValidator
} from '../validator'

describe('CartValidator', () => {
  describe('validateProduct', () => {
    it('应该验证有效的商品', () => {
      const product = {
        id: 'P001',
        name: '苹果',
        price: 5.5,
        pricingType: 'NORMAL'
      }
      
      const result = CartValidator.validateProduct(product)
      expect(result.isValid).toBe(true)
    })
    
    it('应该拒绝无效的商品', () => {
      expect(CartValidator.validateProduct(null).isValid).toBe(false)
      expect(CartValidator.validateProduct({}).isValid).toBe(false)
      
      const invalidProduct = {
        id: '',
        name: '苹果',
        price: 5.5
      }
      expect(CartValidator.validateProduct(invalidProduct).isValid).toBe(false)
    })
    
    it('应该验证商品价格', () => {
      const invalidPriceProduct = {
        id: 'P001',
        name: '苹果',
        price: 0
      }
      expect(CartValidator.validateProduct(invalidPriceProduct).isValid).toBe(false)
      
      const negativePriceProduct = {
        id: 'P001',
        name: '苹果',
        price: -5
      }
      expect(CartValidator.validateProduct(negativePriceProduct).isValid).toBe(false)
    })
  })
  
  describe('validateQuantity', () => {
    it('应该验证有效的数量', () => {
      expect(CartValidator.validateQuantity(1).isValid).toBe(true)
      expect(CartValidator.validateQuantity(2.5).isValid).toBe(true)
      expect(CartValidator.validateQuantity(0.001).isValid).toBe(true)
    })
    
    it('应该拒绝无效的数量', () => {
      expect(CartValidator.validateQuantity(0).isValid).toBe(false)
      expect(CartValidator.validateQuantity(-1).isValid).toBe(false)
      expect(CartValidator.validateQuantity(1001).isValid).toBe(false)
    })
    
    it('应该验证数量精度', () => {
      expect(CartValidator.validateQuantity(1.1234).isValid).toBe(false) // 超过3位小数
      expect(CartValidator.validateQuantity(1.123).isValid).toBe(true)
    })
  })
  
  describe('validateCartItem', () => {
    it('应该验证有效的购物车项', () => {
      const cartItem = {
        id: 'P001',
        name: '苹果',
        price: 5.5,
        quantity: 2,
        subtotal: 11
      }
      
      const result = CartValidator.validateCartItem(cartItem)
      expect(result.isValid).toBe(true)
    })
    
    it('应该验证小计金额计算', () => {
      const cartItem = {
        id: 'P001',
        name: '苹果',
        price: 5.5,
        quantity: 2,
        subtotal: 10 // 错误的小计
      }
      
      const result = CartValidator.validateCartItem(cartItem)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('小计金额计算错误')
    })
  })
  
  describe('validateCart', () => {
    it('应该验证有效的购物车', () => {
      const cartItems = [
        {
          id: 'P001',
          name: '苹果',
          price: 5.5,
          quantity: 2,
          subtotal: 11
        },
        {
          id: 'P002',
          name: '香蕉',
          price: 3,
          quantity: 1,
          subtotal: 3
        }
      ]
      
      const result = CartValidator.validateCart(cartItems)
      expect(result.isValid).toBe(true)
    })
    
    it('应该拒绝空购物车', () => {
      expect(CartValidator.validateCart([]).isValid).toBe(false)
      expect(CartValidator.validateCart(null).isValid).toBe(false)
    })
    
    it('应该检测重复商品', () => {
      const cartItems = [
        {
          id: 'P001',
          name: '苹果',
          price: 5.5,
          quantity: 2,
          subtotal: 11
        },
        {
          id: 'P001', // 重复的商品ID
          name: '苹果',
          price: 5.5,
          quantity: 1,
          subtotal: 5.5
        }
      ]
      
      const result = CartValidator.validateCart(cartItems)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('重复商品')
    })
  })
})

describe('PaymentValidator', () => {
  describe('validatePaymentAmount', () => {
    it('应该验证有效的支付金额', () => {
      expect(PaymentValidator.validatePaymentAmount(100).isValid).toBe(true)
      expect(PaymentValidator.validatePaymentAmount(0.01).isValid).toBe(true)
    })
    
    it('应该拒绝无效的支付金额', () => {
      expect(PaymentValidator.validatePaymentAmount(0).isValid).toBe(false)
      expect(PaymentValidator.validatePaymentAmount(-10).isValid).toBe(false)
      expect(PaymentValidator.validatePaymentAmount(1000000).isValid).toBe(false)
    })
  })
  
  describe('validateCashPayment', () => {
    it('应该验证有效的现金支付', () => {
      const paymentData = {
        payableAmount: 100,
        receivedAmount: 120
      }
      
      const result = PaymentValidator.validateCashPayment(paymentData)
      expect(result.isValid).toBe(true)
    })
    
    it('应该拒绝实收金额少于应付金额', () => {
      const paymentData = {
        payableAmount: 100,
        receivedAmount: 80
      }
      
      const result = PaymentValidator.validateCashPayment(paymentData)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('实收金额不能少于应付金额')
    })
  })
  
  describe('validateQrCodePayment', () => {
    it('应该验证有效的扫码支付', () => {
      const paymentData = {
        payableAmount: 100,
        paymentMethod: 'WECHAT'
      }
      
      const result = PaymentValidator.validateQrCodePayment(paymentData)
      expect(result.isValid).toBe(true)
    })
    
    it('应该拒绝无效的支付方式', () => {
      const paymentData = {
        payableAmount: 100,
        paymentMethod: 'INVALID'
      }
      
      const result = PaymentValidator.validateQrCodePayment(paymentData)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('无效的扫码支付方式')
    })
  })
  
  describe('validateBankCardPayment', () => {
    it('应该验证有效的银行卡支付', () => {
      const paymentData = {
        payableAmount: 100,
        cardNo: '****************' // 测试用的有效卡号
      }
      
      const result = PaymentValidator.validateBankCardPayment(paymentData)
      expect(result.isValid).toBe(true)
    })
    
    it('应该拒绝无效的银行卡号', () => {
      const paymentData = {
        payableAmount: 100,
        cardNo: '****************' // 无效的卡号
      }
      
      const result = PaymentValidator.validateBankCardPayment(paymentData)
      expect(result.isValid).toBe(false)
    })
  })
  
  describe('validateLuhn', () => {
    it('应该验证有效的银行卡号', () => {
      expect(PaymentValidator.validateLuhn('****************')).toBe(true)
      expect(PaymentValidator.validateLuhn('****************')).toBe(true)
    })
    
    it('应该拒绝无效的银行卡号', () => {
      expect(PaymentValidator.validateLuhn('****************')).toBe(false)
      expect(PaymentValidator.validateLuhn('invalid')).toBe(false)
    })
  })
})

describe('MemberValidator', () => {
  describe('validateMemberCardNo', () => {
    it('应该验证有效的会员卡号', () => {
      expect(MemberValidator.validateMemberCardNo('VIP123456').isValid).toBe(true)
      expect(MemberValidator.validateMemberCardNo('*********').isValid).toBe(true)
    })
    
    it('应该拒绝无效的会员卡号', () => {
      expect(MemberValidator.validateMemberCardNo('').isValid).toBe(false)
      expect(MemberValidator.validateMemberCardNo('12345').isValid).toBe(false) // 太短
      expect(MemberValidator.validateMemberCardNo('VIP-123456').isValid).toBe(false) // 包含特殊字符
    })
  })
  
  describe('validatePhone', () => {
    it('应该验证有效的手机号', () => {
      expect(MemberValidator.validatePhone('13812345678').isValid).toBe(true)
      expect(MemberValidator.validatePhone('15987654321').isValid).toBe(true)
    })
    
    it('应该拒绝无效的手机号', () => {
      expect(MemberValidator.validatePhone('12812345678').isValid).toBe(false) // 不是1开头
      expect(MemberValidator.validatePhone('1381234567').isValid).toBe(false) // 长度不对
      expect(MemberValidator.validatePhone('138*********').isValid).toBe(false) // 长度不对
    })
  })
  
  describe('validateMemberName', () => {
    it('应该验证有效的会员姓名', () => {
      expect(MemberValidator.validateMemberName('张三').isValid).toBe(true)
      expect(MemberValidator.validateMemberName('John Smith').isValid).toBe(true)
      expect(MemberValidator.validateMemberName('李 明').isValid).toBe(true)
    })
    
    it('应该拒绝无效的会员姓名', () => {
      expect(MemberValidator.validateMemberName('').isValid).toBe(false)
      expect(MemberValidator.validateMemberName('张').isValid).toBe(false) // 太短
      expect(MemberValidator.validateMemberName('张三123').isValid).toBe(false) // 包含数字
    })
  })
  
  describe('validatePoints', () => {
    it('应该验证有效的积分', () => {
      expect(MemberValidator.validatePoints(0).isValid).toBe(true)
      expect(MemberValidator.validatePoints(1000).isValid).toBe(true)
    })
    
    it('应该拒绝无效的积分', () => {
      expect(MemberValidator.validatePoints(-100).isValid).toBe(false)
      expect(MemberValidator.validatePoints(1.5).isValid).toBe(false) // 不是整数
      expect(MemberValidator.validatePoints(1000000000).isValid).toBe(false) // 超过最大值
    })
  })
  
  describe('validateBalance', () => {
    it('应该验证有效的余额', () => {
      expect(MemberValidator.validateBalance(0).isValid).toBe(true)
      expect(MemberValidator.validateBalance(100.5).isValid).toBe(true)
    })
    
    it('应该拒绝无效的余额', () => {
      expect(MemberValidator.validateBalance(-100).isValid).toBe(false)
      expect(MemberValidator.validateBalance(1000000).isValid).toBe(false)
    })
  })
  
  describe('validateDiscountRate', () => {
    it('应该验证有效的折扣率', () => {
      expect(MemberValidator.validateDiscountRate(0).isValid).toBe(true)
      expect(MemberValidator.validateDiscountRate(0.1).isValid).toBe(true)
      expect(MemberValidator.validateDiscountRate(1).isValid).toBe(true)
    })
    
    it('应该拒绝无效的折扣率', () => {
      expect(MemberValidator.validateDiscountRate(-0.1).isValid).toBe(false)
      expect(MemberValidator.validateDiscountRate(1.1).isValid).toBe(false)
    })
  })
})

describe('OrderValidator', () => {
  describe('validateOrderData', () => {
    it('应该验证有效的订单数据', () => {
      const orderData = {
        items: [
          {
            id: 'P001',
            name: '苹果',
            price: 5.5,
            quantity: 2,
            subtotal: 11
          }
        ],
        totalAmount: 11,
        discountAmount: 1,
        finalAmount: 10
      }
      
      const result = OrderValidator.validateOrderData(orderData)
      expect(result.isValid).toBe(true)
    })
    
    it('应该验证金额计算', () => {
      const orderData = {
        items: [
          {
            id: 'P001',
            name: '苹果',
            price: 5.5,
            quantity: 2,
            subtotal: 11
          }
        ],
        totalAmount: 11,
        discountAmount: 1,
        finalAmount: 15 // 错误的实付金额
      }
      
      const result = OrderValidator.validateOrderData(orderData)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('订单金额计算错误')
    })
  })
  
  describe('validateOrderNo', () => {
    it('应该验证有效的订单号', () => {
      expect(OrderValidator.validateOrderNo('POS20250102001').isValid).toBe(true)
      expect(OrderValidator.validateOrderNo('ORDER*********').isValid).toBe(true)
    })
    
    it('应该拒绝无效的订单号', () => {
      expect(OrderValidator.validateOrderNo('').isValid).toBe(false)
      expect(OrderValidator.validateOrderNo('POS-001').isValid).toBe(false) // 包含特殊字符
      expect(OrderValidator.validateOrderNo('123').isValid).toBe(false) // 太短
    })
  })
  
  describe('validateOrderRemark', () => {
    it('应该验证有效的订单备注', () => {
      expect(OrderValidator.validateOrderRemark('').isValid).toBe(true) // 可以为空
      expect(OrderValidator.validateOrderRemark('客户要求加急').isValid).toBe(true)
    })
    
    it('应该拒绝过长的备注', () => {
      const longRemark = 'a'.repeat(201)
      expect(OrderValidator.validateOrderRemark(longRemark).isValid).toBe(false)
    })
  })
})

describe('ProductValidator', () => {
  describe('validateBarcode', () => {
    it('应该验证有效的商品条码', () => {
      expect(ProductValidator.validateBarcode('*********0123').isValid).toBe(true)
      expect(ProductValidator.validateBarcode('').isValid).toBe(true) // 可以为空
    })
    
    it('应该拒绝无效的条码', () => {
      expect(ProductValidator.validateBarcode('123').isValid).toBe(false) // 太短
      expect(ProductValidator.validateBarcode('ABC123').isValid).toBe(false) // 包含字母
    })
  })
  
  describe('validateCategoryId', () => {
    it('应该验证有效的分类ID', () => {
      expect(ProductValidator.validateCategoryId('CAT001').isValid).toBe(true)
    })
    
    it('应该拒绝无效的分类ID', () => {
      expect(ProductValidator.validateCategoryId('').isValid).toBe(false)
    })
  })
  
  describe('validateStock', () => {
    it('应该验证有效的库存', () => {
      expect(ProductValidator.validateStock(0).isValid).toBe(true)
      expect(ProductValidator.validateStock(100).isValid).toBe(true)
    })
    
    it('应该拒绝无效的库存', () => {
      expect(ProductValidator.validateStock(-1).isValid).toBe(false)
      expect(ProductValidator.validateStock(1000000).isValid).toBe(false)
    })
  })
})

describe('BusinessValidator', () => {
  describe('validateBusinessHours', () => {
    it('应该验证营业时间内', () => {
      const businessTime = new Date('2025-01-02 10:00:00')
      const businessHours = { openTime: '08:00', closeTime: '22:00' }
      
      const result = BusinessValidator.validateBusinessHours(businessTime, businessHours)
      expect(result.isValid).toBe(true)
    })
    
    it('应该拒绝营业时间外', () => {
      const afterHours = new Date('2025-01-02 23:00:00')
      const businessHours = { openTime: '08:00', closeTime: '22:00' }
      
      const result = BusinessValidator.validateBusinessHours(afterHours, businessHours)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('不在营业时间内')
    })
  })
  
  describe('validateStockAvailability', () => {
    it('应该验证库存充足', () => {
      const cartItems = [
        { id: 'P001', name: '苹果', quantity: 2 },
        { id: 'P002', name: '香蕉', quantity: 1 }
      ]
      
      const stockData = [
        { productId: 'P001', stock: 10 },
        { productId: 'P002', stock: 5 }
      ]
      
      const result = BusinessValidator.validateStockAvailability(cartItems, stockData)
      expect(result.isValid).toBe(true)
    })
    
    it('应该检测库存不足', () => {
      const cartItems = [
        { id: 'P001', name: '苹果', quantity: 15 }
      ]
      
      const stockData = [
        { productId: 'P001', stock: 10 }
      ]
      
      const result = BusinessValidator.validateStockAvailability(cartItems, stockData)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('库存不足')
    })
  })
  
  describe('validateMemberPermission', () => {
    it('应该验证有效的会员权限', () => {
      const member = {
        status: 'active',
        allowDiscount: true,
        allowPoints: true,
        allowBalancePayment: true
      }
      
      expect(BusinessValidator.validateMemberPermission(member, 'discount').isValid).toBe(true)
      expect(BusinessValidator.validateMemberPermission(member, 'points').isValid).toBe(true)
      expect(BusinessValidator.validateMemberPermission(member, 'balance').isValid).toBe(true)
    })
    
    it('应该拒绝无效的会员状态', () => {
      const member = {
        status: 'inactive',
        allowDiscount: true
      }
      
      const result = BusinessValidator.validateMemberPermission(member, 'discount')
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('已被冻结或停用')
    })
    
    it('应该检查过期会员', () => {
      const member = {
        status: 'active',
        expireDate: '2024-12-31',
        allowDiscount: true
      }
      
      const result = BusinessValidator.validateMemberPermission(member, 'discount')
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('已过期')
    })
    
    it('应该检查操作权限', () => {
      const member = {
        status: 'active',
        allowDiscount: false
      }
      
      const result = BusinessValidator.validateMemberPermission(member, 'discount')
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('不享受折扣优惠')
    })
  })
})