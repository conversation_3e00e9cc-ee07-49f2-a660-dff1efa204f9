import { describe, it, expect } from 'vitest'

describe('组件统一导出', () => {
  describe('基本导出功能', () => {
    it('应该能够导入主要组件', async () => {
      const indexModule = await import('../index.js')
      
      // 检查主要组件是否存在
      expect(indexModule.ShoppingCart).toBeDefined()
      expect(indexModule.PaymentPanel).toBeDefined()
      expect(indexModule.ProductDisplayArea).toBeDefined()
      expect(indexModule.MemberPanel).toBeDefined()
      expect(indexModule.ToolbarPanel).toBeDefined()
      expect(indexModule.OrderSuspend).toBeDefined()
    })

    it('应该能够导入会员子组件', async () => {
      const indexModule = await import('../index.js')
      
      expect(indexModule.MemberSearch).toBeDefined()
      expect(indexModule.MemberSelector).toBeDefined()
      expect(indexModule.MemberInfo).toBeDefined()
      expect(indexModule.MemberDiscount).toBeDefined()
    })

    it('应该能够导入通用子组件', async () => {
      const indexModule = await import('../index.js')
      
      expect(indexModule.FunctionButton).toBeDefined()
      expect(indexModule.OrderSummary).toBeDefined()
      expect(indexModule.SuspendedOrderItem).toBeDefined()
    })
  })

  describe('子目录导出', () => {
    it('应该能够从cart目录导入组件', async () => {
      const cartComponents = await import('../cart/index.js')
      expect(cartComponents.ShoppingCart).toBeDefined()
      expect(cartComponents.default).toBeDefined()
    })

    it('应该能够从payment目录导入组件', async () => {
      const paymentComponents = await import('../payment/index.js')
      expect(paymentComponents.PaymentPanel).toBeDefined()
      expect(paymentComponents.default).toBeDefined()
    })

    it('应该能够从product目录导入组件', async () => {
      const productComponents = await import('../product/index.js')
      expect(productComponents.ProductDisplayArea).toBeDefined()
      expect(productComponents.default).toBeDefined()
    })

    it('应该能够从member目录导入组件', async () => {
      const memberComponents = await import('../member/index.js')
      expect(memberComponents.MemberSearch).toBeDefined()
      expect(memberComponents.default).toBeDefined()
    })

    it('应该能够从common目录导入组件', async () => {
      const commonComponents = await import('../common/index.js')
      expect(commonComponents.FunctionButton).toBeDefined()
      expect(commonComponents.default).toBeDefined()
    })
  })
})