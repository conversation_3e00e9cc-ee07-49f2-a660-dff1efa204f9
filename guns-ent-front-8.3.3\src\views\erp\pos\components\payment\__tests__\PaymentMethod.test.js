/**
 * 支付方式组件单元测试
 * 
 * 测试支付方式选择组件的渲染和交互功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import PaymentMethod from '../PaymentMethod.vue'
import { PAYMENT_METHODS } from '../../../utils/constants'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  Radio: {
    name: 'ARadio',
    template: '<input type="radio" :value="value" @change="$emit(\'change\', $event)" />'
  },
  RadioGroup: {
    name: 'ARadioGroup',
    template: '<div><slot /></div>'
  },
  Button: {
    name: 'AButton',
    template: '<button @click="$emit(\'click\')"><slot /></button>'
  },
  InputNumber: {
    name: 'AInputNumber',
    template: '<input type="number" :value="value" @input="$emit(\'update:value\', $event.target.value)" />'
  },
  Input: {
    name: 'AInput',
    template: '<input :value="value" @input="$emit(\'update:value\', $event.target.value)" />'
  }
}))

describe('PaymentMethod', () => {
  let wrapper
  let pinia
  
  const mockPaymentConfig = {
    enabledMethods: [
      PAYMENT_METHODS.CASH,
      PAYMENT_METHODS.WECHAT,
      PAYMENT_METHODS.ALIPAY,
      PAYMENT_METHODS.CARD,
      PAYMENT_METHODS.MEMBER
    ],
    limits: {
      [PAYMENT_METHODS.CASH]: { max: 10000 },
      [PAYMENT_METHODS.WECHAT]: { max: 50000 },
      [PAYMENT_METHODS.ALIPAY]: { max: 50000 },
      [PAYMENT_METHODS.CARD]: { max: 100000 },
      [PAYMENT_METHODS.MEMBER]: { max: 5000 }
    }
  }
  
  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(PaymentMethod, {
      props: {
        paymentAmount: 100,
        selectedMethod: '',
        paymentConfig: mockPaymentConfig,
        ...props
      },
      global: {
        plugins: [pinia],
        stubs: {
          'a-radio': true,
          'a-radio-group': true,
          'a-button': true,
          'a-input-number': true,
          'a-input': true
        }
      }
    })
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  describe('基础渲染', () => {
    it('应该正确渲染支付方式选项', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.payment-methods').exists()).toBe(true)
      expect(wrapper.findAll('.payment-method-item')).toHaveLength(5)
    })
    
    it('应该显示所有启用的支付方式', () => {
      wrapper = createWrapper()
      
      const methodItems = wrapper.findAll('.payment-method-item')
      const methodTexts = methodItems.map(item => item.text())
      
      expect(methodTexts).toContain('现金支付')
      expect(methodTexts).toContain('微信支付')
      expect(methodTexts).toContain('支付宝')
      expect(methodTexts).toContain('银行卡')
      expect(methodTexts).toContain('会员卡')
    })
    
    it('应该显示支付金额', () => {
      wrapper = createWrapper({ paymentAmount: 158.50 })
      
      expect(wrapper.text()).toContain('¥158.50')
    })
    
    it('应该隐藏未启用的支付方式', () => {
      const limitedConfig = {
        enabledMethods: [PAYMENT_METHODS.CASH, PAYMENT_METHODS.WECHAT],
        limits: {}
      }
      wrapper = createWrapper({ paymentConfig: limitedConfig })
      
      expect(wrapper.findAll('.payment-method-item')).toHaveLength(2)
      expect(wrapper.text()).toContain('现金支付')
      expect(wrapper.text()).toContain('微信支付')
      expect(wrapper.text()).not.toContain('支付宝')
    })
  })
  
  describe('支付方式选择', () => {
    it('应该在选择支付方式时触发事件', async () => {
      wrapper = createWrapper()
      
      const cashMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.CASH}"]`)
      await cashMethod.trigger('click')
      
      expect(wrapper.emitted('method-change')).toBeTruthy()
      expect(wrapper.emitted('method-change')[0]).toEqual([PAYMENT_METHODS.CASH])
    })
    
    it('应该高亮显示选中的支付方式', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.WECHAT })
      
      const wechatMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.WECHAT}"]`)
      expect(wechatMethod.classes()).toContain('payment-method-item--selected')
    })
    
    it('应该禁用超出限额的支付方式', () => {
      wrapper = createWrapper({ paymentAmount: 60000 }) // 超过微信和支付宝限额
      
      const wechatMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.WECHAT}"]`)
      const alipayMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.ALIPAY}"]`)
      const cardMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.CARD}"]`)
      
      expect(wechatMethod.classes()).toContain('payment-method-item--disabled')
      expect(alipayMethod.classes()).toContain('payment-method-item--disabled')
      expect(cardMethod.classes()).not.toContain('payment-method-item--disabled')
    })
  })
  
  describe('现金支付', () => {
    it('应该显示现金支付的实收金额输入', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.CASH })
      
      const receivedAmountInput = wrapper.find('.received-amount-input')
      expect(receivedAmountInput.exists()).toBe(true)
    })
    
    it('应该计算并显示找零金额', async () => {
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.CASH,
        paymentAmount: 85.50
      })
      
      const receivedAmountInput = wrapper.find('.received-amount-input a-input-number-stub')
      await receivedAmountInput.vm.$emit('update:value', 100)
      
      expect(wrapper.emitted('received-amount-change')).toBeTruthy()
      expect(wrapper.emitted('received-amount-change')[0]).toEqual([100])
      
      // 假设父组件会传递找零金额
      await wrapper.setProps({ changeAmount: 14.50 })
      expect(wrapper.text()).toContain('找零: ¥14.50')
    })
    
    it('应该提供快速金额按钮', async () => {
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.CASH,
        paymentAmount: 85.50
      })
      
      const quickAmountBtns = wrapper.findAll('.quick-amount-btn')
      expect(quickAmountBtns.length).toBeGreaterThan(0)
      
      // 点击100元按钮
      const btn100 = quickAmountBtns.find(btn => btn.text().includes('100'))
      if (btn100) {
        await btn100.trigger('click')
        expect(wrapper.emitted('received-amount-change')).toBeTruthy()
      }
    })
    
    it('应该在实收金额不足时显示警告', () => {
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.CASH,
        paymentAmount: 100,
        receivedAmount: 80
      })
      
      const warning = wrapper.find('.insufficient-amount-warning')
      expect(warning.exists()).toBe(true)
      expect(warning.text()).toContain('实收金额不足')
    })
  })
  
  describe('扫码支付', () => {
    it('应该显示微信支付的二维码区域', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.WECHAT })
      
      const qrCodeArea = wrapper.find('.qr-code-area')
      expect(qrCodeArea.exists()).toBe(true)
      expect(qrCodeArea.text()).toContain('请使用微信扫码支付')
    })
    
    it('应该显示支付宝的二维码区域', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.ALIPAY })
      
      const qrCodeArea = wrapper.find('.qr-code-area')
      expect(qrCodeArea.exists()).toBe(true)
      expect(qrCodeArea.text()).toContain('请使用支付宝扫码支付')
    })
    
    it('应该显示二维码图片', () => {
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.WECHAT,
        qrCodeUrl: 'https://example.com/qr.png'
      })
      
      const qrCodeImg = wrapper.find('.qr-code-img')
      expect(qrCodeImg.exists()).toBe(true)
      expect(qrCodeImg.attributes('src')).toBe('https://example.com/qr.png')
    })
    
    it('应该显示支付状态', () => {
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.WECHAT,
        paymentStatus: 'pending'
      })
      
      const statusText = wrapper.find('.payment-status')
      expect(statusText.exists()).toBe(true)
      expect(statusText.text()).toContain('等待支付')
    })
  })
  
  describe('银行卡支付', () => {
    it('应该显示银行卡输入区域', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.CARD })
      
      const cardInputArea = wrapper.find('.card-input-area')
      expect(cardInputArea.exists()).toBe(true)
    })
    
    it('应该显示卡号输入框', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.CARD })
      
      const cardNoInput = wrapper.find('.card-no-input')
      expect(cardNoInput.exists()).toBe(true)
    })
    
    it('应该在输入卡号时触发验证', async () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.CARD })
      
      const cardNoInput = wrapper.find('.card-no-input a-input-stub')
      await cardNoInput.vm.$emit('update:value', '****************')
      
      expect(wrapper.emitted('card-no-change')).toBeTruthy()
      expect(wrapper.emitted('card-no-change')[0]).toEqual(['****************'])
    })
  })
  
  describe('会员卡支付', () => {
    it('应该显示会员信息', () => {
      const member = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        balance: 500
      }
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.MEMBER,
        member
      })
      
      expect(wrapper.text()).toContain('张三')
      expect(wrapper.text()).toContain('VIP123456')
      expect(wrapper.text()).toContain('余额: ¥500.00')
    })
    
    it('应该在余额不足时显示警告', () => {
      const member = {
        id: 'M001',
        balance: 50
      }
      wrapper = createWrapper({ 
        selectedMethod: PAYMENT_METHODS.MEMBER,
        member,
        paymentAmount: 100
      })
      
      const warning = wrapper.find('.insufficient-balance-warning')
      expect(warning.exists()).toBe(true)
      expect(warning.text()).toContain('余额不足')
    })
    
    it('应该在没有会员时显示提示', () => {
      wrapper = createWrapper({ selectedMethod: PAYMENT_METHODS.MEMBER })
      
      const noMemberTip = wrapper.find('.no-member-tip')
      expect(noMemberTip.exists()).toBe(true)
      expect(noMemberTip.text()).toContain('请先选择会员')
    })
  })
  
  describe('组合支付', () => {
    it('应该支持组合支付模式', () => {
      wrapper = createWrapper({ enableComboPayment: true })
      
      const comboPaymentToggle = wrapper.find('.combo-payment-toggle')
      expect(comboPaymentToggle.exists()).toBe(true)
    })
    
    it('应该在组合支付模式下显示多个支付方式', async () => {
      wrapper = createWrapper({ enableComboPayment: true })
      
      const comboToggle = wrapper.find('.combo-payment-toggle')
      await comboToggle.trigger('click')
      
      expect(wrapper.emitted('combo-payment-toggle')).toBeTruthy()
    })
  })
  
  describe('支付限制', () => {
    it('应该显示支付限额信息', () => {
      wrapper = createWrapper()
      
      const cashMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.CASH}"]`)
      expect(cashMethod.text()).toContain('限额: ¥10,000')
    })
    
    it('应该在超出限额时禁用支付方式', () => {
      wrapper = createWrapper({ paymentAmount: 15000 })
      
      const cashMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.CASH}"]`)
      expect(cashMethod.classes()).toContain('payment-method-item--disabled')
    })
  })
  
  describe('交互行为', () => {
    it('应该支持键盘导航', async () => {
      wrapper = createWrapper()
      
      const firstMethod = wrapper.find('.payment-method-item')
      await firstMethod.trigger('keydown', { key: 'Enter' })
      
      expect(wrapper.emitted('method-change')).toBeTruthy()
    })
    
    it('应该在鼠标悬停时显示详细信息', async () => {
      wrapper = createWrapper()
      
      const wechatMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.WECHAT}"]`)
      await wechatMethod.trigger('mouseenter')
      
      const tooltip = wrapper.find('.payment-method-tooltip')
      if (tooltip.exists()) {
        expect(tooltip.isVisible()).toBe(true)
      }
    })
  })
  
  describe('边界情况', () => {
    it('应该处理空的支付配置', () => {
      wrapper = createWrapper({ paymentConfig: null })
      
      expect(wrapper.find('.payment-methods').exists()).toBe(true)
      expect(wrapper.findAll('.payment-method-item')).toHaveLength(0)
    })
    
    it('应该处理零金额支付', () => {
      wrapper = createWrapper({ paymentAmount: 0 })
      
      expect(wrapper.text()).toContain('¥0.00')
      expect(wrapper.findAll('.payment-method-item--disabled')).toHaveLength(0)
    })
    
    it('应该处理极大金额', () => {
      wrapper = createWrapper({ paymentAmount: 999999.99 })
      
      expect(wrapper.text()).toContain('¥999,999.99')
      // 大部分支付方式应该被禁用
      expect(wrapper.findAll('.payment-method-item--disabled').length).toBeGreaterThan(0)
    })
  })
  
  describe('可访问性', () => {
    it('应该有正确的ARIA标签', () => {
      wrapper = createWrapper()
      
      const paymentMethods = wrapper.find('.payment-methods')
      expect(paymentMethods.attributes('role')).toBe('radiogroup')
      expect(paymentMethods.attributes('aria-label')).toBe('选择支付方式')
    })
    
    it('应该为每个支付方式提供描述', () => {
      wrapper = createWrapper()
      
      const cashMethod = wrapper.find(`[data-method="${PAYMENT_METHODS.CASH}"]`)
      expect(cashMethod.attributes('aria-describedby')).toBeTruthy()
    })
    
    it('应该为禁用的支付方式提供说明', () => {
      wrapper = createWrapper({ paymentAmount: 15000 })
      
      const disabledMethod = wrapper.find('.payment-method-item--disabled')
      expect(disabledMethod.attributes('aria-disabled')).toBe('true')
    })
  })
  
  describe('性能优化', () => {
    it('应该在支付配置未变化时避免重新渲染', async () => {
      wrapper = createWrapper()
      
      const renderSpy = vi.spyOn(wrapper.vm, '$forceUpdate')
      
      await wrapper.setProps({ paymentConfig: { ...mockPaymentConfig } })
      
      expect(renderSpy).not.toHaveBeenCalled()
    })
    
    it('应该正确处理支付方式的动态更新', async () => {
      wrapper = createWrapper()
      
      const newConfig = {
        enabledMethods: [PAYMENT_METHODS.CASH],
        limits: { [PAYMENT_METHODS.CASH]: { max: 5000 } }
      }
      
      await wrapper.setProps({ paymentConfig: newConfig })
      
      expect(wrapper.findAll('.payment-method-item')).toHaveLength(1)
      expect(wrapper.text()).toContain('现金支付')
    })
  })
})