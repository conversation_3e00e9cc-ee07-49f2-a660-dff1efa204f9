import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { create<PERSON><PERSON>, setActive<PERSON>inia } from 'pinia'
import { useMemberStore } from '../member'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('useMemberStore', () => {
  let pinia
  let memberStore
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    memberStore = useMemberStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      expect(memberStore.currentMember).toBe(null)
      expect(memberStore.memberDiscountRate).toBe(0)
      expect(memberStore.pointsDeductionAmount).toBe(0)
      expect(memberStore.pointsExchangeRate).toBe(100)
      expect(memberStore.memberLevels).toEqual([])
    })

    it('应该有正确的计算属性初始值', () => {
      expect(memberStore.hasMember).toBe(false)
      expect(memberStore.memberDisplayName).toBe('')
      expect(memberStore.memberLevelName).toBe('')
      expect(memberStore.availablePoints).toBe(0)
      expect(memberStore.memberDiscountInfo.hasDiscount).toBe(false)
      expect(memberStore.pointsDeductionInfo.hasDeduction).toBe(false)
    })
  })

  describe('设置会员', () => {
    const mockMember = {
      memberId: 1,
      memberName: '张三',
      memberCode: 'M001',
      phone: '13800138000',
      points: 1000,
      discountRate: 10, // 9折
      levelId: 1
    }

    it('应该能够设置会员信息', () => {
      const result = memberStore.setCurrentMember(mockMember)
      
      expect(result).toBe(true)
      expect(memberStore.currentMember).toEqual(expect.objectContaining({
        ...mockMember,
        bindTime: expect.any(Number)
      }))
      expect(memberStore.memberDiscountRate).toBe(10)
      expect(memberStore.hasMember).toBe(true)
      expect(memberStore.memberDisplayName).toBe('张三')
    })

    it('应该验证会员数据', () => {
      const invalidMember = {
        memberName: '张三'
        // 缺少memberId和memberCode
      }
      
      const result = memberStore.setCurrentMember(invalidMember)
      
      expect(result).toBe(false)
      expect(memberStore.currentMember).toBe(null)
    })

    it('应该处理空会员数据', () => {
      const result = memberStore.setCurrentMember(null)
      
      expect(result).toBe(false)
      expect(memberStore.currentMember).toBe(null)
    })

    it('应该处理没有姓名的会员', () => {
      const memberWithoutName = {
        memberId: 1,
        memberCode: 'M001'
        // 缺少memberName
      }
      
      const result = memberStore.setCurrentMember(memberWithoutName)
      
      expect(result).toBe(false)
    })

    it('应该使用memberCode作为显示名称', () => {
      const memberWithCodeOnly = {
        memberId: 1,
        memberCode: 'M001',
        memberName: 'John Doe'
      }
      
      memberStore.setCurrentMember(memberWithCodeOnly)
      
      expect(memberStore.memberDisplayName).toBe('John Doe')
    })
  })

  describe('清除会员', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        memberCode: 'M001',
        points: 1000,
        discountRate: 10
      }
      memberStore.setCurrentMember(mockMember)
      memberStore.setPointsDeduction(500, 5.00)
    })

    it('应该能够清除会员信息', () => {
      memberStore.clearCurrentMember()
      
      expect(memberStore.currentMember).toBe(null)
      expect(memberStore.memberDiscountRate).toBe(0)
      expect(memberStore.pointsDeductionAmount).toBe(0)
      expect(memberStore.hasMember).toBe(false)
    })
  })

  describe('积分抵扣', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        memberCode: 'M001',
        points: 1000,
        maxPointsDeductionRatio: 0.5
      }
      memberStore.setCurrentMember(mockMember)
    })

    it('应该能够设置积分抵扣', () => {
      const result = memberStore.setPointsDeduction(500, 5.00)
      
      expect(result).toBe(true)
      expect(memberStore.pointsDeductionAmount).toBe(5.00)
      expect(memberStore.currentMember.usedPoints).toBe(500)
      expect(memberStore.pointsDeductionInfo.hasDeduction).toBe(true)
      expect(memberStore.pointsDeductionInfo.usedPoints).toBe(500)
    })

    it('应该验证积分数量', () => {
      const result = memberStore.setPointsDeduction(1500, 15.00) // 超过可用积分
      
      expect(result).toBe(false)
      expect(memberStore.pointsDeductionAmount).toBe(0)
    })

    it('应该验证积分兑换比例', () => {
      const result = memberStore.setPointsDeduction(500, 10.00) // 错误的兑换比例
      
      expect(result).toBe(false)
      expect(memberStore.pointsDeductionAmount).toBe(0)
    })

    it('应该处理负数积分', () => {
      const result = memberStore.setPointsDeduction(-100, -1.00)
      
      expect(result).toBe(false)
    })

    it('应该在没有会员时拒绝设置积分抵扣', () => {
      memberStore.clearCurrentMember()
      
      const result = memberStore.setPointsDeduction(500, 5.00)
      
      expect(result).toBe(false)
    })

    it('应该能够清除积分抵扣', () => {
      memberStore.setPointsDeduction(500, 5.00)
      expect(memberStore.pointsDeductionAmount).toBe(5.00)
      
      memberStore.clearPointsDeduction()
      
      expect(memberStore.pointsDeductionAmount).toBe(0)
      expect(memberStore.currentMember.usedPoints).toBe(0)
    })

    it('应该计算最大可抵扣积分', () => {
      // 假设订单金额为1000元，最大抵扣比例50%
      const maxDeductible = memberStore.maxDeductiblePoints
      
      // 最大抵扣金额 = 1000 * 0.5 = 500元
      // 最大抵扣积分 = 500 * 100 = 50000积分
      // 但会员只有1000积分，所以最大可抵扣积分为1000
      expect(maxDeductible).toBe(1000)
    })
  })

  describe('会员折扣', () => {
    it('应该能够应用会员折扣', () => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        discountRate: 10 // 9折
      }
      memberStore.setCurrentMember(mockMember)
      
      const result = memberStore.applyMemberDiscount(100)
      
      expect(result.success).toBe(true)
      expect(result.discountAmount).toBe(10)
      expect(result.finalAmount).toBe(90)
      expect(result.discountRate).toBe(10)
    })

    it('应该处理没有折扣的会员', () => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        discountRate: 0
      }
      memberStore.setCurrentMember(mockMember)
      
      const result = memberStore.applyMemberDiscount(100)
      
      expect(result.success).toBe(false)
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })

    it('应该处理没有会员的情况', () => {
      const result = memberStore.applyMemberDiscount(100)
      
      expect(result.success).toBe(false)
      expect(result.discountAmount).toBe(0)
      expect(result.finalAmount).toBe(100)
    })

    it('应该计算会员折扣信息', () => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        discountRate: 15 // 8.5折
      }
      memberStore.setCurrentMember(mockMember)
      
      const discountInfo = memberStore.memberDiscountInfo
      
      expect(discountInfo.hasDiscount).toBe(true)
      expect(discountInfo.discountRate).toBe(15)
      expect(discountInfo.description).toBe('会员15折优惠')
    })
  })

  describe('积分计算', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        points: 2000,
        maxPointsDeductionRatio: 0.3
      }
      memberStore.setCurrentMember(mockMember)
    })

    it('应该能够计算积分抵扣', () => {
      const result = memberStore.calculatePointsDeduction(1000)
      
      expect(result.success).toBe(true)
      expect(result.deductionAmount).toBe(10.00) // 1000积分 / 100 = 10元
      expect(result.usedPoints).toBe(1000)
      expect(result.exchangeRate).toBe(100)
    })

    it('应该处理积分不足', () => {
      const result = memberStore.calculatePointsDeduction(3000) // 超过可用积分
      
      expect(result.success).toBe(false)
      expect(result.deductionAmount).toBe(0)
    })

    it('应该处理无效积分数量', () => {
      const result = memberStore.calculatePointsDeduction(0)
      
      expect(result.success).toBe(false)
      expect(result.deductionAmount).toBe(0)
    })

    it('应该处理没有会员的情况', () => {
      memberStore.clearCurrentMember()
      
      const result = memberStore.calculatePointsDeduction(1000)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('请先绑定会员')
    })
  })

  describe('会员等级', () => {
    const mockLevels = [
      { levelId: 1, levelName: '普通会员', discountRate: 5 },
      { levelId: 2, levelName: 'VIP会员', discountRate: 10 },
      { levelId: 3, levelName: '钻石会员', discountRate: 15 }
    ]

    beforeEach(() => {
      memberStore.setMemberLevels(mockLevels)
    })

    it('应该能够设置会员等级', () => {
      expect(memberStore.memberLevels).toEqual(mockLevels)
    })

    it('应该根据等级计算折扣率', () => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        levelId: 2 // VIP会员
      }
      memberStore.setCurrentMember(mockMember)
      
      expect(memberStore.memberDiscountRate).toBe(10)
      expect(memberStore.memberLevelName).toBe('VIP会员')
    })

    it('应该优先使用自定义折扣率', () => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        levelId: 2, // VIP会员，等级折扣10%
        customDiscountRate: 20 // 自定义折扣20%
      }
      memberStore.setCurrentMember(mockMember)
      
      expect(memberStore.memberDiscountRate).toBe(20)
    })

    it('应该处理不存在的等级', () => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        levelId: 999 // 不存在的等级
      }
      memberStore.setCurrentMember(mockMember)
      
      expect(memberStore.memberLevelName).toBe('')
      expect(memberStore.memberDiscountRate).toBe(0)
    })
  })

  describe('积分兑换比例', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        points: 1000
      }
      memberStore.setCurrentMember(mockMember)
      memberStore.setPointsDeduction(500, 5.00)
    })

    it('应该能够设置积分兑换比例', () => {
      const result = memberStore.setPointsExchangeRate(200)
      
      expect(result).toBe(true)
      expect(memberStore.pointsExchangeRate).toBe(200)
    })

    it('应该处理无效的兑换比例', () => {
      const result = memberStore.setPointsExchangeRate(0)
      
      expect(result).toBe(false)
      expect(memberStore.pointsExchangeRate).toBe(100) // 保持原值
    })

    it('应该在更新兑换比例时重新计算积分抵扣', () => {
      memberStore.setPointsExchangeRate(200) // 200积分兑换1元
      
      // 原来500积分抵扣5元，现在应该是2.5元
      expect(memberStore.pointsDeductionAmount).toBe(2.5)
    })
  })

  describe('会员信息更新', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        memberCode: 'M001',
        points: 1000,
        discountRate: 10
      }
      memberStore.setCurrentMember(mockMember)
    })

    it('应该能够更新会员信息', () => {
      const updates = {
        memberName: '李四',
        points: 1500,
        discountRate: 15
      }
      
      const result = memberStore.updateMemberInfo(updates)
      
      expect(result).toBe(true)
      expect(memberStore.currentMember.memberName).toBe('李四')
      expect(memberStore.currentMember.points).toBe(1500)
      expect(memberStore.memberDiscountRate).toBe(15)
      expect(memberStore.currentMember.updateTime).toBeDefined()
    })

    it('应该在没有会员时拒绝更新', () => {
      memberStore.clearCurrentMember()
      
      const result = memberStore.updateMemberInfo({ memberName: '李四' })
      
      expect(result).toBe(false)
    })

    it('应该在更新折扣相关信息时重新计算折扣率', () => {
      memberStore.setMemberLevels([
        { levelId: 2, levelName: 'VIP', discountRate: 20 }
      ])
      
      const updates = { levelId: 2 }
      memberStore.updateMemberInfo(updates)
      
      expect(memberStore.memberDiscountRate).toBe(20)
    })
  })

  describe('数据快照', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        points: 1000,
        discountRate: 10
      }
      memberStore.setCurrentMember(mockMember)
      memberStore.setPointsDeduction(500, 5.00)
    })

    it('应该能够获取会员数据快照', () => {
      const snapshot = memberStore.getMemberSnapshot()
      
      expect(snapshot).toEqual({
        currentMember: expect.objectContaining({
          memberId: 1,
          memberName: '张三'
        }),
        memberDiscountRate: 10,
        pointsDeductionAmount: 5.00,
        pointsExchangeRate: 100,
        timestamp: expect.any(Number)
      })
    })

    it('应该能够从快照恢复会员数据', () => {
      const snapshot = memberStore.getMemberSnapshot()
      
      // 清除会员信息
      memberStore.clearCurrentMember()
      expect(memberStore.hasMember).toBe(false)
      
      // 从快照恢复
      const result = memberStore.restoreFromSnapshot(snapshot)
      
      expect(result).toBe(true)
      expect(memberStore.hasMember).toBe(true)
      expect(memberStore.currentMember.memberName).toBe('张三')
      expect(memberStore.pointsDeductionAmount).toBe(5.00)
    })

    it('应该处理无效的快照', () => {
      const result = memberStore.restoreFromSnapshot(null)
      
      expect(result).toBe(false)
    })
  })

  describe('状态重置', () => {
    beforeEach(() => {
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        points: 1000,
        discountRate: 10
      }
      memberStore.setCurrentMember(mockMember)
      memberStore.setPointsDeduction(500, 5.00)
      memberStore.setPointsExchangeRate(200)
    })

    it('应该能够重置会员状态', () => {
      memberStore.resetMemberState()
      
      expect(memberStore.currentMember).toBe(null)
      expect(memberStore.memberDiscountRate).toBe(0)
      expect(memberStore.pointsDeductionAmount).toBe(0)
      expect(memberStore.pointsExchangeRate).toBe(100)
    })
  })

  describe('错误处理', () => {
    it('应该处理设置会员时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      // 模拟错误
      const originalAssign = Object.assign
      Object.assign = vi.fn().mockImplementation(() => {
        throw new Error('Test error')
      })
      
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        memberCode: 'M001'
      }
      
      const result = memberStore.setCurrentMember(mockMember)
      
      expect(result).toBe(false)
      
      // 恢复原始方法
      Object.assign = originalAssign
      consoleSpy.mockRestore()
    })

    it('应该处理计算折扣时的错误', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      const mockMember = {
        memberId: 1,
        memberName: '张三',
        discountRate: 'invalid' // 无效的折扣率
      }
      memberStore.setCurrentMember(mockMember)
      
      const result = memberStore.applyMemberDiscount(100)
      
      expect(result.success).toBe(false)
      expect(result.message).toBe('折扣计算失败')
      
      consoleSpy.mockRestore()
    })
  })
})