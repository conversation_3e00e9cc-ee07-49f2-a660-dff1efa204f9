/**
 * 购物车汇总组件单元测试
 * 
 * 测试购物车汇总组件的渲染和计算功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import CartSummary from '../CartSummary.vue'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  Divider: {
    name: 'ADivider',
    template: '<hr />'
  },
  Tag: {
    name: 'ATag',
    template: '<span class="ant-tag"><slot /></span>'
  },
  Tooltip: {
    name: 'ATooltip',
    template: '<div><slot /></div>'
  }
}))

describe('CartSummary', () => {
  let wrapper
  let pinia
  
  const mockSummary = {
    itemCount: 3,
    totalQuantity: 8.5,
    subtotal: 125.50,
    discountAmount: 12.55,
    finalAmount: 112.95,
    memberDiscount: 10.05,
    couponDiscount: 2.50,
    pointsDeduction: 0,
    taxAmount: 0
  }
  
  const mockMember = {
    id: 'M001',
    cardNo: 'VIP123456',
    name: '张三',
    level: 'GOLD',
    discountRate: 0.08
  }
  
  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(CartSummary, {
      props: {
        summary: mockSummary,
        member: null,
        ...props
      },
      global: {
        plugins: [pinia],
        stubs: {
          'a-divider': true,
          'a-tag': true,
          'a-tooltip': true
        }
      }
    })
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  describe('基础渲染', () => {
    it('应该正确渲染汇总信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.cart-summary').exists()).toBe(true)
      expect(wrapper.text()).toContain('3件商品')
      expect(wrapper.text()).toContain('8.5')
      expect(wrapper.text()).toContain('¥125.50')
      expect(wrapper.text()).toContain('¥112.95')
    })
    
    it('应该显示商品数量统计', () => {
      wrapper = createWrapper()
      
      const itemCount = wrapper.find('.item-count')
      expect(itemCount.exists()).toBe(true)
      expect(itemCount.text()).toContain('3件商品')
      
      const totalQuantity = wrapper.find('.total-quantity')
      expect(totalQuantity.exists()).toBe(true)
      expect(totalQuantity.text()).toContain('共8.5件')
    })
    
    it('应该显示金额明细', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.subtotal-row').text()).toContain('¥125.50')
      expect(wrapper.find('.discount-row').text()).toContain('-¥12.55')
      expect(wrapper.find('.final-amount-row').text()).toContain('¥112.95')
    })
  })
  
  describe('折扣信息显示', () => {
    it('应该显示会员折扣信息', () => {
      wrapper = createWrapper({ member: mockMember })
      
      const memberDiscount = wrapper.find('.member-discount')
      expect(memberDiscount.exists()).toBe(true)
      expect(memberDiscount.text()).toContain('会员折扣')
      expect(memberDiscount.text()).toContain('-¥10.05')
    })
    
    it('应该显示优惠券折扣信息', () => {
      wrapper = createWrapper()
      
      const couponDiscount = wrapper.find('.coupon-discount')
      expect(couponDiscount.exists()).toBe(true)
      expect(couponDiscount.text()).toContain('优惠券')
      expect(couponDiscount.text()).toContain('-¥2.50')
    })
    
    it('应该在没有折扣时隐藏折扣行', () => {
      const noDiscountSummary = {
        ...mockSummary,
        discountAmount: 0,
        memberDiscount: 0,
        couponDiscount: 0
      }
      wrapper = createWrapper({ summary: noDiscountSummary })
      
      expect(wrapper.find('.discount-row').exists()).toBe(false)
      expect(wrapper.find('.member-discount').exists()).toBe(false)
      expect(wrapper.find('.coupon-discount').exists()).toBe(false)
    })
    
    it('应该显示积分抵扣信息', () => {
      const summaryWithPoints = {
        ...mockSummary,
        pointsDeduction: 5.00
      }
      wrapper = createWrapper({ summary: summaryWithPoints })
      
      const pointsDeduction = wrapper.find('.points-deduction')
      expect(pointsDeduction.exists()).toBe(true)
      expect(pointsDeduction.text()).toContain('积分抵扣')
      expect(pointsDeduction.text()).toContain('-¥5.00')
    })
  })
  
  describe('会员信息显示', () => {
    it('应该显示会员信息', () => {
      wrapper = createWrapper({ member: mockMember })
      
      const memberInfo = wrapper.find('.member-info')
      expect(memberInfo.exists()).toBe(true)
      expect(memberInfo.text()).toContain('张三')
      expect(memberInfo.text()).toContain('VIP123456')
    })
    
    it('应该显示会员等级标签', () => {
      wrapper = createWrapper({ member: mockMember })
      
      const memberLevel = wrapper.find('.member-level a-tag-stub')
      expect(memberLevel.exists()).toBe(true)
      expect(memberLevel.text()).toContain('GOLD')
    })
    
    it('应该显示会员折扣率', () => {
      wrapper = createWrapper({ member: mockMember })
      
      const discountRate = wrapper.find('.member-discount-rate')
      expect(discountRate.exists()).toBe(true)
      expect(discountRate.text()).toContain('8%')
    })
    
    it('应该在没有会员时隐藏会员信息', () => {
      wrapper = createWrapper({ member: null })
      
      expect(wrapper.find('.member-info').exists()).toBe(false)
    })
  })
  
  describe('税费信息', () => {
    it('应该显示税费信息', () => {
      const summaryWithTax = {
        ...mockSummary,
        taxAmount: 11.30,
        taxRate: 0.1
      }
      wrapper = createWrapper({ summary: summaryWithTax })
      
      const taxInfo = wrapper.find('.tax-info')
      expect(taxInfo.exists()).toBe(true)
      expect(taxInfo.text()).toContain('税费')
      expect(taxInfo.text()).toContain('¥11.30')
    })
    
    it('应该在没有税费时隐藏税费信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.tax-info').exists()).toBe(false)
    })
  })
  
  describe('格式化显示', () => {
    it('应该正确格式化货币金额', () => {
      const largeSummary = {
        ...mockSummary,
        subtotal: 12345.67,
        finalAmount: 11111.11
      }
      wrapper = createWrapper({ summary: largeSummary })
      
      expect(wrapper.text()).toContain('¥12,345.67')
      expect(wrapper.text()).toContain('¥11,111.11')
    })
    
    it('应该正确格式化数量', () => {
      const decimalSummary = {
        ...mockSummary,
        totalQuantity: 12.345
      }
      wrapper = createWrapper({ summary: decimalSummary })
      
      expect(wrapper.text()).toContain('12.345')
    })
    
    it('应该正确格式化百分比', () => {
      wrapper = createWrapper({ member: mockMember })
      
      expect(wrapper.text()).toContain('8%')
    })
  })
  
  describe('交互功能', () => {
    it('应该支持点击查看详细信息', async () => {
      wrapper = createWrapper()
      
      const detailBtn = wrapper.find('.detail-btn')
      if (detailBtn.exists()) {
        await detailBtn.trigger('click')
        expect(wrapper.emitted('show-detail')).toBeTruthy()
      }
    })
    
    it('应该支持折扣明细展开/收起', async () => {
      wrapper = createWrapper()
      
      const discountToggle = wrapper.find('.discount-toggle')
      if (discountToggle.exists()) {
        await discountToggle.trigger('click')
        expect(wrapper.find('.discount-detail').isVisible()).toBe(true)
      }
    })
  })
  
  describe('提示信息', () => {
    it('应该显示节省金额提示', () => {
      wrapper = createWrapper()
      
      const savedAmount = wrapper.find('.saved-amount')
      if (savedAmount.exists()) {
        expect(savedAmount.text()).toContain('已节省')
        expect(savedAmount.text()).toContain('¥12.55')
      }
    })
    
    it('应该显示会员优惠提示', () => {
      wrapper = createWrapper({ member: mockMember })
      
      const memberBenefit = wrapper.find('.member-benefit')
      if (memberBenefit.exists()) {
        expect(memberBenefit.text()).toContain('会员优惠')
      }
    })
  })
  
  describe('边界情况', () => {
    it('应该处理空的汇总数据', () => {
      wrapper = createWrapper({ summary: null })
      
      expect(wrapper.find('.cart-summary').exists()).toBe(true)
      expect(wrapper.text()).toContain('0件商品')
      expect(wrapper.text()).toContain('¥0.00')
    })
    
    it('应该处理负数金额', () => {
      const negativeSummary = {
        ...mockSummary,
        discountAmount: 150.00, // 折扣大于小计
        finalAmount: -24.50
      }
      wrapper = createWrapper({ summary: negativeSummary })
      
      expect(wrapper.text()).toContain('-¥24.50')
    })
    
    it('应该处理极大的数值', () => {
      const largeSummary = {
        ...mockSummary,
        itemCount: 999,
        totalQuantity: 9999.999,
        subtotal: 999999.99,
        finalAmount: 888888.88
      }
      wrapper = createWrapper({ summary: largeSummary })
      
      expect(wrapper.text()).toContain('999件商品')
      expect(wrapper.text()).toContain('9999.999')
      expect(wrapper.text()).toContain('¥999,999.99')
    })
    
    it('应该处理零值情况', () => {
      const zeroSummary = {
        itemCount: 0,
        totalQuantity: 0,
        subtotal: 0,
        discountAmount: 0,
        finalAmount: 0,
        memberDiscount: 0,
        couponDiscount: 0,
        pointsDeduction: 0,
        taxAmount: 0
      }
      wrapper = createWrapper({ summary: zeroSummary })
      
      expect(wrapper.text()).toContain('0件商品')
      expect(wrapper.text()).toContain('¥0.00')
    })
  })
  
  describe('样式和布局', () => {
    it('应该有正确的CSS类名', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.cart-summary').exists()).toBe(true)
      expect(wrapper.find('.summary-row').exists()).toBe(true)
      expect(wrapper.find('.final-amount-row').exists()).toBe(true)
    })
    
    it('应该在最终金额为负时添加警告样式', () => {
      const negativeSummary = {
        ...mockSummary,
        finalAmount: -10.00
      }
      wrapper = createWrapper({ summary: negativeSummary })
      
      const finalAmountRow = wrapper.find('.final-amount-row')
      expect(finalAmountRow.classes()).toContain('negative-amount')
    })
    
    it('应该在有会员时添加会员样式', () => {
      wrapper = createWrapper({ member: mockMember })
      
      const cartSummary = wrapper.find('.cart-summary')
      expect(cartSummary.classes()).toContain('has-member')
    })
  })
  
  describe('可访问性', () => {
    it('应该有正确的ARIA标签', () => {
      wrapper = createWrapper()
      
      const cartSummary = wrapper.find('.cart-summary')
      expect(cartSummary.attributes('role')).toBe('region')
      expect(cartSummary.attributes('aria-label')).toBe('购物车汇总')
    })
    
    it('应该为金额提供屏幕阅读器友好的文本', () => {
      wrapper = createWrapper()
      
      const finalAmount = wrapper.find('.final-amount')
      expect(finalAmount.attributes('aria-label')).toContain('应付金额')
    })
  })
  
  describe('性能优化', () => {
    it('应该在汇总数据未变化时避免重新计算', async () => {
      wrapper = createWrapper()
      
      const computeSpy = vi.spyOn(wrapper.vm, 'computedSummary', 'get')
      
      // 设置相同的汇总数据
      await wrapper.setProps({ summary: { ...mockSummary } })
      
      // 由于数据相同，不应该触发重新计算
      expect(computeSpy).toHaveBeenCalledTimes(1)
    })
    
    it('应该正确处理汇总数据的更新', async () => {
      wrapper = createWrapper()
      
      const updatedSummary = {
        ...mockSummary,
        itemCount: 5,
        finalAmount: 200.00
      }
      
      await wrapper.setProps({ summary: updatedSummary })
      
      expect(wrapper.text()).toContain('5件商品')
      expect(wrapper.text()).toContain('¥200.00')
    })
  })
})