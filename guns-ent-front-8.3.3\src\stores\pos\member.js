/**
 * POS会员状态管理
 * 
 * 管理会员信息、折扣、积分等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

export const useMemberStore = defineStore('pos-member', () => {
  // ==================== 状态定义 ====================
  
  // 当前会员信息
  const currentMember = ref(null)
  
  // 会员折扣率
  const memberDiscountRate = ref(0)
  
  // 会员积分抵扣金额
  const pointsDeductionAmount = ref(0)
  
  // 积分兑换比例（多少积分兑换1元）
  const pointsExchangeRate = ref(100)
  
  // 会员等级配置
  const memberLevels = ref([])
  
  // ==================== 计算属性 ====================
  
  // 是否有会员信息
  const hasMember = computed(() => {
    return currentMember.value !== null
  })
  
  // 会员显示名称
  const memberDisplayName = computed(() => {
    if (!currentMember.value) return ''
    return currentMember.value.memberName || currentMember.value.memberCode || '未知会员'
  })
  
  // 会员等级名称
  const memberLevelName = computed(() => {
    if (!currentMember.value || !currentMember.value.levelId) return ''
    const level = memberLevels.value.find(l => l.levelId === currentMember.value.levelId)
    return level ? level.levelName : ''
  })
  
  // 可用积分
  const availablePoints = computed(() => {
    return currentMember.value ? (currentMember.value.points || 0) : 0
  })
  
  // 最大可抵扣积分
  const maxDeductiblePoints = computed(() => {
    if (!currentMember.value) return 0
    
    // 根据会员等级和订单金额计算最大可抵扣积分
    const maxDeductionRatio = currentMember.value.maxPointsDeductionRatio || 0.5 // 最多抵扣50%
    const orderAmount = 1000 // 这里应该从购物车获取，暂时写死
    const maxDeductionAmount = orderAmount * maxDeductionRatio
    const maxPoints = Math.floor(maxDeductionAmount * pointsExchangeRate.value)
    
    return Math.min(availablePoints.value, maxPoints)
  })
  
  // 会员折扣信息
  const memberDiscountInfo = computed(() => {
    if (!hasMember.value) {
      return {
        hasDiscount: false,
        discountRate: 0,
        discountAmount: 0,
        description: '无会员折扣'
      }
    }
    
    const rate = memberDiscountRate.value
    const orderAmount = 1000 // 这里应该从购物车获取
    const discountAmount = orderAmount * (rate / 100)
    
    return {
      hasDiscount: rate > 0,
      discountRate: rate,
      discountAmount: discountAmount,
      description: rate > 0 ? `会员${rate}折优惠` : '无折扣'
    }
  })
  
  // 积分抵扣信息
  const pointsDeductionInfo = computed(() => {
    if (!hasMember.value || pointsDeductionAmount.value <= 0) {
      return {
        hasDeduction: false,
        deductionAmount: 0,
        usedPoints: 0,
        description: '未使用积分抵扣'
      }
    }
    
    const usedPoints = Math.ceil(pointsDeductionAmount.value * pointsExchangeRate.value)
    
    return {
      hasDeduction: true,
      deductionAmount: pointsDeductionAmount.value,
      usedPoints: usedPoints,
      description: `使用${usedPoints}积分抵扣${pointsDeductionAmount.value.toFixed(2)}元`
    }
  })
  
  // ==================== 私有方法 ====================
  
  /**
   * 验证会员数据
   * @param {Object} member - 会员信息
   * @returns {boolean} 是否有效
   */
  const validateMember = (member) => {
    if (!member) {
      message.error('会员信息无效')
      return false
    }
    
    if (!member.memberId && !member.memberCode) {
      message.error('会员ID或会员卡号不能为空')
      return false
    }
    
    if (!member.memberName) {
      message.error('会员姓名不能为空')
      return false
    }
    
    return true
  }
  
  /**
   * 计算会员折扣率
   * @param {Object} member - 会员信息
   * @returns {number} 折扣率
   */
  const calculateDiscountRate = (member) => {
    if (!member) return 0
    
    // 优先使用会员自定义折扣率
    if (member.customDiscountRate !== undefined) {
      return member.customDiscountRate
    }
    
    // 使用会员等级折扣率
    if (member.levelId) {
      const level = memberLevels.value.find(l => l.levelId === member.levelId)
      if (level && level.discountRate !== undefined) {
        return level.discountRate
      }
    }
    
    // 使用会员基础折扣率
    return member.discountRate || 0
  }
  
  // ==================== 公共方法 ====================
  
  /**
   * 设置当前会员
   * @param {Object} member - 会员信息
   * @returns {boolean} 是否设置成功
   */
  const setCurrentMember = (member) => {
    try {
      if (!validateMember(member)) {
        return false
      }
      
      currentMember.value = {
        ...member,
        bindTime: Date.now() // 记录绑定时间
      }
      
      // 计算会员折扣率
      memberDiscountRate.value = calculateDiscountRate(member)
      
      // 清除之前的积分抵扣
      pointsDeductionAmount.value = 0
      
      message.success(`已绑定会员：${memberDisplayName.value}`)
      return true
      
    } catch (error) {
      console.error('设置会员信息失败:', error)
      message.error('设置会员信息失败')
      return false
    }
  }
  
  /**
   * 清除当前会员
   */
  const clearCurrentMember = () => {
    try {
      const memberName = memberDisplayName.value
      
      currentMember.value = null
      memberDiscountRate.value = 0
      pointsDeductionAmount.value = 0
      
      if (memberName) {
        message.success(`已取消会员绑定：${memberName}`)
      }
    } catch (error) {
      console.error('清除会员信息失败:', error)
    }
  }
  
  /**
   * 设置积分抵扣
   * @param {number} points - 使用的积分数
   * @param {number} amount - 抵扣金额
   * @returns {boolean} 是否设置成功
   */
  const setPointsDeduction = (points, amount) => {
    try {
      if (!hasMember.value) {
        message.warning('请先绑定会员')
        return false
      }
      
      if (points < 0 || amount < 0) {
        message.error('积分和抵扣金额不能为负数')
        return false
      }
      
      if (points > availablePoints.value) {
        message.warning(`积分不足，可用积分：${availablePoints.value}`)
        return false
      }
      
      if (points > maxDeductiblePoints.value) {
        message.warning(`超出最大可抵扣积分：${maxDeductiblePoints.value}`)
        return false
      }
      
      // 验证积分和金额的兑换比例
      const expectedAmount = points / pointsExchangeRate.value
      if (Math.abs(amount - expectedAmount) > 0.01) {
        message.error('积分兑换金额不正确')
        return false
      }
      
      pointsDeductionAmount.value = amount
      
      // 保存使用的积分数到会员信息中
      if (currentMember.value) {
        currentMember.value.usedPoints = points
      }
      
      message.success(`已设置积分抵扣：${points}积分抵扣${amount.toFixed(2)}元`)
      return true
      
    } catch (error) {
      console.error('设置积分抵扣失败:', error)
      message.error('设置积分抵扣失败')
      return false
    }
  }
  
  /**
   * 清除积分抵扣
   */
  const clearPointsDeduction = () => {
    try {
      const deductionInfo = pointsDeductionInfo.value
      
      pointsDeductionAmount.value = 0
      
      if (currentMember.value) {
        currentMember.value.usedPoints = 0
      }
      
      if (deductionInfo.hasDeduction) {
        message.success('已清除积分抵扣')
      }
    } catch (error) {
      console.error('清除积分抵扣失败:', error)
    }
  }
  
  /**
   * 应用会员折扣
   * @param {number} orderAmount - 订单金额
   * @returns {Object} 折扣信息
   */
  const applyMemberDiscount = (orderAmount) => {
    try {
      if (!hasMember.value || memberDiscountRate.value <= 0) {
        return {
          success: false,
          discountAmount: 0,
          finalAmount: orderAmount,
          message: '无会员折扣'
        }
      }
      
      const discountAmount = orderAmount * (memberDiscountRate.value / 100)
      const finalAmount = orderAmount - discountAmount
      
      return {
        success: true,
        discountAmount: discountAmount,
        finalAmount: finalAmount,
        discountRate: memberDiscountRate.value,
        message: `会员${memberDiscountRate.value}折优惠，优惠${discountAmount.toFixed(2)}元`
      }
      
    } catch (error) {
      console.error('应用会员折扣失败:', error)
      return {
        success: false,
        discountAmount: 0,
        finalAmount: orderAmount,
        message: '折扣计算失败'
      }
    }
  }
  
  /**
   * 计算积分抵扣
   * @param {number} points - 要使用的积分
   * @returns {Object} 抵扣信息
   */
  const calculatePointsDeduction = (points) => {
    try {
      if (!hasMember.value) {
        return {
          success: false,
          deductionAmount: 0,
          message: '请先绑定会员'
        }
      }
      
      if (points <= 0) {
        return {
          success: false,
          deductionAmount: 0,
          message: '积分数量无效'
        }
      }
      
      if (points > availablePoints.value) {
        return {
          success: false,
          deductionAmount: 0,
          message: `积分不足，可用积分：${availablePoints.value}`
        }
      }
      
      if (points > maxDeductiblePoints.value) {
        return {
          success: false,
          deductionAmount: 0,
          message: `超出最大可抵扣积分：${maxDeductiblePoints.value}`
        }
      }
      
      const deductionAmount = points / pointsExchangeRate.value
      
      return {
        success: true,
        deductionAmount: deductionAmount,
        usedPoints: points,
        exchangeRate: pointsExchangeRate.value,
        message: `${points}积分可抵扣${deductionAmount.toFixed(2)}元`
      }
      
    } catch (error) {
      console.error('计算积分抵扣失败:', error)
      return {
        success: false,
        deductionAmount: 0,
        message: '计算失败'
      }
    }
  }
  
  /**
   * 更新会员信息
   * @param {Object} updates - 更新的字段
   * @returns {boolean} 是否更新成功
   */
  const updateMemberInfo = (updates) => {
    try {
      if (!hasMember.value) {
        message.warning('请先绑定会员')
        return false
      }
      
      // 合并更新
      currentMember.value = {
        ...currentMember.value,
        ...updates,
        updateTime: Date.now()
      }
      
      // 如果更新了折扣相关信息，重新计算折扣率
      if (updates.discountRate !== undefined || 
          updates.customDiscountRate !== undefined || 
          updates.levelId !== undefined) {
        memberDiscountRate.value = calculateDiscountRate(currentMember.value)
      }
      
      return true
      
    } catch (error) {
      console.error('更新会员信息失败:', error)
      message.error('更新会员信息失败')
      return false
    }
  }
  
  /**
   * 设置会员等级配置
   * @param {Array} levels - 会员等级列表
   */
  const setMemberLevels = (levels) => {
    try {
      memberLevels.value = levels || []
      
      // 如果当前有会员，重新计算折扣率
      if (hasMember.value) {
        memberDiscountRate.value = calculateDiscountRate(currentMember.value)
      }
    } catch (error) {
      console.error('设置会员等级失败:', error)
    }
  }
  
  /**
   * 设置积分兑换比例
   * @param {number} rate - 兑换比例
   */
  const setPointsExchangeRate = (rate) => {
    try {
      if (rate <= 0) {
        message.error('积分兑换比例必须大于0')
        return false
      }
      
      pointsExchangeRate.value = rate
      
      // 如果当前有积分抵扣，需要重新验证
      if (pointsDeductionAmount.value > 0) {
        const currentPoints = currentMember.value?.usedPoints || 0
        const expectedAmount = currentPoints / rate
        
        if (Math.abs(pointsDeductionAmount.value - expectedAmount) > 0.01) {
          // 重新计算积分抵扣
          pointsDeductionAmount.value = expectedAmount
          message.info('积分兑换比例已更新，积分抵扣金额已重新计算')
        }
      }
      
      return true
    } catch (error) {
      console.error('设置积分兑换比例失败:', error)
      return false
    }
  }
  
  /**
   * 获取会员数据快照（用于备份）
   * @returns {Object} 会员数据快照
   */
  const getMemberSnapshot = () => {
    return {
      currentMember: currentMember.value ? JSON.parse(JSON.stringify(currentMember.value)) : null,
      memberDiscountRate: memberDiscountRate.value,
      pointsDeductionAmount: pointsDeductionAmount.value,
      pointsExchangeRate: pointsExchangeRate.value,
      timestamp: Date.now()
    }
  }
  
  /**
   * 从快照恢复会员数据
   * @param {Object} snapshot - 会员数据快照
   * @returns {boolean} 是否恢复成功
   */
  const restoreFromSnapshot = (snapshot) => {
    try {
      if (!snapshot) return false
      
      currentMember.value = snapshot.currentMember
      memberDiscountRate.value = snapshot.memberDiscountRate || 0
      pointsDeductionAmount.value = snapshot.pointsDeductionAmount || 0
      pointsExchangeRate.value = snapshot.pointsExchangeRate || 100
      
      return true
    } catch (error) {
      console.error('从快照恢复会员数据失败:', error)
      return false
    }
  }
  
  /**
   * 重置会员状态
   */
  const resetMemberState = () => {
    try {
      currentMember.value = null
      memberDiscountRate.value = 0
      pointsDeductionAmount.value = 0
      pointsExchangeRate.value = 100
    } catch (error) {
      console.error('重置会员状态失败:', error)
    }
  }
  
  // ==================== 返回接口 ====================
  
  return {
    // 状态
    currentMember,
    memberDiscountRate,
    pointsDeductionAmount,
    pointsExchangeRate,
    memberLevels,
    
    // 计算属性
    hasMember,
    memberDisplayName,
    memberLevelName,
    availablePoints,
    maxDeductiblePoints,
    memberDiscountInfo,
    pointsDeductionInfo,
    
    // 方法
    setCurrentMember,
    clearCurrentMember,
    setPointsDeduction,
    clearPointsDeduction,
    applyMemberDiscount,
    calculatePointsDeduction,
    updateMemberInfo,
    setMemberLevels,
    setPointsExchangeRate,
    getMemberSnapshot,
    restoreFromSnapshot,
    resetMemberState
  }
})