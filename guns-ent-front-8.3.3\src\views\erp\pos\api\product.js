/**
 * 商品相关API接口
 * 
 * 提供商品查询、分类管理、库存检查等功能的API封装
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import Request from '@/utils/request/request-util'
import { PosErrorHandler } from '@/utils/erp/pos-error-handler'

/**
 * 商品API类
 */
export class ProductApi {
  
  /**
   * 获取商品分类列表
   * @returns {Promise<Array>} 分类列表
   */
  static async getCategories() {
    const apiCall = () => Request.get('/erp/pos/categories')
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '获取商品分类列表',
      retryOptions: { maxRetries: 3 }
    })()
  }
  
  /**
   * 根据分类ID获取商品列表
   * @param {Object} params - 查询参数
   * @param {number} params.categoryId - 分类ID
   * @param {boolean} params.onlyInStock - 是否只显示有库存的商品
   * @param {string} params.pricingType - 计价类型过滤
   * @returns {Promise<Array>} 商品列表
   */
  static async getProductsByCategory(params = {}) {
    const apiCall = () => Request.get('/erp/pos/products', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '获取商品列表',
      retryOptions: { maxRetries: 2 }
    })()
  }
  
  /**
   * 搜索商品
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {boolean} params.onlyInStock - 是否只显示有库存的商品
   * @param {string} params.pricingType - 计价类型过滤
   * @returns {Promise<Array>} 商品列表
   */
  static async searchProducts(params = {}) {
    const apiCall = () => Request.get('/erp/pos/products/search', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '搜索商品',
      retryOptions: { maxRetries: 1 } // 搜索操作重试次数较少
    })()
  }
  
  /**
   * 根据商品ID获取商品详情
   * @param {Object} params - 查询参数
   * @param {number} params.productId - 商品ID
   * @returns {Promise<Object>} 商品详情
   */
  static async getProductDetail(params) {
    const apiCall = () => Request.get('/erp/pos/product/detail', params)
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '获取商品详情',
      showMessage: false
    })()
  }
  
  /**
   * 根据条形码获取商品信息
   * @param {string} barcode - 商品条形码
   * @returns {Promise<Object>} 商品信息
   */
  static async getProductByBarcode(barcode) {
    const apiCall = () => Request.get('/erp/pos/product/barcode', { barcode })
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '根据条形码获取商品',
      showMessage: true
    })()
  }
  
  /**
   * 检查商品库存状态
   * @param {number} productId - 商品ID
   * @returns {Promise<Object>} 库存状态信息
   */
  static async checkProductStock(productId) {
    const apiCall = () => Request.get('/erp/pos/product/stock', { productId })
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '检查商品库存',
      showMessage: false
    })()
  }
  
  /**
   * 批量检查商品库存
   * @param {Array<number>} productIds - 商品ID列表
   * @returns {Promise<Array>} 库存状态列表
   */
  static async batchCheckProductStock(productIds) {
    const apiCall = () => Request.post('/erp/pos/product/stock/batch', { productIds })
    
    return PosErrorHandler.wrapApiCall(apiCall, {
      context: '批量检查商品库存',
      showMessage: false
    })()
  }
}