<!--\n  商品快速预览组件\n  \n  在弹窗中显示商品的详细信息\n  \n  <AUTHOR>  @since 2025/01/02\n-->\n<template>\n  <div class=\"product-quick-view\">\n    <div class=\"quick-view-content\">\n      <!-- 商品图片区域 -->\n      <div class=\"product-images\">\n        <div class=\"main-image\">\n          <img \n            :src=\"currentImage\" \n            :alt=\"product.productName\"\n            @error=\"handleImageError\"\n          />\n          \n          <!-- 图片缺失占位符 -->\n          <div v-if=\"imageError\" class=\"image-placeholder\">\n            <icon-font iconClass=\"icon-product\" />\n            <span>暂无图片</span>\n          </div>\n        </div>\n        \n        <!-- 图片缩略图 -->\n        <div class=\"image-thumbnails\" v-if=\"product.images && product.images.length > 1\">\n          <div \n            v-for=\"(image, index) in product.images\"\n            :key=\"index\"\n            class=\"thumbnail\"\n            :class=\"{ active: currentImageIndex === index }\"\n            @click=\"selectImage(index)\"\n          >\n            <img :src=\"image\" :alt=\"`商品图片${index + 1}`\" />\n          </div>\n        </div>\n      </div>\n      \n      <!-- 商品详情区域 -->\n      <div class=\"product-details\">\n        <!-- 商品基本信息 -->\n        <div class=\"basic-info\">\n          <h3 class=\"product-title\">{{ product.productName }}</h3>\n          \n          <div class=\"product-meta\">\n            <div class=\"meta-item\" v-if=\"product.productCode\">\n              <span class=\"meta-label\">商品编码:</span>\n              <span class=\"meta-value\">{{ product.productCode }}</span>\n            </div>\n            \n            <div class=\"meta-item\" v-if=\"product.barcode\">\n              <span class=\"meta-label\">条形码:</span>\n              <span class=\"meta-value\">{{ product.barcode }}</span>\n            </div>\n            \n            <div class=\"meta-item\" v-if=\"product.categoryName\">\n              <span class=\"meta-label\">商品分类:</span>\n              <span class=\"meta-value\">{{ product.categoryName }}</span>\n            </div>\n            \n            <div class=\"meta-item\" v-if=\"product.brand\">\n              <span class=\"meta-label\">商品品牌:</span>\n              <span class=\"meta-value\">{{ product.brand }}</span>\n            </div>\n          </div>\n          \n          <!-- 商品规格 -->\n          <div class=\"product-specs\" v-if=\"product.specifications\">\n            <h4 class=\"specs-title\">商品规格</h4>\n            <div class=\"specs-content\">{{ product.specifications }}</div>\n          </div>\n          \n          <!-- 商品描述 -->\n          <div class=\"product-description\" v-if=\"product.description\">\n            <h4 class=\"desc-title\">商品描述</h4>\n            <div class=\"desc-content\">{{ product.description }}</div>\n          </div>\n        </div>\n        \n        <!-- 价格和库存信息 -->\n        <div class=\"price-stock-info\">\n          <!-- 价格信息 -->\n          <div class=\"price-section\">\n            <div class=\"current-price\">\n              {{ formatPrice(product.price) }}\n              <span class=\"unit\" v-if=\"product.unit\">/ {{ product.unit }}</span>\n            </div>\n            \n            <div class=\"original-price\" v-if=\"product.originalPrice && product.originalPrice > product.price\">\n              原价: {{ formatPrice(product.originalPrice) }}\n            </div>\n            \n            <div class=\"discount-info\" v-if=\"discountPercent > 0\">\n              <a-tag color=\"red\" size=\"small\">\n                {{ discountPercent }}折\n              </a-tag>\n            </div>\n          </div>\n          \n          <!-- 库存信息 -->\n          <div class=\"stock-section\">\n            <div class=\"stock-item\">\n              <span class=\"stock-label\">当前库存:</span>\n              <span class=\"stock-value\" :class=\"getStockClass()\">\n                {{ formatStock(product.stock) }}\n              </span>\n            </div>\n            \n            <div class=\"stock-item\" v-if=\"product.minStock\">\n              <span class=\"stock-label\">最低库存:</span>\n              <span class=\"stock-value\">{{ formatStock(product.minStock) }}</span>\n            </div>\n            \n            <div class=\"stock-item\" v-if=\"product.maxStock\">\n              <span class=\"stock-label\">最大库存:</span>\n              <span class=\"stock-value\">{{ formatStock(product.maxStock) }}</span>\n            </div>\n            \n            <!-- 库存状态指示器 -->\n            <div class=\"stock-indicator\">\n              <div class=\"indicator-bar\">\n                <div \n                  class=\"indicator-fill\"\n                  :style=\"{ width: `${stockPercent}%`, backgroundColor: getStockColor() }\"\n                ></div>\n              </div>\n              <div class=\"indicator-text\">{{ getStockStatusText() }}</div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 商品标签 -->\n        <div class=\"product-tags\" v-if=\"hasProductTags\">\n          <a-tag v-if=\"product.isNew\" color=\"green\" size=\"small\">\n            <icon-font iconClass=\"icon-new\" />\n            新品\n          </a-tag>\n          <a-tag v-if=\"product.isHot\" color=\"red\" size=\"small\">\n            <icon-font iconClass=\"icon-hot\" />\n            热销\n          </a-tag>\n          <a-tag v-if=\"product.isDiscount\" color=\"orange\" size=\"small\">\n            <icon-font iconClass=\"icon-discount\" />\n            促销\n          </a-tag>\n          <a-tag v-if=\"product.isRecommended\" color=\"blue\" size=\"small\">\n            <icon-font iconClass=\"icon-recommend\" />\n            推荐\n          </a-tag>\n        </div>\n        \n        <!-- 操作区域 -->\n        <div class=\"action-section\">\n          <!-- 数量选择 -->\n          <div class=\"quantity-selector\">\n            <label class=\"quantity-label\">购买数量:</label>\n            <div class=\"quantity-controls\">\n              <a-button \n                size=\"small\"\n                @click=\"decreaseQuantity\"\n                :disabled=\"quantity <= 0.001\"\n              >\n                <template #icon>\n                  <minus-outlined />\n                </template>\n              </a-button>\n              \n              <a-input-number\n                v-model:value=\"quantity\"\n                :min=\"0.001\"\n                :max=\"product.stock\"\n                :precision=\"getPrecision()\"\n                :step=\"getStep()\"\n                size=\"small\"\n                class=\"quantity-input\"\n                @change=\"handleQuantityChange\"\n              />\n              \n              <a-button \n                size=\"small\"\n                @click=\"increaseQuantity\"\n                :disabled=\"quantity >= product.stock\"\n              >\n                <template #icon>\n                  <plus-outlined />\n                </template>\n              </a-button>\n            </div>\n          </div>\n          \n          <!-- 小计金额 -->\n          <div class=\"subtotal-info\">\n            <span class=\"subtotal-label\">小计:</span>\n            <span class=\"subtotal-value\">{{ formatPrice(subtotal) }}</span>\n          </div>\n          \n          <!-- 操作按钮 -->\n          <div class=\"action-buttons\">\n            <a-button \n              size=\"large\"\n              @click=\"handleClose\"\n              class=\"close-btn\"\n            >\n              关闭\n            </a-button>\n            \n            <a-button \n              type=\"primary\"\n              size=\"large\"\n              :disabled=\"isOutOfStock || quantity <= 0\"\n              :loading=\"adding\"\n              @click=\"handleAddToCart\"\n              class=\"add-btn\"\n            >\n              <template #icon>\n                <shopping-cart-outlined />\n              </template>\n              加入购物车\n            </a-button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { message } from 'ant-design-vue'\nimport {\n  MinusOutlined,\n  PlusOutlined,\n  ShoppingCartOutlined\n} from '@ant-design/icons-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\nimport { AmountFormatter } from '../../utils/formatter'\nimport { PRICING_TYPES } from '../../utils/constants'\n\n// 定义组件名称\ndefineOptions({\n  name: 'ProductQuickView'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 商品信息\n  product: {\n    type: Object,\n    required: true\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'add-to-cart',\n  'close'\n])\n\n// 响应式状态\nconst currentImageIndex = ref(0)\nconst imageError = ref(false)\nconst quantity = ref(1)\nconst adding = ref(false)\n\n// ==================== 计算属性 ====================\n\n/**\n * 当前显示的图片\n */\nconst currentImage = computed(() => {\n  if (props.product.images && props.product.images.length > 0) {\n    return props.product.images[currentImageIndex.value]\n  }\n  return props.product.image || ''\n})\n\n/**\n * 是否缺货\n */\nconst isOutOfStock = computed(() => {\n  return props.product.stock <= 0\n})\n\n/**\n * 库存百分比\n */\nconst stockPercent = computed(() => {\n  if (!props.product.maxStock || props.product.maxStock <= 0) return 0\n  return Math.min(100, Math.round((props.product.stock / props.product.maxStock) * 100))\n})\n\n/**\n * 折扣百分比\n */\nconst discountPercent = computed(() => {\n  if (!props.product.originalPrice || props.product.originalPrice <= props.product.price) {\n    return 0\n  }\n  return Math.round((1 - props.product.price / props.product.originalPrice) * 10)\n})\n\n/**\n * 是否有商品标签\n */\nconst hasProductTags = computed(() => {\n  return props.product.isNew || props.product.isHot || \n         props.product.isDiscount || props.product.isRecommended\n})\n\n/**\n * 小计金额\n */\nconst subtotal = computed(() => {\n  return props.product.price * quantity.value\n})\n\n// ==================== 方法 ====================\n\n/**\n * 格式化价格\n * @param {number} price - 价格\n * @returns {string} 格式化后的价格\n */\nconst formatPrice = (price) => {\n  return AmountFormatter.formatCurrency(price || 0)\n}\n\n/**\n * 格式化库存\n * @param {number} stock - 库存数量\n * @returns {string} 格式化后的库存\n */\nconst formatStock = (stock) => {\n  if (stock === null || stock === undefined) return '未知'\n  if (stock <= 0) return '缺货'\n  \n  // 根据商品类型决定显示精度\n  if (props.product.pricingType === PRICING_TYPES.WEIGHT) {\n    return `${stock.toFixed(2)}${props.product.unit || 'kg'}`\n  }\n  \n  return `${Math.floor(stock)}${props.product.unit || '件'}`\n}\n\n/**\n * 获取库存样式类\n * @returns {string} 样式类名\n */\nconst getStockClass = () => {\n  if (isOutOfStock.value) return 'out-of-stock'\n  if (props.product.stock <= (props.product.lowStockThreshold || 10)) return 'low-stock'\n  return 'normal-stock'\n}\n\n/**\n * 获取库存颜色\n * @returns {string} 颜色值\n */\nconst getStockColor = () => {\n  if (stockPercent.value <= 20) return '#ff4d4f'\n  if (stockPercent.value <= 50) return '#faad14'\n  return '#52c41a'\n}\n\n/**\n * 获取库存状态文本\n * @returns {string} 状态文本\n */\nconst getStockStatusText = () => {\n  if (isOutOfStock.value) return '缺货'\n  if (props.product.stock <= (props.product.lowStockThreshold || 10)) return '库存不足'\n  return '库存充足'\n}\n\n/**\n * 获取数量输入精度\n * @returns {number} 精度\n */\nconst getPrecision = () => {\n  return props.product.pricingType === PRICING_TYPES.WEIGHT ? 3 : 0\n}\n\n/**\n * 获取数量步长\n * @returns {number} 步长\n */\nconst getStep = () => {\n  if (props.product.pricingType === PRICING_TYPES.WEIGHT) return 0.1\n  return 1\n}\n\n/**\n * 选择图片\n * @param {number} index - 图片索引\n */\nconst selectImage = (index) => {\n  currentImageIndex.value = index\n  imageError.value = false\n}\n\n/**\n * 处理图片错误\n */\nconst handleImageError = () => {\n  imageError.value = true\n}\n\n/**\n * 减少数量\n */\nconst decreaseQuantity = () => {\n  const step = getStep()\n  const newQuantity = Math.max(0.001, quantity.value - step)\n  quantity.value = newQuantity\n}\n\n/**\n * 增加数量\n */\nconst increaseQuantity = () => {\n  const step = getStep()\n  const newQuantity = Math.min(props.product.stock, quantity.value + step)\n  quantity.value = newQuantity\n}\n\n/**\n * 处理数量变化\n * @param {number} value - 新数量\n */\nconst handleQuantityChange = (value) => {\n  if (value > props.product.stock) {\n    quantity.value = props.product.stock\n    message.warning('数量不能超过库存')\n  } else if (value < 0.001) {\n    quantity.value = 0.001\n  }\n}\n\n/**\n * 处理添加到购物车\n */\nconst handleAddToCart = async () => {\n  if (isOutOfStock.value) {\n    message.warning('商品已缺货，无法添加到购物车')\n    return\n  }\n  \n  if (quantity.value <= 0) {\n    message.warning('请选择购买数量')\n    return\n  }\n  \n  adding.value = true\n  \n  try {\n    emit('add-to-cart', {\n      ...props.product,\n      quantity: quantity.value\n    })\n    \n    message.success(`已添加 ${props.product.productName} 到购物车`)\n    \n    // 延迟关闭弹窗\n    setTimeout(() => {\n      handleClose()\n    }, 1000)\n    \n  } catch (error) {\n    console.error('添加到购物车失败:', error)\n    message.error('添加到购物车失败')\n  } finally {\n    adding.value = false\n  }\n}\n\n/**\n * 处理关闭\n */\nconst handleClose = () => {\n  emit('close')\n}\n</script>\n\n<style scoped>\n.product-quick-view {\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.quick-view-content {\n  display: flex;\n  gap: 24px;\n}\n\n/* 商品图片区域 */\n.product-images {\n  flex: 0 0 300px;\n}\n\n.main-image {\n  width: 100%;\n  height: 300px;\n  background: #f5f5f5;\n  border-radius: 8px;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 12px;\n}\n\n.main-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.image-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #bfbfbf;\n  font-size: 48px;\n}\n\n.image-placeholder span {\n  font-size: 14px;\n}\n\n.image-thumbnails {\n  display: flex;\n  gap: 8px;\n  overflow-x: auto;\n}\n\n.thumbnail {\n  width: 60px;\n  height: 60px;\n  border-radius: 4px;\n  overflow: hidden;\n  cursor: pointer;\n  border: 2px solid transparent;\n  transition: border-color 0.3s;\n}\n\n.thumbnail.active {\n  border-color: #1890ff;\n}\n\n.thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 商品详情区域 */\n.product-details {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n/* 基本信息 */\n.basic-info {\n  border-bottom: 1px solid #f0f0f0;\n  padding-bottom: 20px;\n}\n\n.product-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #262626;\n  margin: 0 0 16px 0;\n  line-height: 1.4;\n}\n\n.product-meta {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 8px;\n  margin-bottom: 16px;\n}\n\n.meta-item {\n  display: flex;\n  font-size: 13px;\n}\n\n.meta-label {\n  color: #595959;\n  min-width: 80px;\n  flex-shrink: 0;\n}\n\n.meta-value {\n  color: #262626;\n  font-weight: 500;\n}\n\n.product-specs,\n.product-description {\n  margin-top: 16px;\n}\n\n.specs-title,\n.desc-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #262626;\n  margin: 0 0 8px 0;\n}\n\n.specs-content,\n.desc-content {\n  font-size: 13px;\n  color: #595959;\n  line-height: 1.6;\n}\n\n/* 价格库存信息 */\n.price-stock-info {\n  display: flex;\n  gap: 24px;\n  border-bottom: 1px solid #f0f0f0;\n  padding-bottom: 20px;\n}\n\n.price-section {\n  flex: 1;\n}\n\n.current-price {\n  font-size: 24px;\n  font-weight: 700;\n  color: #ff4d4f;\n  margin-bottom: 8px;\n}\n\n.unit {\n  font-size: 14px;\n  color: #8c8c8c;\n  font-weight: 400;\n}\n\n.original-price {\n  font-size: 14px;\n  color: #8c8c8c;\n  text-decoration: line-through;\n  margin-bottom: 8px;\n}\n\n.discount-info {\n  display: inline-block;\n}\n\n.stock-section {\n  flex: 1;\n}\n\n.stock-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n  font-size: 13px;\n}\n\n.stock-label {\n  color: #595959;\n}\n\n.stock-value {\n  font-weight: 500;\n}\n\n.stock-value.normal-stock {\n  color: #52c41a;\n}\n\n.stock-value.low-stock {\n  color: #faad14;\n}\n\n.stock-value.out-of-stock {\n  color: #ff4d4f;\n}\n\n.stock-indicator {\n  margin-top: 12px;\n}\n\n.indicator-bar {\n  height: 6px;\n  background: #f0f0f0;\n  border-radius: 3px;\n  overflow: hidden;\n  margin-bottom: 4px;\n}\n\n.indicator-fill {\n  height: 100%;\n  transition: width 0.3s ease;\n}\n\n.indicator-text {\n  font-size: 11px;\n  color: #8c8c8c;\n  text-align: center;\n}\n\n/* 商品标签 */\n.product-tags {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n/* 操作区域 */\n.action-section {\n  margin-top: auto;\n}\n\n.quantity-selector {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 16px;\n}\n\n.quantity-label {\n  font-size: 14px;\n  color: #262626;\n  font-weight: 500;\n}\n\n.quantity-controls {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.quantity-input {\n  width: 80px;\n  text-align: center;\n}\n\n.subtotal-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #f0f9ff;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.subtotal-label {\n  font-size: 14px;\n  color: #595959;\n}\n\n.subtotal-value {\n  font-size: 18px;\n  font-weight: 600;\n  color: #1890ff;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n}\n\n.close-btn {\n  flex: 1;\n  height: 44px;\n  font-size: 14px;\n}\n\n.add-btn {\n  flex: 2;\n  height: 44px;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .quick-view-content {\n    flex-direction: column;\n    gap: 16px;\n  }\n  \n  .product-images {\n    flex: none;\n  }\n  \n  .main-image {\n    height: 250px;\n  }\n  \n  .product-title {\n    font-size: 18px;\n  }\n  \n  .current-price {\n    font-size: 20px;\n  }\n  \n  .price-stock-info {\n    flex-direction: column;\n    gap: 16px;\n  }\n  \n  .product-meta {\n    grid-template-columns: 1fr;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n  }\n  \n  .close-btn,\n  .add-btn {\n    flex: none;\n    height: 40px;\n  }\n}\n\n@media (max-width: 480px) {\n  .main-image {\n    height: 200px;\n  }\n  \n  .product-title {\n    font-size: 16px;\n  }\n  \n  .current-price {\n    font-size: 18px;\n  }\n  \n  .quantity-selector {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n  \n  .quantity-controls {\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* 滚动条样式 */\n.product-quick-view::-webkit-scrollbar {\n  width: 6px;\n}\n\n.product-quick-view::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.product-quick-view::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.product-quick-view::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n.image-thumbnails::-webkit-scrollbar {\n  height: 4px;\n}\n\n.image-thumbnails::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.image-thumbnails::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n/* 动画效果 */\n.thumbnail {\n  transition: all 0.3s ease;\n}\n\n.thumbnail:hover {\n  border-color: #40a9ff;\n}\n\n.indicator-fill {\n  animation: fillProgress 1s ease-out;\n}\n\n@keyframes fillProgress {\n  from {\n    width: 0;\n  }\n  to {\n    width: var(--target-width);\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .main-image,\n  .thumbnail {\n    border: 2px solid #000;\n  }\n  \n  .subtotal-info {\n    border: 1px solid #000;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .thumbnail {\n    transition: none;\n  }\n  \n  .indicator-fill {\n    animation: none;\n    transition: none;\n  }\n}\n</style>"