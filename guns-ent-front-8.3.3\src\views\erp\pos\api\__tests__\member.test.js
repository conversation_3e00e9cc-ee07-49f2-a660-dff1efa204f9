/**
 * 会员API单元测试
 * 
 * 测试会员相关API接口的调用和错误处理
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { MemberApi } from '../member'
import Request from '@/utils/request/request-util'
import { MEMBER_LEVELS } from '../../utils/constants'

// Mock Request模块
vi.mock('@/utils/request/request-util', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn()
  }
}))

// Mock 错误处理器和性能监控器
vi.mock('../../utils/error-handler', () => ({
  PosErrorHandler: {
    wrapApiCall: vi.fn((fn) => fn)
  }
}))

vi.mock('../../utils/performance-monitor', () => ({
  PerformanceMonitor: {
    measureApiCall: vi.fn((name, fn) => fn)
  }
}))

describe('MemberApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('getMemberByCardNo', () => {
    it('应该成功根据卡号查询会员', async () => {
      const mockMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        phone: '13812345678',
        level: MEMBER_LEVELS.GOLD,
        points: 5000,
        balance: 1000
      }
      Request.get.mockResolvedValue(mockMember)
      
      const result = await MemberApi.getMemberByCardNo('VIP123456')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/getByCardNo', {
        cardNo: 'VIP123456'
      })
      expect(result).toEqual(mockMember)
    })
    
    it('应该处理会员不存在的情况', async () => {
      const mockError = new Error('会员不存在')
      Request.get.mockRejectedValue(mockError)
      
      await expect(MemberApi.getMemberByCardNo('INVALID')).rejects.toThrow('会员不存在')
    })
  })
  
  describe('getMemberByPhone', () => {
    it('应该成功根据手机号查询会员', async () => {
      const mockMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        phone: '13812345678',
        level: MEMBER_LEVELS.SILVER
      }
      Request.get.mockResolvedValue(mockMember)
      
      const result = await MemberApi.getMemberByPhone('13812345678')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/getByPhone', {
        phone: '13812345678'
      })
      expect(result).toEqual(mockMember)
    })
  })
  
  describe('getMemberByIdCard', () => {
    it('应该成功根据身份证查询会员', async () => {
      const mockMember = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        idCard: '110101199001011234',
        level: MEMBER_LEVELS.BRONZE
      }
      Request.get.mockResolvedValue(mockMember)
      
      const result = await MemberApi.getMemberByIdCard('110101199001011234')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/getByIdCard', {
        idCard: '110101199001011234'
      })
      expect(result).toEqual(mockMember)
    })
  })
  
  describe('getMemberDetail', () => {
    it('应该成功获取会员详细信息', async () => {
      const mockMemberDetail = {
        id: 'M001',
        cardNo: 'VIP123456',
        name: '张三',
        phone: '13812345678',
        level: MEMBER_LEVELS.GOLD,
        points: 5000,
        balance: 1000,
        totalConsumption: 50000,
        registrationDate: '2024-01-01',
        lastVisit: '2025-01-01'
      }
      Request.get.mockResolvedValue(mockMemberDetail)
      
      const result = await MemberApi.getMemberDetail('M001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/detail', {
        memberId: 'M001'
      })
      expect(result).toEqual(mockMemberDetail)
    })
  })
  
  describe('searchMembers', () => {
    it('应该成功搜索会员', async () => {
      const mockMembers = [
        { id: 'M001', name: '张三', phone: '13812345678' },
        { id: 'M002', name: '张四', phone: '13812345679' }
      ]
      Request.get.mockResolvedValue(mockMembers)
      
      const result = await MemberApi.searchMembers({
        keyword: '张',
        level: MEMBER_LEVELS.GOLD
      })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/search', {
        keyword: '张',
        level: MEMBER_LEVELS.GOLD,
        limit: 20
      })
      expect(result).toEqual(mockMembers)
    })
    
    it('应该使用默认的limit参数', async () => {
      Request.get.mockResolvedValue([])
      
      await MemberApi.searchMembers({ keyword: '测试' })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/search', {
        keyword: '测试',
        limit: 20
      })
    })
  })
  
  describe('validateMemberPassword', () => {
    it('应该成功验证会员密码', async () => {
      const mockResponse = {
        valid: true,
        message: '密码验证成功'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.validateMemberPassword({
        memberId: 'M001',
        password: '123456'
      })
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/validatePassword', {
        memberId: 'M001',
        password: '123456'
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理密码错误的情况', async () => {
      const mockError = new Error('密码错误')
      Request.post.mockRejectedValue(mockError)
      
      await expect(MemberApi.validateMemberPassword({
        memberId: 'M001',
        password: 'wrong'
      })).rejects.toThrow('密码错误')
    })
  })
  
  describe('calculateMemberDiscount', () => {
    it('应该成功计算会员折扣', async () => {
      const params = {
        memberId: 'M001',
        totalAmount: 1000,
        items: [
          { id: 'P001', price: 500, quantity: 1 },
          { id: 'P002', price: 500, quantity: 1 }
        ]
      }
      const mockResponse = {
        discountAmount: 100,
        discountRate: 0.1,
        finalAmount: 900,
        discountRules: [
          { name: '金牌会员折扣', discountAmount: 100 }
        ]
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.calculateMemberDiscount(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/calculateDiscount', params)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('calculatePointsDeduction', () => {
    it('应该成功计算积分抵扣', async () => {
      const params = {
        memberId: 'M001',
        points: 1000,
        totalAmount: 500,
        maxDeductionRate: 0.5
      }
      const mockResponse = {
        deductionAmount: 10,
        usedPoints: 1000,
        remainingPoints: 4000,
        finalAmount: 490
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.calculatePointsDeduction(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/calculatePointsDeduction', params)
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getMemberPoints', () => {
    it('应该成功获取会员积分', async () => {
      const mockResponse = {
        memberId: 'M001',
        totalPoints: 5000,
        availablePoints: 4500,
        frozenPoints: 500,
        expiringPoints: 200,
        expiringDate: '2025-12-31'
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.getMemberPoints('M001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/points', {
        memberId: 'M001'
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('addMemberPoints', () => {
    it('应该成功增加会员积分', async () => {
      const params = {
        memberId: 'M001',
        points: 100,
        orderId: 'ORDER001',
        source: '消费获得',
        remark: '购买商品获得积分'
      }
      const mockResponse = {
        success: true,
        newTotalPoints: 5100,
        addedPoints: 100
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.addMemberPoints(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/addPoints', {
        ...params,
        operateTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('deductMemberPoints', () => {
    it('应该成功扣减会员积分', async () => {
      const params = {
        memberId: 'M001',
        points: 500,
        orderId: 'ORDER002',
        reason: '积分抵扣',
        remark: '使用积分抵扣现金'
      }
      const mockResponse = {
        success: true,
        newTotalPoints: 4500,
        deductedPoints: 500
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.deductMemberPoints(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/deductPoints', {
        ...params,
        operateTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理积分不足的情况', async () => {
      const mockError = new Error('积分余额不足')
      Request.post.mockRejectedValue(mockError)
      
      await expect(MemberApi.deductMemberPoints({
        memberId: 'M001',
        points: 10000,
        orderId: 'ORDER002',
        reason: '积分抵扣'
      })).rejects.toThrow('积分余额不足')
    })
  })
  
  describe('getMemberPointsHistory', () => {
    it('应该成功获取积分明细', async () => {
      const params = {
        memberId: 'M001',
        startTime: '2025-01-01',
        endTime: '2025-01-31',
        type: 'EARN'
      }
      const mockResponse = {
        total: 50,
        pageNo: 1,
        pageSize: 20,
        records: [
          {
            id: 'PH001',
            points: 100,
            type: 'EARN',
            source: '消费获得',
            createTime: '2025-01-02T10:00:00Z'
          }
        ]
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.getMemberPointsHistory(params)
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/pointsHistory', {
        ...params,
        pageNo: 1,
        pageSize: 20
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getMemberBalance', () => {
    it('应该成功获取会员余额', async () => {
      const mockResponse = {
        memberId: 'M001',
        totalBalance: 1000,
        availableBalance: 950,
        frozenBalance: 50
      }
      Request.get.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.getMemberBalance('M001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/balance', {
        memberId: 'M001'
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('rechargeMemberBalance', () => {
    it('应该成功充值会员余额', async () => {
      const params = {
        memberId: 'M001',
        amount: 500,
        paymentMethod: 'CASH',
        operatorId: 'OP001',
        remark: '现金充值'
      }
      const mockResponse = {
        success: true,
        rechargeId: 'RC001',
        newBalance: 1500
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.rechargeMemberBalance(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/recharge', {
        ...params,
        rechargeTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('deductMemberBalance', () => {
    it('应该成功扣减会员余额', async () => {
      const params = {
        memberId: 'M001',
        amount: 200,
        orderId: 'ORDER003',
        reason: '消费扣减',
        operatorId: 'OP001'
      }
      const mockResponse = {
        success: true,
        newBalance: 800,
        deductedAmount: 200
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.deductMemberBalance(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/deductBalance', {
        ...params,
        operateTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
    
    it('应该处理余额不足的情况', async () => {
      const mockError = new Error('余额不足')
      Request.post.mockRejectedValue(mockError)
      
      await expect(MemberApi.deductMemberBalance({
        memberId: 'M001',
        amount: 2000,
        orderId: 'ORDER003',
        reason: '消费扣减',
        operatorId: 'OP001'
      })).rejects.toThrow('余额不足')
    })
  })
  
  describe('upgradeMemberLevel', () => {
    it('应该成功升级会员等级', async () => {
      const params = {
        memberId: 'M001',
        newLevel: MEMBER_LEVELS.PLATINUM,
        reason: '消费达标自动升级',
        operatorId: 'SYSTEM'
      }
      const mockResponse = {
        success: true,
        oldLevel: MEMBER_LEVELS.GOLD,
        newLevel: MEMBER_LEVELS.PLATINUM,
        upgradeTime: '2025-01-02T10:00:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.upgradeMemberLevel(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/upgradeLevel', {
        ...params,
        upgradeTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getMemberLevelRules', () => {
    it('应该成功获取会员等级规则', async () => {
      const mockRules = [
        {
          level: MEMBER_LEVELS.BRONZE,
          name: '铜牌会员',
          minConsumption: 0,
          discountRate: 0.05
        },
        {
          level: MEMBER_LEVELS.SILVER,
          name: '银牌会员',
          minConsumption: 1000,
          discountRate: 0.08
        },
        {
          level: MEMBER_LEVELS.GOLD,
          name: '金牌会员',
          minConsumption: 5000,
          discountRate: 0.1
        }
      ]
      Request.get.mockResolvedValue(mockRules)
      
      const result = await MemberApi.getMemberLevelRules()
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/levelRules')
      expect(result).toEqual(mockRules)
    })
  })
  
  describe('getMemberStatistics', () => {
    it('应该成功获取会员统计信息', async () => {
      const mockStats = {
        memberId: 'M001',
        totalConsumption: 50000,
        totalOrders: 100,
        averageOrderAmount: 500,
        lastOrderDate: '2025-01-01',
        favoriteCategory: '食品',
        totalSavings: 5000
      }
      Request.get.mockResolvedValue(mockStats)
      
      const result = await MemberApi.getMemberStatistics('M001')
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/statistics', {
        memberId: 'M001'
      })
      expect(result).toEqual(mockStats)
    })
  })
  
  describe('freezeMember', () => {
    it('应该成功冻结会员账户', async () => {
      const params = {
        memberId: 'M001',
        reason: '违规操作',
        operatorId: 'OP001'
      }
      const mockResponse = {
        success: true,
        freezeTime: '2025-01-02T10:00:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.freezeMember(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/freeze', {
        ...params,
        freezeTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('unfreezeMember', () => {
    it('应该成功解冻会员账户', async () => {
      const params = {
        memberId: 'M001',
        reason: '申诉成功',
        operatorId: 'OP001'
      }
      const mockResponse = {
        success: true,
        unfreezeTime: '2025-01-02T11:00:00Z'
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.unfreezeMember(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/unfreeze', {
        ...params,
        unfreezeTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
  
  describe('getMemberCoupons', () => {
    it('应该成功获取会员优惠券', async () => {
      const mockCoupons = [
        {
          id: 'C001',
          name: '满100减10',
          type: 'FULL_REDUCTION',
          status: 'AVAILABLE',
          expireDate: '2025-12-31'
        },
        {
          id: 'C002',
          name: '9折优惠券',
          type: 'PERCENTAGE',
          status: 'USED',
          expireDate: '2025-06-30'
        }
      ]
      Request.get.mockResolvedValue(mockCoupons)
      
      const result = await MemberApi.getMemberCoupons({
        memberId: 'M001',
        status: 'AVAILABLE',
        available: true
      })
      
      expect(Request.get).toHaveBeenCalledWith('/erp/member/coupons', {
        memberId: 'M001',
        status: 'AVAILABLE',
        available: true
      })
      expect(result).toEqual(mockCoupons)
    })
  })
  
  describe('issueCouponToMember', () => {
    it('应该成功发放优惠券给会员', async () => {
      const params = {
        memberId: 'M001',
        couponTemplateId: 'CT001',
        quantity: 2,
        reason: '生日礼品',
        operatorId: 'OP001'
      }
      const mockResponse = {
        success: true,
        issuedCount: 2,
        coupons: [
          { id: 'C003', code: 'BIRTHDAY001' },
          { id: 'C004', code: 'BIRTHDAY002' }
        ]
      }
      Request.post.mockResolvedValue(mockResponse)
      
      const result = await MemberApi.issueCouponToMember(params)
      
      expect(Request.post).toHaveBeenCalledWith('/erp/member/issueCoupon', {
        ...params,
        issueTime: expect.any(String)
      })
      expect(result).toEqual(mockResponse)
    })
  })
})