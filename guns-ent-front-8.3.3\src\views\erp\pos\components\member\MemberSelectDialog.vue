<!--
  会员选择对话框
  
  简化的会员选择界面，用于购物车底部的会员选择功能
  
  <AUTHOR>
  @since 2025/01/03
-->
<template>
  <a-modal
    v-model:open="visible"
    title="选择会员"
    width="500px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="member-select-dialog">
      <!-- 搜索区域 -->
      <div class="search-section">
        <a-input
          v-model:value="searchKeyword"
          placeholder="输入会员卡号或手机号"
          size="large"
          @pressEnter="handleSearch"
          allowClear
        >
          <template #prefix>
            <search-outlined />
          </template>
          <template #suffix>
            <a-button type="primary" @click="handleSearch" :loading="searching">
              搜索
            </a-button>
          </template>
        </a-input>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="searchResult" class="search-result">
        <div class="member-card" @click="handleSelectMember(searchResult)">
          <div class="member-info">
            <div class="member-name">{{ searchResult.memberName }}</div>
            <div class="member-details">
              <span class="member-card-no">卡号: {{ searchResult.cardNo }}</span>
              <span class="member-phone">手机: {{ searchResult.phone }}</span>
            </div>
            <div class="member-balance">
              <span class="balance-item">余额: ¥{{ formatAmount(searchResult.balance) }}</span>
              <span class="points-item">积分: {{ searchResult.points }}</span>
            </div>
          </div>
          <div class="select-action">
            <a-button type="primary" size="small">选择</a-button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="searched && !searching" class="empty-result">
        <a-empty description="未找到匹配的会员" />
      </div>
      
      <!-- 加载状态 -->
      <div v-if="searching" class="loading-state">
        <a-spin size="large" />
        <div class="loading-text">正在搜索会员...</div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { MemberApi } from '../../api/member'

// 定义组件名称
defineOptions({
  name: 'MemberSelectDialog'
})

// 定义Props
const props = defineProps({
  open: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits(['update:open', 'select'])

// 响应式状态
const visible = ref(false)
const searchKeyword = ref('')
const searching = ref(false)
const searched = ref(false)
const searchResult = ref(null)

// 监听open属性变化
watch(() => props.open, (newVal) => {
  visible.value = newVal
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:open', newVal)
  if (!newVal) {
    // 关闭时重置状态
    resetState()
  }
})

// 方法
const formatAmount = (amount) => {
  return (amount || 0).toFixed(2)
}

const resetState = () => {
  searchKeyword.value = ''
  searching.value = false
  searched.value = false
  searchResult.value = null
}

const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    message.warning('请输入会员卡号或手机号')
    return
  }
  
  try {
    searching.value = true
    searched.value = false
    searchResult.value = null
    
    const keyword = searchKeyword.value.trim()
    let member = null
    
    // 判断是手机号还是卡号
    if (/^1[3-9]\d{9}$/.test(keyword)) {
      // 手机号格式
      member = await MemberApi.getMemberByPhone(keyword)
    } else {
      // 卡号格式
      member = await MemberApi.getMemberByCardNo(keyword)
    }
    
    searched.value = true
    
    if (member && member.data) {
      searchResult.value = member.data
    } else {
      searchResult.value = null
    }
    
  } catch (error) {
    console.error('搜索会员失败:', error)
    message.error('搜索会员失败，请重试')
    searchResult.value = null
  } finally {
    searching.value = false
    searched.value = true
  }
}

const handleSelectMember = (member) => {
  emit('select', member)
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.member-select-dialog {
  padding: 16px 0;
}

.search-section {
  margin-bottom: 20px;
}

.search-result {
  margin-top: 16px;
}

.member-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.member-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.member-details {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.member-card-no,
.member-phone {
  font-size: 13px;
  color: #8c8c8c;
}

.member-balance {
  display: flex;
  gap: 16px;
}

.balance-item,
.points-item {
  font-size: 13px;
  color: #52c41a;
  font-weight: 500;
}

.select-action {
  flex-shrink: 0;
}

.empty-result,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #8c8c8c;
}
</style>