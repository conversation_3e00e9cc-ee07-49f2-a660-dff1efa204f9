<template>
  <div class="product-display-area">
    <!-- 商品分类水平导航栏 -->
    <div class="category-section">
      <div class="category-tabs" v-loading="categoriesLoading">
        <a-tabs
          v-model:activeKey="selectedCategoryKey"
          type="card"
          size="small"
          @tab-click="handleCategoryClick"
          class="pos-category-tabs"
        >
          <a-tab-pane key="all" tab="全部商品" />
          <a-tab-pane
            v-for="category in categories"
            :key="category.categoryId"
            :tab="category.categoryName"
          />
        </a-tabs>
      </div>
    </div>

    <!-- 商品搜索和过滤区域 -->
    <div class="product-filter-section">
      <div class="filter-row">
        <!-- 搜索框 -->
        <div class="search-wrapper">
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索商品名称、编码、条形码"
            class="search-input"
            @pressEnter="handleSearch"
            @change="handleSearchChange"
            allowClear
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>

        <!-- 价格过滤 -->
        <div class="filter-item">
          <a-select
            v-model:value="priceFilter"
            placeholder="选择价格范围"
            class="filter-select"
            @change="handlePriceFilterChange"
            allowClear
            :clearable="true"
            :show-arrow="true"
          >
            <a-select-option value="0-10">0-10元</a-select-option>
            <a-select-option value="10-50">10-50元</a-select-option>
            <a-select-option value="50-100">50-100元</a-select-option>
            <a-select-option value="100+">100元以上</a-select-option>
          </a-select>
        </div>

        <!-- 库存过滤 -->
        <div class="filter-item">
          <a-select
            v-model:value="stockFilter"
            placeholder="选择库存状态"
            class="filter-select"
            @change="handleStockFilterChange"
            allowClear
            :clearable="true"
            :show-arrow="true"
          >
            <a-select-option value="in-stock">有库存</a-select-option>
            <a-select-option value="low-stock">库存不足</a-select-option>
            <a-select-option value="out-of-stock">缺货</a-select-option>
          </a-select>
        </div>

        <!-- 重置按钮 -->
        <div class="filter-item">
          <a-button @click="handleResetFilters" class="reset-btn" size="small">
            <template #icon>
              <reload-outlined />
            </template>
            重置
          </a-button>
        </div>
      </div>
    </div>

    <!-- 商品网格展示区域 -->
    <div class="product-section">
      <ProductGrid
        :category-id="selectedCategoryKey === 'all' ? null : selectedCategoryKey"
        :search-keyword="searchKeyword"
        :price-filter="priceFilter"
        :stock-filter="stockFilter"
        @productAdd="handleProductAdd"
        @productSelect="handleProductSelect"
        @clearSearch="handleClearSearch"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { PosApi } from '@/api/erp/pos'
import ProductGrid from './ProductGrid.vue'

// 导入样式文件
import '../styles/common.css'
import '../styles/product-display.css'

// 定义组件名称
defineOptions({
  name: 'ProductDisplayArea'
})

// 定义事件
const emit = defineEmits(['productAdd', 'productSelect'])

// 响应式数据
const selectedCategoryKey = ref('all')
const searchKeyword = ref('')
const priceFilter = ref('')
const stockFilter = ref('')
const categories = ref([])
const categoriesLoading = ref(false)

/**
 * 加载商品分类
 */
const loadCategories = async () => {
  try {
    categoriesLoading.value = true

    const result = await PosApi.getCategories()

    // 验证返回数据格式
    if (Array.isArray(result)) {
      categories.value = result

      // 验证必要字段
      if (categories.value.length > 0) {
        const firstCategory = categories.value[0]
        if (!firstCategory.categoryId || !firstCategory.categoryName) {
          message.warning('分类数据格式不完整，请检查后端接口')
        }
      }
    } else if (result && Array.isArray(result.data)) {
      // 处理可能的嵌套数据结构（ResponseData包装）
      categories.value = result.data
    } else if (result && result.success && Array.isArray(result.data)) {
      // 处理ResponseData格式
      categories.value = result.data
    } else {
      categories.value = []
      message.warning('分类数据格式异常，请检查后端接口')
    }
  } catch (error) {
    console.error('加载分类失败:', error)
    categories.value = []

    // 提供更详细的错误信息
    if (error.response) {
      console.error('HTTP错误状态:', error.response.status)
      console.error('错误响应数据:', error.response.data)
      message.error(`加载商品分类失败: ${error.response.status} ${error.response.statusText}`)
    } else if (error.request) {
      console.error('网络请求失败:', error.request)
      message.error('网络连接失败，请检查网络连接')
    } else {
      console.error('其他错误:', error.message)
      message.error('加载商品分类失败，请重试')
    }
  } finally {
    categoriesLoading.value = false
  }
}

/**
 * 处理分类点击 - 支持取消选中
 */
const handleCategoryClick = (categoryKey) => {
  // 如果点击的是当前已选中的分类，则取消选中（回到全部商品）
  if (selectedCategoryKey.value === categoryKey && categoryKey !== 'all') {
    selectedCategoryKey.value = 'all'
  } else {
    selectedCategoryKey.value = categoryKey
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  // 搜索逻辑已在computed中实现
}

/**
 * 处理搜索变化
 */
const handleSearchChange = () => {
  // 实时搜索，可以添加防抖处理
}

/**
 * 处理价格过滤变化
 */
const handlePriceFilterChange = (value) => {
  // 价格过滤逻辑已在computed中实现
}

/**
 * 处理库存过滤变化
 */
const handleStockFilterChange = (value) => {
  // 库存过滤逻辑已在computed中实现
}

/**
 * 重置所有过滤条件
 */
const handleResetFilters = () => {
  searchKeyword.value = ''
  priceFilter.value = ''
  stockFilter.value = ''
  selectedCategoryKey.value = 'all'
  message.success('已重置所有过滤条件')
}

/**
 * 处理清除搜索
 */
const handleClearSearch = () => {
  searchKeyword.value = ''
  message.success('已清除搜索条件')
}

/**
 * 处理商品添加
 */
const handleProductAdd = (product) => {
  emit('productAdd', product)
}

/**
 * 处理商品选择
 */
const handleProductSelect = (product) => {
  emit('productSelect', product)
}

// 组件挂载时加载分类
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.product-display-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 分类导航区域 */
.category-section {
  height: 60px; /* 增加高度为滚动条预留空间 */
  flex-shrink: 0;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 16px 12px 16px; /* 增加底部padding */
  overflow: visible; /* 改为visible避免外层截断 */
}

.category-tabs {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.pos-category-tabs {
  height: 100%;
  width: 100%;
}

.pos-category-tabs .ant-tabs-content-holder {
  display: none;
}

.pos-category-tabs .ant-tabs-nav {
  margin-bottom: 0;
  padding-bottom: 12px; /* 增加底部padding为滚动条预留更多空间 */
  width: 100%;
}

.pos-category-tabs .ant-tabs-nav-wrap {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: #d9d9d9 transparent;
  padding-bottom: 4px; /* 为滚动条预留额外空间 */
  width: 100%;
}

.pos-category-tabs .ant-tabs-nav-list {
  display: flex;
  flex-wrap: nowrap;
  min-width: max-content;
}

/* 自定义滚动条样式 */
.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar {
  height: 6px; /* 增加滚动条高度使其更明显 */
}

.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
  margin: 0 8px; /* 左右留边距 */
}

.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  border: 1px solid #f5f5f5; /* 添加边框增加视觉分离 */
}

.pos-category-tabs .ant-tabs-nav-wrap::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.pos-category-tabs .ant-tabs-tab {
  padding: 6px 12px !important;
  margin: 0 4px !important;
  font-size: 12px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.pos-category-tabs .ant-tabs-tab:hover {
  background: #f0f9ff !important;
  color: #1890ff !important;
}

.pos-category-tabs .ant-tabs-tab-active {
  background: #1890ff !important;
  color: #fff !important;
  border-color: #1890ff !important;
}

/* 搜索和过滤区域 */
.product-filter-section {
  height: 50px;
  flex-shrink: 0;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 16px;
  background: #fafafa;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 100%;
  overflow: hidden; /* 防止滚动条出现 */
  flex-wrap: nowrap; /* 确保不换行 */
}

.search-wrapper {
  flex: 1;
  min-width: 180px;
  max-width: 300px; /* 限制最大宽度 */
}

.search-input {
  width: 100%;
}

.filter-item {
  flex-shrink: 0;
}

.filter-select {
  width: 110px; /* 稍微减小宽度 */
}

.reset-btn {
  color: #666;
  border-color: #d9d9d9;
  padding: 0 12px; /* 减小按钮内边距 */
}

.reset-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* 响应式优化 - 小屏幕时调整布局 */
@media (max-width: 768px) {
  .product-filter-section {
    height: auto;
    padding: 8px 12px;
  }

  .filter-row {
    flex-wrap: wrap;
    gap: 6px;
    overflow: visible;
  }

  .search-wrapper {
    flex: 1 1 100%;
    min-width: unset;
    max-width: unset;
    margin-bottom: 4px;
  }

  .filter-item {
    flex: 1;
    min-width: 80px;
  }

  .filter-select {
    width: 100%;
    min-width: 80px;
  }

  .reset-btn {
    flex: 0 0 auto;
    min-width: 60px;
  }
}

/* 商品展示区域 */
.product-section {
  flex: 1;
  overflow: hidden;
}

/* 分类标签页响应式优化 */
@media (max-width: 1024px) {
  .pos-category-tabs .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .category-section {
    height: auto;
    min-height: 40px;
  }

  .pos-category-tabs .ant-tabs-tab {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 60px;
  }

  .pos-category-tabs .ant-tabs-nav {
    margin-bottom: 0;
  }
}
</style>
