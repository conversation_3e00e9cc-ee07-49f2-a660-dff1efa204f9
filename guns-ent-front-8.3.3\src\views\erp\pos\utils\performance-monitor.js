/**
 * POS模块性能监控器
 * 
 * 提供组件渲染性能、API调用性能、内存使用等监控功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { NUMERIC_CONSTANTS } from './constants'

/**
 * 性能监控器类
 */
export class PerformanceMonitor {
  
  /**
   * 初始化性能监控
   */
  static init() {
    // 初始化性能指标存储
    if (!window.posMetrics) {
      window.posMetrics = []
    }
    
    // 设置性能观察器
    this.setupPerformanceObserver()
    
    // 设置内存监控
    this.setupMemoryMonitor()
    
    // 设置页面可见性监控
    this.setupVisibilityMonitor()
  }
  
  /**
   * 测量组件渲染性能
   * @param {string} componentName - 组件名称
   * @param {Function} renderFunction - 渲染函数
   * @returns {any} 渲染函数的返回值
   */
  static measureComponentRender(componentName, renderFunction) {
    const startTime = performance.now()
    const startMark = `${componentName}-render-start`
    const endMark = `${componentName}-render-end`
    const measureName = `${componentName}-render`
    
    // 设置性能标记
    performance.mark(startMark)
    
    try {
      const result = renderFunction()
      
      // 如果是Promise，等待完成后再测量
      if (result && typeof result.then === 'function') {
        return result.then(res => {
          this.completeRenderMeasurement(componentName, startTime, startMark, endMark, measureName)
          return res
        }).catch(error => {
          this.completeRenderMeasurement(componentName, startTime, startMark, endMark, measureName, error)
          throw error
        })
      } else {
        this.completeRenderMeasurement(componentName, startTime, startMark, endMark, measureName)
        return result
      }
    } catch (error) {
      this.completeRenderMeasurement(componentName, startTime, startMark, endMark, measureName, error)
      throw error
    }
  }
  
  /**
   * 完成渲染性能测量
   * @param {string} componentName - 组件名称
   * @param {number} startTime - 开始时间
   * @param {string} startMark - 开始标记
   * @param {string} endMark - 结束标记
   * @param {string} measureName - 测量名称
   * @param {Error} error - 错误对象（可选）
   */
  static completeRenderMeasurement(componentName, startTime, startMark, endMark, measureName, error = null) {
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // 设置结束标记和测量
    performance.mark(endMark)
    performance.measure(measureName, startMark, endMark)
    
    // 记录性能数据
    this.recordMetric('component_render', {
      component: componentName,
      renderTime,
      timestamp: Date.now(),
      status: error ? 'error' : 'success',
      error: error ? error.message : null
    })
    
    // 如果渲染时间过长，发出警告
    const threshold = NUMERIC_CONSTANTS.PERFORMANCE_THRESHOLDS.COMPONENT_RENDER_TIME
    if (renderTime > threshold) {
      console.warn(`组件 ${componentName} 渲染时间过长: ${renderTime.toFixed(2)}ms (阈值: ${threshold}ms)`)
      
      // 记录性能警告
      this.recordMetric('performance_warning', {
        type: 'slow_render',
        component: componentName,
        renderTime,
        threshold,
        timestamp: Date.now()
      })
    }
  }
  
  /**
   * 测量API调用性能
   * @param {string} apiName - API名称
   * @param {Function} apiFunction - API函数
   * @returns {Function} 包装后的API函数
   */
  static measureApiCall(apiName, apiFunction) {
    return async (...args) => {
      const startTime = performance.now()
      const startMark = `${apiName}-api-start`
      const endMark = `${apiName}-api-end`
      const measureName = `${apiName}-api`
      
      // 设置性能标记
      performance.mark(startMark)
      
      try {
        const result = await apiFunction(...args)
        
        const endTime = performance.now()
        const apiTime = endTime - startTime
        
        // 设置结束标记和测量
        performance.mark(endMark)
        performance.measure(measureName, startMark, endMark)
        
        this.recordMetric('api_call', {
          api: apiName,
          duration: apiTime,
          status: 'success',
          timestamp: Date.now(),
          args: this.sanitizeArgs(args)
        })
        
        // 检查API调用时间
        const threshold = NUMERIC_CONSTANTS.PERFORMANCE_THRESHOLDS.API_CALL_TIME
        if (apiTime > threshold) {
          console.warn(`API ${apiName} 调用时间过长: ${apiTime.toFixed(2)}ms (阈值: ${threshold}ms)`)
          
          this.recordMetric('performance_warning', {
            type: 'slow_api',
            api: apiName,
            duration: apiTime,
            threshold,
            timestamp: Date.now()
          })
        }
        
        return result
      } catch (error) {
        const endTime = performance.now()
        const apiTime = endTime - startTime
        
        // 设置结束标记和测量
        performance.mark(endMark)
        performance.measure(measureName, startMark, endMark)
        
        this.recordMetric('api_call', {
          api: apiName,
          duration: apiTime,
          status: 'error',
          error: error.message,
          timestamp: Date.now(),
          args: this.sanitizeArgs(args)
        })
        
        throw error
      }
    }
  }
  
  /**
   * 清理参数（移除敏感信息）
   * @param {Array} args - 参数数组
   * @returns {Array} 清理后的参数
   */
  static sanitizeArgs(args) {
    return args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        const sanitized = { ...arg }
        
        // 移除敏感字段
        const sensitiveFields = ['password', 'token', 'cardNo', 'phone', 'idCard']
        sensitiveFields.forEach(field => {
          if (sanitized[field]) {
            sanitized[field] = '***'
          }
        })
        
        return sanitized
      }
      return arg
    })
  }
  
  /**
   * 记录性能指标
   * @param {string} type - 指标类型
   * @param {Object} data - 指标数据
   */
  static recordMetric(type, data) {
    if (!window.posMetrics) {
      window.posMetrics = []
    }
    
    const metric = {
      type,
      ...data,
      id: this.generateMetricId(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    window.posMetrics.push(metric)
    
    // 限制指标数量，避免内存泄漏
    if (window.posMetrics.length > 1000) {
      window.posMetrics = window.posMetrics.slice(-500)
    }
    
    // 发送到监控服务
    this.sendMetricToService(metric)
  }
  
  /**
   * 生成指标ID
   * @returns {string} 指标ID
   */
  static generateMetricId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 发送指标到监控服务
   * @param {Object} metric - 指标数据
   */
  static sendMetricToService(metric) {
    // 只发送重要的性能指标
    const importantTypes = ['performance_warning', 'api_call', 'component_render']
    
    if (importantTypes.includes(metric.type)) {
      try {
        // 这里可以集成实际的监控服务，如DataDog、New Relic等
        if (window.monitoringService && typeof window.monitoringService.track === 'function') {
          window.monitoringService.track('pos_performance', metric)
        }
      } catch (error) {
        console.warn('发送性能指标失败:', error)
      }
    }
  }
  
  /**
   * 获取性能指标
   * @param {Object} filters - 过滤条件
   * @returns {Array} 性能指标列表
   */
  static getMetrics(filters = {}) {
    if (!window.posMetrics) {
      return []
    }
    
    let metrics = [...window.posMetrics]
    
    // 应用过滤条件
    if (filters.type) {
      metrics = metrics.filter(metric => metric.type === filters.type)
    }
    
    if (filters.component) {
      metrics = metrics.filter(metric => metric.component === filters.component)
    }
    
    if (filters.api) {
      metrics = metrics.filter(metric => metric.api === filters.api)
    }
    
    if (filters.startTime && filters.endTime) {
      metrics = metrics.filter(metric => 
        metric.timestamp >= filters.startTime && metric.timestamp <= filters.endTime
      )
    }
    
    return metrics
  }
  
  /**
   * 获取性能统计
   * @param {string} type - 指标类型
   * @returns {Object} 性能统计
   */
  static getPerformanceStats(type) {
    const metrics = this.getMetrics({ type })
    
    if (metrics.length === 0) {
      return {
        count: 0,
        average: 0,
        min: 0,
        max: 0,
        p95: 0,
        p99: 0
      }
    }
    
    const durations = metrics
      .map(metric => metric.renderTime || metric.duration || 0)
      .sort((a, b) => a - b)
    
    const sum = durations.reduce((acc, duration) => acc + duration, 0)
    const count = durations.length
    
    return {
      count,
      average: sum / count,
      min: durations[0],
      max: durations[count - 1],
      p95: durations[Math.floor(count * 0.95)],
      p99: durations[Math.floor(count * 0.99)]
    }
  }
  
  /**
   * 清除性能指标
   */
  static clearMetrics() {
    if (window.posMetrics) {
      window.posMetrics = []
    }
    
    // 清除性能标记和测量
    if (performance.clearMarks) {
      performance.clearMarks()
    }
    if (performance.clearMeasures) {
      performance.clearMeasures()
    }
  }
  
  /**
   * 设置性能观察器
   */
  static setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      try {
        // 观察导航性能
        const navObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach(entry => {
            if (entry.entryType === 'navigation') {
              this.recordMetric('navigation', {
                domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                loadComplete: entry.loadEventEnd - entry.loadEventStart,
                firstPaint: entry.responseEnd - entry.requestStart,
                timestamp: Date.now()
              })
            }
          })
        })
        navObserver.observe({ entryTypes: ['navigation'] })
        
        // 观察资源加载性能
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach(entry => {
            if (entry.duration > 1000) { // 只记录加载时间超过1秒的资源
              this.recordMetric('resource_load', {
                name: entry.name,
                duration: entry.duration,
                size: entry.transferSize,
                timestamp: Date.now()
              })
            }
          })
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        
        // 观察长任务
        const longTaskObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach(entry => {
            this.recordMetric('long_task', {
              duration: entry.duration,
              startTime: entry.startTime,
              timestamp: Date.now()
            })
          })
        })
        
        if ('longtask' in PerformanceObserver.supportedEntryTypes) {
          longTaskObserver.observe({ entryTypes: ['longtask'] })
        }
      } catch (error) {
        console.warn('设置性能观察器失败:', error)
      }
    }
  }
  
  /**
   * 设置内存监控
   */
  static setupMemoryMonitor() {
    // 定期检查内存使用情况
    setInterval(() => {
      if (performance.memory) {
        const memInfo = {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        }
        
        const usagePercent = (memInfo.used / memInfo.limit) * 100
        
        this.recordMetric('memory_usage', {
          ...memInfo,
          usagePercent,
          timestamp: Date.now()
        })
        
        // 内存使用率过高时发出警告
        const threshold = NUMERIC_CONSTANTS.PERFORMANCE_THRESHOLDS.MEMORY_USAGE_PERCENT
        if (usagePercent > threshold) {
          console.warn(`内存使用率过高: ${usagePercent.toFixed(2)}% (阈值: ${threshold}%)`)
          
          this.recordMetric('performance_warning', {
            type: 'high_memory_usage',
            usagePercent,
            threshold,
            timestamp: Date.now()
          })
          
          // 建议垃圾回收
          if (window.gc && typeof window.gc === 'function') {
            try {
              window.gc()
            } catch (error) {
              console.warn('手动垃圾回收失败:', error)
            }
          }
        }
      }
    }, 30000) // 每30秒检查一次
  }
  
  /**
   * 设置页面可见性监控
   */
  static setupVisibilityMonitor() {
    let visibilityStartTime = Date.now()
    
    document.addEventListener('visibilitychange', () => {
      const now = Date.now()
      
      if (document.hidden) {
        // 页面变为不可见
        const visibleDuration = now - visibilityStartTime
        this.recordMetric('page_visibility', {
          event: 'hidden',
          visibleDuration,
          timestamp: now
        })
      } else {
        // 页面变为可见
        visibilityStartTime = now
        this.recordMetric('page_visibility', {
          event: 'visible',
          timestamp: now
        })
      }
    })
  }
  
  /**
   * 生成性能报告
   * @param {Object} options - 报告选项
   * @returns {Object} 性能报告
   */
  static generateReport(options = {}) {
    const {
      timeRange = 3600000, // 默认1小时
      includeDetails = false
    } = options
    
    const endTime = Date.now()
    const startTime = endTime - timeRange
    
    const metrics = this.getMetrics({ startTime, endTime })
    
    const report = {
      timeRange: { startTime, endTime },
      summary: {
        totalMetrics: metrics.length,
        componentRenders: metrics.filter(m => m.type === 'component_render').length,
        apiCalls: metrics.filter(m => m.type === 'api_call').length,
        warnings: metrics.filter(m => m.type === 'performance_warning').length
      },
      performance: {
        componentRender: this.getPerformanceStats('component_render'),
        apiCall: this.getPerformanceStats('api_call')
      },
      warnings: metrics.filter(m => m.type === 'performance_warning'),
      memory: this.getLatestMemoryUsage()
    }
    
    if (includeDetails) {
      report.details = metrics
    }
    
    return report
  }
  
  /**
   * 获取最新的内存使用情况
   * @returns {Object} 内存使用情况
   */
  static getLatestMemoryUsage() {
    const memoryMetrics = this.getMetrics({ type: 'memory_usage' })
    return memoryMetrics.length > 0 ? memoryMetrics[memoryMetrics.length - 1] : null
  }
  
  /**
   * 导出性能数据
   * @param {string} format - 导出格式 ('json' | 'csv')
   * @returns {string} 导出的数据
   */
  static exportMetrics(format = 'json') {
    const metrics = this.getMetrics()
    
    if (format === 'csv') {
      const headers = ['type', 'timestamp', 'component', 'api', 'duration', 'status']
      const csvData = [
        headers.join(','),
        ...metrics.map(metric => 
          headers.map(header => metric[header] || '').join(',')
        )
      ].join('\n')
      
      return csvData
    }
    
    return JSON.stringify(metrics, null, 2)
  }
}