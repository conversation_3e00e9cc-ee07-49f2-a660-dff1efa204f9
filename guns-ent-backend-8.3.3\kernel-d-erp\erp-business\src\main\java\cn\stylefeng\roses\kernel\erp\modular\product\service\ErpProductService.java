package cn.stylefeng.roses.kernel.erp.modular.product.service;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;

import java.util.List;

/**
 * 商品主档案Service接口
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
public interface ErpProductService {

    /**
     * 新增商品
     */
    void add(ErpProductRequest erpProductRequest);

    /**
     * 删除商品
     */
    void del(ErpProductRequest erpProductRequest);

    /**
     * 批量删除商品
     */
    void batchDelete(ErpProductRequest erpProductRequest);

    /**
     * 编辑商品
     */
    void edit(ErpProductRequest erpProductRequest);

    /**
     * 查询商品详情
     */
    ErpProductResponse detail(ErpProductRequest erpProductRequest);

    /**
     * 分页查询商品列表
     */
    PageResult<ErpProductResponse> findPage(ErpProductRequest erpProductRequest);

    /**
     * 查询商品列表
     */
    List<ErpProductResponse> findList(ErpProductRequest erpProductRequest);

    /**
     * 更新商品状态
     */
    void updateStatus(ErpProductRequest erpProductRequest);



    /**
     * 根据分类ID查询商品列表
     */
    List<ErpProductResponse> findListByCategoryId(Long categoryId);

    /**
     * 校验商品编码是否重复
     */
    boolean validateProductCodeRepeat(String productCode, Long productId);

    /**
     * 校验条形码是否重复
     */
    boolean validateBarcodeRepeat(String barcode, Long productId);

    /**
     * 校验商品是否可以删除
     */
    boolean validateCanDelete(Long productId);

    /**
     * 校验商品是否可以停用
     */
    boolean validateCanInactive(Long productId);

    /**
     * 根据供应商ID查询商品列表
     */
    List<ErpProductResponse> findListBySupplierId(Long supplierId);

    /**
     * 校验计价类型变更的影响
     */
    cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse validatePricingTypeChange(Long productId, String newPricingType);

}