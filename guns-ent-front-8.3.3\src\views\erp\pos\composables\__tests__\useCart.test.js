/**
 * 购物车组合式函数单元测试
 * 
 * 测试购物车业务逻辑的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useCart } from '../useCart'
import { usePosStore } from '@/stores/pos'
import { CartApi } from '../../api/cart'
import { message } from 'ant-design-vue'

// Mock dependencies
vi.mock('../../api/cart')
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

vi.mock('../useDataRecovery', () => ({
  useDataRecovery: () => ({
    saveCartState: vi.fn(),
    restoreCartState: vi.fn()
  })
}))

describe('useCart', () => {
  let pinia
  let store
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = usePosStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('基础状态', () => {
    it('应该正确初始化空购物车状态', () => {
      const { items, isEmpty, canCheckout, cartTotal } = useCart()
      
      expect(items.value).toEqual([])
      expect(isEmpty.value).toBe(true)
      expect(canCheckout.value).toBe(false)
      expect(cartTotal.value).toEqual({
        totalAmount: 0,
        discountAmount: 0,
        finalAmount: 0,
        itemCount: 0,
        totalQuantity: 0
      })
    })
    
    it('应该正确计算购物车总金额', () => {
      const { cartTotal } = useCart()
      
      // 添加测试数据
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 },
        { id: 'P002', name: '香蕉', price: 3.0, quantity: 3, subtotal: 9 }
      ]
      
      expect(cartTotal.value).toEqual({
        totalAmount: 20,
        discountAmount: 0,
        finalAmount: 20,
        itemCount: 2,
        totalQuantity: 5
      })
    })
    
    it('应该正确处理折扣金额', () => {
      const { cartTotal } = useCart()
      
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 10, quantity: 2, subtotal: 20 }
      ]
      store.discountAmount = 5
      
      expect(cartTotal.value).toEqual({
        totalAmount: 20,
        discountAmount: 5,
        finalAmount: 15,
        itemCount: 1,
        totalQuantity: 2
      })
    })
  })
  
  describe('addItem', () => {
    it('应该成功添加新商品', async () => {
      const { addItem, items } = useCart()
      
      const product = {
        id: 'P001',
        name: '苹果',
        price: 5.5,
        unit: '斤'
      }
      
      // Mock API调用
      CartApi.checkInventory.mockResolvedValue({ available: true, stock: 100 })
      
      const result = await addItem(product, 2)
      
      expect(result).toBe(true)
      expect(CartApi.checkInventory).toHaveBeenCalledWith('P001', 2)
      expect(message.success).toHaveBeenCalledWith('已添加"苹果"到购物车')
      expect(items.value).toHaveLength(1)
      expect(items.value[0]).toMatchObject({
        id: 'P001',
        name: '苹果',
        price: 5.5,
        quantity: 2,
        subtotal: 11
      })
    })
    
    it('应该处理库存不足的情况', async () => {
      const { addItem, items } = useCart()
      
      const product = {
        id: 'P001',
        name: '苹果',
        price: 5.5
      }
      
      CartApi.checkInventory.mockResolvedValue({ available: false, stock: 1 })
      
      const result = await addItem(product, 5)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('商品"苹果"库存不足，当前库存：1')
      expect(items.value).toHaveLength(0)
    })
    
    it('应该合并相同商品的数量', async () => {
      const { addItem, items } = useCart()
      
      const product = {
        id: 'P001',
        name: '苹果',
        price: 5.5
      }
      
      // 先添加一个商品
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
      
      CartApi.checkInventory.mockResolvedValue({ available: true, stock: 100 })
      
      const result = await addItem(product, 3)
      
      expect(result).toBe(true)
      expect(CartApi.checkInventory).toHaveBeenCalledWith('P001', 5) // 2 + 3
    })
    
    it('应该验证商品信息', async () => {
      const { addItem } = useCart()
      
      const invalidProduct = {
        id: '',
        name: '苹果',
        price: 5.5
      }
      
      const result = await addItem(invalidProduct, 1)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalled()
    })
    
    it('应该验证数量', async () => {
      const { addItem } = useCart()
      
      const product = {
        id: 'P001',
        name: '苹果',
        price: 5.5
      }
      
      const result = await addItem(product, 0)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalled()
    })
  })
  
  describe('updateQuantity', () => {
    beforeEach(() => {
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
    })
    
    it('应该成功更新商品数量', async () => {
      const { updateQuantity, items } = useCart()
      
      CartApi.checkInventory.mockResolvedValue({ available: true, stock: 100 })
      
      const result = await updateQuantity('P001', 5)
      
      expect(result).toBe(true)
      expect(CartApi.checkInventory).toHaveBeenCalledWith('P001', 5)
    })
    
    it('应该在数量为0时删除商品', async () => {
      const { updateQuantity, items } = useCart()
      
      const result = await updateQuantity('P001', 0)
      
      expect(result).toBe(true)
      expect(message.success).toHaveBeenCalledWith('已移除"苹果"')
    })
    
    it('应该处理商品不存在的情况', async () => {
      const { updateQuantity } = useCart()
      
      const result = await updateQuantity('INVALID', 5)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('商品不存在')
    })
    
    it('应该处理库存不足的情况', async () => {
      const { updateQuantity } = useCart()
      
      CartApi.checkInventory.mockResolvedValue({ available: false, stock: 3 })
      
      const result = await updateQuantity('P001', 10)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('商品"苹果"库存不足，当前库存：3')
    })
  })
  
  describe('removeItem', () => {
    beforeEach(() => {
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
    })
    
    it('应该成功移除商品', async () => {
      const { removeItem } = useCart()
      
      const result = await removeItem('P001')
      
      expect(result).toBe(true)
      expect(message.success).toHaveBeenCalledWith('已移除"苹果"')
    })
    
    it('应该处理商品不存在的情况', async () => {
      const { removeItem } = useCart()
      
      const result = await removeItem('INVALID')
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('商品不存在')
    })
  })
  
  describe('clearCart', () => {
    it('应该成功清空购物车', async () => {
      const { clearCart } = useCart()
      
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
      
      const result = await clearCart()
      
      expect(result).toBe(true)
      expect(message.success).toHaveBeenCalledWith('购物车已清空')
    })
    
    it('应该处理空购物车的情况', async () => {
      const { clearCart } = useCart()
      
      const result = await clearCart()
      
      expect(result).toBe(true)
      expect(message.info).toHaveBeenCalledWith('购物车已经是空的')
    })
  })
  
  describe('batchAddItems', () => {
    it('应该成功批量添加商品', async () => {
      const { batchAddItems } = useCart()
      
      const products = [
        { product: { id: 'P001', name: '苹果', price: 5.5 }, quantity: 2 },
        { product: { id: 'P002', name: '香蕉', price: 3.0 }, quantity: 3 }
      ]
      
      CartApi.batchCheckInventory.mockResolvedValue([
        { productId: 'P001', available: true, stock: 100 },
        { productId: 'P002', available: true, stock: 50 }
      ])
      
      const result = await batchAddItems(products)
      
      expect(result.success).toBe(2)
      expect(result.failed).toBe(0)
      expect(message.success).toHaveBeenCalledWith('成功添加2件商品')
    })
    
    it('应该处理部分商品库存不足的情况', async () => {
      const { batchAddItems } = useCart()
      
      const products = [
        { product: { id: 'P001', name: '苹果', price: 5.5 }, quantity: 2 },
        { product: { id: 'P002', name: '香蕉', price: 3.0 }, quantity: 3 }
      ]
      
      CartApi.batchCheckInventory.mockResolvedValue([
        { productId: 'P001', available: true, stock: 100 },
        { productId: 'P002', available: false, stock: 1 }
      ])
      
      const result = await batchAddItems(products)
      
      expect(result.success).toBe(1)
      expect(result.failed).toBe(1)
      expect(result.errors).toContain('商品"香蕉"库存不足')
    })
  })
  
  describe('validateCart', () => {
    it('应该成功验证购物车', async () => {
      const { validateCart } = useCart()
      
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
      
      CartApi.validateCart.mockResolvedValue({ valid: true, errors: [] })
      
      const result = await validateCart()
      
      expect(result.valid).toBe(true)
      expect(CartApi.validateCart).toHaveBeenCalledWith({
        items: store.cartItems
      })
    })
  })
  
  describe('applyCoupon', () => {
    beforeEach(() => {
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 10, quantity: 2, subtotal: 20 }
      ]
    })
    
    it('应该成功应用优惠券', async () => {
      const { applyCoupon } = useCart()
      
      CartApi.applyCoupon.mockResolvedValue({
        success: true,
        discountAmount: 5,
        couponInfo: { name: '满20减5' }
      })
      
      const result = await applyCoupon('DISCOUNT5')
      
      expect(result).toBe(true)
      expect(message.success).toHaveBeenCalledWith('优惠券应用成功，优惠5元')
      expect(CartApi.applyCoupon).toHaveBeenCalledWith({
        items: store.cartItems,
        couponCode: 'DISCOUNT5',
        member: null
      })
    })
    
    it('应该处理空购物车的情况', async () => {
      const { applyCoupon } = useCart()
      
      store.cartItems = []
      
      const result = await applyCoupon('DISCOUNT5')
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('购物车为空，无法使用优惠券')
    })
    
    it('应该处理优惠券无效的情况', async () => {
      const { applyCoupon } = useCart()
      
      CartApi.applyCoupon.mockResolvedValue({
        success: false,
        message: '优惠券已过期'
      })
      
      const result = await applyCoupon('EXPIRED')
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('优惠券已过期')
    })
  })
  
  describe('addItemByBarcode', () => {
    it('应该成功根据条码添加商品', async () => {
      const { addItemByBarcode } = useCart()
      
      const product = {
        id: 'P001',
        name: '苹果',
        price: 5.5,
        barcode: '1234567890123'
      }
      
      CartApi.getProductByBarcode.mockResolvedValue(product)
      CartApi.checkInventory.mockResolvedValue({ available: true, stock: 100 })
      
      const result = await addItemByBarcode('1234567890123', 2)
      
      expect(result).toBe(true)
      expect(CartApi.getProductByBarcode).toHaveBeenCalledWith('1234567890123')
    })
    
    it('应该处理空条码的情况', async () => {
      const { addItemByBarcode } = useCart()
      
      const result = await addItemByBarcode('', 1)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('请输入商品条码')
    })
    
    it('应该处理条码不存在的情况', async () => {
      const { addItemByBarcode } = useCart()
      
      CartApi.getProductByBarcode.mockResolvedValue(null)
      
      const result = await addItemByBarcode('INVALID', 1)
      
      expect(result).toBe(false)
      expect(message.error).toHaveBeenCalledWith('未找到对应的商品')
    })
  })
})