import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

/**
 * POS收银系统状态管理
 * 管理购物车、会员信息、支付状态、挂单等核心业务状态
 */
export const usePosStore = defineStore('pos', () => {
  // ==================== 购物车状态 ====================

  // 购物车商品列表
  const cartItems = ref([])

  // 购物车总金额
  const totalAmount = ref(0)

  // 折扣金额
  const discountAmount = ref(0)

  // 实付金额
  const finalAmount = ref(0)

  // ==================== 会员信息状态 ====================

  // 当前会员信息
  const currentMember = ref(null)

  // 会员折扣率
  const memberDiscountRate = ref(0)

  // 会员积分抵扣金额
  const pointsDeductionAmount = ref(0)

  // ==================== 支付状态 ====================

  // 支付状态: idle(空闲), processing(处理中), success(成功), failed(失败)
  const paymentStatus = ref('idle')

  // 当前选择的支付方式
  const selectedPaymentMethod = ref('')

  // 实收金额（现金支付时使用）
  const receivedAmount = ref(0)

  // 找零金额
  const changeAmount = ref(0)

  // ==================== 挂单状态 ====================

  // 挂起的订单列表
  const suspendedOrders = ref([])

  // 挂单计数器（用于生成挂单编号）
  const suspendCounter = ref(1)

  // ==================== 商品和分类状态 ====================

  // 商品分类列表
  const categories = ref([])

  // 当前选中的分类
  const selectedCategory = ref(null)

  // 商品列表
  const products = ref([])

  // 商品搜索关键词
  const searchKeyword = ref('')

  // ==================== 计算属性 ====================

  // 购物车商品总数量
  const cartItemCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })

  // 是否有商品在购物车中
  const hasCartItems = computed(() => {
    return cartItems.value.length > 0
  })

  // 是否可以结算（购物车不为空且不在支付处理中）
  const canCheckout = computed(() => {
    return hasCartItems.value && paymentStatus.value !== 'processing'
  })

  // 是否有会员信息
  const hasMember = computed(() => {
    return currentMember.value !== null
  })

  // 是否有挂起的订单
  const hasSuspendedOrders = computed(() => {
    return suspendedOrders.value.length > 0
  })

  // ==================== 购物车操作方法 ====================

  /**
   * 添加商品到购物车
   * @param {Object} product 商品信息
   * @param {number} quantity 数量，默认为1
   */
  const addToCart = (product, quantity = 1) => {
    try {
      // 检查商品库存
      const stockQuantity = product.stock || product.stockQuantity || 0
      if (stockQuantity < quantity) {
        message.warning(`商品 ${product.productName || product.name} 库存不足`)
        return false
      }

      // 检查商品是否已在购物车中
      const productId = product.productId || product.id
      const existingItem = cartItems.value.find(item =>
        item.productId === productId || item.id === productId
      )

      if (existingItem) {
        // 如果商品已存在，增加数量
        const newQuantity = existingItem.quantity + quantity
        const stockQuantity = product.stock || product.stockQuantity || 0
        if (stockQuantity < newQuantity) {
          message.warning(`商品 ${product.productName || product.name} 库存不足`)
          return false
        }
        existingItem.quantity = newQuantity
        existingItem.totalPrice = existingItem.unitPrice * newQuantity
        existingItem.subtotal = existingItem.unitPrice * newQuantity // 兼容CartItem组件
      } else {
        // 添加新商品到购物车
        const unitPrice = product.price || product.retailPrice || product.unitPrice || 0
        cartItems.value.push({
          productId: productId,
          id: productId, // 同时保存id字段以兼容useCart
          productName: product.productName || product.name || '未知商品',
          name: product.productName || product.name || '未知商品', // 兼容CartItem组件
          productCode: product.productCode || '',
          barcode: product.barcode || product.productCode || '',
          unitPrice: unitPrice,
          price: unitPrice, // 兼容CartItem组件
          quantity: quantity,
          totalPrice: unitPrice * quantity,
          subtotal: unitPrice * quantity, // 兼容CartItem组件
          unit: product.unit || '件',
          pricingType: product.pricingType || 'NORMAL',
          retailPrice: product.retailPrice || product.price || unitPrice,
          piecePrice: product.piecePrice || 0,
          referencePrice: product.referencePrice || 0,
          imageUrl: product.imageUrl || product.image || '',
          image: product.imageUrl || product.image || '', // 兼容CartItem组件
          specifications: product.specification || product.specifications || '',
          categoryId: product.categoryId || '',
          categoryName: product.categoryName || '',
          addedAt: new Date().toISOString()
        })
      }

      // 重新计算总金额
      calculateTotal()

      message.success(`已添加 ${product.productName || product.name} 到购物车`)
      return true
    } catch (error) {
      console.error('添加商品到购物车失败:', error)
      message.error('添加商品失败，请重试')
      return false
    }
  }

  /**
   * 从购物车移除商品
   * @param {number|string} productId 商品ID
   */
  const removeFromCart = (productId) => {
    try {
      const index = cartItems.value.findIndex(item =>
        item.productId === productId || item.id === productId
      )
      if (index > -1) {
        const removedItem = cartItems.value.splice(index, 1)[0]
        calculateTotal()
        message.success(`已移除 ${removedItem.productName || removedItem.name}`)
      }
    } catch (error) {
      console.error('移除商品失败:', error)
      message.error('移除商品失败，请重试')
    }
  }

  /**
   * 更新购物车中商品的数量
   * @param {number|string} productId 商品ID
   * @param {number} quantity 新数量
   */
  const updateQuantity = (productId, quantity) => {
    try {
      if (quantity <= 0) {
        removeFromCart(productId)
        return
      }

      const item = cartItems.value.find(item =>
        item.productId === productId || item.id === productId
      )
      if (item) {
        // 这里应该检查库存，但由于没有直接的商品信息，暂时跳过
        item.quantity = quantity
        item.totalPrice = item.unitPrice * quantity
        item.subtotal = item.unitPrice * quantity // 兼容CartItem组件
        calculateTotal()
      }
    } catch (error) {
      console.error('更新商品数量失败:', error)
      message.error('更新数量失败，请重试')
    }
  }

  /**
   * 清空购物车
   */
  const clearCart = () => {
    try {
      cartItems.value = []
      totalAmount.value = 0
      discountAmount.value = 0
      finalAmount.value = 0
      pointsDeductionAmount.value = 0
      message.success('购物车已清空')
    } catch (error) {
      console.error('清空购物车失败:', error)
      message.error('清空购物车失败')
    }
  }

  /**
   * 计算购物车总金额
   */
  const calculateTotal = () => {
    try {
      // 计算商品总金额
      totalAmount.value = cartItems.value.reduce((total, item) => {
        return total + (item.totalPrice || item.subtotal || 0)
      }, 0)

      // 计算会员折扣
      let memberDiscount = 0
      if (hasMember.value && memberDiscountRate.value > 0) {
        memberDiscount = totalAmount.value * (memberDiscountRate.value / 100)
      }

      // 总折扣 = 会员折扣 + 其他折扣
      discountAmount.value = memberDiscount

      // 实付金额 = 总金额 - 折扣金额 - 积分抵扣
      finalAmount.value = Math.max(0, totalAmount.value - discountAmount.value - pointsDeductionAmount.value)

    } catch (error) {
      console.error('计算总金额失败:', error)
    }
  }

  // ==================== 会员管理方法 ====================

  /**
   * 设置当前会员
   * @param {Object} member 会员信息
   */
  const setCurrentMember = (member) => {
    try {
      currentMember.value = member

      // 设置会员折扣率
      if (member && member.discountRate) {
        memberDiscountRate.value = member.discountRate
      } else {
        memberDiscountRate.value = 0
      }

      // 重新计算总金额
      calculateTotal()

      if (member) {
        message.success(`已绑定会员：${member.memberName}`)
      }
    } catch (error) {
      console.error('设置会员信息失败:', error)
      message.error('设置会员信息失败')
    }
  }

  /**
   * 清除当前会员
   */
  const clearCurrentMember = () => {
    try {
      currentMember.value = null
      memberDiscountRate.value = 0
      pointsDeductionAmount.value = 0
      calculateTotal()
      message.success('已取消会员绑定')
    } catch (error) {
      console.error('清除会员信息失败:', error)
    }
  }

  /**
   * 设置积分抵扣
   * @param {number} points 使用的积分数
   * @param {number} amount 抵扣金额
   */
  const setPointsDeduction = (points, amount) => {
    try {
      if (amount < 0) {
        amount = 0
      }

      // 积分抵扣不能超过订单总金额
      const maxDeduction = totalAmount.value - discountAmount.value
      if (amount > maxDeduction) {
        amount = maxDeduction
      }

      pointsDeductionAmount.value = amount

      // 保存使用的积分数
      if (currentMember.value) {
        currentMember.value.usedPoints = points
      }

      calculateTotal()
    } catch (error) {
      console.error('设置积分抵扣失败:', error)
      message.error('设置积分抵扣失败')
    }
  }

  /**
   * 应用会员折扣
   * @param {number} memberId 会员ID
   */
  const applyMemberDiscount = async (memberId) => {
    try {
      if (!memberId || totalAmount.value <= 0) {
        return
      }

      // 这里可以调用API计算会员折扣
      // const discountInfo = await PosApi.calculateMemberDiscount(memberId, totalAmount.value)

      // 暂时使用会员等级的默认折扣率
      if (currentMember.value && currentMember.value.discountRate) {
        memberDiscountRate.value = currentMember.value.discountRate
      }

      calculateTotal()
    } catch (error) {
      console.error('应用会员折扣失败:', error)
    }
  }

  /**
   * 清除会员折扣
   */
  const clearMemberDiscount = () => {
    try {
      memberDiscountRate.value = 0
      pointsDeductionAmount.value = 0
      calculateTotal()
    } catch (error) {
      console.error('清除会员折扣失败:', error)
    }
  }

  // 积分兑换比例（多少积分兑换1元）
  const pointsExchangeRate = ref(100)

  // ==================== 支付处理方法 ====================

  /**
   * 设置支付方式
   * @param {string} method 支付方式
   */
  const setPaymentMethod = (method) => {
    selectedPaymentMethod.value = method

    // 如果不是现金支付，清除现金相关数据
    if (method !== 'CASH') {
      receivedAmount.value = 0
      changeAmount.value = 0
    }
  }

  /**
   * 设置实收金额（现金支付）
   * @param {number} amount 实收金额
   */
  const setReceivedAmount = (amount) => {
    try {
      receivedAmount.value = amount

      // 计算找零
      changeAmount.value = Math.max(0, amount - finalAmount.value)
    } catch (error) {
      console.error('设置实收金额失败:', error)
    }
  }

  /**
   * 开始支付处理
   */
  const startPayment = () => {
    paymentStatus.value = 'processing'
  }

  /**
   * 支付成功
   */
  const paymentSuccess = () => {
    paymentStatus.value = 'success'
    message.success('支付成功')
  }

  /**
   * 支付失败
   * @param {string} errorMessage 错误信息
   */
  const paymentFailed = (errorMessage = '支付失败') => {
    paymentStatus.value = 'failed'
    message.error(errorMessage)
  }

  /**
   * 重置支付状态
   */
  const resetPaymentStatus = () => {
    paymentStatus.value = 'idle'
    selectedPaymentMethod.value = ''
    receivedAmount.value = 0
    changeAmount.value = 0
  }

  /**
   * 完成订单（支付成功后清理状态）
   */
  const completeOrder = () => {
    try {
      // 清空购物车
      clearCart()

      // 清除会员信息
      clearCurrentMember()

      // 重置支付状态
      resetPaymentStatus()

      message.success('订单完成')
    } catch (error) {
      console.error('完成订单失败:', error)
    }
  }

  // ==================== 挂单管理方法 ====================

  /**
   * 挂起当前订单
   * @param {string} remark 挂单备注
   */
  const suspendCurrentOrder = (remark = '') => {
    try {
      if (!hasCartItems.value) {
        message.warning('购物车为空，无法挂单')
        return false
      }

      // 生成挂单编号
      const suspendNo = `SUSPEND-${Date.now()}-${suspendCounter.value.toString().padStart(3, '0')}`
      suspendCounter.value++

      // 创建挂单数据
      const suspendedOrder = {
        suspendId: Date.now(),
        suspendNo: suspendNo,
        suspendTime: new Date().toISOString(),
        remark: remark,
        orderData: {
          cartItems: JSON.parse(JSON.stringify(cartItems.value)),
          totalAmount: totalAmount.value,
          discountAmount: discountAmount.value,
          finalAmount: finalAmount.value,
          currentMember: currentMember.value ? JSON.parse(JSON.stringify(currentMember.value)) : null,
          memberDiscountRate: memberDiscountRate.value,
          pointsDeductionAmount: pointsDeductionAmount.value
        }
      }

      // 添加到挂单列表
      suspendedOrders.value.push(suspendedOrder)

      // 保存到本地存储
      saveSuspendedOrdersToLocal()

      // 清空当前订单
      clearCart()
      clearCurrentMember()

      message.success(`订单已挂起，挂单号：${suspendNo}`)
      return true
    } catch (error) {
      console.error('挂单失败:', error)
      message.error('挂单失败，请重试')
      return false
    }
  }

  /**
   * 恢复挂起的订单
   * @param {number} suspendId 挂单ID
   */
  const resumeSuspendedOrder = (suspendId) => {
    try {
      const suspendedOrder = suspendedOrders.value.find(order => order.suspendId === suspendId)
      if (!suspendedOrder) {
        message.error('挂单不存在')
        return false
      }

      // 如果当前有商品，提示用户
      if (hasCartItems.value) {
        // 这里应该弹出确认对话框，暂时直接覆盖
        message.warning('当前购物车将被清空')
      }

      // 恢复订单数据
      const orderData = suspendedOrder.orderData
      cartItems.value = orderData.cartItems || []
      totalAmount.value = orderData.totalAmount || 0
      discountAmount.value = orderData.discountAmount || 0
      finalAmount.value = orderData.finalAmount || 0

      // 恢复会员信息
      if (orderData.currentMember) {
        currentMember.value = orderData.currentMember
        memberDiscountRate.value = orderData.memberDiscountRate || 0
        pointsDeductionAmount.value = orderData.pointsDeductionAmount || 0
      }

      // 从挂单列表中移除
      const index = suspendedOrders.value.findIndex(order => order.suspendId === suspendId)
      if (index > -1) {
        suspendedOrders.value.splice(index, 1)
        saveSuspendedOrdersToLocal()
      }

      message.success(`已恢复挂单：${suspendedOrder.suspendNo}`)
      return true
    } catch (error) {
      console.error('恢复挂单失败:', error)
      message.error('恢复挂单失败，请重试')
      return false
    }
  }

  /**
   * 删除挂起的订单
   * @param {number} suspendId 挂单ID
   */
  const deleteSuspendedOrder = (suspendId) => {
    try {
      const index = suspendedOrders.value.findIndex(order => order.suspendId === suspendId)
      if (index > -1) {
        const deletedOrder = suspendedOrders.value.splice(index, 1)[0]
        saveSuspendedOrdersToLocal()
        message.success(`已删除挂单：${deletedOrder.suspendNo}`)
        return true
      }
      return false
    } catch (error) {
      console.error('删除挂单失败:', error)
      message.error('删除挂单失败')
      return false
    }
  }

  /**
   * 清理过期的挂单
   * @param {number} expireHours 过期时间（小时），默认24小时
   */
  const clearExpiredSuspendedOrders = (expireHours = 24) => {
    try {
      const expireTime = Date.now() - (expireHours * 60 * 60 * 1000)
      const beforeCount = suspendedOrders.value.length

      suspendedOrders.value = suspendedOrders.value.filter(order => {
        const suspendTime = new Date(order.suspendTime).getTime()
        return suspendTime > expireTime
      })

      const clearedCount = beforeCount - suspendedOrders.value.length
      if (clearedCount > 0) {
        saveSuspendedOrdersToLocal()
        message.info(`已清理 ${clearedCount} 个过期挂单`)
      }

      return clearedCount
    } catch (error) {
      console.error('清理过期挂单失败:', error)
      return 0
    }
  }

  // ==================== 本地存储方法 ====================

  /**
   * 保存挂单数据到本地存储
   */
  const saveSuspendedOrdersToLocal = () => {
    try {
      const data = {
        suspendedOrders: suspendedOrders.value,
        suspendCounter: suspendCounter.value,
        timestamp: Date.now()
      }
      localStorage.setItem('pos_suspended_orders', JSON.stringify(data))
    } catch (error) {
      console.error('保存挂单数据到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储加载挂单数据
   */
  const loadSuspendedOrdersFromLocal = () => {
    try {
      const dataStr = localStorage.getItem('pos_suspended_orders')
      if (dataStr) {
        const data = JSON.parse(dataStr)
        suspendedOrders.value = data.suspendedOrders || []
        suspendCounter.value = data.suspendCounter || 1

        // 自动清理过期挂单
        clearExpiredSuspendedOrders()
      }
    } catch (error) {
      console.error('从本地存储加载挂单数据失败:', error)
      // 如果加载失败，重置为空数组
      suspendedOrders.value = []
      suspendCounter.value = 1
    }
  }

  /**
   * 保存当前购物车状态到本地存储（用于页面刷新恢复）
   */
  const saveCartToLocal = () => {
    try {
      const cartData = {
        cartItems: cartItems.value,
        totalAmount: totalAmount.value,
        discountAmount: discountAmount.value,
        finalAmount: finalAmount.value,
        currentMember: currentMember.value,
        memberDiscountRate: memberDiscountRate.value,
        pointsDeductionAmount: pointsDeductionAmount.value,
        timestamp: Date.now()
      }
      localStorage.setItem('pos_current_cart', JSON.stringify(cartData))
    } catch (error) {
      console.error('保存购物车到本地存储失败:', error)
    }
  }

  /**
   * 从本地存储恢复购物车状态
   */
  const loadCartFromLocal = () => {
    try {
      const cartDataStr = localStorage.getItem('pos_current_cart')
      if (cartDataStr) {
        const cartData = JSON.parse(cartDataStr)

        // 检查数据是否过期（超过1小时自动清除）
        const expireTime = Date.now() - (60 * 60 * 1000)
        if (cartData.timestamp && cartData.timestamp > expireTime) {
          cartItems.value = cartData.cartItems || []
          totalAmount.value = cartData.totalAmount || 0
          discountAmount.value = cartData.discountAmount || 0
          finalAmount.value = cartData.finalAmount || 0
          currentMember.value = cartData.currentMember || null
          memberDiscountRate.value = cartData.memberDiscountRate || 0
          pointsDeductionAmount.value = cartData.pointsDeductionAmount || 0

          if (cartItems.value.length > 0) {
            message.info('已恢复上次未完成的订单')
          }
        } else {
          // 数据过期，清除本地存储
          localStorage.removeItem('pos_current_cart')
        }
      }
    } catch (error) {
      console.error('从本地存储恢复购物车失败:', error)
      localStorage.removeItem('pos_current_cart')
    }
  }

  /**
   * 清除本地存储的购物车数据
   */
  const clearCartFromLocal = () => {
    try {
      localStorage.removeItem('pos_current_cart')
    } catch (error) {
      console.error('清除本地购物车数据失败:', error)
    }
  }

  // ==================== 商品和分类管理方法 ====================

  /**
   * 设置商品分类列表
   * @param {Array} categoryList 分类列表
   */
  const setCategories = (categoryList) => {
    categories.value = categoryList || []
  }

  /**
   * 设置当前选中的分类
   * @param {Object} category 分类对象
   */
  const setSelectedCategory = (category) => {
    selectedCategory.value = category
  }

  /**
   * 设置商品列表
   * @param {Array} productList 商品列表
   */
  const setProducts = (productList) => {
    products.value = productList || []
  }

  /**
   * 设置搜索关键词
   * @param {string} keyword 搜索关键词
   */
  const setSearchKeyword = (keyword) => {
    searchKeyword.value = keyword || ''
  }

  // ==================== 工具方法 ====================

  /**
   * 格式化金额显示
   * @param {number} amount 金额
   * @returns {string} 格式化后的金额字符串
   */
  const formatAmount = (amount) => {
    if (typeof amount !== 'number') {
      return '0.00'
    }
    return amount.toFixed(2)
  }

  /**
   * 获取购物车摘要信息
   * @returns {Object} 购物车摘要
   */
  const getCartSummary = () => {
    return {
      itemCount: cartItemCount.value,
      totalAmount: formatAmount(totalAmount.value),
      discountAmount: formatAmount(discountAmount.value),
      finalAmount: formatAmount(finalAmount.value),
      hasItems: hasCartItems.value,
      canCheckout: canCheckout.value
    }
  }

  /**
   * 重置所有状态到初始状态
   */
  const resetAllState = () => {
    try {
      // 清空购物车
      clearCart()

      // 清除会员信息
      clearCurrentMember()

      // 重置支付状态
      resetPaymentStatus()

      // 清空商品和分类
      categories.value = []
      selectedCategory.value = null
      products.value = []
      searchKeyword.value = ''

      // 清除本地存储
      clearCartFromLocal()

      message.success('状态已重置')
    } catch (error) {
      console.error('重置状态失败:', error)
    }
  }

  // ==================== 初始化方法 ====================

  /**
   * 初始化store（在应用启动时调用）
   */
  const initializeStore = () => {
    try {
      // 加载挂单数据
      loadSuspendedOrdersFromLocal()

      // 加载购物车数据
      loadCartFromLocal()

      console.log('POS Store 初始化完成')
    } catch (error) {
      console.error('POS Store 初始化失败:', error)
    }
  }

  // ==================== 返回store接口 ====================

  return {
    // 状态
    cartItems,
    totalAmount,
    discountAmount,
    finalAmount,
    currentMember,
    memberDiscountRate,
    pointsDeductionAmount,
    pointsExchangeRate,
    paymentStatus,
    selectedPaymentMethod,
    receivedAmount,
    changeAmount,
    suspendedOrders,
    categories,
    selectedCategory,
    products,
    searchKeyword,

    // 计算属性
    cartItemCount,
    hasCartItems,
    canCheckout,
    hasMember,
    hasSuspendedOrders,

    // 购物车方法
    addToCart,
    addCartItem: addToCart, // 为了兼容useCart中的调用
    removeFromCart,
    removeCartItem: removeFromCart, // 为了兼容useCart中的调用
    updateQuantity,
    updateCartItem: (itemId, updatedItem) => {
      const index = cartItems.value.findIndex(item => item.productId === itemId || item.id === itemId)
      if (index !== -1) {
        cartItems.value[index] = { ...cartItems.value[index], ...updatedItem }
        calculateTotal()
      }
    },
    clearCart,
    calculateTotal,

    // 会员管理方法
    setCurrentMember,
    clearCurrentMember,
    setPointsDeduction,
    applyMemberDiscount,
    clearMemberDiscount,

    // 支付处理方法
    setPaymentMethod,
    setReceivedAmount,
    startPayment,
    paymentSuccess,
    paymentFailed,
    resetPaymentStatus,
    completeOrder,

    // 挂单管理方法
    suspendCurrentOrder,
    resumeSuspendedOrder,
    deleteSuspendedOrder,
    clearExpiredSuspendedOrders,

    // 本地存储方法
    saveCartToLocal,
    loadCartFromLocal,
    clearCartFromLocal,

    // 商品和分类方法
    setCategories,
    setSelectedCategory,
    setProducts,
    setSearchKeyword,

    // 工具方法
    formatAmount,
    getCartSummary,
    resetAllState,
    initializeStore
  }
})