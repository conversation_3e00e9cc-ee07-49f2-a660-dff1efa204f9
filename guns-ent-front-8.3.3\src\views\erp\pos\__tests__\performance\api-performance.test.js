/**
 * API性能测试
 * 
 * 测试API调用的性能和响应时间
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { performance } from 'perf_hooks'
import { CartApi } from '../../api/cart'
import { MemberApi } from '../../api/member'
import { PaymentApi } from '../../api/payment'
import { OrderApi } from '../../api/order'
import { ProductApi } from '../../api/product'

// Mock Request with performance tracking
const createMockRequest = (responseTime = 100) => ({
  get: vi.fn().mockImplementation(async (url, params) => {
    await new Promise(resolve => setTimeout(resolve, responseTime))
    return { success: true, data: 'mock data', url, params }
  }),
  post: vi.fn().mockImplementation(async (url, data) => {
    await new Promise(resolve => setTimeout(resolve, responseTime))
    return { success: true, data: 'mock data', url, requestData: data }
  }),
  put: vi.fn().mockImplementation(async (url, data) => {
    await new Promise(resolve => setTimeout(resolve, responseTime))
    return { success: true, data: 'mock data', url, requestData: data }
  }),
  delete: vi.fn().mockImplementation(async (url) => {
    await new Promise(resolve => setTimeout(resolve, responseTime))
    return { success: true, data: 'mock data', url }
  })
})

vi.mock('@/utils/request/request-util', () => ({
  default: createMockRequest(50) // 默认50ms响应时间
}))

describe('API性能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('单个API调用性能', () => {
    it('商品搜索API响应时间应该在可接受范围内', async () => {
      const startTime = performance.now()
      
      await ProductApi.searchProducts({ keyword: '苹果' })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // API响应时间应该小于200ms
      expect(responseTime).toBeLessThan(200)
    })
    
    it('库存检查API响应时间测试', async () => {
      const startTime = performance.now()
      
      await CartApi.checkInventory('P001', 5)
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 库存检查应该很快
      expect(responseTime).toBeLessThan(100)
    })
    
    it('会员搜索API响应时间测试', async () => {
      const startTime = performance.now()
      
      await MemberApi.searchMember({ cardNo: 'VIP123456' })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 会员搜索应该快速响应
      expect(responseTime).toBeLessThan(150)
    })
    
    it('支付处理API响应时间测试', async () => {
      const startTime = performance.now()
      
      await PaymentApi.processCashPayment({
        orderId: 'ORDER001',
        paymentAmount: 100,
        receivedAmount: 120
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 支付处理应该在合理时间内完成
      expect(responseTime).toBeLessThan(300)
    })
    
    it('订单创建API响应时间测试', async () => {
      const startTime = performance.now()
      
      await OrderApi.createOrder({
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2 }
        ],
        totalAmount: 11,
        finalAmount: 11
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 订单创建应该快速完成
      expect(responseTime).toBeLessThan(200)
    })
  })
  
  describe('批量API调用性能', () => {
    it('批量库存检查性能测试', async () => {
      const items = Array.from({ length: 50 }, (_, i) => ({
        productId: `P${i.toString().padStart(3, '0')}`,
        quantity: Math.floor(Math.random() * 10) + 1
      }))
      
      const startTime = performance.now()
      
      await CartApi.batchCheckInventory(items)
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 批量检查50个商品的库存应该在合理时间内
      expect(responseTime).toBeLessThan(500)
    })
    
    it('批量商品信息获取性能测试', async () => {
      const productIds = Array.from({ length: 100 }, (_, i) => 
        `P${i.toString().padStart(3, '0')}`
      )
      
      const startTime = performance.now()
      
      await ProductApi.getProductsBatch(productIds)
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 批量获取100个商品信息应该在合理时间内
      expect(responseTime).toBeLessThan(800)
    })
    
    it('批量订单更新性能测试', async () => {
      const orderIds = Array.from({ length: 20 }, (_, i) => 
        `ORDER${i.toString().padStart(3, '0')}`
      )
      
      const startTime = performance.now()
      
      await OrderApi.batchUpdateOrders({
        orderIds,
        updates: { status: 'COMPLETED' }
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      // 批量更新20个订单应该快速完成
      expect(responseTime).toBeLessThan(400)
    })
  })
  
  describe('并发API调用性能', () => {
    it('并发商品搜索性能测试', async () => {
      const searchTerms = ['苹果', '香蕉', '橙子', '葡萄', '西瓜']
      
      const startTime = performance.now()
      
      const promises = searchTerms.map(term => 
        ProductApi.searchProducts({ keyword: term })
      )
      
      await Promise.all(promises)
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      
      // 并发搜索5个关键词应该比串行快
      expect(totalTime).toBeLessThan(300) // 如果串行需要5*50=250ms，并发应该更快
    })
    
    it('并发库存检查性能测试', async () => {
      const products = Array.from({ length: 10 }, (_, i) => ({
        productId: `P${i}`,
        quantity: 5
      }))
      
      const startTime = performance.now()
      
      const promises = products.map(({ productId, quantity }) => 
        CartApi.checkInventory(productId, quantity)
      )
      
      await Promise.all(promises)
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      
      // 并发检查10个商品库存
      expect(totalTime).toBeLessThan(200)
    })
    
    it('混合API并发调用性能测试', async () => {
      const startTime = performance.now()
      
      const promises = [
        ProductApi.searchProducts({ keyword: '测试' }),
        MemberApi.searchMember({ cardNo: 'VIP123456' }),
        CartApi.checkInventory('P001', 5),
        OrderApi.getOrderStatistics({ startTime: '2025-01-01' })
      ]
      
      await Promise.all(promises)
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      
      // 并发调用不同类型的API
      expect(totalTime).toBeLessThan(250)
    })
  })
  
  describe('API缓存性能测试', () => {
    it('重复API调用应该利用缓存提高性能', async () => {
      const searchParams = { keyword: '苹果' }
      
      // 第一次调用
      const firstCallStart = performance.now()
      await ProductApi.searchProducts(searchParams)
      const firstCallEnd = performance.now()
      const firstCallTime = firstCallEnd - firstCallStart
      
      // 第二次调用相同参数（应该使用缓存）
      const secondCallStart = performance.now()
      await ProductApi.searchProducts(searchParams)
      const secondCallEnd = performance.now()
      const secondCallTime = secondCallEnd - secondCallStart
      
      // 如果有缓存，第二次调用应该更快
      // 注意：这里假设API层实现了缓存机制
      expect(secondCallTime).toBeLessThanOrEqual(firstCallTime)
    })
    
    it('商品详情缓存性能测试', async () => {
      const productId = 'P001'
      
      // 第一次获取商品详情
      const firstCallStart = performance.now()
      await ProductApi.getProductDetail(productId)
      const firstCallEnd = performance.now()
      const firstCallTime = firstCallEnd - firstCallStart
      
      // 第二次获取相同商品详情
      const secondCallStart = performance.now()
      await ProductApi.getProductDetail(productId)
      const secondCallEnd = performance.now()
      const secondCallTime = secondCallEnd - secondCallStart
      
      // 缓存应该提高性能
      expect(secondCallTime).toBeLessThanOrEqual(firstCallTime)
    })
  })
  
  describe('API错误处理性能', () => {
    it('API错误处理不应该显著影响性能', async () => {
      // Mock一个会失败的请求
      const mockRequest = createMockRequest(50)
      mockRequest.get.mockRejectedValueOnce(new Error('Network Error'))
      
      vi.doMock('@/utils/request/request-util', () => ({
        default: mockRequest
      }))
      
      const startTime = performance.now()
      
      try {
        await ProductApi.searchProducts({ keyword: '测试' })
      } catch (error) {
        // 预期会出错
      }
      
      const endTime = performance.now()
      const errorHandlingTime = endTime - startTime
      
      // 错误处理时间应该在合理范围内
      expect(errorHandlingTime).toBeLessThan(200)
    })
    
    it('重试机制性能测试', async () => {
      let callCount = 0
      const mockRequest = createMockRequest(30)
      
      // 前两次调用失败，第三次成功
      mockRequest.post.mockImplementation(async () => {
        callCount++
        if (callCount < 3) {
          throw new Error('Temporary Error')
        }
        await new Promise(resolve => setTimeout(resolve, 30))
        return { success: true, data: 'success' }
      })
      
      vi.doMock('@/utils/request/request-util', () => ({
        default: mockRequest
      }))
      
      const startTime = performance.now()
      
      // 假设API有重试机制
      try {
        await CartApi.checkInventory('P001', 5)
      } catch (error) {
        // 可能仍然失败
      }
      
      const endTime = performance.now()
      const retryTime = endTime - startTime
      
      // 重试机制不应该导致过长的等待时间
      expect(retryTime).toBeLessThan(500)
      expect(callCount).toBeGreaterThan(1) // 确实进行了重试
    })
  })
  
  describe('API响应数据处理性能', () => {
    it('大量数据响应处理性能', async () => {
      // Mock返回大量数据的API
      const largeDataResponse = {
        products: Array.from({ length: 1000 }, (_, i) => ({
          id: `P${i}`,
          name: `商品${i}`,
          price: Math.random() * 100,
          description: `这是商品${i}的详细描述`.repeat(10)
        })),
        total: 1000
      }
      
      const mockRequest = createMockRequest(100)
      mockRequest.get.mockResolvedValueOnce(largeDataResponse)
      
      vi.doMock('@/utils/request/request-util', () => ({
        default: mockRequest
      }))
      
      const startTime = performance.now()
      
      const result = await ProductApi.getProductList({ limit: 1000 })
      
      const endTime = performance.now()
      const processingTime = endTime - startTime
      
      // 处理1000个商品的数据应该在合理时间内
      expect(processingTime).toBeLessThan(300)
      expect(result.products).toHaveLength(1000)
    })
    
    it('复杂数据结构处理性能', async () => {
      const complexOrderData = {
        orders: Array.from({ length: 100 }, (_, i) => ({
          id: `ORDER${i}`,
          items: Array.from({ length: 10 }, (_, j) => ({
            id: `P${j}`,
            name: `商品${j}`,
            price: Math.random() * 50,
            quantity: Math.floor(Math.random() * 5) + 1,
            specifications: {
              color: '红色',
              size: 'L',
              material: '棉质',
              origin: '中国'
            }
          })),
          member: {
            id: `M${i}`,
            name: `会员${i}`,
            level: 'GOLD',
            discountHistory: Array.from({ length: 5 }, (_, k) => ({
              date: `2025-01-${k + 1}`,
              amount: Math.random() * 100
            }))
          },
          payments: Array.from({ length: 2 }, (_, p) => ({
            method: p === 0 ? 'CASH' : 'WECHAT',
            amount: Math.random() * 200,
            timestamp: Date.now()
          }))
        }))
      }
      
      const mockRequest = createMockRequest(150)
      mockRequest.get.mockResolvedValueOnce(complexOrderData)
      
      vi.doMock('@/utils/request/request-util', () => ({
        default: mockRequest
      }))
      
      const startTime = performance.now()
      
      const result = await OrderApi.getOrderList({ limit: 100 })
      
      const endTime = performance.now()
      const processingTime = endTime - startTime
      
      // 处理复杂数据结构应该在合理时间内
      expect(processingTime).toBeLessThan(400)
      expect(result.orders).toHaveLength(100)
    })
  })
  
  describe('API性能监控', () => {
    it('应该能够监控API调用的性能指标', async () => {
      const performanceMetrics = []
      
      // Mock性能监控
      const originalFetch = global.fetch
      global.fetch = vi.fn().mockImplementation(async (url, options) => {
        const startTime = performance.now()
        
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 80))
        
        const endTime = performance.now()
        
        performanceMetrics.push({
          url,
          method: options?.method || 'GET',
          duration: endTime - startTime,
          timestamp: Date.now()
        })
        
        return {
          ok: true,
          json: async () => ({ success: true, data: 'test' })
        }
      })
      
      // 执行多个API调用
      await Promise.all([
        ProductApi.searchProducts({ keyword: '测试1' }),
        ProductApi.searchProducts({ keyword: '测试2' }),
        CartApi.checkInventory('P001', 5),
        MemberApi.searchMember({ cardNo: 'VIP123' })
      ])
      
      // 恢复原始fetch
      global.fetch = originalFetch
      
      // 验证性能指标收集
      expect(performanceMetrics.length).toBeGreaterThan(0)
      
      // 计算平均响应时间
      const averageResponseTime = performanceMetrics.reduce(
        (sum, metric) => sum + metric.duration, 0
      ) / performanceMetrics.length
      
      expect(averageResponseTime).toBeLessThan(200)
      
      // 检查是否有异常慢的请求
      const slowRequests = performanceMetrics.filter(
        metric => metric.duration > 300
      )
      
      expect(slowRequests.length).toBe(0)
    })
  })
  
  describe('API性能基准测试', () => {
    it('API性能应该满足基准要求', async () => {
      const benchmarks = {
        productSearch: 150,      // ms
        inventoryCheck: 100,     // ms
        memberSearch: 120,       // ms
        orderCreation: 200,      // ms
        paymentProcess: 300      // ms
      }
      
      const results = {}
      
      // 商品搜索基准测试
      let startTime = performance.now()
      await ProductApi.searchProducts({ keyword: '基准测试' })
      results.productSearch = performance.now() - startTime
      
      // 库存检查基准测试
      startTime = performance.now()
      await CartApi.checkInventory('P001', 5)
      results.inventoryCheck = performance.now() - startTime
      
      // 会员搜索基准测试
      startTime = performance.now()
      await MemberApi.searchMember({ cardNo: 'VIP123456' })
      results.memberSearch = performance.now() - startTime
      
      // 订单创建基准测试
      startTime = performance.now()
      await OrderApi.createOrder({
        items: [{ id: 'P001', name: '测试商品', price: 10, quantity: 1 }],
        totalAmount: 10,
        finalAmount: 10
      })
      results.orderCreation = performance.now() - startTime
      
      // 支付处理基准测试
      startTime = performance.now()
      await PaymentApi.processCashPayment({
        orderId: 'ORDER001',
        paymentAmount: 10,
        receivedAmount: 10
      })
      results.paymentProcess = performance.now() - startTime
      
      // 验证所有API都满足基准要求
      Object.entries(benchmarks).forEach(([api, benchmark]) => {
        expect(results[api]).toBeLessThan(benchmark)
      })
      
      // 输出性能报告
      console.log('API性能基准测试结果:')
      Object.entries(results).forEach(([api, time]) => {
        const benchmark = benchmarks[api]
        const performance = ((benchmark - time) / benchmark * 100).toFixed(1)
        console.log(`${api}: ${time.toFixed(1)}ms (基准: ${benchmark}ms, 性能: +${performance}%)`)
      })
    })
  })
})