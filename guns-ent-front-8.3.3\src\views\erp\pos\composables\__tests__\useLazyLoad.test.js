import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useLazyLoad } from '../useLazyLoad'

// Mock LazyLoader
vi.mock('../../utils/lazy-loader', () => ({
  LazyLoader: {
    lazyModule: vi.fn(),
    getLoadStats: vi.fn(() => ({
      total: 10,
      success: 8,
      failed: 2,
      cached: 5,
      successRate: '80%'
    })),
    getCacheInfo: vi.fn(() => ({
      cached: 5,
      loading: 1,
      cacheKeys: ['test1', 'test2'],
      loadingKeys: ['test3']
    }))
  }
}))

// Mock lazy-config
vi.mock('../../config/lazy-config', () => ({
  getLazyComponent: vi.fn((category, name) => {
    if (category === 'cart' && name === 'ShoppingCart') {
      return { name: 'LazyShoppingCart' }
    }
    return null
  }),
  getLazyModule: vi.fn((category, name) => {
    if (category === 'api' && name === 'cart') {
      return () => Promise.resolve({ cartApi: 'mocked' })
    }
    if (category === 'api' && name === 'payment') {
      return () => Promise.reject(new Error('Load failed'))
    }
    return null
  }),
  preloadByPriority: vi.fn((priority) => {
    return Promise.resolve([
      { resource: 'test1', status: 'success' },
      { resource: 'test2', status: 'success' }
    ])
  }),
  conditionalLoad: vi.fn((condition, value) => {
    return Promise.resolve([
      { resource: 'conditional1', status: 'success' }
    ])
  })
}))

// Mock Vue
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    nextTick: vi.fn(fn => Promise.resolve().then(fn))
  }
})

describe('useLazyLoad', () => {
  let lazyLoad
  
  beforeEach(() => {
    vi.clearAllMocks()
    lazyLoad = useLazyLoad()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('基础功能', () => {
    it('应该返回所有必要的状态和方法', () => {
      expect(lazyLoad.loadingStates).toBeDefined()
      expect(lazyLoad.loadedModules).toBeDefined()
      expect(lazyLoad.loadErrors).toBeDefined()
      expect(lazyLoad.config).toBeDefined()
      expect(lazyLoad.isLoading).toBeDefined()
      expect(lazyLoad.hasErrors).toBeDefined()
      expect(lazyLoad.loadStats).toBeDefined()
      
      expect(lazyLoad.loadComponent).toBeDefined()
      expect(lazyLoad.loadModule).toBeDefined()
      expect(lazyLoad.loadModules).toBeDefined()
      expect(lazyLoad.preload).toBeDefined()
      expect(lazyLoad.setupIntelligentPreload).toBeDefined()
      expect(lazyLoad.conditionalLoadModules).toBeDefined()
      expect(lazyLoad.retryFailedLoads).toBeDefined()
      expect(lazyLoad.clearLoadState).toBeDefined()
      expect(lazyLoad.getLoadState).toBeDefined()
      expect(lazyLoad.updateConfig).toBeDefined()
      expect(lazyLoad.getLoadStatistics).toBeDefined()
    })

    it('应该正确初始化状态', () => {
      expect(lazyLoad.loadingStates.value.size).toBe(0)
      expect(lazyLoad.loadedModules.value.size).toBe(0)
      expect(lazyLoad.loadErrors.value.size).toBe(0)
      expect(lazyLoad.isLoading.value).toBe(false)
      expect(lazyLoad.hasErrors.value).toBe(false)
    })

    it('应该正确初始化配置', () => {
      expect(lazyLoad.config.value.enablePreload).toBe(true)
      expect(lazyLoad.config.value.enableIntelligentLoad).toBe(true)
      expect(lazyLoad.config.value.retryCount).toBe(3)
      expect(lazyLoad.config.value.timeout).toBe(10000)
    })
  })

  describe('组件加载', () => {
    it('应该能够加载组件', () => {
      const component = lazyLoad.loadComponent('cart', 'ShoppingCart')
      
      expect(component).toEqual({ name: 'LazyShoppingCart' })
      expect(lazyLoad.loadingStates.value.has('cart.ShoppingCart')).toBe(true)
    })

    it('应该处理不存在的组件', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      const component = lazyLoad.loadComponent('nonexistent', 'Component')
      
      expect(component).toBe(null)
      expect(consoleSpy).toHaveBeenCalledWith('组件 nonexistent.Component 未找到')
      
      consoleSpy.mockRestore()
    })
  })

  describe('模块加载', () => {
    it('应该能够成功加载模块', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockResolvedValue({ cartApi: 'mocked' })
      
      const module = await lazyLoad.loadModule('api', 'cart')
      
      expect(module).toEqual({ cartApi: 'mocked' })
      expect(lazyLoad.loadedModules.value.has('api.cart')).toBe(true)
      expect(lazyLoad.loadingStates.value.get('api.cart')).toBe(false)
    })

    it('应该处理模块加载失败', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockRejectedValue(new Error('Load failed'))
      
      await expect(lazyLoad.loadModule('api', 'payment')).rejects.toThrow('Load failed')
      
      expect(lazyLoad.loadErrors.value.has('api.payment')).toBe(true)
      expect(lazyLoad.hasErrors.value).toBe(true)
    })

    it('应该处理不存在的模块', async () => {
      await expect(lazyLoad.loadModule('nonexistent', 'module'))
        .rejects.toThrow('模块 nonexistent.module 未找到')
      
      expect(lazyLoad.loadErrors.value.has('nonexistent.module')).toBe(true)
    })

    it('应该避免重复加载同一模块', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockResolvedValue({ cartApi: 'mocked' })
      
      // 第一次加载
      const module1 = await lazyLoad.loadModule('api', 'cart')
      
      // 第二次加载应该返回缓存的结果
      const module2 = await lazyLoad.loadModule('api', 'cart')
      
      expect(module1).toBe(module2)
      expect(LazyLoader.lazyModule).toHaveBeenCalledTimes(1)
    })
  })

  describe('批量加载', () => {
    it('应该能够批量加载模块', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule
        .mockResolvedValueOnce({ cartApi: 'mocked' })
        .mockResolvedValueOnce({ productApi: 'mocked' })
      
      const modules = [
        { category: 'api', name: 'cart' },
        { category: 'api', name: 'product' }
      ]
      
      const results = await lazyLoad.loadModules(modules)
      
      expect(results).toHaveLength(2)
      expect(results[0].status).toBe('success')
      expect(results[1].status).toBe('success')
    })

    it('应该处理批量加载中的失败', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule
        .mockResolvedValueOnce({ cartApi: 'mocked' })
        .mockRejectedValueOnce(new Error('Load failed'))
      
      const modules = [
        { category: 'api', name: 'cart' },
        { category: 'api', name: 'payment' }
      ]
      
      const results = await lazyLoad.loadModules(modules)
      
      expect(results).toHaveLength(2)
      expect(results[0].status).toBe('success')
      expect(results[1].status).toBe('failed')
    })

    it('应该支持进度回调', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockResolvedValue({ api: 'mocked' })
      
      const onProgress = vi.fn()
      const modules = [
        { category: 'api', name: 'cart' },
        { category: 'api', name: 'product' }
      ]
      
      await lazyLoad.loadModules(modules, { onProgress })
      
      expect(onProgress).toHaveBeenCalledWith(1, 2)
      expect(onProgress).toHaveBeenCalledWith(2, 2)
    })
  })

  describe('预加载', () => {
    it('应该能够预加载指定优先级的资源', async () => {
      const result = await lazyLoad.preload('high')
      
      expect(result).toHaveLength(2)
      expect(result[0].status).toBe('success')
    })

    it('应该在禁用预加载时跳过', async () => {
      lazyLoad.updateConfig({ enablePreload: false })
      
      const result = await lazyLoad.preload('high')
      
      expect(result).toBeUndefined()
    })
  })

  describe('条件加载', () => {
    it('应该能够条件加载资源', async () => {
      const result = await lazyLoad.conditionalLoadModules('device', 'mobile')
      
      expect(result).toHaveLength(1)
      expect(result[0].status).toBe('success')
    })
  })

  describe('重试机制', () => {
    it('应该能够重试失败的加载', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      
      // 先让一个模块加载失败
      LazyLoader.lazyModule.mockRejectedValueOnce(new Error('Load failed'))
      await expect(lazyLoad.loadModule('api', 'payment')).rejects.toThrow()
      
      // 然后重试成功
      LazyLoader.lazyModule.mockResolvedValueOnce({ paymentApi: 'mocked' })
      const result = await lazyLoad.retryFailedLoads('api.payment')
      
      expect(result.successful).toBe(1)
      expect(result.failed).toBe(0)
    })

    it('应该能够重试所有失败的加载', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      
      // 让两个模块加载失败
      LazyLoader.lazyModule.mockRejectedValue(new Error('Load failed'))
      await expect(lazyLoad.loadModule('api', 'payment')).rejects.toThrow()
      await expect(lazyLoad.loadModule('api', 'member')).rejects.toThrow()
      
      // 重试时一个成功一个失败
      LazyLoader.lazyModule
        .mockResolvedValueOnce({ paymentApi: 'mocked' })
        .mockRejectedValueOnce(new Error('Still failed'))
      
      const result = await lazyLoad.retryFailedLoads()
      
      expect(result.successful).toBe(1)
      expect(result.failed).toBe(1)
    })
  })

  describe('状态管理', () => {
    it('应该能够获取模块加载状态', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockResolvedValue({ cartApi: 'mocked' })
      
      // 加载前
      let state = lazyLoad.getLoadState('api', 'cart')
      expect(state.loaded).toBe(false)
      expect(state.loading).toBe(false)
      
      // 加载后
      await lazyLoad.loadModule('api', 'cart')
      state = lazyLoad.getLoadState('api', 'cart')
      expect(state.loaded).toBe(true)
      expect(state.loading).toBe(false)
      expect(state.module).toEqual({ cartApi: 'mocked' })
    })

    it('应该能够清除加载状态', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockResolvedValue({ cartApi: 'mocked' })
      
      await lazyLoad.loadModule('api', 'cart')
      expect(lazyLoad.loadedModules.value.size).toBe(1)
      
      lazyLoad.clearLoadState('api.cart')
      expect(lazyLoad.loadedModules.value.size).toBe(0)
    })

    it('应该能够清除所有加载状态', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule.mockResolvedValue({ api: 'mocked' })
      
      await lazyLoad.loadModule('api', 'cart')
      await lazyLoad.loadModule('api', 'product')
      expect(lazyLoad.loadedModules.value.size).toBe(2)
      
      lazyLoad.clearLoadState()
      expect(lazyLoad.loadedModules.value.size).toBe(0)
    })
  })

  describe('配置管理', () => {
    it('应该能够更新配置', () => {
      const newConfig = {
        enablePreload: false,
        retryCount: 5
      }
      
      lazyLoad.updateConfig(newConfig)
      
      expect(lazyLoad.config.value.enablePreload).toBe(false)
      expect(lazyLoad.config.value.retryCount).toBe(5)
      expect(lazyLoad.config.value.timeout).toBe(10000) // 保持原值
    })
  })

  describe('统计信息', () => {
    it('应该能够获取加载统计信息', () => {
      const stats = lazyLoad.getLoadStatistics()
      
      expect(stats.loading).toBeDefined()
      expect(stats.loaded).toBeDefined()
      expect(stats.errors).toBeDefined()
      expect(stats.total).toBeDefined()
      expect(stats.global).toBeDefined()
      expect(stats.cacheInfo).toBeDefined()
    })

    it('应该正确计算加载统计', async () => {
      const { LazyLoader } = await import('../../utils/lazy-loader')
      LazyLoader.lazyModule
        .mockResolvedValueOnce({ cartApi: 'mocked' })
        .mockRejectedValueOnce(new Error('Load failed'))
      
      await lazyLoad.loadModule('api', 'cart')
      await expect(lazyLoad.loadModule('api', 'payment')).rejects.toThrow()
      
      const stats = lazyLoad.loadStats.value
      expect(stats.loaded).toBe(1)
      expect(stats.errors).toBe(1)
      expect(stats.total).toBe(2)
    })
  })
})