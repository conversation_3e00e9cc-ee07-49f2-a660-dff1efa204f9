/**
 * 购物车相关API接口
 * 
 * 提供购物车商品管理、库存检查、状态保存等功能的API封装
 * 从原始pos.js中抽离出购物车相关的API接口
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import Request from '@/utils/request/request-util'
import { PosErrorHandler } from '../utils/error-handler'
import { PerformanceMonitor } from '../utils/performance-monitor'

/**
 * 购物车API类
 */
export class CartApi {
  
  /**
   * 创建带错误处理和性能监控的API包装器
   * @param {Function} apiFunction - 原始API函数
   * @param {Object} options - 配置选项
   * @returns {Function} 包装后的API函数
   */
  static createApiWrapper(apiFunction, options = {}) {
    const {
      context = 'Cart API调用',
      showMessage = true,
      showNotification = false,
      retryOptions = { maxRetries: 2, retryDelay: 1000 }
    } = options
    
    // 包装性能监控
    const monitoredFunction = PerformanceMonitor.measureApiCall(context, apiFunction)
    
    // 包装错误处理
    return PosErrorHandler.wrapApiCall(monitoredFunction, {
      showMessage,
      showNotification,
      context,
      retryOptions
    })
  }
  
  // 库存检查方法已移除，直接使用商品本身的库存信息进行检查
  
  /**
   * 获取商品详细信息
   * @param {Object} params - 查询参数
   * @param {number} params.productId - 商品ID
   * @returns {Promise<Object>} 商品信息
   */
  static async getProductDetail(params) {
    const apiCall = () => Request.get('/erp/pos/product/detail', params)
    
    return this.createApiWrapper(apiCall, {
      context: '获取商品详情',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 根据条形码获取商品信息
   * @param {string} barcode - 商品条形码
   * @returns {Promise<Object>} 商品信息
   */
  static async getProductByBarcode(barcode) {
    const apiCall = () => Request.get('/erp/pos/product/barcode', { barcode })
    
    return this.createApiWrapper(apiCall, {
      context: '根据条形码获取商品',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 500 }
    })()
  }
  
  /**
   * 批量获取商品信息
   * @param {Array<string>} productIds - 商品ID列表
   * @returns {Promise<Array>} 商品信息列表
   */
  static async getProductsBatch(productIds) {
    const apiCall = () => Request.post('/erp/pos/product/batch', {
      productIds
    })
    
    return this.createApiWrapper(apiCall, {
      context: '批量获取商品信息',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 搜索商品（用于购物车添加商品）
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {boolean} params.onlyInStock - 是否只显示有库存的商品
   * @param {number} params.limit - 返回结果数量限制
   * @returns {Promise<Array>} 商品列表
   */
  static async searchProducts(params = {}) {
    const apiCall = () => Request.get('/erp/pos/products/search', {
      ...params,
      limit: params.limit || 20
    })
    
    return this.createApiWrapper(apiCall, {
      context: '搜索商品',
      showMessage: false,
      retryOptions: { maxRetries: 1, retryDelay: 300 }
    })()
  }
  
  /**
   * 保存购物车状态（用于挂单）
   * @param {Object} cartData - 购物车数据
   * @param {Array} cartData.items - 购物车商品列表
   * @param {Object} cartData.member - 会员信息
   * @param {number} cartData.totalAmount - 总金额
   * @param {number} cartData.discountAmount - 折扣金额
   * @param {string} cartData.remark - 备注
   * @returns {Promise<Object>} 保存结果
   */
  static async saveCartState(cartData) {
    const apiCall = () => Request.post('/erp/pos/cart/suspend', {
      ...cartData,
      suspendTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '挂单保存',
      showMessage: true,
      showNotification: true,
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 恢复购物车状态
   * @param {string} suspendId - 挂单ID
   * @returns {Promise<Object>} 购物车数据
   */
  static async restoreCartState(suspendId) {
    const apiCall = () => Request.get(`/erp/pos/cart/restore/${suspendId}`)
    
    return this.createApiWrapper(apiCall, {
      context: '恢复挂单',
      showMessage: true,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 获取挂单列表
   * @param {Object} params - 查询参数
   * @param {number} params.cashierId - 收银员ID
   * @param {number} params.limit - 返回数量限制
   * @returns {Promise<Array>} 挂单列表
   */
  static async getSuspendedCarts(params = {}) {
    const apiCall = () => Request.get('/erp/pos/cart/suspended', {
      ...params,
      limit: params.limit || 50
    })
    
    return this.createApiWrapper(apiCall, {
      context: '获取挂单列表',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 500 }
    })()
  }
  
  /**
   * 删除挂单
   * @param {string} suspendId - 挂单ID
   * @returns {Promise<Object>} 删除结果
   */
  static async deleteSuspendedCart(suspendId) {
    const apiCall = () => Request.delete(`/erp/pos/cart/suspended/${suspendId}`)
    
    return this.createApiWrapper(apiCall, {
      context: '删除挂单',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 500 }
    })()
  }
  
  /**
   * 清空过期挂单
   * @param {number} expireHours - 过期时间（小时）
   * @returns {Promise<Object>} 清理结果
   */
  static async clearExpiredCarts(expireHours = 24) {
    const apiCall = () => Request.post('/erp/pos/cart/clearExpired', {
      expireHours
    })
    
    return this.createApiWrapper(apiCall, {
      context: '清理过期挂单',
      showMessage: false,
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 验证购物车数据
   * @param {Object} cartData - 购物车数据
   * @returns {Promise<Object>} 验证结果
   */
  static async validateCart(cartData) {
    const apiCall = () => Request.post('/erp/pos/cart/validate', cartData)
    
    return this.createApiWrapper(apiCall, {
      context: '验证购物车',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 500 }
    })()
  }
  
  /**
   * 计算购物车总金额（包含折扣）
   * @param {Object} cartData - 购物车数据
   * @param {Array} cartData.items - 商品列表
   * @param {Object} cartData.member - 会员信息
   * @param {Array} cartData.coupons - 优惠券列表
   * @returns {Promise<Object>} 计算结果
   */
  static async calculateCartTotal(cartData) {
    const apiCall = () => Request.post('/erp/pos/cart/calculate', cartData)
    
    return this.createApiWrapper(apiCall, {
      context: '计算购物车金额',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 300 }
    })()
  }
  
  /**
   * 应用优惠券到购物车
   * @param {Object} params - 参数
   * @param {Array} params.items - 购物车商品
   * @param {string} params.couponCode - 优惠券代码
   * @param {Object} params.member - 会员信息
   * @returns {Promise<Object>} 应用结果
   */
  static async applyCoupon(params) {
    const apiCall = () => Request.post('/erp/pos/cart/applyCoupon', params)
    
    return this.createApiWrapper(apiCall, {
      context: '应用优惠券',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 500 }
    })()
  }
  
  /**
   * 移除优惠券
   * @param {Object} params - 参数
   * @param {Array} params.items - 购物车商品
   * @param {string} params.couponId - 优惠券ID
   * @returns {Promise<Object>} 移除结果
   */
  static async removeCoupon(params) {
    const apiCall = () => Request.post('/erp/pos/cart/removeCoupon', params)
    
    return this.createApiWrapper(apiCall, {
      context: '移除优惠券',
      showMessage: true,
      retryOptions: { maxRetries: 1, retryDelay: 500 }
    })()
  }
}