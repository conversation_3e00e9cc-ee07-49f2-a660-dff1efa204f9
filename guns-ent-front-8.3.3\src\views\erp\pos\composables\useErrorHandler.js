/**
 * 错误处理组合式函数
 * 
 * 为组件提供统一的错误处理能力
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { EnhancedPosErrorHandler } from '../utils/enhanced-error-handler'
import { PosErrorTypes } from '../utils/error-types'

export function useErrorHandler(options = {}) {
  const {
    componentName = 'Unknown',
    enableErrorBoundary = true,
    enableErrorStats = true,
    autoRetry = true,
    retryOptions = { maxRetries: 2, retryDelay: 1000 }
  } = options
  
  // 错误状态
  const hasError = ref(false)
  const lastError = ref(null)
  const errorCount = ref(0)
  const isRecovering = ref(false)
  
  // 错误监听器
  let errorListener = null
  
  /**
   * 包装异步操作
   * @param {Function} asyncFunction - 异步函数
   * @param {Object} wrapOptions - 包装选项
   * @returns {Function} 包装后的函数
   */
  const wrapAsync = (asyncFunction, wrapOptions = {}) => {
    const {
      context = componentName,
      showMessage = true,
      showNotification = false,
      recoveryStrategy,
      fallbackValue,
      onError,
      onSuccess
    } = wrapOptions
    
    return EnhancedPosErrorHandler.wrapApiCall(asyncFunction, {
      context,
      showMessage,
      showNotification,
      retryOptions: autoRetry ? retryOptions : { maxRetries: 0 },
      recoveryStrategy,
      fallbackValue,
      onError: (error) => {
        // 更新组件错误状态
        hasError.value = true
        lastError.value = error
        errorCount.value++
        
        // 执行自定义错误处理
        if (typeof onError === 'function') {
          onError(error)
        }
      },
      onSuccess: (result) => {
        // 清除错误状态
        if (hasError.value) {
          hasError.value = false
          lastError.value = null
        }
        
        // 执行成功回调
        if (typeof onSuccess === 'function') {
          onSuccess(result)
        }
      }
    })
  }
  
  /**
   * 处理同步错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 处理选项
   */
  const handleError = (error, options = {}) => {
    const {
      context = componentName,
      showMessage = true,
      showNotification = false
    } = options
    
    const processedError = EnhancedPosErrorHandler.processError(error, context)
    
    // 更新组件错误状态
    hasError.value = true
    lastError.value = processedError
    errorCount.value++
    
    // 显示用户提示
    EnhancedPosErrorHandler.handleUserNotification(processedError, {
      showMessage,
      showNotification,
      context
    })
    
    // 记录错误
    EnhancedPosErrorHandler.logError(processedError, context)
  }
  
  /**
   * 清除错误状态
   */
  const clearError = () => {
    hasError.value = false
    lastError.value = null
  }
  
  /**
   * 重置错误计数
   */
  const resetErrorCount = () => {
    errorCount.value = 0
  }
  
  /**
   * 尝试错误恢复
   * @param {Function} recoveryFunction - 恢复函数
   */
  const tryRecover = async (recoveryFunction) => {
    if (!hasError.value || isRecovering.value) {
      return
    }
    
    try {
      isRecovering.value = true
      await recoveryFunction()
      clearError()
    } catch (recoveryError) {
      console.error('错误恢复失败:', recoveryError)
      handleError(recoveryError, { context: `${componentName} 错误恢复` })
    } finally {
      isRecovering.value = false
    }
  }
  
  /**
   * 创建错误边界
   * @param {Function} renderFunction - 渲染函数
   * @returns {Function} 带错误边界的渲染函数
   */
  const createErrorBoundary = (renderFunction) => {
    return (...args) => {
      try {
        return renderFunction(...args)
      } catch (error) {
        handleError(error, { context: `${componentName} 渲染错误` })
        
        // 返回错误状态的渲染结果
        return {
          error: true,
          message: '组件渲染出错，请刷新页面'
        }
      }
    }
  }
  
  /**
   * 获取错误信息摘要
   * @returns {Object} 错误摘要
   */
  const getErrorSummary = () => {
    return {
      hasError: hasError.value,
      errorCount: errorCount.value,
      lastErrorType: lastError.value?.type,
      lastErrorMessage: lastError.value?.message,
      lastErrorTime: lastError.value?.timestamp,
      isRecovering: isRecovering.value
    }
  }
  
  /**
   * 创建特定类型的错误处理器
   * @param {string} errorType - 错误类型
   * @returns {Function} 错误处理器
   */
  const createTypeSpecificHandler = (errorType) => {
    const handlers = {
      [PosErrorTypes.NETWORK_ERROR]: (error) => {
        // 网络错误的特殊处理
        return wrapAsync(async () => {
          // 可以尝试重新连接或使用缓存数据
          throw error
        }, {
          context: '网络请求',
          recoveryStrategy: 'useCache',
          showNotification: true
        })
      },
      
      [PosErrorTypes.INSUFFICIENT_INVENTORY]: (error) => {
        // 库存不足的特殊处理
        return wrapAsync(async () => {
          throw error
        }, {
          context: '库存检查',
          showMessage: true,
          fallbackValue: { available: 0 }
        })
      },
      
      [PosErrorTypes.PAYMENT_FAILED]: (error) => {
        // 支付失败的特殊处理
        return wrapAsync(async () => {
          throw error
        }, {
          context: '支付处理',
          showNotification: true,
          recoveryStrategy: async (error, args) => {
            // 可以尝试其他支付方式
            return null
          }
        })
      }
    }
    
    return handlers[errorType] || handleError
  }
  
  /**
   * 批量处理错误
   * @param {Array} errors - 错误数组
   * @param {Object} options - 处理选项
   */
  const handleBatchErrors = (errors, options = {}) => {
    const {
      showSummary = true,
      groupByType = true
    } = options
    
    if (!Array.isArray(errors) || errors.length === 0) {
      return
    }
    
    // 按类型分组错误
    const errorGroups = groupByType ? 
      errors.reduce((groups, error) => {
        const type = error.type || 'unknown'
        if (!groups[type]) {
          groups[type] = []
        }
        groups[type].push(error)
        return groups
      }, {}) : 
      { all: errors }
    
    // 处理每组错误
    Object.entries(errorGroups).forEach(([type, groupErrors]) => {
      if (groupErrors.length === 1) {
        handleError(groupErrors[0])
      } else if (showSummary) {
        // 显示批量错误摘要
        const message = `发现 ${groupErrors.length} 个${type}类型的错误`
        handleError(new Error(message), {
          context: `${componentName} 批量错误`,
          showNotification: true
        })
      }
    })
  }
  
  // 组件挂载时的初始化
  onMounted(() => {
    if (enableErrorStats) {
      // 添加错误监听器
      errorListener = (error, context) => {
        if (context.includes(componentName)) {
          // 这是当前组件的错误，更新统计
          errorCount.value++
        }
      }
      
      EnhancedPosErrorHandler.addErrorListener(errorListener)
    }
    
    if (enableErrorBoundary) {
      // 注册组件级错误边界
      const originalErrorHandler = window.onerror
      window.onerror = (message, source, lineno, colno, error) => {
        if (source && source.includes(componentName)) {
          handleError(error || new Error(message), {
            context: `${componentName} 全局错误`
          })
        }
        
        // 调用原始错误处理器
        if (originalErrorHandler) {
          return originalErrorHandler(message, source, lineno, colno, error)
        }
      }
    }
  })
  
  // 组件卸载时的清理
  onUnmounted(() => {
    if (errorListener) {
      EnhancedPosErrorHandler.removeErrorListener(errorListener)
    }
  })
  
  return {
    // 状态
    hasError,
    lastError,
    errorCount,
    isRecovering,
    
    // 方法
    wrapAsync,
    handleError,
    clearError,
    resetErrorCount,
    tryRecover,
    createErrorBoundary,
    getErrorSummary,
    createTypeSpecificHandler,
    handleBatchErrors
  }
}