import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { message } from 'ant-design-vue'
import MemberSearch from '../MemberSearch.vue'

// Mock MemberApi
const mockMemberApi = {
  getMemberByCardNo: vi.fn(),
  getMemberByPhone: vi.fn()
}

vi.mock('../api', () => ({
  MemberApi: mockMemberApi
}))

// Mock ant-design-vue message
vi.mock('ant-design-vue', async () => {
  const actual = await vi.importActual('ant-design-vue')
  return {
    ...actual,
    message: {
      warning: vi.fn()
    }
  }
})

describe('MemberSearch', () => {
  let wrapper

  const createWrapper = (props = {}) => {
    return mount(MemberSearch, {
      props,
      global: {
        stubs: {
          'icon-font': true,
          'a-tabs': {
            template: '<div><slot /></div>',
            props: ['activeKey']
          },
          'a-tab-pane': {
            template: '<div><slot /></div>',
            props: ['key', 'tab']
          },
          'a-input-search': {
            template: '<input @keyup.enter="$emit(\'pressEnter\')" @search="$emit(\'search\')" />',
            props: ['value', 'placeholder', 'size', 'loading', 'allowClear'],
            emits: ['update:value', 'search', 'pressEnter']
          },
          'a-button': {
            template: '<button><slot /></button>',
            props: ['type']
          },
          'a-empty': {
            template: '<div class="empty"><slot /></div>',
            props: ['description', 'image']
          },
          'a-alert': {
            template: '<div class="alert">{{ message }}</div>',
            props: ['message', 'type', 'showIcon', 'closable'],
            emits: ['close']
          }
        }
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('组件渲染', () => {
    it('应该正确渲染搜索组件', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.member-search').exists()).toBe(true)
      expect(wrapper.find('.search-methods').exists()).toBe(true)
    })

    it('应该在搜索失败时显示空状态', async () => {
      wrapper = createWrapper()
      
      // 设置搜索尝试状态
      wrapper.vm.searchAttempted = true
      wrapper.vm.searching = false
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.no-member-found').exists()).toBe(true)
    })

    it('应该在有错误时显示错误信息', async () => {
      wrapper = createWrapper()
      
      wrapper.vm.errorMessage = '搜索失败'
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.error-message').exists()).toBe(true)
      expect(wrapper.find('.alert').text()).toContain('搜索失败')
    })
  })

  describe('会员卡搜索', () => {
    it('应该在输入为空时显示警告', async () => {
      wrapper = createWrapper()
      
      wrapper.vm.memberCardNo = ''
      await wrapper.vm.handleCardSearch()
      
      expect(message.warning).toHaveBeenCalledWith('请输入会员卡号')
    })

    it('应该成功搜索会员卡', async () => {
      const testMember = {
        memberId: '1',
        memberName: '测试会员',
        cardNo: '123456'
      }
      
      mockMemberApi.getMemberByCardNo.mockResolvedValue(testMember)
      
      wrapper = createWrapper()
      wrapper.vm.memberCardNo = '123456'
      
      await wrapper.vm.handleCardSearch()
      
      expect(mockMemberApi.getMemberByCardNo).toHaveBeenCalledWith('123456')
      expect(wrapper.emitted('memberFound')).toBeTruthy()
      expect(wrapper.emitted('memberFound')[0][0]).toEqual(testMember)
    })

    it('应该处理搜索失败的情况', async () => {
      mockMemberApi.getMemberByCardNo.mockRejectedValue(new Error('网络错误'))
      
      wrapper = createWrapper()
      wrapper.vm.memberCardNo = '123456'
      
      await wrapper.vm.handleCardSearch()
      
      expect(wrapper.vm.errorMessage).toContain('搜索会员失败')
      expect(wrapper.emitted('searchError')).toBeTruthy()
    })

    it('应该在未找到会员时显示警告', async () => {
      mockMemberApi.getMemberByCardNo.mockResolvedValue(null)
      
      wrapper = createWrapper()
      wrapper.vm.memberCardNo = '123456'
      
      await wrapper.vm.handleCardSearch()
      
      expect(message.warning).toHaveBeenCalledWith('未找到会员信息')
    })
  })

  describe('手机号搜索', () => {
    it('应该在输入为空时显示警告', async () => {
      wrapper = createWrapper()
      
      wrapper.vm.memberPhone = ''
      await wrapper.vm.handlePhoneSearch()
      
      expect(message.warning).toHaveBeenCalledWith('请输入手机号')
    })

    it('应该成功搜索手机号', async () => {
      const testMember = {
        memberId: '1',
        memberName: '测试会员',
        phone: '13800138000'
      }
      
      mockMemberApi.getMemberByPhone.mockResolvedValue(testMember)
      
      wrapper = createWrapper()
      wrapper.vm.memberPhone = '13800138000'
      
      await wrapper.vm.handlePhoneSearch()
      
      expect(mockMemberApi.getMemberByPhone).toHaveBeenCalledWith('13800138000')
      expect(wrapper.emitted('memberFound')).toBeTruthy()
      expect(wrapper.emitted('memberFound')[0][0]).toEqual(testMember)
    })

    it('应该处理搜索失败的情况', async () => {
      mockMemberApi.getMemberByPhone.mockRejectedValue(new Error('网络错误'))
      
      wrapper = createWrapper()
      wrapper.vm.memberPhone = '13800138000'
      
      await wrapper.vm.handlePhoneSearch()
      
      expect(wrapper.vm.errorMessage).toContain('搜索会员失败')
      expect(wrapper.emitted('searchError')).toBeTruthy()
    })
  })

  describe('组件方法', () => {
    it('应该能够重置搜索状态', () => {
      wrapper = createWrapper()
      
      // 设置一些状态
      wrapper.vm.memberCardNo = '123456'
      wrapper.vm.memberPhone = '13800138000'
      wrapper.vm.searchAttempted = true
      wrapper.vm.errorMessage = '错误信息'
      wrapper.vm.searching = true
      
      wrapper.vm.resetSearch()
      
      expect(wrapper.vm.memberCardNo).toBe('')
      expect(wrapper.vm.memberPhone).toBe('')
      expect(wrapper.vm.searchAttempted).toBe(false)
      expect(wrapper.vm.errorMessage).toBe('')
      expect(wrapper.vm.searching).toBe(false)
    })

    it('应该能够清除错误信息', () => {
      wrapper = createWrapper()
      
      wrapper.vm.errorMessage = '错误信息'
      wrapper.vm.clearError()
      
      expect(wrapper.vm.errorMessage).toBe('')
    })
  })

  describe('搜索状态管理', () => {
    it('应该在搜索时设置loading状态', async () => {
      mockMemberApi.getMemberByCardNo.mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => resolve(null), 100)
        })
      })
      
      wrapper = createWrapper()
      wrapper.vm.memberCardNo = '123456'
      
      const searchPromise = wrapper.vm.handleCardSearch()
      
      expect(wrapper.vm.searching).toBe(true)
      
      await searchPromise
      
      expect(wrapper.vm.searching).toBe(false)
    })

    it('应该在搜索成功后清空输入', async () => {
      const testMember = {
        memberId: '1',
        memberName: '测试会员'
      }
      
      mockMemberApi.getMemberByCardNo.mockResolvedValue(testMember)
      
      wrapper = createWrapper()
      wrapper.vm.memberCardNo = '123456'
      
      await wrapper.vm.handleCardSearch()
      
      expect(wrapper.vm.memberCardNo).toBe('')
      expect(wrapper.vm.searchAttempted).toBe(false)
    })
  })
})