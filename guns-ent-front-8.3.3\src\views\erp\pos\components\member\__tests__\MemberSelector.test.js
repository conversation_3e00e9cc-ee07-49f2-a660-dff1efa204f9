/**
 * 会员选择器组件单元测试
 * 
 * 测试会员选择器组件的渲染和交互功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import MemberSelector from '../MemberSelector.vue'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  Input: {
    name: 'AInput',
    template: '<input :value="value" @input="$emit(\'update:value\', $event.target.value)" @keydown="$emit(\'keydown\', $event)" />'
  },
  Button: {
    name: 'AButton',
    template: '<button @click="$emit(\'click\')"><slot /></button>'
  },
  Card: {
    name: 'ACard',
    template: '<div class="ant-card"><slot /></div>'
  },
  Tag: {
    name: 'ATag',
    template: '<span class="ant-tag"><slot /></span>'
  },
  Avatar: {
    name: 'AAvatar',
    template: '<div class="ant-avatar"><slot /></div>'
  },
  Spin: {
    name: 'ASpin',
    template: '<div class="ant-spin"><slot /></div>'
  },
  Empty: {
    name: 'AEmpty',
    template: '<div class="ant-empty">暂无数据</div>'
  }
}))

// Mock API
const mockMemberApi = {
  searchMember: vi.fn(),
  validateMemberCard: vi.fn()
}

vi.mock('../../../api/member', () => ({
  MemberApi: mockMemberApi
}))

describe('MemberSelector', () => {
  let wrapper
  let pinia
  
  const mockMember = {
    id: 'M001',
    cardNo: 'VIP123456',
    name: '张三',
    phone: '13812345678',
    level: 'GOLD',
    levelName: '金牌会员',
    discountRate: 0.1,
    points: 1500,
    balance: 800,
    status: 'ACTIVE',
    avatar: '/images/avatar.jpg',
    joinDate: '2024-01-15',
    lastVisit: '2025-01-01'
  }
  
  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(MemberSelector, {
      props: {
        selectedMember: null,
        ...props
      },
      global: {
        plugins: [pinia],
        stubs: {
          'a-input': true,
          'a-button': true,
          'a-card': true,
          'a-tag': true,
          'a-avatar': true,
          'a-spin': true,
          'a-empty': true
        }
      }
    })
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  describe('基础渲染', () => {
    it('应该正确渲染会员搜索界面', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.member-selector').exists()).toBe(true)
      expect(wrapper.find('.member-search-input').exists()).toBe(true)
      expect(wrapper.find('.search-btn').exists()).toBe(true)
    })
    
    it('应该显示搜索提示信息', () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      expect(searchInput.attributes('placeholder')).toContain('输入会员卡号或手机号')
    })
    
    it('应该显示快速操作按钮', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.clear-member-btn').exists()).toBe(true)
      expect(wrapper.find('.register-member-btn').exists()).toBe(true)
    })
  })
  
  describe('会员搜索', () => {
    it('应该在输入时触发搜索', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', 'VIP123456')
      
      // 模拟防抖延迟
      await new Promise(resolve => setTimeout(resolve, 300))
      
      expect(mockMemberApi.searchMember).toHaveBeenCalledWith({
        cardNo: 'VIP123456'
      })
    })
    
    it('应该在按回车时立即搜索', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', '13812345678')
      await searchInput.vm.$emit('keydown', { key: 'Enter' })
      
      expect(mockMemberApi.searchMember).toHaveBeenCalledWith({
        phone: '13812345678'
      })
    })
    
    it('应该在点击搜索按钮时搜索', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', 'VIP123456')
      
      const searchBtn = wrapper.find('.search-btn')
      await searchBtn.trigger('click')
      
      expect(mockMemberApi.searchMember).toHaveBeenCalledWith({
        cardNo: 'VIP123456'
      })
    })
    
    it('应该显示搜索加载状态', async () => {
      mockMemberApi.searchMember.mockImplementation(() => new Promise(() => {})) // 永不resolve
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', 'VIP123456')
      await searchInput.vm.$emit('keydown', { key: 'Enter' })
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.search-loading').exists()).toBe(true)
    })
  })
  
  describe('会员信息显示', () => {
    it('应该显示找到的会员信息', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', 'VIP123456')
      await searchInput.vm.$emit('keydown', { key: 'Enter' })
      
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.member-info').exists()).toBe(true)
      expect(wrapper.text()).toContain('张三')
      expect(wrapper.text()).toContain('VIP123456')
      expect(wrapper.text()).toContain('13812345678')
    })
    
    it('应该显示会员头像', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      const avatar = wrapper.find('.member-avatar')
      expect(avatar.exists()).toBe(true)
    })
    
    it('应该显示会员等级标签', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      const levelTag = wrapper.find('.member-level-tag')
      expect(levelTag.exists()).toBe(true)
      expect(levelTag.text()).toContain('金牌会员')
    })
    
    it('应该显示会员余额和积分', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('余额: ¥800.00')
      expect(wrapper.text()).toContain('积分: 1,500')
    })
    
    it('应该显示会员折扣率', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.text()).toContain('折扣: 9折')
    })
  })
  
  describe('会员选择', () => {
    it('应该在点击选择按钮时选择会员', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      const selectBtn = wrapper.find('.select-member-btn')
      await selectBtn.trigger('click')
      
      expect(wrapper.emitted('member-select')).toBeTruthy()
      expect(wrapper.emitted('member-select')[0]).toEqual([mockMember])
    })
    
    it('应该在双击会员卡片时选择会员', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      const memberCard = wrapper.find('.member-info')
      await memberCard.trigger('dblclick')
      
      expect(wrapper.emitted('member-select')).toBeTruthy()
      expect(wrapper.emitted('member-select')[0]).toEqual([mockMember])
    })
    
    it('应该显示已选择的会员', () => {
      wrapper = createWrapper({ selectedMember: mockMember })
      
      expect(wrapper.find('.selected-member').exists()).toBe(true)
      expect(wrapper.text()).toContain('当前会员: 张三')
    })
  })
  
  describe('会员状态处理', () => {
    it('应该处理冻结的会员', async () => {
      const frozenMember = { ...mockMember, status: 'FROZEN' }
      mockMemberApi.searchMember.mockResolvedValue(frozenMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.member-status-warning').exists()).toBe(true)
      expect(wrapper.text()).toContain('已冻结')
      
      const selectBtn = wrapper.find('.select-member-btn')
      expect(selectBtn.attributes('disabled')).toBeDefined()
    })
    
    it('应该处理过期的会员', async () => {
      const expiredMember = { 
        ...mockMember, 
        status: 'EXPIRED',
        expireDate: '2024-12-31'
      }
      mockMemberApi.searchMember.mockResolvedValue(expiredMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.member-status-warning').exists()).toBe(true)
      expect(wrapper.text()).toContain('已过期')
    })
    
    it('应该处理停用的会员', async () => {
      const inactiveMember = { ...mockMember, status: 'INACTIVE' }
      mockMemberApi.searchMember.mockResolvedValue(inactiveMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.member-status-warning').exists()).toBe(true)
      expect(wrapper.text()).toContain('已停用')
    })
  })
  
  describe('搜索结果处理', () => {
    it('应该处理未找到会员的情况', async () => {
      mockMemberApi.searchMember.mockResolvedValue(null)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('INVALID')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.no-member-found').exists()).toBe(true)
      expect(wrapper.text()).toContain('未找到对应的会员')
    })
    
    it('应该处理搜索错误', async () => {
      mockMemberApi.searchMember.mockRejectedValue(new Error('搜索失败'))
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.search-error').exists()).toBe(true)
      expect(wrapper.text()).toContain('搜索失败')
    })
    
    it('应该显示搜索历史', async () => {
      wrapper = createWrapper()
      
      // 模拟多次搜索
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.handleSearch('VIP789012')
      
      const searchHistory = wrapper.find('.search-history')
      if (searchHistory.exists()) {
        expect(searchHistory.text()).toContain('VIP123456')
        expect(searchHistory.text()).toContain('VIP789012')
      }
    })
  })
  
  describe('快速操作', () => {
    it('应该支持清除当前会员', async () => {
      wrapper = createWrapper({ selectedMember: mockMember })
      
      const clearBtn = wrapper.find('.clear-member-btn')
      await clearBtn.trigger('click')
      
      expect(wrapper.emitted('member-clear')).toBeTruthy()
    })
    
    it('应该支持快速注册新会员', async () => {
      wrapper = createWrapper()
      
      const registerBtn = wrapper.find('.register-member-btn')
      await registerBtn.trigger('click')
      
      expect(wrapper.emitted('member-register')).toBeTruthy()
    })
    
    it('应该支持扫码搜索会员', async () => {
      wrapper = createWrapper()
      
      const scanBtn = wrapper.find('.scan-member-btn')
      if (scanBtn.exists()) {
        await scanBtn.trigger('click')
        expect(wrapper.emitted('scan-member')).toBeTruthy()
      }
    })
  })
  
  describe('输入验证', () => {
    it('应该验证会员卡号格式', async () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', '123') // 太短的卡号
      
      const validationError = wrapper.find('.validation-error')
      if (validationError.exists()) {
        expect(validationError.text()).toContain('卡号格式不正确')
      }
    })
    
    it('应该验证手机号格式', async () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', '12345') // 无效手机号
      
      const validationError = wrapper.find('.validation-error')
      if (validationError.exists()) {
        expect(validationError.text()).toContain('手机号格式不正确')
      }
    })
  })
  
  describe('键盘导航', () => {
    it('应该支持上下键选择搜索历史', async () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('keydown', { key: 'ArrowDown' })
      
      // 应该高亮第一个历史记录
      const firstHistoryItem = wrapper.find('.search-history-item--active')
      if (firstHistoryItem.exists()) {
        expect(firstHistoryItem.exists()).toBe(true)
      }
    })
    
    it('应该支持ESC键清除搜索', async () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      await searchInput.vm.$emit('update:value', 'VIP123456')
      await searchInput.vm.$emit('keydown', { key: 'Escape' })
      
      expect(searchInput.attributes('value')).toBe('')
    })
  })
  
  describe('边界情况', () => {
    it('应该处理空的搜索关键词', async () => {
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('')
      
      expect(mockMemberApi.searchMember).not.toHaveBeenCalled()
    })
    
    it('应该处理特殊字符的搜索', async () => {
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP@#$%')
      
      // 应该清理特殊字符或显示错误
      const validationError = wrapper.find('.validation-error')
      if (validationError.exists()) {
        expect(validationError.text()).toContain('包含无效字符')
      }
    })
    
    it('应该处理网络超时', async () => {
      mockMemberApi.searchMember.mockRejectedValue(new Error('timeout'))
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.search-timeout').exists()).toBe(true)
    })
  })
  
  describe('可访问性', () => {
    it('应该有正确的ARIA标签', () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input')
      expect(searchInput.attributes('aria-label')).toBe('搜索会员')
      
      const memberSelector = wrapper.find('.member-selector')
      expect(memberSelector.attributes('role')).toBe('search')
    })
    
    it('应该为搜索结果提供屏幕阅读器支持', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      await wrapper.vm.handleSearch('VIP123456')
      await wrapper.vm.$nextTick()
      
      const memberInfo = wrapper.find('.member-info')
      expect(memberInfo.attributes('aria-label')).toContain('找到会员')
    })
  })
  
  describe('性能优化', () => {
    it('应该防抖搜索请求', async () => {
      wrapper = createWrapper()
      
      const searchInput = wrapper.find('.member-search-input a-input-stub')
      
      // 快速输入多个字符
      await searchInput.vm.$emit('update:value', 'V')
      await searchInput.vm.$emit('update:value', 'VI')
      await searchInput.vm.$emit('update:value', 'VIP')
      await searchInput.vm.$emit('update:value', 'VIP1')
      
      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 350))
      
      // 应该只调用一次搜索
      expect(mockMemberApi.searchMember).toHaveBeenCalledTimes(1)
    })
    
    it('应该缓存搜索结果', async () => {
      mockMemberApi.searchMember.mockResolvedValue(mockMember)
      wrapper = createWrapper()
      
      // 第一次搜索
      await wrapper.vm.handleSearch('VIP123456')
      // 第二次搜索相同关键词
      await wrapper.vm.handleSearch('VIP123456')
      
      // 应该只调用一次API
      expect(mockMemberApi.searchMember).toHaveBeenCalledTimes(1)
    })
  })
})