/**
 * POS模块错误处理器
 * 
 * 提供统一的错误处理机制，包括错误分类、用户提示、日志记录等
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { message, notification } from 'ant-design-vue'
import { PosErrorTypes, getErrorMessage, getErrorSeverity, isUserFacingError, createError } from './error-types'

/**
 * POS错误处理器类
 */
export class PosErrorHandler {
  
  /**
   * 包装API调用，提供统一的错误处理
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 错误处理选项
   * @param {boolean} options.showMessage - 是否显示消息提示，默认true
   * @param {boolean} options.showNotification - 是否显示通知，默认false
   * @param {string} options.context - 操作上下文，用于日志记录
   * @param {Function} options.onError - 自定义错误处理函数
   * @param {Object} options.retryOptions - 重试选项
   * @returns {Function} 包装后的API函数
   */
  static wrapApiCall(apiFunction, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      context = '操作',
      onError,
      retryOptions = {}
    } = options
    
    return async (...args) => {
      try {
        // 如果有重试选项，使用重试机制
        if (retryOptions.maxRetries > 0) {
          const { RetryHandler } = await import('./retry-handler')
          return await RetryHandler.withRetry(() => apiFunction(...args), retryOptions)
        }
        
        return await apiFunction(...args)
      } catch (error) {
        const processedError = this.processError(error, context)
        
        // 显示用户提示
        if (processedError.userFacing) {
          if (showMessage) {
            this.showErrorMessage(processedError)
          }
          
          if (showNotification) {
            this.showErrorNotification(processedError, context)
          }
        }
        
        // 记录错误日志
        this.logError(processedError, context)
        
        // 执行自定义错误处理
        if (typeof onError === 'function') {
          onError(processedError)
        }
        
        throw processedError
      }
    }
  }
  
  /**
   * 处理错误，将原始错误转换为标准化错误对象
   * @param {Error|Object} error - 原始错误
   * @param {string} context - 操作上下文
   * @returns {Object} 标准化错误对象
   */
  static processError(error, context = '') {
    // 如果已经是标准化错误对象，直接返回
    if (error && error.type && error.severity) {
      return error
    }
    
    let errorType = PosErrorTypes.UNKNOWN_ERROR
    let errorMessage = ''
    let errorDetails = {}
    
    if (error instanceof Error) {
      errorMessage = error.message
      errorDetails = {
        name: error.name,
        stack: error.stack
      }
      
      // 根据错误消息推断错误类型
      errorType = this.inferErrorType(error)
    } else if (typeof error === 'object' && error !== null) {
      // 处理API响应错误
      if (error.response) {
        const { status, data } = error.response
        errorDetails = { status, data }
        
        if (status >= 500) {
          errorType = PosErrorTypes.SYSTEM_ERROR
        } else if (status === 404) {
          errorType = PosErrorTypes.PRODUCT_NOT_FOUND // 根据上下文可能需要调整
        } else if (status === 401) {
          errorType = PosErrorTypes.UNAUTHORIZED
        } else if (status === 403) {
          errorType = PosErrorTypes.ACCESS_FORBIDDEN
        } else if (status === 400) {
          errorType = this.inferErrorTypeFromResponse(data)
        }
        
        errorMessage = data?.message || error.message || `HTTP ${status} 错误`
      } else if (error.code) {
        // 处理带错误码的错误
        errorType = error.code
        errorMessage = error.message || ''
        errorDetails = error.details || {}
      } else {
        errorMessage = error.message || JSON.stringify(error)
      }
    } else {
      errorMessage = String(error)
    }
    
    return createError(errorType, errorMessage, {
      ...errorDetails,
      context,
      originalError: error
    })
  }
  
  /**
   * 根据错误对象推断错误类型
   * @param {Error} error - 错误对象
   * @returns {string} 错误类型
   */
  static inferErrorType(error) {
    const message = error.message.toLowerCase()
    
    if (message.includes('network') || message.includes('网络')) {
      return PosErrorTypes.NETWORK_ERROR
    }
    
    if (message.includes('timeout') || message.includes('超时')) {
      return PosErrorTypes.TIMEOUT_ERROR
    }
    
    if (message.includes('connection') || message.includes('连接')) {
      return PosErrorTypes.CONNECTION_ERROR
    }
    
    if (message.includes('inventory') || message.includes('库存')) {
      return PosErrorTypes.INSUFFICIENT_INVENTORY
    }
    
    if (message.includes('member') || message.includes('会员')) {
      return PosErrorTypes.INVALID_MEMBER
    }
    
    if (message.includes('payment') || message.includes('支付')) {
      return PosErrorTypes.PAYMENT_FAILED
    }
    
    if (message.includes('permission') || message.includes('权限')) {
      return PosErrorTypes.PERMISSION_DENIED
    }
    
    return PosErrorTypes.UNKNOWN_ERROR
  }
  
  /**
   * 根据API响应推断错误类型
   * @param {Object} responseData - API响应数据
   * @returns {string} 错误类型
   */
  static inferErrorTypeFromResponse(responseData) {
    if (!responseData) {
      return PosErrorTypes.UNKNOWN_ERROR
    }
    
    const { code, message = '' } = responseData
    
    // 如果响应中包含错误码，直接使用
    if (code && Object.values(PosErrorTypes).includes(code)) {
      return code
    }
    
    // 根据消息内容推断
    const lowerMessage = message.toLowerCase()
    
    if (lowerMessage.includes('quantity') || lowerMessage.includes('数量')) {
      return PosErrorTypes.INVALID_QUANTITY
    }
    
    if (lowerMessage.includes('price') || lowerMessage.includes('价格')) {
      return PosErrorTypes.INVALID_PRICE
    }
    
    if (lowerMessage.includes('amount') || lowerMessage.includes('金额')) {
      return PosErrorTypes.INVALID_AMOUNT
    }
    
    if (lowerMessage.includes('cart') || lowerMessage.includes('购物车')) {
      return PosErrorTypes.EMPTY_CART
    }
    
    return PosErrorTypes.UNKNOWN_ERROR
  }
  
  /**
   * 显示错误消息
   * @param {Object} error - 标准化错误对象
   */
  static showErrorMessage(error) {
    const messageType = this.getMessageType(error.severity)
    
    message[messageType](error.message, 3)
  }
  
  /**
   * 显示错误通知
   * @param {Object} error - 标准化错误对象
   * @param {string} context - 操作上下文
   */
  static showErrorNotification(error, context) {
    const notificationType = this.getNotificationType(error.severity)
    
    notification[notificationType]({
      message: `${context}失败`,
      description: error.message,
      duration: this.getNotificationDuration(error.severity),
      placement: 'topRight'
    })
  }
  
  /**
   * 根据错误严重级别获取消息类型
   * @param {string} severity - 错误严重级别
   * @returns {string} 消息类型
   */
  static getMessageType(severity) {
    switch (severity) {
      case 'LOW':
        return 'warning'
      case 'MEDIUM':
        return 'error'
      case 'HIGH':
        return 'error'
      case 'CRITICAL':
        return 'error'
      default:
        return 'error'
    }
  }
  
  /**
   * 根据错误严重级别获取通知类型
   * @param {string} severity - 错误严重级别
   * @returns {string} 通知类型
   */
  static getNotificationType(severity) {
    switch (severity) {
      case 'LOW':
        return 'warning'
      case 'MEDIUM':
        return 'error'
      case 'HIGH':
        return 'error'
      case 'CRITICAL':
        return 'error'
      default:
        return 'error'
    }
  }
  
  /**
   * 根据错误严重级别获取通知持续时间
   * @param {string} severity - 错误严重级别
   * @returns {number} 持续时间（秒）
   */
  static getNotificationDuration(severity) {
    switch (severity) {
      case 'LOW':
        return 3
      case 'MEDIUM':
        return 5
      case 'HIGH':
        return 8
      case 'CRITICAL':
        return 0 // 不自动关闭
      default:
        return 5
    }
  }
  
  /**
   * 记录错误日志
   * @param {Object} error - 标准化错误对象
   * @param {string} context - 操作上下文
   */
  static logError(error, context) {
    const logLevel = this.getLogLevel(error.severity)
    const logMessage = `[POS Error] ${context}: ${error.message}`
    
    const logData = {
      type: error.type,
      message: error.message,
      severity: error.severity,
      context,
      timestamp: error.timestamp,
      details: error.details
    }
    
    // 根据日志级别输出到控制台
    switch (logLevel) {
      case 'warn':
        console.warn(logMessage, logData)
        break
      case 'error':
        console.error(logMessage, logData)
        break
      default:
        console.log(logMessage, logData)
    }
    
    // 发送到日志服务（如果配置了）
    this.sendToLogService(error, context)
  }
  
  /**
   * 根据错误严重级别获取日志级别
   * @param {string} severity - 错误严重级别
   * @returns {string} 日志级别
   */
  static getLogLevel(severity) {
    switch (severity) {
      case 'LOW':
        return 'info'
      case 'MEDIUM':
        return 'warn'
      case 'HIGH':
        return 'error'
      case 'CRITICAL':
        return 'error'
      default:
        return 'info'
    }
  }
  
  /**
   * 发送错误日志到日志服务
   * @param {Object} error - 标准化错误对象
   * @param {string} context - 操作上下文
   */
  static sendToLogService(error, context) {
    // 只有高级别和严重级别的错误才发送到日志服务
    if (error.severity === 'HIGH' || error.severity === 'CRITICAL') {
      try {
        // 这里可以集成实际的日志服务，如Sentry、LogRocket等
        // 目前只是示例实现
        if (window.logService && typeof window.logService.captureException === 'function') {
          window.logService.captureException(error, {
            tags: {
              context,
              severity: error.severity,
              type: error.type
            },
            extra: error.details
          })
        }
      } catch (logError) {
        console.warn('发送错误日志失败:', logError)
      }
    }
  }
  
  /**
   * 创建错误边界处理函数
   * @param {string} componentName - 组件名称
   * @returns {Function} 错误处理函数
   */
  static createErrorBoundary(componentName) {
    return (error, errorInfo) => {
      const processedError = this.processError(error, `组件 ${componentName}`)
      
      // 显示用户友好的错误提示
      message.error('页面出现错误，请刷新后重试')
      
      // 记录详细的错误信息
      this.logError({
        ...processedError,
        details: {
          ...processedError.details,
          componentStack: errorInfo.componentStack,
          errorBoundary: true
        }
      }, `组件 ${componentName} 错误边界`)
    }
  }
  
  /**
   * 全局错误处理器
   * @param {Error} error - 错误对象
   * @param {string} source - 错误来源
   */
  static globalErrorHandler(error, source = 'global') {
    const processedError = this.processError(error, source)
    
    // 对于严重错误，显示全局错误提示
    if (processedError.severity === 'CRITICAL') {
      notification.error({
        message: '系统错误',
        description: '系统遇到严重错误，请刷新页面或联系管理员',
        duration: 0,
        placement: 'topRight'
      })
    }
    
    this.logError(processedError, source)
  }
  
  /**
   * Promise 拒绝处理器
   * @param {PromiseRejectionEvent} event - Promise拒绝事件
   */
  static unhandledRejectionHandler(event) {
    const error = event.reason
    const processedError = this.processError(error, 'unhandled promise rejection')
    
    // 阻止默认的控制台错误输出
    event.preventDefault()
    
    this.logError(processedError, 'unhandled promise rejection')
    
    // 对于用户相关的错误，显示提示
    if (processedError.userFacing) {
      message.error(processedError.message)
    }
  }
}