<template>
  <div class="toolbar-panel">
    <div class="function-buttons">
      <!-- 挂单列表按钮 -->
      <FunctionButton
        title="挂单列表"
        icon="icon-suspend"
        type="primary"
        :badge="suspendedOrdersCount"
        @click="handleShowSuspendedOrders"
      />

      <!-- 会员管理按钮 -->
      <FunctionButton
        title="会员管理"
        icon="icon-member"
        type="default"
        @click="handleMemberManagement"
      />
    </div>
  </div>
</template>

<script setup>
import FunctionButton from '../common/FunctionButton.vue'

// 导入样式文件
import '../../styles/common.css'
import '../../styles/toolbar.css'

// 定义组件名称
defineOptions({
  name: 'ToolbarPanel'
})

// 定义Props
const props = defineProps({
  suspendedOrdersCount: {
    type: Number,
    default: 0
  }
})

// 定义事件
const emit = defineEmits([
  'showSuspendedOrders',
  'memberManagement'
])

/**
 * 显示挂单列表
 */
const handleShowSuspendedOrders = () => {
  emit('showSuspendedOrders')
}

/**
 * 会员管理
 */
const handleMemberManagement = () => {
  emit('memberManagement')
}
</script>

<style scoped>
.toolbar-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  overflow: hidden;
}

.function-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-panel {
    padding: 8px;
    max-width: 100vw;
  }

  .function-buttons {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .toolbar-panel {
    padding: 6px;
  }

  .function-buttons {
    gap: 8px;
  }
}
</style>
