/**
 * 懒加载组合式函数
 * 
 * 提供组件级别的懒加载功能，包括组件、模块、样式的按需加载
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { LazyLoader } from '../utils/lazy-loader'
import { 
  getLazyComponent, 
  getLazyModule, 
  preloadByPriority,
  conditionalLoad 
} from '../config/lazy-config'

export function useLazyLoad(options = {}) {
  // 懒加载状态
  const loadingStates = ref(new Map())
  const loadedModules = ref(new Map())
  const loadErrors = ref(new Map())
  
  // 配置选项
  const config = ref({
    enablePreload: true,
    enableIntelligentLoad: true,
    preloadDelay: 2000,
    retryCount: 3,
    timeout: 10000,
    ...options
  })
  
  // 计算属性
  const isLoading = computed(() => {
    return Array.from(loadingStates.value.values()).some(state => state)
  })
  
  const hasErrors = computed(() => {
    return loadErrors.value.size > 0
  })
  
  const loadStats = computed(() => {
    return {
      loading: Array.from(loadingStates.value.values()).filter(Boolean).length,
      loaded: loadedModules.value.size,
      errors: loadErrors.value.size,
      total: loadingStates.value.size
    }
  })
  
  /**
   * 懒加载组件
   * @param {string} category - 组件分类
   * @param {string} name - 组件名称
   * @param {Object} options - 加载选项
   * @returns {Object} 懒加载组件
   */
  const loadComponent = (category, name, options = {}) => {
    const component = getLazyComponent(category, name)
    
    if (!component) {
      console.warn(`组件 ${category}.${name} 未找到`)
      return null
    }
    
    const key = `${category}.${name}`
    loadingStates.value.set(key, false)
    
    return component
  }
  
  /**
   * 懒加载模块
   * @param {string} category - 模块分类
   * @param {string} name - 模块名称
   * @param {Object} options - 加载选项
   * @returns {Promise} 模块Promise
   */
  const loadModule = async (category, name, options = {}) => {\n    const key = `${category}.${name}`\n    \n    // 检查是否已加载\n    if (loadedModules.value.has(key)) {\n      return loadedModules.value.get(key)\n    }\n    \n    // 检查是否正在加载\n    if (loadingStates.value.get(key)) {\n      // 等待加载完成\n      return new Promise((resolve, reject) => {\n        const checkLoaded = () => {\n          if (loadedModules.value.has(key)) {\n            resolve(loadedModules.value.get(key))\n          } else if (loadErrors.value.has(key)) {\n            reject(loadErrors.value.get(key))\n          } else {\n            setTimeout(checkLoaded, 100)\n          }\n        }\n        checkLoaded()\n      })\n    }\n    \n    const loader = getLazyModule(category, name)\n    \n    if (!loader) {\n      const error = new Error(`模块 ${category}.${name} 未找到`)\n      loadErrors.value.set(key, error)\n      throw error\n    }\n    \n    try {\n      loadingStates.value.set(key, true)\n      loadErrors.value.delete(key)\n      \n      const module = await LazyLoader.lazyModule(loader, {\n        timeout: config.value.timeout,\n        retry: config.value.retryCount,\n        ...options\n      })\n      \n      loadedModules.value.set(key, module)\n      loadingStates.value.set(key, false)\n      \n      return module\n      \n    } catch (error) {\n      loadingStates.value.set(key, false)\n      loadErrors.value.set(key, error)\n      \n      console.error(`模块 ${key} 加载失败:`, error)\n      throw error\n    }\n  }\n  \n  /**\n   * 批量加载模块\n   * @param {Array} modules - 模块列表 [{category, name, options}]\n   * @param {Object} batchOptions - 批量选项\n   * @returns {Promise} 批量加载结果\n   */\n  const loadModules = async (modules, batchOptions = {}) => {\n    const {\n      concurrent = 3,\n      failFast = false,\n      onProgress = null\n    } = batchOptions\n    \n    const results = []\n    let completed = 0\n    \n    // 分批并发加载\n    for (let i = 0; i < modules.length; i += concurrent) {\n      const batch = modules.slice(i, i + concurrent)\n      \n      const batchPromises = batch.map(async ({ category, name, options = {} }) => {\n        try {\n          const module = await loadModule(category, name, options)\n          completed++\n          \n          if (onProgress) {\n            onProgress(completed, modules.length)\n          }\n          \n          return { category, name, module, status: 'success' }\n          \n        } catch (error) {\n          completed++\n          \n          if (onProgress) {\n            onProgress(completed, modules.length)\n          }\n          \n          if (failFast) {\n            throw error\n          }\n          \n          return { category, name, error, status: 'failed' }\n        }\n      })\n      \n      if (failFast) {\n        const batchResults = await Promise.all(batchPromises)\n        results.push(...batchResults)\n      } else {\n        const batchResults = await Promise.allSettled(batchPromises)\n        results.push(...batchResults.map(r => r.value || r.reason))\n      }\n    }\n    \n    return results\n  }\n  \n  /**\n   * 预加载指定优先级的资源\n   * @param {string} priority - 优先级 (high, medium, low)\n   * @returns {Promise} 预加载结果\n   */\n  const preload = async (priority = 'medium') => {\n    if (!config.value.enablePreload) {\n      return\n    }\n    \n    try {\n      const result = await preloadByPriority(priority)\n      console.log(`✅ ${priority} 优先级资源预加载完成:`, result)\n      return result\n    } catch (error) {\n      console.error(`❌ ${priority} 优先级资源预加载失败:`, error)\n      throw error\n    }\n  }\n  \n  /**\n   * 智能预加载（基于用户行为）\n   * @param {Object} triggers - 触发器配置\n   */\n  const setupIntelligentPreload = (triggers = {}) => {\n    if (!config.value.enableIntelligentLoad) {\n      return\n    }\n    \n    const {\n      idle = true,\n      hover = true,\n      intersection = true,\n      scroll = false\n    } = triggers\n    \n    // 空闲时预加载\n    if (idle && 'requestIdleCallback' in window) {\n      setTimeout(() => {\n        requestIdleCallback(() => {\n          preload('low')\n        })\n      }, config.value.preloadDelay)\n    }\n    \n    // 鼠标悬停预加载\n    if (hover) {\n      document.addEventListener('mouseover', (event) => {\n        const target = event.target.closest('[data-lazy-preload]')\n        if (target) {\n          const { category, name } = target.dataset\n          if (category && name) {\n            loadModule(category, name).catch(() => {}) // 静默失败\n          }\n        }\n      })\n    }\n    \n    // 可视区域预加载\n    if (intersection && 'IntersectionObserver' in window) {\n      const observer = new IntersectionObserver((entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            const { category, name } = entry.target.dataset\n            if (category && name) {\n              loadModule(category, name).catch(() => {}) // 静默失败\n              observer.unobserve(entry.target)\n            }\n          }\n        })\n      }, { threshold: 0.1 })\n      \n      // 观察带有data-lazy-load属性的元素\n      nextTick(() => {\n        document.querySelectorAll('[data-lazy-load]').forEach(el => {\n          observer.observe(el)\n        })\n      })\n    }\n    \n    // 滚动预加载\n    if (scroll) {\n      let scrollTimer = null\n      document.addEventListener('scroll', () => {\n        if (scrollTimer) clearTimeout(scrollTimer)\n        \n        scrollTimer = setTimeout(() => {\n          const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100\n          \n          if (scrollPercent > 50) {\n            preload('medium')\n          }\n        }, 200)\n      }, { passive: true })\n    }\n  }\n  \n  /**\n   * 条件加载\n   * @param {string} condition - 条件类型\n   * @param {string} value - 条件值\n   * @returns {Promise} 加载结果\n   */\n  const conditionalLoadModules = async (condition, value) => {\n    try {\n      const result = await conditionalLoad(condition, value)\n      console.log(`✅ 条件加载完成 (${condition}=${value}):`, result)\n      return result\n    } catch (error) {\n      console.error(`❌ 条件加载失败 (${condition}=${value}):`, error)\n      throw error\n    }\n  }\n  \n  /**\n   * 重试加载失败的模块\n   * @param {string} key - 模块键名（可选）\n   * @returns {Promise} 重试结果\n   */\n  const retryFailedLoads = async (key = null) => {\n    const failedKeys = key ? [key] : Array.from(loadErrors.value.keys())\n    const retryPromises = []\n    \n    failedKeys.forEach(failedKey => {\n      const [category, name] = failedKey.split('.')\n      if (category && name) {\n        // 清除错误状态\n        loadErrors.value.delete(failedKey)\n        // 重新加载\n        retryPromises.push(\n          loadModule(category, name).catch(error => ({ key: failedKey, error }))\n        )\n      }\n    })\n    \n    const results = await Promise.allSettled(retryPromises)\n    \n    const successful = results.filter(r => r.status === 'fulfilled' && !r.value.error).length\n    const failed = results.length - successful\n    \n    console.log(`🔄 重试完成: ${successful}个成功, ${failed}个失败`)\n    \n    return { successful, failed, results }\n  }\n  \n  /**\n   * 清除加载状态\n   * @param {string} key - 模块键名（可选）\n   */\n  const clearLoadState = (key = null) => {\n    if (key) {\n      loadingStates.value.delete(key)\n      loadedModules.value.delete(key)\n      loadErrors.value.delete(key)\n    } else {\n      loadingStates.value.clear()\n      loadedModules.value.clear()\n      loadErrors.value.clear()\n    }\n  }\n  \n  /**\n   * 获取模块加载状态\n   * @param {string} category - 模块分类\n   * @param {string} name - 模块名称\n   * @returns {Object} 加载状态\n   */\n  const getLoadState = (category, name) => {\n    const key = `${category}.${name}`\n    \n    return {\n      loading: loadingStates.value.get(key) || false,\n      loaded: loadedModules.value.has(key),\n      error: loadErrors.value.get(key) || null,\n      module: loadedModules.value.get(key) || null\n    }\n  }\n  \n  /**\n   * 更新配置\n   * @param {Object} newConfig - 新配置\n   */\n  const updateConfig = (newConfig) => {\n    config.value = { ...config.value, ...newConfig }\n  }\n  \n  /**\n   * 获取加载统计信息\n   * @returns {Object} 统计信息\n   */\n  const getLoadStatistics = () => {\n    const globalStats = LazyLoader.getLoadStats()\n    \n    return {\n      ...loadStats.value,\n      global: globalStats,\n      cacheInfo: LazyLoader.getCacheInfo()\n    }\n  }\n  \n  // 生命周期钩子\n  onMounted(() => {\n    // 自动预加载高优先级资源\n    if (config.value.enablePreload) {\n      nextTick(() => {\n        preload('high').catch(() => {}) // 静默失败\n      })\n    }\n    \n    // 设置智能预加载\n    if (config.value.enableIntelligentLoad) {\n      setupIntelligentPreload()\n    }\n  })\n  \n  onUnmounted(() => {\n    // 清理资源\n    clearLoadState()\n  })\n  \n  return {\n    // 状态\n    loadingStates,\n    loadedModules,\n    loadErrors,\n    config,\n    isLoading,\n    hasErrors,\n    loadStats,\n    \n    // 方法\n    loadComponent,\n    loadModule,\n    loadModules,\n    preload,\n    setupIntelligentPreload,\n    conditionalLoadModules,\n    retryFailedLoads,\n    clearLoadState,\n    getLoadState,\n    updateConfig,\n    getLoadStatistics\n  }\n}