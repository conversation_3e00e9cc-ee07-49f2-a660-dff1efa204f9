/**
 * 支付相关API接口
 * 
 * 提供现金支付、扫码支付、刷卡支付等各种支付方式的API封装
 * 从原始pos.js中抽离出支付相关的API接口
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import Request from '@/utils/request/request-util'
import { PosErrorHandler } from '../utils/error-handler'
import { PerformanceMonitor } from '../utils/performance-monitor'
import { PAYMENT_METHODS } from '../utils/constants'

/**
 * 支付API类
 */
export class PaymentApi {
  
  /**
   * 创建带错误处理和性能监控的API包装器
   * @param {Function} apiFunction - 原始API函数
   * @param {Object} options - 配置选项
   * @returns {Function} 包装后的API函数
   */
  static createApiWrapper(apiFunction, options = {}) {
    const {
      context = 'Payment API调用',
      showMessage = true,
      showNotification = true,
      retryOptions = { maxRetries: 0 } // 支付操作默认不重试
    } = options
    
    // 包装性能监控
    const monitoredFunction = PerformanceMonitor.measureApiCall(context, apiFunction)
    
    // 包装错误处理
    return PosErrorHandler.wrapApiCall(monitoredFunction, {
      showMessage,
      showNotification,
      context,
      retryOptions
    })
  }
  
  /**
   * 处理现金支付
   * @param {Object} params - 支付参数
   * @param {number} params.orderId - 订单ID
   * @param {number} params.paymentAmount - 支付金额
   * @param {number} params.receivedAmount - 实收金额
   * @param {number} params.changeAmount - 找零金额
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 支付结果
   */
  static async processCashPayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/cash', {
      ...params,
      paymentMethod: PAYMENT_METHODS.CASH,
      paymentTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '处理现金支付',
      retryOptions: { maxRetries: 0 } // 支付操作不重试，避免重复扣款
    })()
  }
  
  /**
   * 处理扫码支付
   * @param {Object} params - 支付参数
   * @param {number} params.orderId - 订单ID
   * @param {number} params.paymentAmount - 支付金额
   * @param {string} params.paymentMethod - 支付方式（WECHAT/ALIPAY）
   * @param {string} params.qrCode - 二维码内容
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 支付结果
   */
  static async processQrCodePayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/qrcode', {
      ...params,
      paymentTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '处理扫码支付',
      retryOptions: { maxRetries: 0 }
    })()
  }
  
  /**
   * 查询扫码支付状态
   * @param {Object} params - 查询参数
   * @param {number} params.paymentId - 支付ID
   * @param {string} params.outTradeNo - 商户订单号
   * @returns {Promise<Object>} 支付状态
   */
  static async queryQrCodePaymentStatus(params) {
    const apiCall = () => Request.get('/erp/pos/payment/qrcode/status', params)
    
    return this.createApiWrapper(apiCall, {
      context: '查询扫码支付状态',
      showMessage: false,
      showNotification: false,
      retryOptions: { maxRetries: 3, retryDelay: 2000 }
    })()
  }
  
  /**
   * 处理会员卡支付
   * @param {Object} params - 支付参数
   * @param {number} params.orderId - 订单ID
   * @param {number} params.paymentAmount - 支付金额
   * @param {number} params.memberId - 会员ID
   * @param {string} params.memberCardNo - 会员卡号
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 支付结果
   */
  static async processMemberCardPayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/member', {
      ...params,
      paymentMethod: PAYMENT_METHODS.MEMBER,
      paymentTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '处理会员卡支付',
      retryOptions: { maxRetries: 0 }
    })()
  }
  
  /**
   * 处理积分支付
   * @param {Object} params - 支付参数
   * @param {number} params.orderId - 订单ID
   * @param {number} params.paymentAmount - 支付金额
   * @param {number} params.memberId - 会员ID
   * @param {number} params.pointsUsed - 使用的积分数
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 支付结果
   */
  static async processPointsPayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/points', {
      ...params,
      paymentMethod: PAYMENT_METHODS.POINTS,
      paymentTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '处理积分支付',
      retryOptions: { maxRetries: 0 }
    })()
  }
  
  /**
   * 处理银行卡支付
   * @param {Object} params - 支付参数
   * @param {number} params.orderId - 订单ID
   * @param {number} params.paymentAmount - 支付金额
   * @param {string} params.cardNo - 银行卡号（脱敏）
   * @param {string} params.bankName - 银行名称
   * @param {string} params.authCode - 授权码
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 支付结果
   */
  static async processBankCardPayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/bankcard', {
      ...params,
      paymentMethod: PAYMENT_METHODS.CARD,
      paymentTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '处理银行卡支付',
      retryOptions: { maxRetries: 0 }
    })()
  }
  
  /**
   * 处理组合支付
   * @param {Object} params - 支付参数
   * @param {number} params.orderId - 订单ID
   * @param {number} params.totalAmount - 总支付金额
   * @param {Array} params.paymentMethods - 支付方式列表
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 支付结果
   */
  static async processComboPayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/combo', {
      ...params,
      paymentTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '处理组合支付',
      retryOptions: { maxRetries: 0 }
    })()
  }
  
  /**
   * 确认支付成功
   * @param {Object} params - 确认参数
   * @param {number} params.paymentId - 支付ID
   * @param {string} params.transactionId - 第三方交易号
   * @param {number} params.actualAmount - 实际支付金额
   * @param {string} params.confirmTime - 确认时间
   * @returns {Promise<Object>} 确认结果
   */
  static async confirmPaymentSuccess(params) {
    const apiCall = () => Request.post('/erp/pos/payment/confirm', {
      ...params,
      confirmTime: params.confirmTime || new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '确认支付成功',
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 取消支付
   * @param {Object} params - 取消参数
   * @param {number} params.paymentId - 支付ID
   * @param {string} params.cancelReason - 取消原因
   * @param {string} params.cancelBy - 取消操作人
   * @returns {Promise<Object>} 取消结果
   */
  static async cancelPayment(params) {
    const apiCall = () => Request.post('/erp/pos/payment/cancel', {
      ...params,
      cancelTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '取消支付',
      retryOptions: { maxRetries: 1, retryDelay: 1000 }
    })()
  }
  
  /**
   * 申请退款
   * @param {Object} params - 退款参数
   * @param {number} params.paymentId - 支付ID
   * @param {number} params.refundAmount - 退款金额
   * @param {string} params.refundReason - 退款原因
   * @param {string} params.refundBy - 退款操作人
   * @returns {Promise<Object>} 退款结果
   */
  static async requestRefund(params) {
    const apiCall = () => Request.post('/erp/pos/payment/refund', {
      ...params,
      refundTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '申请退款',
      retryOptions: { maxRetries: 1, retryDelay: 2000 }
    })()
  }
  
  /**
   * 查询退款状态
   * @param {Object} params - 查询参数
   * @param {number} params.refundId - 退款ID
   * @param {string} params.outRefundNo - 商户退款单号
   * @returns {Promise<Object>} 退款状态
   */
  static async queryRefundStatus(params) {
    const apiCall = () => Request.get('/erp/pos/payment/refund/status', params)
    
    return this.createApiWrapper(apiCall, {
      context: '查询退款状态',
      showMessage: false,
      showNotification: false,
      retryOptions: { maxRetries: 3, retryDelay: 2000 }
    })()
  }
  
  /**
   * 获取支付记录
   * @param {Object} params - 查询参数
   * @param {number} params.orderId - 订单ID
   * @param {string} params.paymentMethod - 支付方式
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @returns {Promise<Array>} 支付记录列表
   */
  static async getPaymentRecords(params = {}) {
    const apiCall = () => Request.get('/erp/pos/payment/records', params)
    
    return this.createApiWrapper(apiCall, {
      context: '获取支付记录',
      showMessage: false,
      showNotification: false,
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 获取支付统计
   * @param {Object} params - 查询参数
   * @param {string} params.cashierId - 收银员ID
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @param {string} params.paymentMethod - 支付方式
   * @returns {Promise<Object>} 支付统计数据
   */
  static async getPaymentStatistics(params = {}) {
    const apiCall = () => Request.get('/erp/pos/payment/statistics', params)
    
    return this.createApiWrapper(apiCall, {
      context: '获取支付统计',
      showMessage: false,
      showNotification: false,
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 验证支付密码
   * @param {Object} params - 验证参数
   * @param {string} params.password - 支付密码
   * @param {string} params.cashierId - 收银员ID
   * @returns {Promise<Object>} 验证结果
   */
  static async validatePaymentPassword(params) {
    const apiCall = () => Request.post('/erp/pos/payment/validatePassword', params)
    
    return this.createApiWrapper(apiCall, {
      context: '验证支付密码',
      showMessage: false,
      retryOptions: { maxRetries: 0 }
    })()
  }
  
  /**
   * 获取支付配置
   * @param {Object} params - 查询参数
   * @param {string} params.storeId - 门店ID
   * @returns {Promise<Object>} 支付配置
   */
  static async getPaymentConfig(params = {}) {
    const apiCall = () => Request.get('/erp/pos/payment/config', params)
    
    return this.createApiWrapper(apiCall, {
      context: '获取支付配置',
      showMessage: false,
      showNotification: false,
      retryOptions: { maxRetries: 2, retryDelay: 1000 }
    })()
  }
  
  /**
   * 测试支付连接
   * @param {Object} params - 测试参数
   * @param {string} params.paymentMethod - 支付方式
   * @param {Object} params.config - 支付配置
   * @returns {Promise<Object>} 测试结果
   */
  static async testPaymentConnection(params) {
    const apiCall = () => Request.post('/erp/pos/payment/test', params)
    
    return this.createApiWrapper(apiCall, {
      context: '测试支付连接',
      retryOptions: { maxRetries: 1, retryDelay: 3000 }
    })()
  }
  
  /**
   * 同步支付状态
   * @param {Object} params - 同步参数
   * @param {number} params.paymentId - 支付ID
   * @param {string} params.paymentMethod - 支付方式
   * @returns {Promise<Object>} 同步结果
   */
  static async syncPaymentStatus(params) {
    const apiCall = () => Request.post('/erp/pos/payment/sync', params)
    
    return this.createApiWrapper(apiCall, {
      context: '同步支付状态',
      showMessage: false,
      retryOptions: { maxRetries: 2, retryDelay: 2000 }
    })()
  }
  
  /**
   * 批量处理支付
   * @param {Object} params - 批量支付参数
   * @param {Array} params.payments - 支付列表
   * @param {string} params.batchId - 批次ID
   * @returns {Promise<Object>} 批量处理结果
   */
  static async batchProcessPayments(params) {
    const apiCall = () => Request.post('/erp/pos/payment/batch', {
      ...params,
      processTime: new Date().toISOString()
    })
    
    return this.createApiWrapper(apiCall, {
      context: '批量处理支付',
      retryOptions: { maxRetries: 1, retryDelay: 3000 }
    })()
  }
}