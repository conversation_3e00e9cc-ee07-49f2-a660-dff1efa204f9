/**
 * 增强版POS错误处理器
 * 
 * 提供分层错误处理功能，包括自动重试机制和用户友好的错误提示
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { message, notification, Modal } from 'ant-design-vue'
import { PosErrorHandler } from './error-handler'
import { RetryHandler } from './retry-handler'
import { PosErrorTypes, createError } from './error-types'

/**
 * 增强版POS错误处理器类
 */
export class EnhancedPosErrorHandler extends PosErrorHandler {
  
  // 错误统计
  static errorStats = {
    total: 0,
    byType: {},
    byContext: {},
    recentErrors: []
  }
  
  // 错误恢复策略
  static recoveryStrategies = new Map()
  
  // 错误监听器
  static errorListeners = new Set()
  
  /**
   * 初始化错误处理器
   */
  static initialize() {
    // 注册全局错误处理器
    window.addEventListener('error', (event) => {
      this.globalErrorHandler(event.error, 'window.error')
    })
    
    // 注册Promise拒绝处理器
    window.addEventListener('unhandledrejection', (event) => {
      this.unhandledRejectionHandler(event)
    })
    
    // 注册Vue错误处理器（如果在Vue环境中）
    if (window.Vue && window.Vue.config) {
      window.Vue.config.errorHandler = (error, vm, info) => {
        this.vueErrorHandler(error, vm, info)
      }
    }
    
    console.log('[POS] 增强版错误处理器已初始化')
  }
  
  /**
   * 包装API调用，提供增强的错误处理
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 错误处理选项
   * @returns {Function} 包装后的API函数
   */
  static wrapApiCall(apiFunction, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      context = '操作',
      onError,
      retryOptions = { maxRetries: 2, retryDelay: 1000 },
      recoveryStrategy,
      fallbackValue,
      timeout = 30000
    } = options
    
    return async (...args) => {
      const startTime = Date.now()
      let lastError = null
      
      try {
        // 添加超时控制
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), timeout)
        })
        
        // 执行API调用（带重试机制）
        const result = await Promise.race([
          RetryHandler.withRetry(() => apiFunction(...args), retryOptions),
          timeoutPromise
        ])
        
        // 记录成功调用
        this.recordSuccess(context, Date.now() - startTime)
        
        return result
        
      } catch (error) {
        lastError = error
        const processedError = this.processError(error, context)
        
        // 更新错误统计
        this.updateErrorStats(processedError, context)
        
        // 尝试错误恢复
        if (recoveryStrategy) {
          try {
            const recoveryResult = await this.executeRecoveryStrategy(
              recoveryStrategy, 
              processedError, 
              args
            )
            if (recoveryResult !== undefined) {
              console.log(`[POS] 错误恢复成功: ${context}`)
              return recoveryResult
            }
          } catch (recoveryError) {
            console.warn(`[POS] 错误恢复失败: ${context}`, recoveryError)
          }
        }
        
        // 如果有fallback值，返回fallback
        if (fallbackValue !== undefined) {
          console.log(`[POS] 使用fallback值: ${context}`)
          return fallbackValue
        }
        
        // 显示用户提示
        await this.handleUserNotification(processedError, {
          showMessage,
          showNotification,
          context
        })
        
        // 执行自定义错误处理
        if (typeof onError === 'function') {
          try {
            await onError(processedError)
          } catch (handlerError) {
            console.error('自定义错误处理器执行失败:', handlerError)
          }
        }
        
        // 通知错误监听器
        this.notifyErrorListeners(processedError, context)
        
        throw processedError
      }
    }
  }
  
  /**
   * 处理用户通知
   * @param {Object} error - 标准化错误对象
   * @param {Object} options - 通知选项
   */
  static async handleUserNotification(error, options) {
    const { showMessage, showNotification, context } = options
    
    // 根据错误类型决定通知方式
    if (error.severity === 'CRITICAL') {
      // 严重错误显示模态框
      await this.showCriticalErrorModal(error, context)
    } else if (error.userFacing) {
      if (showMessage) {
        this.showErrorMessage(error)
      }
      
      if (showNotification) {
        this.showErrorNotification(error, context)
      }
    }
  }
  
  /**
   * 显示严重错误模态框
   * @param {Object} error - 错误对象
   * @param {string} context - 上下文
   */
  static async showCriticalErrorModal(error, context) {
    return new Promise((resolve) => {
      Modal.error({
        title: '系统错误',
        content: `${context}时发生严重错误：${error.message}`,
        okText: '刷新页面',
        cancelText: '继续使用',
        onOk: () => {
          window.location.reload()
          resolve()
        },
        onCancel: () => {
          resolve()
        }
      })
    })
  }
  
  /**
   * 执行错误恢复策略
   * @param {string|Function} strategy - 恢复策略
   * @param {Object} error - 错误对象
   * @param {Array} originalArgs - 原始参数
   * @returns {*} 恢复结果
   */
  static async executeRecoveryStrategy(strategy, error, originalArgs) {
    if (typeof strategy === 'function') {
      return await strategy(error, originalArgs)
    }
    
    if (typeof strategy === 'string' && this.recoveryStrategies.has(strategy)) {
      const strategyFunction = this.recoveryStrategies.get(strategy)
      return await strategyFunction(error, originalArgs)
    }
    
    // 内置恢复策略
    switch (strategy) {
      case 'useCache':
        return this.getCachedResult(originalArgs)
      case 'useDefault':
        return this.getDefaultResult(error.type)
      case 'retry':
        // 已经在上层处理了重试
        return undefined
      default:
        return undefined
    }
  }
  
  /**
   * 获取缓存结果
   * @param {Array} args - 参数
   * @returns {*} 缓存结果
   */
  static getCachedResult(args) {
    // 这里可以实现缓存逻辑
    const cacheKey = JSON.stringify(args)
    return localStorage.getItem(`pos_cache_${cacheKey}`)
  }
  
  /**
   * 获取默认结果
   * @param {string} errorType - 错误类型
   * @returns {*} 默认结果
   */
  static getDefaultResult(errorType) {
    const defaults = {
      [PosErrorTypes.PRODUCT_NOT_FOUND]: null,
      [PosErrorTypes.INSUFFICIENT_INVENTORY]: { available: 0 },
      [PosErrorTypes.INVALID_MEMBER]: null,
      [PosErrorTypes.NETWORK_ERROR]: { offline: true }
    }
    
    return defaults[errorType]
  }
  
  /**
   * 更新错误统计
   * @param {Object} error - 错误对象
   * @param {string} context - 上下文
   */
  static updateErrorStats(error, context) {
    this.errorStats.total++
    
    // 按类型统计
    if (!this.errorStats.byType[error.type]) {
      this.errorStats.byType[error.type] = 0
    }
    this.errorStats.byType[error.type]++
    
    // 按上下文统计
    if (!this.errorStats.byContext[context]) {
      this.errorStats.byContext[context] = 0
    }
    this.errorStats.byContext[context]++
    
    // 记录最近错误
    this.errorStats.recentErrors.unshift({
      ...error,
      context,
      timestamp: new Date().toISOString()
    })
    
    // 只保留最近50个错误
    if (this.errorStats.recentErrors.length > 50) {
      this.errorStats.recentErrors = this.errorStats.recentErrors.slice(0, 50)
    }
    
    // 检查错误频率
    this.checkErrorFrequency(error.type, context)
  }
  
  /**
   * 检查错误频率
   * @param {string} errorType - 错误类型
   * @param {string} context - 上下文
   */
  static checkErrorFrequency(errorType, context) {
    const recentErrors = this.errorStats.recentErrors.filter(
      err => err.type === errorType && 
             Date.now() - new Date(err.timestamp).getTime() < 60000 // 最近1分钟
    )
    
    // 如果同类型错误在1分钟内超过5次，发出警告
    if (recentErrors.length >= 5) {
      console.warn(`[POS] 检测到高频错误: ${errorType} (${context})`)
      
      // 可以在这里实现自动降级或其他保护措施
      this.handleHighFrequencyError(errorType, context, recentErrors.length)
    }
  }
  
  /**
   * 处理高频错误
   * @param {string} errorType - 错误类型
   * @param {string} context - 上下文
   * @param {number} frequency - 频率
   */
  static handleHighFrequencyError(errorType, context, frequency) {
    // 显示系统状态警告
    notification.warning({
      message: '系统状态警告',
      description: `检测到${context}功能异常，系统正在尝试自动恢复`,
      duration: 8,
      placement: 'topRight'
    })
    
    // 可以在这里实现熔断器模式
    // 或者自动切换到离线模式等保护措施
  }
  
  /**
   * 记录成功调用
   * @param {string} context - 上下文
   * @param {number} duration - 执行时间
   */
  static recordSuccess(context, duration) {
    // 可以在这里记录成功调用的统计信息
    if (duration > 5000) {
      console.warn(`[POS] 慢查询检测: ${context} 耗时 ${duration}ms`)
    }
  }
  
  /**
   * 注册错误恢复策略
   * @param {string} name - 策略名称
   * @param {Function} strategy - 策略函数
   */
  static registerRecoveryStrategy(name, strategy) {
    this.recoveryStrategies.set(name, strategy)
  }
  
  /**
   * 添加错误监听器
   * @param {Function} listener - 监听器函数
   */
  static addErrorListener(listener) {
    this.errorListeners.add(listener)
  }
  
  /**
   * 移除错误监听器
   * @param {Function} listener - 监听器函数
   */
  static removeErrorListener(listener) {
    this.errorListeners.delete(listener)
  }
  
  /**
   * 通知错误监听器
   * @param {Object} error - 错误对象
   * @param {string} context - 上下文
   */
  static notifyErrorListeners(error, context) {
    this.errorListeners.forEach(listener => {
      try {
        listener(error, context)
      } catch (listenerError) {
        console.error('错误监听器执行失败:', listenerError)
      }
    })
  }
  
  /**
   * Vue错误处理器
   * @param {Error} error - 错误对象
   * @param {Object} vm - Vue实例
   * @param {string} info - 错误信息
   */
  static vueErrorHandler(error, vm, info) {
    const componentName = vm?.$options?.name || 'Unknown'
    const processedError = this.processError(error, `Vue组件 ${componentName}`)
    
    // 记录Vue特定的错误信息
    processedError.details = {
      ...processedError.details,
      componentName,
      errorInfo: info,
      vueError: true
    }
    
    this.updateErrorStats(processedError, `Vue组件 ${componentName}`)
    this.logError(processedError, `Vue组件 ${componentName}`)
    
    // 对于严重的Vue错误，显示用户提示
    if (processedError.severity === 'HIGH' || processedError.severity === 'CRITICAL') {
      message.error('页面组件出现错误，请刷新页面')
    }
  }
  
  /**
   * 获取错误统计信息
   * @returns {Object} 错误统计
   */
  static getErrorStats() {
    return {
      ...this.errorStats,
      // 计算错误率
      errorRate: this.calculateErrorRate(),
      // 最常见的错误类型
      mostCommonErrorType: this.getMostCommonErrorType(),
      // 最常见的错误上下文
      mostCommonContext: this.getMostCommonContext()
    }
  }
  
  /**
   * 计算错误率
   * @returns {number} 错误率
   */
  static calculateErrorRate() {
    const recentErrors = this.errorStats.recentErrors.filter(
      err => Date.now() - new Date(err.timestamp).getTime() < 300000 // 最近5分钟
    )
    
    // 这里需要总请求数来计算准确的错误率
    // 暂时返回最近错误数量
    return recentErrors.length
  }
  
  /**
   * 获取最常见的错误类型
   * @returns {string} 错误类型
   */
  static getMostCommonErrorType() {
    const types = Object.entries(this.errorStats.byType)
    if (types.length === 0) return null
    
    return types.reduce((a, b) => a[1] > b[1] ? a : b)[0]
  }
  
  /**
   * 获取最常见的错误上下文
   * @returns {string} 上下文
   */
  static getMostCommonContext() {
    const contexts = Object.entries(this.errorStats.byContext)
    if (contexts.length === 0) return null
    
    return contexts.reduce((a, b) => a[1] > b[1] ? a : b)[0]
  }
  
  /**
   * 清理错误统计
   */
  static clearErrorStats() {
    this.errorStats = {
      total: 0,
      byType: {},
      byContext: {},
      recentErrors: []
    }
  }
  
  /**
   * 导出错误报告
   * @returns {Object} 错误报告
   */
  static exportErrorReport() {
    return {
      timestamp: new Date().toISOString(),
      stats: this.getErrorStats(),
      systemInfo: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString()
      }
    }
  }
}