<template>
  <div class="function-button-item" :class="{ 'checkout-button': isCheckout }">
    <div 
      class="function-card" 
      :class="cardClass"
      @click="handleClick"
      :disabled="disabled"
    >
      <div class="card-content">
        <div class="card-icon" v-if="icon">
          <icon-font :iconClass="icon" />
        </div>
        <div class="card-title">{{ title }}</div>
        <div class="card-subtitle" v-if="subtitle">{{ subtitle }}</div>
      </div>
      <div class="card-badge" v-if="badge && badge > 0">
        {{ badge }}
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义组件名称
defineOptions({
  name: 'FunctionButton'
})

// 定义属性
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  badge: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'normal',
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  isCheckout: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['click'])

// 计算卡片样式类
const cardClass = computed(() => {
  return [
    `function-card-${props.type}`,
    `function-card-${props.size}`,
    {
      'function-card-disabled': props.disabled,
      'function-card-checkout': props.isCheckout
    }
  ]
})

/**
 * 处理点击事件
 */
const handleClick = () => {
  if (!props.disabled) {
    emit('click')
  }
}
</script>

<script>
import { computed } from 'vue'
</script>

<style scoped>
.function-button-item {
  position: relative;
  max-width: 100%;
}

.function-button-item.checkout-button {
  margin-top: auto;
}

.function-card {
  position: relative;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.function-card:hover:not(.function-card-disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.function-card-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.card-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.card-subtitle {
  font-size: 12px;
  opacity: 0.8;
  text-align: center;
}

.card-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ff4d4f;
  color: #fff;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  border: 2px solid #fff;
}

/* 不同类型的样式 */
.function-card-default {
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  color: #262626;
}

.function-card-default:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #e8e8e8 0%, #d9d9d9 100%);
}

.function-card-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.function-card-primary:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.function-card-success {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  color: #fff;
}

.function-card-success:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #00a085 0%, #00b7b3 100%);
}

.function-card-warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  color: #fff;
}

.function-card-warning:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #f39c12 0%, #d63031 100%);
}

.function-card-danger {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: #fff;
}

.function-card-danger:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #e84393 0%, #d63031 100%);
}

/* 不同尺寸的样式 */
.function-card-small {
  min-height: 50px;
  padding: 8px 12px;
}

.function-card-small .card-title {
  font-size: 12px;
}

.function-card-small .card-subtitle {
  font-size: 11px;
}

.function-card-small .card-icon {
  font-size: 16px;
}

.function-card-large {
  min-height: 80px;
  padding: 16px 20px;
}

.function-card-large .card-title {
  font-size: 16px;
}

.function-card-large .card-subtitle {
  font-size: 13px;
}

.function-card-large .card-icon {
  font-size: 24px;
}

.function-card-checkout {
  min-height: 90px;
}

.function-card-checkout .card-title {
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-card {
    min-height: 50px;
    padding: 8px 12px;
  }

  .card-title {
    font-size: 12px;
  }

  .card-subtitle {
    font-size: 11px;
  }

  .card-icon {
    font-size: 16px;
  }

  .function-card-checkout {
    min-height: 60px;
  }

  .function-card-checkout .card-title {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .function-card {
    min-height: 45px;
    padding: 6px 10px;
  }

  .card-title {
    font-size: 11px;
  }

  .card-subtitle {
    font-size: 10px;
  }

  .card-icon {
    font-size: 14px;
  }

  .function-card-checkout {
    min-height: 50px;
  }

  .function-card-checkout .card-title {
    font-size: 12px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .function-card:hover:not(.function-card-disabled) {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .function-card {
    transition: none;
  }

  .function-card:hover:not(.function-card-disabled) {
    transform: none;
  }
}
</style>