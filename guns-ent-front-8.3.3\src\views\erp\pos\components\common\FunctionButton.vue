<template>
  <div class="function-button-item" :class="{ 'checkout-button': isCheckout }">
    <div 
      class="function-card" 
      :class="cardClass"
      @click="handleClick"
      :disabled="disabled"
    >
      <div class="card-content">
        <div class="card-icon" v-if="icon">
          <icon-font :iconClass="icon" />
        </div>
        <div class="card-title">{{ title }}</div>
        <div class="card-subtitle" v-if="subtitle">{{ subtitle }}</div>
      </div>
      <div class="card-badge" v-if="badge && badge > 0">
        {{ badge }}
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义组件名称
defineOptions({
  name: 'FunctionButton'
})

// 定义属性
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  badge: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'normal',
    validator: (value) => ['small', 'normal', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  isCheckout: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['click'])

// 计算卡片样式类
const cardClass = computed(() => {
  return [
    `function-card-${props.type}`,
    `function-card-${props.size}`,
    {
      'function-card-disabled': props.disabled,
      'function-card-checkout': props.isCheckout
    }
  ]
})

/**
 * 处理点击事件
 */
const handleClick = () => {
  if (!props.disabled) {
    emit('click')
  }
}
</script>

<script>
import { computed } from 'vue'
</script>

<style scoped>
.function-button-item {
  position: relative;
  max-width: 100%;
}

.function-button-item.checkout-button {
  margin-top: auto;
}

.function-card {
  position: relative;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 20px; /* 进一步减小按钮高度 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.function-card:hover:not(.function-card-disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.function-card-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.card-icon {
  font-size: 18px; /* 调整图标大小 */
  margin-bottom: 4px;
}

.card-title {
  font-size: 16px; /* 增大字体 */
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.card-subtitle {
  font-size: 12px;
  opacity: 0.8;
  text-align: center;
}

.card-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ff4d4f;
  color: #fff;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  border: 2px solid #fff;
}

/* 统一的按钮样式 */
.function-card-default,
.function-card-primary,
.function-card-success,
.function-card-warning,
.function-card-danger {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: #fff;
}

.function-card-default:hover:not(.function-card-disabled),
.function-card-primary:hover:not(.function-card-disabled),
.function-card-success:hover:not(.function-card-disabled),
.function-card-warning:hover:not(.function-card-disabled),
.function-card-danger:hover:not(.function-card-disabled) {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

/* 不同尺寸的样式 */
.function-card-small {
  min-height: 40px;
  padding: 6px 10px;
}

.function-card-small .card-title {
  font-size: 14px;
}

.function-card-small .card-subtitle {
  font-size: 11px;
}

.function-card-small .card-icon {
  font-size: 16px;
}

.function-card-large {
  min-height: 60px;
  padding: 12px 16px;
}

.function-card-large .card-title {
  font-size: 18px;
}

.function-card-large .card-subtitle {
  font-size: 13px;
}

.function-card-large .card-icon {
  font-size: 20px;
}

.function-card-checkout {
  min-height: 90px;
}

.function-card-checkout .card-title {
  font-size: 18px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .function-card {
    min-height: 40px;
    padding: 6px 10px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-subtitle {
    font-size: 11px;
  }

  .card-icon {
    font-size: 16px;
  }

  .function-card-checkout {
    min-height: 50px;
  }

  .function-card-checkout .card-title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .function-card {
    min-height: 35px;
    padding: 4px 8px;
  }

  .card-title {
    font-size: 12px;
  }

  .card-subtitle {
    font-size: 10px;
  }

  .card-icon {
    font-size: 14px;
  }

  .function-card-checkout {
    min-height: 45px;
  }

  .function-card-checkout .card-title {
    font-size: 14px;
  }
}

/* 触屏设备优化 */
@media (hover: none) {
  .function-card:hover:not(.function-card-disabled) {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .function-card {
    transition: none;
  }

  .function-card:hover:not(.function-card-disabled) {
    transform: none;
  }
}
</style>