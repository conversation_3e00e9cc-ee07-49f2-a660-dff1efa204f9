/**
 * 购物车项组件单元测试
 * 
 * 测试购物车项组件的渲染和交互功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import CartItem from '../CartItem.vue'

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  InputNumber: {
    name: 'AInputNumber',
    template: '<input :value="value" @input="$emit(\'update:value\', $event.target.value)" />'
  },
  Button: {
    name: 'AButton',
    template: '<button @click="$emit(\'click\')"><slot /></button>'
  },
  Popconfirm: {
    name: 'APopconfirm',
    template: '<div @click="$emit(\'confirm\')"><slot /></div>'
  }
}))

describe('CartItem', () => {
  let wrapper
  let pinia
  
  const mockItem = {
    id: 'P001',
    name: '红苹果',
    price: 5.5,
    quantity: 2,
    subtotal: 11,
    unit: '斤',
    specifications: '新鲜红苹果',
    image: '/images/apple.jpg'
  }
  
  const createWrapper = (props = {}) => {
    pinia = createPinia()
    return mount(CartItem, {
      props: {
        item: mockItem,
        ...props
      },
      global: {
        plugins: [pinia],
        stubs: {
          'a-input-number': true,
          'a-button': true,
          'a-popconfirm': true
        }
      }
    })
  }
  
  beforeEach(() => {
    vi.clearAllMocks()
  })
  
  describe('组件渲染', () => {
    it('应该正确渲染商品信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.cart-item').exists()).toBe(true)
      expect(wrapper.text()).toContain('红苹果')
      expect(wrapper.text()).toContain('¥5.50')
      expect(wrapper.text()).toContain('¥11.00')
      expect(wrapper.text()).toContain('斤')
    })
    
    it('应该显示商品图片', () => {
      wrapper = createWrapper()
      
      const image = wrapper.find('.item-image img')
      expect(image.exists()).toBe(true)
      expect(image.attributes('src')).toBe('/images/apple.jpg')
      expect(image.attributes('alt')).toBe('红苹果')
    })
    
    it('应该在没有图片时显示默认图片', () => {
      const itemWithoutImage = { ...mockItem, image: null }
      wrapper = createWrapper({ item: itemWithoutImage })
      
      const image = wrapper.find('.item-image img')
      expect(image.attributes('src')).toContain('default-product.png')
    })
    
    it('应该显示商品规格信息', () => {
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('新鲜红苹果')
    })
    
    it('应该在没有规格时隐藏规格信息', () => {
      const itemWithoutSpecs = { ...mockItem, specifications: null }
      wrapper = createWrapper({ item: itemWithoutSpecs })
      
      expect(wrapper.find('.item-specifications').exists()).toBe(false)
    })
  })
  
  describe('数量控制', () => {
    it('应该显示数量输入框', () => {
      wrapper = createWrapper()
      
      const quantityInput = wrapper.find('.quantity-control a-input-number-stub')
      expect(quantityInput.exists()).toBe(true)
      expect(quantityInput.attributes('value')).toBe('2')
    })
    
    it('应该在数量变化时触发事件', async () => {
      wrapper = createWrapper()
      
      const quantityInput = wrapper.find('.quantity-control a-input-number-stub')
      await quantityInput.vm.$emit('update:value', 3)
      
      expect(wrapper.emitted('quantity-change')).toBeTruthy()
      expect(wrapper.emitted('quantity-change')[0]).toEqual([mockItem.id, 3])
    })
    
    it('应该限制数量的最小值和最大值', () => {
      wrapper = createWrapper()
      
      const quantityInput = wrapper.find('.quantity-control a-input-number-stub')
      expect(quantityInput.attributes('min')).toBe('0.001')
      expect(quantityInput.attributes('max')).toBe('999.999')
      expect(quantityInput.attributes('step')).toBe('0.001')
    })
    
    it('应该支持快速增减按钮', async () => {
      wrapper = createWrapper()
      
      const decreaseBtn = wrapper.find('.quantity-decrease')
      const increaseBtn = wrapper.find('.quantity-increase')
      
      await decreaseBtn.trigger('click')
      expect(wrapper.emitted('quantity-change')).toBeTruthy()
      expect(wrapper.emitted('quantity-change')[0]).toEqual([mockItem.id, 1])
      
      await increaseBtn.trigger('click')
      expect(wrapper.emitted('quantity-change')[1]).toEqual([mockItem.id, 3])
    })
  })
  
  describe('删除功能', () => {
    it('应该显示删除按钮', () => {
      wrapper = createWrapper()
      
      const deleteBtn = wrapper.find('.delete-btn')
      expect(deleteBtn.exists()).toBe(true)
    })
    
    it('应该在点击删除时显示确认对话框', async () => {
      wrapper = createWrapper()
      
      const popconfirm = wrapper.find('a-popconfirm-stub')
      expect(popconfirm.exists()).toBe(true)
      expect(popconfirm.attributes('title')).toContain('确定要删除')
    })
    
    it('应该在确认删除时触发删除事件', async () => {
      wrapper = createWrapper()
      
      const popconfirm = wrapper.find('a-popconfirm-stub')
      await popconfirm.vm.$emit('confirm')
      
      expect(wrapper.emitted('remove')).toBeTruthy()
      expect(wrapper.emitted('remove')[0]).toEqual([mockItem.id])
    })
  })
  
  describe('价格显示', () => {
    it('应该正确格式化单价', () => {
      wrapper = createWrapper()
      
      const priceElement = wrapper.find('.item-price')
      expect(priceElement.text()).toBe('¥5.50/斤')
    })
    
    it('应该正确格式化小计', () => {
      wrapper = createWrapper()
      
      const subtotalElement = wrapper.find('.item-subtotal')
      expect(subtotalElement.text()).toBe('¥11.00')
    })
    
    it('应该在有折扣时显示原价和折后价', () => {
      const discountedItem = {
        ...mockItem,
        originalPrice: 6.0,
        discountAmount: 1.0
      }
      wrapper = createWrapper({ item: discountedItem })
      
      expect(wrapper.find('.original-price').exists()).toBe(true)
      expect(wrapper.find('.discount-amount').exists()).toBe(true)
    })
  })
  
  describe('状态显示', () => {
    it('应该在库存不足时显示警告', () => {
      const lowStockItem = {
        ...mockItem,
        stock: 1,
        quantity: 2
      }
      wrapper = createWrapper({ item: lowStockItem })
      
      expect(wrapper.find('.stock-warning').exists()).toBe(true)
      expect(wrapper.text()).toContain('库存不足')
    })
    
    it('应该在商品下架时显示提示', () => {
      const unavailableItem = {
        ...mockItem,
        status: 'UNAVAILABLE'
      }
      wrapper = createWrapper({ item: unavailableItem })
      
      expect(wrapper.find('.unavailable-warning').exists()).toBe(true)
      expect(wrapper.text()).toContain('商品已下架')
    })
  })
  
  describe('交互行为', () => {
    it('应该在鼠标悬停时高亮显示', async () => {
      wrapper = createWrapper()
      
      const cartItem = wrapper.find('.cart-item')
      await cartItem.trigger('mouseenter')
      
      expect(cartItem.classes()).toContain('cart-item--hover')
      
      await cartItem.trigger('mouseleave')
      expect(cartItem.classes()).not.toContain('cart-item--hover')
    })
    
    it('应该支持键盘导航', async () => {
      wrapper = createWrapper()
      
      const cartItem = wrapper.find('.cart-item')
      await cartItem.trigger('keydown', { key: 'Enter' })
      
      expect(wrapper.emitted('item-click')).toBeTruthy()
    })
  })
  
  describe('可访问性', () => {
    it('应该有正确的ARIA标签', () => {
      wrapper = createWrapper()
      
      const cartItem = wrapper.find('.cart-item')
      expect(cartItem.attributes('role')).toBe('listitem')
      expect(cartItem.attributes('aria-label')).toContain('红苹果')
    })
    
    it('应该为数量输入框提供标签', () => {
      wrapper = createWrapper()
      
      const quantityInput = wrapper.find('.quantity-control a-input-number-stub')
      expect(quantityInput.attributes('aria-label')).toBe('商品数量')
    })
    
    it('应该为删除按钮提供描述', () => {
      wrapper = createWrapper()
      
      const deleteBtn = wrapper.find('.delete-btn')
      expect(deleteBtn.attributes('aria-label')).toContain('删除红苹果')
    })
  })
  
  describe('边界情况', () => {
    it('应该处理空的商品数据', () => {
      wrapper = createWrapper({ item: null })
      
      expect(wrapper.find('.cart-item').exists()).toBe(false)
    })
    
    it('应该处理缺少必要字段的商品', () => {
      const incompleteItem = {
        id: 'P001',
        name: '测试商品'
        // 缺少price, quantity等字段
      }
      
      expect(() => {
        wrapper = createWrapper({ item: incompleteItem })
      }).not.toThrow()
    })
    
    it('应该处理极大的数量值', () => {
      const largeQuantityItem = {
        ...mockItem,
        quantity: 999.999
      }
      wrapper = createWrapper({ item: largeQuantityItem })
      
      const quantityInput = wrapper.find('.quantity-control a-input-number-stub')
      expect(quantityInput.attributes('value')).toBe('999.999')
    })
    
    it('应该处理极小的数量值', () => {
      const smallQuantityItem = {
        ...mockItem,
        quantity: 0.001
      }
      wrapper = createWrapper({ item: smallQuantityItem })
      
      const quantityInput = wrapper.find('.quantity-control a-input-number-stub')
      expect(quantityInput.attributes('value')).toBe('0.001')
    })
  })
  
  describe('性能优化', () => {
    it('应该在商品数据未变化时避免重新渲染', async () => {
      wrapper = createWrapper()
      
      const renderSpy = vi.spyOn(wrapper.vm, '$forceUpdate')
      
      // 设置相同的商品数据
      await wrapper.setProps({ item: { ...mockItem } })
      
      expect(renderSpy).not.toHaveBeenCalled()
    })
    
    it('应该正确处理商品数据的深度变化', async () => {
      wrapper = createWrapper()
      
      const updatedItem = {
        ...mockItem,
        quantity: 3,
        subtotal: 16.5
      }
      
      await wrapper.setProps({ item: updatedItem })
      
      expect(wrapper.find('.quantity-control a-input-number-stub').attributes('value')).toBe('3')
      expect(wrapper.find('.item-subtotal').text()).toBe('¥16.50')
    })
  })
})