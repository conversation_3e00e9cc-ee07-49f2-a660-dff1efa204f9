import{r as at,a as M,c as J,b as G,ah as lt,F as Et,h as Nt,L as ge,k as U,e as qt,f as L,w as R,d as E,t as We,B as ve,bk as ye,bN as He,bO as ze,at as _t,X as Zt,aK as Qt,o as Pe,bs as Xe,a2 as Ye,aH as Ue,M as $e,_ as Ve,b3 as je,s as Fe,bh as Kt,bi as Ge,g as qe,bJ as Jt,m as Rt,v as Ze,bK as Qe,G as Ke}from"./index-18a1ea24.js";/* empty css              */import{F as Je,a as ti}from"./FileApi-418f4d35.js";/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present Chen <PERSON>
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */function te(a,t){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(a);t&&(e=e.filter(function(o){return Object.getOwnPropertyDescriptor(a,o).enumerable})),i.push.apply(i,e)}return i}function we(a){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?te(Object(i),!0).forEach(function(e){ri(a,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):te(Object(i)).forEach(function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(i,e))})}return a}function ei(a,t){if(typeof a!="object"||!a)return a;var i=a[Symbol.toPrimitive];if(i!==void 0){var e=i.call(a,t||"default");if(typeof e!="object")return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(a)}function be(a){var t=ei(a,"string");return typeof t=="symbol"?t:t+""}function kt(a){"@babel/helpers - typeof";return kt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kt(a)}function ii(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function ee(a,t){for(var i=0;i<t.length;i++){var e=t[i];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,be(e.key),e)}}function ai(a,t,i){return t&&ee(a.prototype,t),i&&ee(a,i),Object.defineProperty(a,"prototype",{writable:!1}),a}function ri(a,t,i){return t=be(t),t in a?Object.defineProperty(a,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):a[t]=i,a}function xe(a){return oi(a)||ni(a)||si(a)||hi()}function oi(a){if(Array.isArray(a))return Bt(a)}function ni(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function si(a,t){if(a){if(typeof a=="string")return Bt(a,t);var i=Object.prototype.toString.call(a).slice(8,-1);if(i==="Object"&&a.constructor&&(i=a.constructor.name),i==="Map"||i==="Set")return Array.from(a);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Bt(a,t)}}function Bt(a,t){(t==null||t>a.length)&&(t=a.length);for(var i=0,e=new Array(t);i<t;i++)e[i]=a[i];return e}function hi(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var Tt=typeof window<"u"&&typeof window.document<"u",Q=Tt?window:{},Ut=Tt&&Q.document.documentElement?"ontouchstart"in Q.document.documentElement:!1,$t=Tt?"PointerEvent"in Q:!1,O="cropper",Vt="all",De="crop",_e="move",Me="zoom",rt="e",ot="w",ct="s",tt="n",gt="ne",vt="nw",yt="se",wt="sw",Lt="".concat(O,"-crop"),ie="".concat(O,"-disabled"),V="".concat(O,"-hidden"),ae="".concat(O,"-hide"),ci="".concat(O,"-invisible"),Ot="".concat(O,"-modal"),It="".concat(O,"-move"),xt="".concat(O,"Action"),Mt="".concat(O,"Preview"),jt="crop",Ce="move",Ee="none",Wt="crop",Ht="cropend",zt="cropmove",Pt="cropstart",re="dblclick",li=Ut?"touchstart":"mousedown",pi=Ut?"touchmove":"mousemove",di=Ut?"touchend touchcancel":"mouseup",oe=$t?"pointerdown":li,ne=$t?"pointermove":pi,se=$t?"pointerup pointercancel":di,he="ready",ce="resize",le="wheel",Xt="zoom",pe="image/jpeg",ui=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,fi=/^data:/,mi=/^data:image\/jpeg;base64,/,gi=/^img|canvas$/i,Oe=200,Ne=100,de={viewMode:0,dragMode:jt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:Oe,minContainerHeight:Ne,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},vi='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>',yi=Number.isNaN||Q.isNaN;function v(a){return typeof a=="number"&&!yi(a)}var ue=function(t){return t>0&&t<1/0};function St(a){return typeof a>"u"}function nt(a){return kt(a)==="object"&&a!==null}var wi=Object.prototype.hasOwnProperty;function pt(a){if(!nt(a))return!1;try{var t=a.constructor,i=t.prototype;return t&&i&&wi.call(i,"isPrototypeOf")}catch(e){return!1}}function $(a){return typeof a=="function"}var bi=Array.prototype.slice;function Te(a){return Array.from?Array.from(a):bi.call(a)}function A(a,t){return a&&$(t)&&(Array.isArray(a)||v(a.length)?Te(a).forEach(function(i,e){t.call(a,i,e,a)}):nt(a)&&Object.keys(a).forEach(function(i){t.call(a,a[i],i,a)})),a}var N=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),o=1;o<i;o++)e[o-1]=arguments[o];return nt(t)&&e.length>0&&e.forEach(function(r){nt(r)&&Object.keys(r).forEach(function(n){t[n]=r[n]})}),t},xi=/\.\d*(?:0|9){12}\d*$/;function ut(a){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return xi.test(a)?Math.round(a*t)/t:a}var Di=/^width|height|left|top|marginLeft|marginTop$/;function et(a,t){var i=a.style;A(t,function(e,o){Di.test(o)&&v(e)&&(e="".concat(e,"px")),i[o]=e})}function _i(a,t){return a.classList?a.classList.contains(t):a.className.indexOf(t)>-1}function B(a,t){if(t){if(v(a.length)){A(a,function(e){B(e,t)});return}if(a.classList){a.classList.add(t);return}var i=a.className.trim();i?i.indexOf(t)<0&&(a.className="".concat(i," ").concat(t)):a.className=t}}function Z(a,t){if(t){if(v(a.length)){A(a,function(i){Z(i,t)});return}if(a.classList){a.classList.remove(t);return}a.className.indexOf(t)>=0&&(a.className=a.className.replace(t,""))}}function dt(a,t,i){if(t){if(v(a.length)){A(a,function(e){dt(e,t,i)});return}i?B(a,t):Z(a,t)}}var Mi=/([a-z\d])([A-Z])/g;function Ft(a){return a.replace(Mi,"$1-$2").toLowerCase()}function Yt(a,t){return nt(a[t])?a[t]:a.dataset?a.dataset[t]:a.getAttribute("data-".concat(Ft(t)))}function Dt(a,t,i){nt(i)?a[t]=i:a.dataset?a.dataset[t]=i:a.setAttribute("data-".concat(Ft(t)),i)}function Ci(a,t){if(nt(a[t]))try{delete a[t]}catch(i){a[t]=void 0}else if(a.dataset)try{delete a.dataset[t]}catch(i){a.dataset[t]=void 0}else a.removeAttribute("data-".concat(Ft(t)))}var Re=/\s\s*/,Se=function(){var a=!1;if(Tt){var t=!1,i=function(){},e=Object.defineProperty({},"once",{get:function(){return a=!0,t},set:function(r){t=r}});Q.addEventListener("test",i,e),Q.removeEventListener("test",i,e)}return a}();function q(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=i;t.trim().split(Re).forEach(function(r){if(!Se){var n=a.listeners;n&&n[r]&&n[r][i]&&(o=n[r][i],delete n[r][i],Object.keys(n[r]).length===0&&delete n[r],Object.keys(n).length===0&&delete a.listeners)}a.removeEventListener(r,o,e)})}function F(a,t,i){var e=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=i;t.trim().split(Re).forEach(function(r){if(e.once&&!Se){var n=a.listeners,s=n===void 0?{}:n;o=function(){delete s[r][i],a.removeEventListener(r,o,e);for(var l=arguments.length,c=new Array(l),p=0;p<l;p++)c[p]=arguments[p];i.apply(a,c)},s[r]||(s[r]={}),s[r][i]&&a.removeEventListener(r,s[r][i],e),s[r][i]=o,a.listeners=s}a.addEventListener(r,o,e)})}function ft(a,t,i){var e;return $(Event)&&$(CustomEvent)?e=new CustomEvent(t,{detail:i,bubbles:!0,cancelable:!0}):(e=document.createEvent("CustomEvent"),e.initCustomEvent(t,!0,!0,i)),a.dispatchEvent(e)}function Ae(a){var t=a.getBoundingClientRect();return{left:t.left+(window.pageXOffset-document.documentElement.clientLeft),top:t.top+(window.pageYOffset-document.documentElement.clientTop)}}var At=Q.location,Ei=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function fe(a){var t=a.match(Ei);return t!==null&&(t[1]!==At.protocol||t[2]!==At.hostname||t[3]!==At.port)}function me(a){var t="timestamp=".concat(new Date().getTime());return a+(a.indexOf("?")===-1?"?":"&")+t}function bt(a){var t=a.rotate,i=a.scaleX,e=a.scaleY,o=a.translateX,r=a.translateY,n=[];v(o)&&o!==0&&n.push("translateX(".concat(o,"px)")),v(r)&&r!==0&&n.push("translateY(".concat(r,"px)")),v(t)&&t!==0&&n.push("rotate(".concat(t,"deg)")),v(i)&&i!==1&&n.push("scaleX(".concat(i,")")),v(e)&&e!==1&&n.push("scaleY(".concat(e,")"));var s=n.length?n.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Oi(a){var t=we({},a),i=0;return A(a,function(e,o){delete t[o],A(t,function(r){var n=Math.abs(e.startX-r.startX),s=Math.abs(e.startY-r.startY),h=Math.abs(e.endX-r.endX),l=Math.abs(e.endY-r.endY),c=Math.sqrt(n*n+s*s),p=Math.sqrt(h*h+l*l),d=(p-c)/c;Math.abs(d)>Math.abs(i)&&(i=d)})}),i}function Ct(a,t){var i=a.pageX,e=a.pageY,o={endX:i,endY:e};return t?o:we({startX:i,startY:e},o)}function Ni(a){var t=0,i=0,e=0;return A(a,function(o){var r=o.startX,n=o.startY;t+=r,i+=n,e+=1}),t/=e,i/=e,{pageX:t,pageY:i}}function it(a){var t=a.aspectRatio,i=a.height,e=a.width,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=ue(e),n=ue(i);if(r&&n){var s=i*t;o==="contain"&&s>e||o==="cover"&&s<e?i=e/t:e=i*t}else r?i=e/t:n&&(e=i*t);return{width:e,height:i}}function Ti(a){var t=a.width,i=a.height,e=a.degree;if(e=Math.abs(e)%180,e===90)return{width:i,height:t};var o=e%90*Math.PI/180,r=Math.sin(o),n=Math.cos(o),s=t*n+i*r,h=t*r+i*n;return e>90?{width:h,height:s}:{width:s,height:h}}function Ri(a,t,i,e){var o=t.aspectRatio,r=t.naturalWidth,n=t.naturalHeight,s=t.rotate,h=s===void 0?0:s,l=t.scaleX,c=l===void 0?1:l,p=t.scaleY,d=p===void 0?1:p,m=i.aspectRatio,f=i.naturalWidth,x=i.naturalHeight,g=e.fillColor,C=g===void 0?"transparent":g,b=e.imageSmoothingEnabled,_=b===void 0?!0:b,X=e.imageSmoothingQuality,w=X===void 0?"low":X,u=e.maxWidth,y=u===void 0?1/0:u,T=e.maxHeight,I=T===void 0?1/0:T,Y=e.minWidth,D=Y===void 0?0:Y,S=e.minHeight,k=S===void 0?0:S,j=document.createElement("canvas"),W=j.getContext("2d"),K=it({aspectRatio:m,width:y,height:I}),st=it({aspectRatio:m,width:D,height:k},"cover"),mt=Math.min(K.width,Math.max(st.width,f)),ht=Math.min(K.height,Math.max(st.height,x)),H=it({aspectRatio:o,width:y,height:I}),z=it({aspectRatio:o,width:D,height:k},"cover"),P=Math.min(H.width,Math.max(z.width,r)),Gt=Math.min(H.height,Math.max(z.height,n)),Le=[-P/2,-Gt/2,P,Gt];return j.width=ut(mt),j.height=ut(ht),W.fillStyle=C,W.fillRect(0,0,mt,ht),W.save(),W.translate(mt/2,ht/2),W.rotate(h*Math.PI/180),W.scale(c,d),W.imageSmoothingEnabled=_,W.imageSmoothingQuality=w,W.drawImage.apply(W,[a].concat(xe(Le.map(function(Ie){return Math.floor(ut(Ie))})))),W.restore(),j}var ke=String.fromCharCode;function Si(a,t,i){var e="";i+=t;for(var o=t;o<i;o+=1)e+=ke(a.getUint8(o));return e}var Ai=/^data:.*,/;function ki(a){var t=a.replace(Ai,""),i=atob(t),e=new ArrayBuffer(i.length),o=new Uint8Array(e);return A(o,function(r,n){o[n]=i.charCodeAt(n)}),e}function Bi(a,t){for(var i=[],e=8192,o=new Uint8Array(a);o.length>0;)i.push(ke.apply(null,Te(o.subarray(0,e)))),o=o.subarray(e);return"data:".concat(t,";base64,").concat(btoa(i.join("")))}function Li(a){var t=new DataView(a),i;try{var e,o,r;if(t.getUint8(0)===255&&t.getUint8(1)===216)for(var n=t.byteLength,s=2;s+1<n;){if(t.getUint8(s)===255&&t.getUint8(s+1)===225){o=s;break}s+=1}if(o){var h=o+4,l=o+10;if(Si(t,h,4)==="Exif"){var c=t.getUint16(l);if(e=c===18761,(e||c===19789)&&t.getUint16(l+2,e)===42){var p=t.getUint32(l+4,e);p>=8&&(r=l+p)}}}if(r){var d=t.getUint16(r,e),m,f;for(f=0;f<d;f+=1)if(m=r+f*12+2,t.getUint16(m,e)===274){m+=8,i=t.getUint16(m,e),t.setUint16(m,1,e);break}}}catch(x){i=1}return i}function Ii(a){var t=0,i=1,e=1;switch(a){case 2:i=-1;break;case 3:t=-180;break;case 4:e=-1;break;case 5:t=90,e=-1;break;case 6:t=90;break;case 7:t=90,i=-1;break;case 8:t=-90;break}return{rotate:t,scaleX:i,scaleY:e}}var Wi={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,o=this.cropper,r=Number(i.minContainerWidth),n=Number(i.minContainerHeight);B(o,V),Z(t,V);var s={width:Math.max(e.offsetWidth,r>=0?r:Oe),height:Math.max(e.offsetHeight,n>=0?n:Ne)};this.containerData=s,et(o,{width:s.width,height:s.height}),B(t,V),Z(o,V)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,o=Math.abs(i.rotate)%180===90,r=o?i.naturalHeight:i.naturalWidth,n=o?i.naturalWidth:i.naturalHeight,s=r/n,h=t.width,l=t.height;t.height*s>t.width?e===3?h=t.height*s:l=t.width/s:e===3?l=t.width/s:h=t.height*s;var c={aspectRatio:s,naturalWidth:r,naturalHeight:n,width:h,height:l};this.canvasData=c,this.limited=e===1||e===2,this.limitCanvas(!0,!0),c.width=Math.min(Math.max(c.width,c.minWidth),c.maxWidth),c.height=Math.min(Math.max(c.height,c.minHeight),c.maxHeight),c.left=(t.width-c.width)/2,c.top=(t.height-c.height)/2,c.oldLeft=c.left,c.oldTop=c.top,this.initialCanvasData=N({},c)},limitCanvas:function(t,i){var e=this.options,o=this.containerData,r=this.canvasData,n=this.cropBoxData,s=e.viewMode,h=r.aspectRatio,l=this.cropped&&n;if(t){var c=Number(e.minCanvasWidth)||0,p=Number(e.minCanvasHeight)||0;s>1?(c=Math.max(c,o.width),p=Math.max(p,o.height),s===3&&(p*h>c?c=p*h:p=c/h)):s>0&&(c?c=Math.max(c,l?n.width:0):p?p=Math.max(p,l?n.height:0):l&&(c=n.width,p=n.height,p*h>c?c=p*h:p=c/h));var d=it({aspectRatio:h,width:c,height:p});c=d.width,p=d.height,r.minWidth=c,r.minHeight=p,r.maxWidth=1/0,r.maxHeight=1/0}if(i)if(s>(l?0:1)){var m=o.width-r.width,f=o.height-r.height;r.minLeft=Math.min(0,m),r.minTop=Math.min(0,f),r.maxLeft=Math.max(0,m),r.maxTop=Math.max(0,f),l&&this.limited&&(r.minLeft=Math.min(n.left,n.left+(n.width-r.width)),r.minTop=Math.min(n.top,n.top+(n.height-r.height)),r.maxLeft=n.left,r.maxTop=n.top,s===2&&(r.width>=o.width&&(r.minLeft=Math.min(0,m),r.maxLeft=Math.max(0,m)),r.height>=o.height&&(r.minTop=Math.min(0,f),r.maxTop=Math.max(0,f))))}else r.minLeft=-r.width,r.minTop=-r.height,r.maxLeft=o.width,r.maxTop=o.height},renderCanvas:function(t,i){var e=this.canvasData,o=this.imageData;if(i){var r=Ti({width:o.naturalWidth*Math.abs(o.scaleX||1),height:o.naturalHeight*Math.abs(o.scaleY||1),degree:o.rotate||0}),n=r.width,s=r.height,h=e.width*(n/e.naturalWidth),l=e.height*(s/e.naturalHeight);e.left-=(h-e.width)/2,e.top-=(l-e.height)/2,e.width=h,e.height=l,e.aspectRatio=n/s,e.naturalWidth=n,e.naturalHeight=s,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,et(this.canvas,N({width:e.width,height:e.height},bt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,o=e.naturalWidth*(i.width/i.naturalWidth),r=e.naturalHeight*(i.height/i.naturalHeight);N(e,{width:o,height:r,left:(i.width-o)/2,top:(i.height-r)/2}),et(this.image,N({width:e.width,height:e.height},bt(N({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,o=Number(t.autoCropArea)||.8,r={width:i.width,height:i.height};e&&(i.height*e>i.width?r.height=r.width/e:r.width=r.height*e),this.cropBoxData=r,this.limitCropBox(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.width=Math.max(r.minWidth,r.width*o),r.height=Math.max(r.minHeight,r.height*o),r.left=i.left+(i.width-r.width)/2,r.top=i.top+(i.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCropBoxData=N({},r)},limitCropBox:function(t,i){var e=this.options,o=this.containerData,r=this.canvasData,n=this.cropBoxData,s=this.limited,h=e.aspectRatio;if(t){var l=Number(e.minCropBoxWidth)||0,c=Number(e.minCropBoxHeight)||0,p=s?Math.min(o.width,r.width,r.width+r.left,o.width-r.left):o.width,d=s?Math.min(o.height,r.height,r.height+r.top,o.height-r.top):o.height;l=Math.min(l,o.width),c=Math.min(c,o.height),h&&(l&&c?c*h>l?c=l/h:l=c*h:l?c=l/h:c&&(l=c*h),d*h>p?d=p/h:p=d*h),n.minWidth=Math.min(l,p),n.minHeight=Math.min(c,d),n.maxWidth=p,n.maxHeight=d}i&&(s?(n.minLeft=Math.max(0,r.left),n.minTop=Math.max(0,r.top),n.maxLeft=Math.min(o.width,r.left+r.width)-n.width,n.maxTop=Math.min(o.height,r.top+r.height)-n.height):(n.minLeft=0,n.minTop=0,n.maxLeft=o.width-n.width,n.maxTop=o.height-n.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&Dt(this.face,xt,e.width>=i.width&&e.height>=i.height?_e:Vt),et(this.cropBox,N({width:e.width,height:e.height},bt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),ft(this.element,Wt,this.getData())}},Hi={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,o=i?this.crossOriginUrl:this.url,r=t.alt||"The image to preview",n=document.createElement("img");if(i&&(n.crossOrigin=i),n.src=o,n.alt=r,this.viewBox.appendChild(n),this.viewBoxImage=n,!!e){var s=e;typeof e=="string"?s=t.ownerDocument.querySelectorAll(e):e.querySelector&&(s=[e]),this.previews=s,A(s,function(h){var l=document.createElement("img");Dt(h,Mt,{width:h.offsetWidth,height:h.offsetHeight,html:h.innerHTML}),i&&(l.crossOrigin=i),l.src=o,l.alt=r,l.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',h.innerHTML="",h.appendChild(l)})}},resetPreview:function(){A(this.previews,function(t){var i=Yt(t,Mt);et(t,{width:i.width,height:i.height}),t.innerHTML=i.html,Ci(t,Mt)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,o=e.width,r=e.height,n=t.width,s=t.height,h=e.left-i.left-t.left,l=e.top-i.top-t.top;!this.cropped||this.disabled||(et(this.viewBoxImage,N({width:n,height:s},bt(N({translateX:-h,translateY:-l},t)))),A(this.previews,function(c){var p=Yt(c,Mt),d=p.width,m=p.height,f=d,x=m,g=1;o&&(g=d/o,x=r*g),r&&x>m&&(g=m/r,f=o*g,x=m),et(c,{width:f,height:x}),et(c.getElementsByTagName("img")[0],N({width:n*g,height:s*g},bt(N({translateX:-h*g,translateY:-l*g},t))))}))}},zi={bind:function(){var t=this.element,i=this.options,e=this.cropper;$(i.cropstart)&&F(t,Pt,i.cropstart),$(i.cropmove)&&F(t,zt,i.cropmove),$(i.cropend)&&F(t,Ht,i.cropend),$(i.crop)&&F(t,Wt,i.crop),$(i.zoom)&&F(t,Xt,i.zoom),F(e,oe,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&F(e,le,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&F(e,re,this.onDblclick=this.dblclick.bind(this)),F(t.ownerDocument,ne,this.onCropMove=this.cropMove.bind(this)),F(t.ownerDocument,se,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&F(window,ce,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;$(i.cropstart)&&q(t,Pt,i.cropstart),$(i.cropmove)&&q(t,zt,i.cropmove),$(i.cropend)&&q(t,Ht,i.cropend),$(i.crop)&&q(t,Wt,i.crop),$(i.zoom)&&q(t,Xt,i.zoom),q(e,oe,this.onCropStart),i.zoomable&&i.zoomOnWheel&&q(e,le,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&q(e,re,this.onDblclick),q(t.ownerDocument,ne,this.onCropMove),q(t.ownerDocument,se,this.onCropEnd),i.responsive&&q(window,ce,this.onResize)}},Pi={resize:function(){if(!this.disabled){var t=this.options,i=this.container,e=this.containerData,o=i.offsetWidth/e.width,r=i.offsetHeight/e.height,n=Math.abs(o-1)>Math.abs(r-1)?o:r;if(n!==1){var s,h;t.restore&&(s=this.getCanvasData(),h=this.getCropBoxData()),this.render(),t.restore&&(this.setCanvasData(A(s,function(l,c){s[c]=l*n})),this.setCropBoxData(A(h,function(l,c){h[c]=l*n})))}}},dblclick:function(){this.disabled||this.options.dragMode===Ee||this.setDragMode(_i(this.dragBox,Lt)?Ce:jt)},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,o=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?o=t.deltaY>0?1:-1:t.wheelDelta?o=-t.wheelDelta/120:t.detail&&(o=t.detail>0?1:-1),this.zoom(-o*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(v(i)&&i!==1||v(e)&&e!==0||t.ctrlKey))){var o=this.options,r=this.pointers,n;t.changedTouches?A(t.changedTouches,function(s){r[s.identifier]=Ct(s)}):r[t.pointerId||0]=Ct(t),Object.keys(r).length>1&&o.zoomable&&o.zoomOnTouch?n=Me:n=Yt(t.target,xt),ui.test(n)&&ft(this.element,Pt,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===De&&(this.cropping=!0,B(this.dragBox,Ot)))}},cropMove:function(t){var i=this.action;if(!(this.disabled||!i)){var e=this.pointers;t.preventDefault(),ft(this.element,zt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?A(t.changedTouches,function(o){N(e[o.identifier]||{},Ct(o,!0))}):N(e[t.pointerId||0]||{},Ct(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?A(t.changedTouches,function(o){delete e[o.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,dt(this.dragBox,Ot,this.cropped&&this.options.modal)),ft(this.element,Ht,{originalEvent:t,action:i}))}}},Xi={change:function(t){var i=this.options,e=this.canvasData,o=this.containerData,r=this.cropBoxData,n=this.pointers,s=this.action,h=i.aspectRatio,l=r.left,c=r.top,p=r.width,d=r.height,m=l+p,f=c+d,x=0,g=0,C=o.width,b=o.height,_=!0,X;!h&&t.shiftKey&&(h=p&&d?p/d:1),this.limited&&(x=r.minLeft,g=r.minTop,C=x+Math.min(o.width,e.width,e.left+e.width),b=g+Math.min(o.height,e.height,e.top+e.height));var w=n[Object.keys(n)[0]],u={x:w.endX-w.startX,y:w.endY-w.startY},y=function(I){switch(I){case rt:m+u.x>C&&(u.x=C-m);break;case ot:l+u.x<x&&(u.x=x-l);break;case tt:c+u.y<g&&(u.y=g-c);break;case ct:f+u.y>b&&(u.y=b-f);break}};switch(s){case Vt:l+=u.x,c+=u.y;break;case rt:if(u.x>=0&&(m>=C||h&&(c<=g||f>=b))){_=!1;break}y(rt),p+=u.x,p<0&&(s=ot,p=-p,l-=p),h&&(d=p/h,c+=(r.height-d)/2);break;case tt:if(u.y<=0&&(c<=g||h&&(l<=x||m>=C))){_=!1;break}y(tt),d-=u.y,c+=u.y,d<0&&(s=ct,d=-d,c-=d),h&&(p=d*h,l+=(r.width-p)/2);break;case ot:if(u.x<=0&&(l<=x||h&&(c<=g||f>=b))){_=!1;break}y(ot),p-=u.x,l+=u.x,p<0&&(s=rt,p=-p,l-=p),h&&(d=p/h,c+=(r.height-d)/2);break;case ct:if(u.y>=0&&(f>=b||h&&(l<=x||m>=C))){_=!1;break}y(ct),d+=u.y,d<0&&(s=tt,d=-d,c-=d),h&&(p=d*h,l+=(r.width-p)/2);break;case gt:if(h){if(u.y<=0&&(c<=g||m>=C)){_=!1;break}y(tt),d-=u.y,c+=u.y,p=d*h}else y(tt),y(rt),u.x>=0?m<C?p+=u.x:u.y<=0&&c<=g&&(_=!1):p+=u.x,u.y<=0?c>g&&(d-=u.y,c+=u.y):(d-=u.y,c+=u.y);p<0&&d<0?(s=wt,d=-d,p=-p,c-=d,l-=p):p<0?(s=vt,p=-p,l-=p):d<0&&(s=yt,d=-d,c-=d);break;case vt:if(h){if(u.y<=0&&(c<=g||l<=x)){_=!1;break}y(tt),d-=u.y,c+=u.y,p=d*h,l+=r.width-p}else y(tt),y(ot),u.x<=0?l>x?(p-=u.x,l+=u.x):u.y<=0&&c<=g&&(_=!1):(p-=u.x,l+=u.x),u.y<=0?c>g&&(d-=u.y,c+=u.y):(d-=u.y,c+=u.y);p<0&&d<0?(s=yt,d=-d,p=-p,c-=d,l-=p):p<0?(s=gt,p=-p,l-=p):d<0&&(s=wt,d=-d,c-=d);break;case wt:if(h){if(u.x<=0&&(l<=x||f>=b)){_=!1;break}y(ot),p-=u.x,l+=u.x,d=p/h}else y(ct),y(ot),u.x<=0?l>x?(p-=u.x,l+=u.x):u.y>=0&&f>=b&&(_=!1):(p-=u.x,l+=u.x),u.y>=0?f<b&&(d+=u.y):d+=u.y;p<0&&d<0?(s=gt,d=-d,p=-p,c-=d,l-=p):p<0?(s=yt,p=-p,l-=p):d<0&&(s=vt,d=-d,c-=d);break;case yt:if(h){if(u.x>=0&&(m>=C||f>=b)){_=!1;break}y(rt),p+=u.x,d=p/h}else y(ct),y(rt),u.x>=0?m<C?p+=u.x:u.y>=0&&f>=b&&(_=!1):p+=u.x,u.y>=0?f<b&&(d+=u.y):d+=u.y;p<0&&d<0?(s=vt,d=-d,p=-p,c-=d,l-=p):p<0?(s=wt,p=-p,l-=p):d<0&&(s=gt,d=-d,c-=d);break;case _e:this.move(u.x,u.y),_=!1;break;case Me:this.zoom(Oi(n),t),_=!1;break;case De:if(!u.x||!u.y){_=!1;break}X=Ae(this.cropper),l=w.startX-X.left,c=w.startY-X.top,p=r.minWidth,d=r.minHeight,u.x>0?s=u.y>0?yt:gt:u.x<0&&(l-=p,s=u.y>0?wt:vt),u.y<0&&(c-=d),this.cropped||(Z(this.cropBox,V),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0));break}_&&(r.width=p,r.height=d,r.left=l,r.top=c,this.action=s,this.renderCropBox()),A(n,function(T){T.startX=T.endX,T.startY=T.endY})}},Yi={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&B(this.dragBox,Ot),Z(this.cropBox,V),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=N({},this.initialImageData),this.canvasData=N({},this.initialCanvasData),this.cropBoxData=N({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(N(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),Z(this.dragBox,Ot),B(this.cropBox,V)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,A(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,Z(this.cropper,ie)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,B(this.cropper,ie)),this},destroy:function(){var t=this.element;return t[O]?(t[O]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,o=e.left,r=e.top;return this.moveTo(St(t)?t:o+Number(t),St(i)?i:r+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,o=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(v(t)&&(e.left=t,o=!0),v(i)&&(e.top=i,o=!0),o&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=Number(t),t<0?t=1/(1-t):t=1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var o=this.options,r=this.canvasData,n=r.width,s=r.height,h=r.naturalWidth,l=r.naturalHeight;if(t=Number(t),t>=0&&this.ready&&!this.disabled&&o.zoomable){var c=h*t,p=l*t;if(ft(this.element,Xt,{ratio:t,oldRatio:n/h,originalEvent:e})===!1)return this;if(e){var d=this.pointers,m=Ae(this.cropper),f=d&&Object.keys(d).length?Ni(d):{pageX:e.pageX,pageY:e.pageY};r.left-=(c-n)*((f.pageX-m.left-r.left)/n),r.top-=(p-s)*((f.pageY-m.top-r.top)/s)}else pt(i)&&v(i.x)&&v(i.y)?(r.left-=(c-n)*((i.x-r.left)/n),r.top-=(p-s)*((i.y-r.top)/s)):(r.left-=(c-n)/2,r.top-=(p-s)/2);r.width=c,r.height=p,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return t=Number(t),v(t)&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,v(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(v(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,o=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(v(t)&&(e.scaleX=t,o=!0),v(i)&&(e.scaleY=i,o=!0),o&&this.renderCanvas(!0,!0)),this},getData:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,i=this.options,e=this.imageData,o=this.canvasData,r=this.cropBoxData,n;if(this.ready&&this.cropped){n={x:r.left-o.left,y:r.top-o.top,width:r.width,height:r.height};var s=e.width/e.naturalWidth;if(A(n,function(c,p){n[p]=c/s}),t){var h=Math.round(n.y+n.height),l=Math.round(n.x+n.width);n.x=Math.round(n.x),n.y=Math.round(n.y),n.width=l-n.x,n.height=h-n.y}}else n={x:0,y:0,width:0,height:0};return i.rotatable&&(n.rotate=e.rotate||0),i.scalable&&(n.scaleX=e.scaleX||1,n.scaleY=e.scaleY||1),n},setData:function(t){var i=this.options,e=this.imageData,o=this.canvasData,r={};if(this.ready&&!this.disabled&&pt(t)){var n=!1;i.rotatable&&v(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,n=!0),i.scalable&&(v(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,n=!0),v(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,n=!0)),n&&this.renderCanvas(!0,!0);var s=e.width/e.naturalWidth;v(t.x)&&(r.left=t.x*s+o.left),v(t.y)&&(r.top=t.y*s+o.top),v(t.width)&&(r.width=t.width*s),v(t.height)&&(r.height=t.height*s),this.setCropBoxData(r)}return this},getContainerData:function(){return this.ready?N({},this.containerData):{}},getImageData:function(){return this.sized?N({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&A(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&pt(t)&&(v(t.left)&&(i.left=t.left),v(t.top)&&(i.top=t.top),v(t.width)?(i.width=t.width,i.height=t.width/e):v(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t=this.cropBoxData,i;return this.ready&&this.cropped&&(i={left:t.left,top:t.top,width:t.width,height:t.height}),i||{}},setCropBoxData:function(t){var i=this.cropBoxData,e=this.options.aspectRatio,o,r;return this.ready&&this.cropped&&!this.disabled&&pt(t)&&(v(t.left)&&(i.left=t.left),v(t.top)&&(i.top=t.top),v(t.width)&&t.width!==i.width&&(o=!0,i.width=t.width),v(t.height)&&t.height!==i.height&&(r=!0,i.height=t.height),e&&(o?i.height=i.width/e:r&&(i.width=i.height*e)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=Ri(this.image,this.imageData,i,t);if(!this.cropped)return e;var o=this.getData(t.rounded),r=o.x,n=o.y,s=o.width,h=o.height,l=e.width/Math.floor(i.naturalWidth);l!==1&&(r*=l,n*=l,s*=l,h*=l);var c=s/h,p=it({aspectRatio:c,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=it({aspectRatio:c,width:t.minWidth||0,height:t.minHeight||0},"cover"),m=it({aspectRatio:c,width:t.width||(l!==1?e.width:s),height:t.height||(l!==1?e.height:h)}),f=m.width,x=m.height;f=Math.min(p.width,Math.max(d.width,f)),x=Math.min(p.height,Math.max(d.height,x));var g=document.createElement("canvas"),C=g.getContext("2d");g.width=ut(f),g.height=ut(x),C.fillStyle=t.fillColor||"transparent",C.fillRect(0,0,f,x);var b=t.imageSmoothingEnabled,_=b===void 0?!0:b,X=t.imageSmoothingQuality;C.imageSmoothingEnabled=_,X&&(C.imageSmoothingQuality=X);var w=e.width,u=e.height,y=r,T=n,I,Y,D,S,k,j;y<=-s||y>w?(y=0,I=0,D=0,k=0):y<=0?(D=-y,y=0,I=Math.min(w,s+y),k=I):y<=w&&(D=0,I=Math.min(s,w-y),k=I),I<=0||T<=-h||T>u?(T=0,Y=0,S=0,j=0):T<=0?(S=-T,T=0,Y=Math.min(u,h+T),j=Y):T<=u&&(S=0,Y=Math.min(h,u-T),j=Y);var W=[y,T,I,Y];if(k>0&&j>0){var K=f/s;W.push(D*K,S*K,k*K,j*K)}return C.drawImage.apply(C,[e].concat(xe(W.map(function(st){return Math.floor(ut(st))})))),g},setAspectRatio:function(t){var i=this.options;return!this.disabled&&!St(t)&&(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,o=this.face;if(this.ready&&!this.disabled){var r=t===jt,n=i.movable&&t===Ce;t=r||n?t:Ee,i.dragMode=t,Dt(e,xt,t),dt(e,Lt,r),dt(e,It,n),i.cropBoxMovable||(Dt(o,xt,t),dt(o,Lt,r),dt(o,It,n))}return this}},Ui=Q.Cropper,Be=function(){function a(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ii(this,a),!t||!gi.test(t.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=t,this.options=N({},de,pt(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return ai(a,[{key:"init",value:function(){var i=this.element,e=i.tagName.toLowerCase(),o;if(!i[O]){if(i[O]=this,e==="img"){if(this.isImg=!0,o=i.getAttribute("src")||"",this.originalUrl=o,!o)return;o=i.src}else e==="canvas"&&window.HTMLCanvasElement&&(o=i.toDataURL());this.load(o)}}},{key:"load",value:function(i){var e=this;if(i){this.url=i,this.imageData={};var o=this.element,r=this.options;if(!r.rotatable&&!r.scalable&&(r.checkOrientation=!1),!r.checkOrientation||!window.ArrayBuffer){this.clone();return}if(fi.test(i)){mi.test(i)?this.read(ki(i)):this.clone();return}var n=new XMLHttpRequest,s=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=s,n.onerror=s,n.ontimeout=s,n.onprogress=function(){n.getResponseHeader("content-type")!==pe&&n.abort()},n.onload=function(){e.read(n.response)},n.onloadend=function(){e.reloading=!1,e.xhr=null},r.checkCrossOrigin&&fe(i)&&o.crossOrigin&&(i=me(i)),n.open("GET",i,!0),n.responseType="arraybuffer",n.withCredentials=o.crossOrigin==="use-credentials",n.send()}}},{key:"read",value:function(i){var e=this.options,o=this.imageData,r=Li(i),n=0,s=1,h=1;if(r>1){this.url=Bi(i,pe);var l=Ii(r);n=l.rotate,s=l.scaleX,h=l.scaleY}e.rotatable&&(o.rotate=n),e.scalable&&(o.scaleX=s,o.scaleY=h),this.clone()}},{key:"clone",value:function(){var i=this.element,e=this.url,o=i.crossOrigin,r=e;this.options.checkCrossOrigin&&fe(e)&&(o||(o="anonymous"),r=me(e)),this.crossOrigin=o,this.crossOriginUrl=r;var n=document.createElement("img");o&&(n.crossOrigin=o),n.src=r||e,n.alt=i.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),B(n,ae),i.parentNode.insertBefore(n,i.nextSibling)}},{key:"start",value:function(){var i=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var o=Q.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(Q.navigator.userAgent),r=function(l,c){N(i.imageData,{naturalWidth:l,naturalHeight:c,aspectRatio:l/c}),i.initialImageData=N({},i.imageData),i.sizing=!1,i.sized=!0,i.build()};if(e.naturalWidth&&!o){r(e.naturalWidth,e.naturalHeight);return}var n=document.createElement("img"),s=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){r(n.width,n.height),o||s.removeChild(n)},n.src=e.src,o||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",s.appendChild(n))}},{key:"stop",value:function(){var i=this.image;i.onload=null,i.onerror=null,i.parentNode.removeChild(i),this.image=null}},{key:"build",value:function(){if(!(!this.sized||this.ready)){var i=this.element,e=this.options,o=this.image,r=i.parentNode,n=document.createElement("div");n.innerHTML=vi;var s=n.querySelector(".".concat(O,"-container")),h=s.querySelector(".".concat(O,"-canvas")),l=s.querySelector(".".concat(O,"-drag-box")),c=s.querySelector(".".concat(O,"-crop-box")),p=c.querySelector(".".concat(O,"-face"));this.container=r,this.cropper=s,this.canvas=h,this.dragBox=l,this.cropBox=c,this.viewBox=s.querySelector(".".concat(O,"-view-box")),this.face=p,h.appendChild(o),B(i,V),r.insertBefore(s,i.nextSibling),Z(o,ae),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,B(c,V),e.guides||B(c.getElementsByClassName("".concat(O,"-dashed")),V),e.center||B(c.getElementsByClassName("".concat(O,"-center")),V),e.background&&B(s,"".concat(O,"-bg")),e.highlight||B(p,ci),e.cropBoxMovable&&(B(p,It),Dt(p,xt,Vt)),e.cropBoxResizable||(B(c.getElementsByClassName("".concat(O,"-line")),V),B(c.getElementsByClassName("".concat(O,"-point")),V)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),$(e.ready)&&F(i,he,e.ready,{once:!0}),ft(i,he)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var i=this.cropper.parentNode;i&&i.removeChild(this.cropper),Z(this.element,V)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=Ui,a}},{key:"setDefaults",value:function(i){N(de,pt(i)&&i)}}])}();N(Be.prototype,Wi,Hi,zi,Pi,Xi,Yi);const $i={__name:"cropper-preview",props:{previewWidth:{type:Number,required:!0},aspectRatio:{type:Number,required:!0}},setup(a,{expose:t}){const i=at(null);return t({getPreviews:()=>{var o;return(o=i.value)==null?void 0:o.querySelectorAll(".guns-cropper-preview")}}),(o,r)=>(M(),J("div",{ref_key:"rootRef",ref:i,class:"guns-cropper-preview-group",style:lt({width:"".concat(a.previewWidth+14,"px")})},[G("div",{class:"guns-cropper-preview",style:lt({width:"".concat(a.previewWidth,"px"),height:"".concat(a.previewWidth/(a.aspectRatio||1),"px"),marginTop:"0px"})},null,4),a.aspectRatio===1?(M(),J("div",{key:0,class:"guns-cropper-preview guns-cropper-preview-circle",style:lt({width:"".concat(a.previewWidth,"px"),height:"".concat(a.previewWidth/a.aspectRatio,"px")})},null,4)):a.aspectRatio?(M(),J(Et,{key:1},[G("div",{class:"guns-cropper-preview",style:lt({width:"".concat(a.previewWidth,"px"),height:"".concat((a.previewWidth/3*2-10)/a.aspectRatio,"px")})},null,4),G("div",{class:"guns-cropper-preview",style:lt({width:"".concat(a.previewWidth,"px"),height:"".concat(a.previewWidth/3/a.aspectRatio,"px"),marginLeft:"10px"})},null,4)],64)):Nt("",!0)],4))}},Vi={class:"guns-cropper-tool"},ji={__name:"cropper-tools",props:{tools:String,accept:String,okText:String},emits:["crop","move-b","move-l","move-r","move-t","reset","rotate-l","rotate-r","scale-x","scale-y","replace","zoom-in","zoom-out"],setup(a,{emit:t}){const i=a,e=t,o=ge(()=>i.tools?i.tools.split("|").map(b=>b.split(",").map(_=>_.trim())):[]),r=()=>{e("zoom-in")},n=()=>{e("zoom-out")},s=()=>{e("rotate-l")},h=()=>{e("rotate-r")},l=()=>{e("move-l")},c=()=>{e("move-r")},p=()=>{e("move-t")},d=()=>{e("move-b")},m=()=>{e("scale-x")},f=()=>{e("scale-y")},x=()=>{e("reset")},g=()=>{e("crop")},C=({file:b})=>{const _=new FileReader;return _.onload=X=>{var w;e("replace",{data:(w=X.target)==null?void 0:w.result,type:b.type})},_.readAsDataURL(b),!1};return(b,_)=>{const X=U("ZoomInOutlined"),w=ve,u=U("ZoomOutOutlined"),y=U("RotateLeftOutlined"),T=U("RotateRightOutlined"),I=U("ArrowLeftOutlined"),Y=U("ArrowRightOutlined"),D=U("ArrowUpOutlined"),S=U("ArrowDownOutlined"),k=U("SwapOutlined"),j=U("SyncOutlined"),W=U("UploadOutlined"),K=ye,st=U("CheckOutlined"),mt=He;return M(),J("div",Vi,[(M(!0),J(Et,null,qt(o.value,(ht,H)=>(M(),L(mt,{key:H+ht.join(","),class:"guns-cropper-tool-item"},{default:R(()=>[(M(!0),J(Et,null,qt(ht,(z,P)=>(M(),J(Et,null,[z==="zoomIn"?(M(),L(w,{key:H+"-"+P+"zoomIn",type:"primary",class:"guns-cropper-tool-btn",title:"\u653E\u5927",onClick:r},{icon:R(()=>[E(X)]),_:2},1024)):z==="zoomOut"?(M(),L(w,{key:H+"-"+P+"zoomOut",type:"primary",class:"guns-cropper-tool-btn",title:"\u7F29\u5C0F",onClick:n},{icon:R(()=>[E(u)]),_:2},1024)):z==="rotateL"?(M(),L(w,{key:H+"-"+P+"rotateL",type:"primary",class:"guns-cropper-tool-btn",title:"\u5411\u5DE6\u65CB\u8F6C",onClick:s},{icon:R(()=>[E(y)]),_:2},1024)):z==="rotateR"?(M(),L(w,{key:H+"-"+P+"rotateR",type:"primary",class:"guns-cropper-tool-btn",title:"\u5411\u53F3\u65CB\u8F6C",onClick:h},{icon:R(()=>[E(T)]),_:2},1024)):z==="moveL"?(M(),L(w,{key:H+"-"+P+"moveL",type:"primary",class:"guns-cropper-tool-btn",title:"\u5DE6\u79FB",onClick:l},{icon:R(()=>[E(I)]),_:2},1024)):z==="moveR"?(M(),L(w,{key:H+"-"+P+"moveR",type:"primary",class:"guns-cropper-tool-btn",title:"\u53F3\u79FB",onClick:c},{icon:R(()=>[E(Y)]),_:2},1024)):z==="moveT"?(M(),L(w,{key:H+"-"+P+"moveT",type:"primary",class:"guns-cropper-tool-btn",title:"\u4E0A\u79FB",onClick:p},{icon:R(()=>[E(D)]),_:2},1024)):z==="moveB"?(M(),L(w,{key:H+"-"+P+"moveB",type:"primary",class:"guns-cropper-tool-btn",title:"\u4E0B\u79FB",onClick:d},{icon:R(()=>[E(S)]),_:2},1024)):z==="scaleX"?(M(),L(w,{key:H+"-"+P+"scaleX",type:"primary",class:"guns-cropper-tool-btn",title:"\u5DE6\u53F3\u7FFB\u8F6C",onClick:m},{icon:R(()=>[E(k)]),_:2},1024)):z==="scaleY"?(M(),L(w,{key:H+"-"+P+"scaleY",type:"primary",class:"guns-cropper-tool-btn",title:"\u4E0A\u4E0B\u7FFB\u8F6C",onClick:f},{icon:R(()=>[E(k,{style:{transform:"rotate(90deg)"}})]),_:2},1024)):z==="reset"?(M(),L(w,{key:H+"-"+P+"reset",type:"primary",class:"guns-cropper-tool-btn",title:"\u91CD\u65B0\u5F00\u59CB",onClick:x},{icon:R(()=>[E(j)]),_:2},1024)):z==="upload"?(M(),L(K,{key:H+"-"+P+"upload",accept:a.accept,"custom-request":C,"show-upload-list":!1},{default:R(()=>[E(w,{type:"primary",class:"guns-cropper-tool-btn",title:"\u9009\u62E9\u56FE\u7247",style:{"border-top-right-radius":"2px","border-bottom-right-radius":"2px"}},{icon:R(()=>[E(W)]),_:1})]),_:2},1032,["accept"])):z==="crop"?(M(),L(w,{key:"crop"+P,type:"primary",class:"guns-cropper-tool-btn-ok",onClick:g},{icon:R(()=>[E(st)]),default:R(()=>[G("span",null,We(i.okText||"\u5B8C\u6210"),1)]),_:2},1024)):Nt("",!0)],64))),256))]),_:2},1024))),128))])}}};const Fi={class:"guns-cropper-group"},Gi={class:"guns-cropper-img-group"},qi=["src"],Zi={__name:"cropper",props:{src:String,imageType:{type:String,default:"image/png"},accept:{type:String,default:"image/*"},tools:{type:String},showPreview:{type:Boolean,default:!0},previewWidth:{type:Number,default:120},okText:String,toBlob:Boolean,options:Object,croppedOptions:Object,aspectRatio:{type:Number,default:1},viewMode:{type:Number,default:0},dragMode:{type:String,default:"crop"},initialAspectRatio:Number,minContainerWidth:{type:Number,default:200},minContainerHeight:{type:Number,default:100},minCanvasWidth:{type:Number,default:0},minCanvasHeight:{type:Number,default:0},minCropBoxWidth:{type:Number,default:0},minCropBoxHeight:{type:Number,default:0},croppedWidth:Number,croppedHeight:Number,croppedMinWidth:{type:Number,default:0},croppedMinHeight:{type:Number,default:0},croppedMaxWidth:Number,croppedMaxHeight:Number,croppedFillColor:{type:String,default:"transparent"},imageSmoothingEnabled:Boolean,imageSmoothingQuality:String},emits:["done"],setup(a,{emit:t}){const i=a,e=t,o=ze("GunsLayoutState"),r=at(null),n=at(null),s=at(i.imageType);let h=null,l=-1,c=-1;const p=ge(()=>{var D,S;return(S=(D=_t(o))==null?void 0:D.styleResponsive)!=null?S:!0}),d=()=>{Qt(()=>{var k;Y();const D={aspectRatio:i.aspectRatio,viewMode:i.viewMode,dragMode:i.dragMode,initialAspectRatio:i.initialAspectRatio,minContainerWidth:i.minContainerWidth,minContainerHeight:i.minContainerHeight,minCanvasWidth:i.minCanvasWidth,minCanvasHeight:i.minCanvasHeight,minCropBoxWidth:i.minCropBoxWidth,minCropBoxHeight:i.minCropBoxHeight,...i.options};i.showPreview&&(D.preview=(k=_t(n.value))==null?void 0:k.getPreviews());const S=_t(r.value);S&&(h=new Be(S,D))})},m=()=>{h&&h.zoom(.1)},f=()=>{h&&h.zoom(-.1)},x=()=>{h&&h.move(-10,0)},g=()=>{h&&h.move(10,0)},C=()=>{h&&h.move(0,-10)},b=()=>{h&&h.move(0,10)},_=()=>{h&&h.rotate(-45)},X=()=>{h&&h.rotate(45)},w=()=>{h&&h.scaleX(l),l=-l},u=()=>{h&&h.scaleY(c),c=-c},y=()=>{h&&h.reset()},T=()=>{const D=h==null?void 0:h.getCroppedCanvas({width:i.croppedWidth,height:i.croppedHeight,minWidth:i.croppedMinWidth,minHeight:i.croppedMinHeight,maxWidth:i.croppedMaxWidth,maxHeight:i.croppedMaxHeight,fillColor:i.croppedFillColor,imageSmoothingEnabled:i.imageSmoothingEnabled,imageSmoothingQuality:i.imageSmoothingQuality,...i.croppedOptions});D?i.toBlob?D.toBlob(S=>{e("done",S)},s.value):e("done",D.toDataURL(s.value)):e("done")},I=({data:D,type:S})=>{if(s.value=S,h)h.replace(D);else{const k=_t(r.value);k&&(k.src=D,k.style.display="block"),d()}},Y=()=>{h&&h.destroy(),h=null};return Zt(()=>i.src,D=>{D?h?h.replace(D):Qt(()=>{d()}):Y()}),Zt(()=>i.imageType,D=>{D&&(s.value=D)}),Pe(()=>{i.src&&d()}),Xe(()=>{Y()}),(D,S)=>(M(),J("div",{class:Ye(["guns-cropper",{"guns-cropper-responsive":p.value}])},[G("div",Fi,[G("div",Gi,[G("img",{src:a.src,alt:"cropper",ref_key:"imageRef",ref:r,style:lt({maxWidth:"100%",display:a.src?"block":"none"})},null,12,qi)]),i.showPreview?(M(),L($i,{key:0,ref_key:"previewRef",ref:n,"aspect-ratio":i.aspectRatio,"preview-width":i.previewWidth},null,8,["aspect-ratio","preview-width"])):Nt("",!0)]),E(ji,{tools:a.tools,"ok-text":i.okText,onCrop:T,onMoveB:b,onMoveL:x,onMoveR:g,onMoveT:C,onReset:y,onRotateL:_,onRotateR:X,onScaleX:w,onScaleY:u,onReplace:I,onZoomIn:m,onZoomOut:f},null,8,["tools","ok-text"])],2))}},Qi={__name:"index",props:{visible:Boolean,title:{type:String,default:"\u88C1\u526A\u56FE\u7247"},width:{type:String,default:"660px"},src:String,imageType:{type:String,default:"image/png"},accept:{type:String,default:"image/*"},tools:{type:String,default:["zoomIn,zoomOut","moveL,moveR,moveT,moveB","rotateL,rotateR","scaleX,scaleY","reset,upload","crop"].join(" | ")},showPreview:{type:Boolean,default:!0},previewWidth:{type:Number,default:120},okText:{type:String,defalut:"\u5B8C\u6210"},toBlob:{type:Boolean,defalut:!1},options:{type:Object,defalut:{}},croppedOptions:{type:Object,defalut:{}},aspectRatio:{type:Number,default:1},viewMode:{type:Number,default:0},dragMode:{type:String,default:"crop"},initialAspectRatio:Number,minContainerWidth:{type:Number,default:200},minContainerHeight:{type:Number,default:100},minCanvasWidth:{type:Number,default:0},minCanvasHeight:{type:Number,default:0},minCropBoxWidth:{type:Number,default:0},minCropBoxHeight:{type:Number,default:0},croppedWidth:Number,croppedHeight:Number,croppedMinWidth:{type:Number,default:0},croppedMinHeight:{type:Number,default:0},croppedMaxWidth:Number,croppedMaxHeight:Number,croppedFillColor:{type:String,default:"transparent"},imageSmoothingEnabled:Boolean,imageSmoothingQuality:String},emits:["update:visible","done"],setup(a,{emit:t}){const i=a,e=t,o=at(!1),r=s=>{e("update:visible",s)},n=s=>{e("done",s)};return(s,h)=>{const l=$e;return M(),L(l,{width:i.width,maskClosable:!1,visible:i.visible,"confirm-loading":o.value,forceRender:!0,title:i.title,"body-style":{padding:"16px 18px 18px 18px"},"onUpdate:visible":r,footer:"",onClose:h[0]||(h[0]=c=>r(!1))},{default:R(()=>[i.visible?(M(),L(Zi,Ue({key:0},i,{onDone:n,"ok-text":i.okText}),null,16,["ok-text"])):Nt("",!0)]),_:1},8,["width","visible","confirm-loading","title"])}}};const Ki={class:"form"},Ji={class:"avatar"},ta={class:"upload-btn"},ea={__name:"update-avatar",setup(a){const t=je(),i=at(!1),e=at([]),o=at(t.info.avatarUrl),r=Fe({fileUploadUrl:"".concat(Kt).concat(Je,"?secretFlag=N"),filePreviewUrl:"".concat(Kt,"/sysFileInfo/public/preview?fileId="),headers:{Authorization:Ge()}}),n=async h=>{if(h.file.status==="done"){let l=h.file.response.data;o.value=l.fileUrl,e.value=[h.file];const c=await Jt.updateAvatar({avatar:l.fileId});Rt.success(c.message,.5).then(()=>{window.location.reload()})}else h.file.status==="error"&&Rt.error("\u5934\u50CF\u4E0A\u4F20\u5931\u8D25")},s=async h=>{i.value=!1;const l=new FormData;l.append("file",h,"avatar.jpg");const c=await ti.commonUpload("N",l),p=await Jt.updateAvatar({avatar:c.data.fileId});Rt.success(p.message,.5).then(()=>{window.location.reload()})};return(h,l)=>{const c=Ze,p=Qe,d=U("upload-outlined"),m=Qi,f=U("cloud-upload-outlined"),x=ve,g=ye,C=Ke;return M(),J("div",Ki,[E(C,null,{default:R(()=>[E(c,{md:6,xs:24,sm:24,style:{display:"flex","align-items":"center"}},{default:R(()=>l[3]||(l[3]=[G("div",{class:"label"},[G("span",null,"\u5934\u50CF\uFF1A")],-1)])),_:1,__:[3]}),E(c,{md:18,xs:24,sm:24,style:{display:"flex","align-items":"center"}},{default:R(()=>[G("div",Ji,[G("div",{class:"user-info-avatar-group",onClick:l[0]||(l[0]=b=>i.value=!0)},[E(p,{size:110,src:o.value},null,8,["src"]),E(d,{class:"user-info-avatar-icon"})]),E(m,{"mask-closable":!1,visible:i.value,"onUpdate:visible":l[1]||(l[1]=b=>i.value=b),src:o.value,"to-blob":!0,onDone:s},null,8,["visible","src"])]),G("div",ta,[E(g,{"file-list":e.value,"onUpdate:fileList":l[2]||(l[2]=b=>e.value=b),name:"file","show-upload-list":!1,action:r.fileUploadUrl,headers:r.headers,onChange:n,"max-count":1},{default:R(()=>[E(x,{type:"primary"},{default:R(()=>[E(f),l[4]||(l[4]=qe(" \u4E0A\u4F20\u5934\u50CF "))]),_:1,__:[4]})]),_:1},8,["file-list","action","headers"])])]),_:1})]),_:1})])}}},oa=Ve(ea,[["__scopeId","data-v-05dad288"]]);export{oa as default};
