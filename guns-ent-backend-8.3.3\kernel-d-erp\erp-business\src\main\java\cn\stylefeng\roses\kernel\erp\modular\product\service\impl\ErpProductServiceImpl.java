package cn.stylefeng.roses.kernel.erp.modular.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.stylefeng.roses.kernel.db.api.factory.PageFactory;
import cn.stylefeng.roses.kernel.db.api.factory.PageResultFactory;
import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.constants.ErpProductConstants;
import cn.stylefeng.roses.kernel.erp.api.exception.enums.ErpProductExceptionEnum;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProduct;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpProductCategory;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.modular.product.mapper.ErpProductMapper;
import cn.stylefeng.roses.kernel.erp.modular.product.service.ErpProductService;
import cn.stylefeng.roses.kernel.erp.modular.productcategory.service.ErpProductCategoryService;
import cn.stylefeng.roses.kernel.erp.modular.supplier.service.ErpSupplierService;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.ErpSupplier;
import cn.stylefeng.roses.kernel.erp.modular.inventory.service.InventoryService;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.Inventory;
import cn.stylefeng.roses.kernel.rule.enums.YesOrNotEnum;
import cn.stylefeng.roses.kernel.rule.exception.base.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品主档案Service实现类
 *
 * <AUTHOR>
 * @since 2025/07/20 11:00
 */
@Service
public class ErpProductServiceImpl extends ServiceImpl<ErpProductMapper, ErpProduct> implements ErpProductService {

    @Resource
    private ErpProductMapper erpProductMapper;



    @Resource
    private ErpProductCategoryService erpProductCategoryService;

    @Resource
    private ErpSupplierService erpSupplierService;

    @Resource
    private InventoryService inventoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ErpProductRequest erpProductRequest) {
        ErpProduct erpProduct = new ErpProduct();
        BeanUtil.copyProperties(erpProductRequest, erpProduct);

        // 验证分类ID有效性
        this.validateCategoryId(erpProduct.getCategoryId());

        // 校验商品编码是否重复
        if (this.validateProductCodeRepeat(erpProduct.getProductCode(), null)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_CODE_REPEAT);
        }

        // 校验条形码是否重复
        if (StrUtil.isNotBlank(erpProduct.getBarcode()) && 
            this.validateBarcodeRepeat(erpProduct.getBarcode(), null)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_BARCODE_REPEAT);
        }

        // 设置默认值
        if (StrUtil.isBlank(erpProduct.getUnit())) {
            erpProduct.setUnit(ErpProductConstants.DEFAULT_UNIT);
        }
        if (StrUtil.isBlank(erpProduct.getStatus())) {
            erpProduct.setStatus(ErpProductConstants.DEFAULT_PRODUCT_STATUS);
        }

        // 校验参数
        this.validateProductParams(erpProduct);

        // 保存商品基本信息
        this.save(erpProduct);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(ErpProductRequest erpProductRequest) {
        ErpProduct erpProduct = this.queryProduct(erpProductRequest);

        // 校验是否可以删除
        if (!this.validateCanDelete(erpProduct.getProductId())) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_HAS_BUSINESS_DATA_CANNOT_DELETE);
        }

        // 删除商品-分类关联关系（简化处理，主分类在商品删除时自动清除）

        // 删除商品
        this.removeById(erpProduct.getProductId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(ErpProductRequest erpProductRequest) {
        List<Long> productIdList = erpProductRequest.getProductIdList();
        if (ObjectUtil.isEmpty(productIdList)) {
            return;
        }

        // 校验每个商品是否可以删除
        for (Long productId : productIdList) {
            if (!this.validateCanDelete(productId)) {
                throw new ServiceException(ErpProductExceptionEnum.PRODUCT_HAS_BUSINESS_DATA_CANNOT_DELETE);
            }
        }

        // 批量删除商品-分类关联关系（简化处理，主分类在商品删除时自动清除）

        // 批量删除商品
        this.removeBatchByIds(productIdList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ErpProductRequest erpProductRequest) {
        ErpProduct erpProduct = this.queryProduct(erpProductRequest);

        // 验证分类ID有效性
        this.validateCategoryId(erpProductRequest.getCategoryId());

        // 校验商品编码是否重复（排除自己）
        if (this.validateProductCodeRepeat(erpProductRequest.getProductCode(), erpProduct.getProductId())) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_CODE_REPEAT);
        }

        // 校验条形码是否重复（排除自己）
        if (StrUtil.isNotBlank(erpProductRequest.getBarcode()) && 
            this.validateBarcodeRepeat(erpProductRequest.getBarcode(), erpProduct.getProductId())) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_BARCODE_REPEAT);
        }

        BeanUtil.copyProperties(erpProductRequest, erpProduct);

        // 校验参数
        this.validateProductParams(erpProduct);

        // 更新商品基本信息
        this.updateById(erpProduct);
    }

    @Override
    public ErpProductResponse detail(ErpProductRequest erpProductRequest) {
        ErpProduct erpProduct = this.queryProduct(erpProductRequest);
        ErpProductResponse response = new ErpProductResponse();
        BeanUtil.copyProperties(erpProduct, response);

        // 填充扩展信息
        this.fillProductExtInfo(response);

        return response;
    }

    @Override
    public PageResult<ErpProductResponse> findPage(ErpProductRequest erpProductRequest) {
        // 构建查询条件（简化处理，直接使用主分类过滤）
        LambdaQueryWrapper<ErpProduct> wrapper = this.createWrapper(erpProductRequest);
        
        // 执行分页查询
        Page<ErpProduct> page = this.page(PageFactory.defaultPage(), wrapper);

        // 转换为响应对象
        List<ErpProductResponse> responseList = page.getRecords().stream().map(product -> {
            ErpProductResponse response = new ErpProductResponse();
            BeanUtil.copyProperties(product, response);
            
            // 填充扩展信息
            this.fillProductExtInfo(response);
            

            
            return response;
        }).collect(Collectors.toList());

        return PageResultFactory.createPageResult(responseList, page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public List<ErpProductResponse> findList(ErpProductRequest erpProductRequest) {
        // 构建查询条件（简化处理，直接使用主分类过滤）
        LambdaQueryWrapper<ErpProduct> wrapper = this.createWrapper(erpProductRequest);
        
        // 执行查询
        List<ErpProduct> productList = this.list(wrapper);

        // 转换为响应对象
        return productList.stream().map(product -> {
            ErpProductResponse response = new ErpProductResponse();
            BeanUtil.copyProperties(product, response);
            
            // 填充扩展信息
            this.fillProductExtInfo(response);
            
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(ErpProductRequest erpProductRequest) {
        ErpProduct erpProduct = this.queryProduct(erpProductRequest);

        // 如果要停用，校验是否可以停用
        if (ErpProductConstants.PRODUCT_STATUS_INACTIVE.equals(erpProductRequest.getStatus()) ||
            ErpProductConstants.PRODUCT_STATUS_DISCONTINUED.equals(erpProductRequest.getStatus())) {
            if (!this.validateCanInactive(erpProduct.getProductId())) {
                throw new ServiceException(ErpProductExceptionEnum.PRODUCT_HAS_BUSINESS_DATA_CANNOT_INACTIVE);
            }
        }

        // 校验状态参数
        this.validateStatus(erpProductRequest.getStatus());

        LambdaUpdateWrapper<ErpProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ErpProduct::getProductId, erpProduct.getProductId())
                .set(ErpProduct::getStatus, erpProductRequest.getStatus());

        this.update(updateWrapper);
    }



    @Override
    public List<ErpProductResponse> findListByCategoryId(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            return new ArrayList<>();
        }

        // 查询指定分类下的商品（简化处理，直接查询主分类）
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getCategoryId, categoryId)
               .eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()) // 只查询未删除的商品
               .eq(ErpProduct::getStatus, ErpProductConstants.PRODUCT_STATUS_ACTIVE)
               .orderByDesc(ErpProduct::getCreateTime);

        List<ErpProduct> productList = this.list(wrapper);

        // 转换为响应对象
        return productList.stream().map(product -> {
            ErpProductResponse response = new ErpProductResponse();
            BeanUtil.copyProperties(product, response);
            
            // 填充扩展信息
            this.fillProductExtInfo(response);
            

            
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean validateProductCodeRepeat(String productCode, Long productId) {
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getProductCode, productCode);
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()); // 只检查未删除的商品
        if (ObjectUtil.isNotNull(productId)) {
            wrapper.ne(ErpProduct::getProductId, productId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean validateBarcodeRepeat(String barcode, Long productId) {
        if (StrUtil.isBlank(barcode)) {
            return false;
        }
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getBarcode, barcode);
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()); // 只检查未删除的商品
        if (ObjectUtil.isNotNull(productId)) {
            wrapper.ne(ErpProduct::getProductId, productId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean validateCanDelete(Long productId) {
        // TODO: 这里需要检查商品是否有关联的业务数据
        // 例如：采购订单、销售订单、库存记录等
        // 暂时返回true，后续根据业务需要完善
        return true;
    }

    @Override
    public boolean validateCanInactive(Long productId) {
        // TODO: 这里需要检查商品是否有未完成的业务数据
        // 例如：未完成的订单等
        // 暂时返回true，后续根据业务需要完善
        return true;
    }

    /**
     * 根据主键查询商品
     */
    private ErpProduct queryProduct(ErpProductRequest erpProductRequest) {
        ErpProduct erpProduct = this.getById(erpProductRequest.getProductId());
        if (ObjectUtil.isNull(erpProduct)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_NOT_EXIST);
        }
        return erpProduct;
    }

    /**
     * 创建查询条件（不包含分类过滤）
     */
    private LambdaQueryWrapper<ErpProduct> createWrapperWithoutCategory(ErpProductRequest erpProductRequest) {
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();

        // 搜索文本（支持商品名称和编码模糊查询）
        if (StrUtil.isNotBlank(erpProductRequest.getSearchText())) {
            wrapper.and(w -> w.like(ErpProduct::getProductName, erpProductRequest.getSearchText())
                           .or().like(ErpProduct::getProductCode, erpProductRequest.getSearchText()));
        }

        // 商品编码
        if (StrUtil.isNotBlank(erpProductRequest.getProductCode())) {
            wrapper.like(ErpProduct::getProductCode, erpProductRequest.getProductCode());
        }

        // 商品名称
        if (StrUtil.isNotBlank(erpProductRequest.getProductName())) {
            wrapper.like(ErpProduct::getProductName, erpProductRequest.getProductName());
        }

        // 条形码
        if (StrUtil.isNotBlank(erpProductRequest.getBarcode())) {
            wrapper.like(ErpProduct::getBarcode, erpProductRequest.getBarcode());
        }

        // 品牌
        if (StrUtil.isNotBlank(erpProductRequest.getBrand())) {
            wrapper.like(ErpProduct::getBrand, erpProductRequest.getBrand());
        }

        // 状态
        if (StrUtil.isNotBlank(erpProductRequest.getStatus())) {
            wrapper.eq(ErpProduct::getStatus, erpProductRequest.getStatus());
        }

        // 基本单位
        if (StrUtil.isNotBlank(erpProductRequest.getUnit())) {
            wrapper.eq(ErpProduct::getUnit, erpProductRequest.getUnit());
        }

        // 供应商ID
        if (ObjectUtil.isNotNull(erpProductRequest.getSupplierId())) {
            wrapper.eq(ErpProduct::getSupplierId, erpProductRequest.getSupplierId());
        }

        // 计价类型
        if (StrUtil.isNotBlank(erpProductRequest.getPricingType())) {
            wrapper.eq(ErpProduct::getPricingType, erpProductRequest.getPricingType());
        }

        // 只查询未删除的商品
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode());

        // 按创建时间倒序
        wrapper.orderByDesc(ErpProduct::getCreateTime);

        return wrapper;
    }

    /**
     * 创建查询条件（包含分类过滤，用于兼容性）
     */
    private LambdaQueryWrapper<ErpProduct> createWrapper(ErpProductRequest erpProductRequest) {
        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();

        // 搜索文本（支持商品名称和编码模糊查询）
        if (StrUtil.isNotBlank(erpProductRequest.getSearchText())) {
            wrapper.and(w -> w.like(ErpProduct::getProductName, erpProductRequest.getSearchText())
                           .or().like(ErpProduct::getProductCode, erpProductRequest.getSearchText()));
        }

        // 商品编码
        if (StrUtil.isNotBlank(erpProductRequest.getProductCode())) {
            wrapper.like(ErpProduct::getProductCode, erpProductRequest.getProductCode());
        }

        // 商品名称
        if (StrUtil.isNotBlank(erpProductRequest.getProductName())) {
            wrapper.like(ErpProduct::getProductName, erpProductRequest.getProductName());
        }

        // 条形码
        if (StrUtil.isNotBlank(erpProductRequest.getBarcode())) {
            wrapper.like(ErpProduct::getBarcode, erpProductRequest.getBarcode());
        }

        // 商品分类过滤
        if (ObjectUtil.isNotNull(erpProductRequest.getCategoryId()) && erpProductRequest.getCategoryId() > 0) {
            wrapper.eq(ErpProduct::getCategoryId, erpProductRequest.getCategoryId());
        }

        // 品牌
        if (StrUtil.isNotBlank(erpProductRequest.getBrand())) {
            wrapper.like(ErpProduct::getBrand, erpProductRequest.getBrand());
        }

        // 状态
        if (StrUtil.isNotBlank(erpProductRequest.getStatus())) {
            wrapper.eq(ErpProduct::getStatus, erpProductRequest.getStatus());
        }

        // 基本单位
        if (StrUtil.isNotBlank(erpProductRequest.getUnit())) {
            wrapper.eq(ErpProduct::getUnit, erpProductRequest.getUnit());
        }

        // 供应商ID
        if (ObjectUtil.isNotNull(erpProductRequest.getSupplierId())) {
            wrapper.eq(ErpProduct::getSupplierId, erpProductRequest.getSupplierId());
        }

        // 计价类型
        if (StrUtil.isNotBlank(erpProductRequest.getPricingType())) {
            wrapper.eq(ErpProduct::getPricingType, erpProductRequest.getPricingType());
        }

        // 只查询未删除的商品
        wrapper.eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode());

        // 按创建时间倒序
        wrapper.orderByDesc(ErpProduct::getCreateTime);

        return wrapper;
    }

    /**
     * 校验商品参数
     */
    private void validateProductParams(ErpProduct erpProduct) {
        // 校验状态
        this.validateStatus(erpProduct.getStatus());

        // 校验重量
        if (ObjectUtil.isNotNull(erpProduct.getWeight()) && 
            erpProduct.getWeight().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_WEIGHT_FORMAT_ERROR);
        }

        // 校验体积
        if (ObjectUtil.isNotNull(erpProduct.getVolume()) && 
            erpProduct.getVolume().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_VOLUME_FORMAT_ERROR);
        }

        // 校验保质期
        if (ObjectUtil.isNotNull(erpProduct.getShelfLife()) && erpProduct.getShelfLife() < 0) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_SHELF_LIFE_FORMAT_ERROR);
        }

        // 校验计价类型
        this.validatePricingType(erpProduct.getPricingType());

        // 校验价格设置
        this.validatePriceSettings(erpProduct);
    }

    /**
     * 校验状态
     */
    private void validateStatus(String status) {
        if (!ErpProductConstants.PRODUCT_STATUS_ACTIVE.equals(status) &&
                !ErpProductConstants.PRODUCT_STATUS_INACTIVE.equals(status) &&
                !ErpProductConstants.PRODUCT_STATUS_DISCONTINUED.equals(status)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_STATUS_ERROR);
        }
    }

    /**
     * 填充商品扩展信息
     */
    private void fillProductExtInfo(ErpProductResponse response) {
        // 填充状态名称
        if (ErpProductConstants.PRODUCT_STATUS_ACTIVE.equals(response.getStatus())) {
            response.setStatusName("正常");
        } else if (ErpProductConstants.PRODUCT_STATUS_INACTIVE.equals(response.getStatus())) {
            response.setStatusName("停用");
        } else if (ErpProductConstants.PRODUCT_STATUS_DISCONTINUED.equals(response.getStatus())) {
            response.setStatusName("停产");
        }

        // 填充分类名称（单分类处理）
        if (ObjectUtil.isNotNull(response.getCategoryId())) {
            ErpProductCategory category = erpProductCategoryService.getById(response.getCategoryId());
            if (ObjectUtil.isNotNull(category)) {
                response.setCategoryName(category.getCategoryName());
            }
        }

        // 填充供应商名称
        if (ObjectUtil.isNotNull(response.getSupplierId())) {
            ErpSupplier supplier = erpSupplierService.getById(response.getSupplierId());
            if (ObjectUtil.isNotNull(supplier)) {
                response.setSupplierName(supplier.getSupplierName());
            }
        }

        // 填充计价类型名称
        if (StrUtil.isNotBlank(response.getPricingType())) {
            response.setPricingTypeName(this.getPricingTypeName(response.getPricingType()));
        }

        // 填充库存信息
        if (ObjectUtil.isNotNull(response.getProductId())) {
            try {
                // 查询商品库存信息
                LambdaQueryWrapper<Inventory> inventoryWrapper = new LambdaQueryWrapper<>();
                inventoryWrapper.eq(Inventory::getProductId, response.getProductId());
                Inventory inventory = inventoryService.getOne(inventoryWrapper);

                if (ObjectUtil.isNotNull(inventory)) {
                    response.setStockQuantity(inventory.getCurrentStock());
                    response.setMinStock(inventory.getMinStock());
                    response.setStockValue(inventory.getTotalValue());

                    // 计算库存状态
                    BigDecimal currentStock = inventory.getCurrentStock();
                    BigDecimal minStock = inventory.getMinStock();

                    if (currentStock.compareTo(BigDecimal.ZERO) <= 0) {
                        response.setStockStatus("OUT_OF_STOCK");
                        response.setStockStatusName("缺货");
                    } else if (minStock != null && currentStock.compareTo(minStock) <= 0) {
                        response.setStockStatus("WARNING");
                        response.setStockStatusName("预警");
                    } else {
                        response.setStockStatus("NORMAL");
                        response.setStockStatusName("正常");
                    }
                } else {
                    // 没有库存记录，设置默认值
                    response.setStockQuantity(BigDecimal.ZERO);
                    response.setMinStock(BigDecimal.ZERO);
                    response.setStockValue(BigDecimal.ZERO);
                    response.setStockStatus("OUT_OF_STOCK");
                    response.setStockStatusName("缺货");
                }
            } catch (Exception e) {
                // 查询库存信息失败时，设置默认值
                response.setStockQuantity(BigDecimal.ZERO);
                response.setMinStock(BigDecimal.ZERO);
                response.setStockValue(BigDecimal.ZERO);
                response.setStockStatus("UNKNOWN");
                response.setStockStatusName("未知");
            }
        }
    }



    @Override
    public List<ErpProductResponse> findListBySupplierId(Long supplierId) {
        if (ObjectUtil.isNull(supplierId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ErpProduct> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ErpProduct::getSupplierId, supplierId)
               .eq(ErpProduct::getDelFlag, YesOrNotEnum.N.getCode()) // 只查询未删除的商品
               .eq(ErpProduct::getStatus, ErpProductConstants.PRODUCT_STATUS_ACTIVE)
               .orderByDesc(ErpProduct::getCreateTime);

        List<ErpProduct> productList = this.list(wrapper);

        // 转换为响应对象
        return productList.stream().map(product -> {
            ErpProductResponse response = new ErpProductResponse();
            BeanUtil.copyProperties(product, response);
            
            // 填充扩展信息
            this.fillProductExtInfo(response);
            

            
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse validatePricingTypeChange(Long productId, String newPricingType) {
        cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse response = 
                new cn.stylefeng.roses.kernel.erp.api.pojo.response.PricingTypeChangeValidationResponse();

        // 校验商品是否存在
        ErpProduct product = this.getById(productId);
        if (ObjectUtil.isNull(product)) {
            response.setCanChange(false);
            response.setErrors(List.of("商品不存在"));
            return response;
        }

        // 校验新的计价类型是否有效
        if (!this.isValidPricingType(newPricingType)) {
            response.setCanChange(false);
            response.setErrors(List.of("无效的计价类型"));
            return response;
        }

        // 如果计价类型没有变化，直接返回可以变更
        if (StrUtil.equals(product.getPricingType(), newPricingType)) {
            response.setCanChange(true);
            response.setSuggestion("计价类型没有变化");
            return response;
        }

        List<String> warnings = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        // TODO: 检查是否有相关的销售记录
        // 这里需要查询销售订单表，检查是否有使用该商品的未完成订单
        int affectedSalesCount = 0; // 暂时设为0，后续实现时需要查询实际数据
        response.setAffectedSalesCount(affectedSalesCount);

        // TODO: 检查是否有相关的库存记录
        // 这里需要查询库存表，检查是否有该商品的库存记录
        int affectedInventoryCount = 0; // 暂时设为0，后续实现时需要查询实际数据
        response.setAffectedInventoryCount(affectedInventoryCount);

        // 根据影响程度给出建议
        if (affectedSalesCount > 0) {
            warnings.add(String.format("该商品有 %d 条相关销售记录，变更计价类型可能影响价格计算", affectedSalesCount));
        }

        if (affectedInventoryCount > 0) {
            warnings.add(String.format("该商品有 %d 条库存记录，变更计价类型可能影响库存管理", affectedInventoryCount));
        }

        // 检查价格设置的兼容性
        String currentPricingType = product.getPricingType();
        if (ErpProductConstants.PRICING_TYPE_NORMAL.equals(currentPricingType) && 
            !ErpProductConstants.PRICING_TYPE_NORMAL.equals(newPricingType)) {
            warnings.add("从普通商品变更为其他计价类型，需要重新设置相应的价格");
        }

        response.setWarnings(warnings);
        response.setErrors(errors);
        response.setCanChange(errors.isEmpty());

        if (response.getCanChange()) {
            response.setSuggestion("可以变更计价类型，但请注意上述警告信息");
        } else {
            response.setSuggestion("存在阻止变更的错误，请先解决相关问题");
        }

        return response;
    }

    /**
     * 校验计价类型
     */
    private void validatePricingType(String pricingType) {
        if (StrUtil.isBlank(pricingType)) {
            pricingType = ErpProductConstants.DEFAULT_PRICING_TYPE;
        }
        
        if (!this.isValidPricingType(pricingType)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_PRICING_TYPE_ERROR);
        }
    }

    /**
     * 检查是否是有效的计价类型
     */
    private boolean isValidPricingType(String pricingType) {
        return ErpProductConstants.PRICING_TYPE_NORMAL.equals(pricingType) ||
               ErpProductConstants.PRICING_TYPE_WEIGHT.equals(pricingType) ||
               ErpProductConstants.PRICING_TYPE_PIECE.equals(pricingType) ||
               ErpProductConstants.PRICING_TYPE_VARIABLE.equals(pricingType);
    }

    /**
     * 校验价格设置
     */
    private void validatePriceSettings(ErpProduct erpProduct) {
        String pricingType = StrUtil.isBlank(erpProduct.getPricingType()) ? 
                ErpProductConstants.DEFAULT_PRICING_TYPE : erpProduct.getPricingType();

        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                // 普通商品必须设置零售价格
                if (ObjectUtil.isNull(erpProduct.getRetailPrice()) || 
                    erpProduct.getRetailPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException(ErpProductExceptionEnum.PRODUCT_RETAIL_PRICE_REQUIRED);
                }
                break;
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                // 计重商品必须设置单位价格
                if (ObjectUtil.isNull(erpProduct.getUnitPrice()) || 
                    erpProduct.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException(ErpProductExceptionEnum.PRODUCT_UNIT_PRICE_REQUIRED);
                }
                break;
            case ErpProductConstants.PRICING_TYPE_PIECE:
                // 计件商品必须设置单份价格
                if (ObjectUtil.isNull(erpProduct.getPiecePrice()) || 
                    erpProduct.getPiecePrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException(ErpProductExceptionEnum.PRODUCT_PIECE_PRICE_REQUIRED);
                }
                break;
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                // 不定价商品可以设置参考价格，但不是必须的
                if (ObjectUtil.isNotNull(erpProduct.getReferencePrice()) && 
                    erpProduct.getReferencePrice().compareTo(BigDecimal.ZERO) < 0) {
                    throw new ServiceException(ErpProductExceptionEnum.PRODUCT_REFERENCE_PRICE_FORMAT_ERROR);
                }
                break;
        }
    }

    /**
     * 获取计价类型名称
     */
    private String getPricingTypeName(String pricingType) {
        switch (pricingType) {
            case ErpProductConstants.PRICING_TYPE_NORMAL:
                return "普通商品";
            case ErpProductConstants.PRICING_TYPE_WEIGHT:
                return "计重商品";
            case ErpProductConstants.PRICING_TYPE_PIECE:
                return "计件商品";
            case ErpProductConstants.PRICING_TYPE_VARIABLE:
                return "不定价商品";
            default:
                return "未知";
        }
    }

    /**
     * 验证分类ID有效性
     */
    private void validateCategoryId(Long categoryId) {
        if (ObjectUtil.isNull(categoryId)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_CATEGORY_REQUIRED);
        }
        
        ErpProductCategory category = erpProductCategoryService.getById(categoryId);
        if (ObjectUtil.isNull(category)) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_CATEGORY_NOT_EXIST);
        }
        
        if (!"Y".equals(category.getStatus())) {
            throw new ServiceException(ErpProductExceptionEnum.PRODUCT_CATEGORY_DISABLED);
        }
    }
}