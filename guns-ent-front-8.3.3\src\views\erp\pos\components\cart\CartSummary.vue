<!--
  购物车汇总组件
  
  显示购物车总计信息，包含优惠券应用和结账功能
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cart-summary">
    <!-- 金额明细 -->
    <div class="amount-details">
      <!-- 商品总计 -->
      <div class="amount-row">
        <span class="amount-label">
          <icon-font iconClass="icon-shopping" />
          商品总计（{{ summary.itemCount }}件）
        </span>
        <span class="amount-value">{{ formatPrice(total.totalAmount) }}</span>
      </div>
      
      <!-- 折扣金额 -->
      <div v-if="total.discountAmount > 0" class="amount-row discount-row">
        <span class="amount-label">
          <icon-font iconClass="icon-discount" />
          优惠折扣
        </span>
        <span class="amount-value discount-value">-{{ formatPrice(total.discountAmount) }}</span>
      </div>
    </div>
    
    <!-- 总计分割线 -->
    <a-divider style="margin: 12px 0;" />
    
    <!-- 应付总额 -->
    <div class="total-amount">
      <div class="total-row">
        <span class="total-label">
          <icon-font iconClass="icon-money" />
          应付总额
        </span>
        <span class="total-value">{{ formatPrice(total.payableAmount) }}</span>
      </div>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="action-buttons">
      <!-- 结账按钮 -->
      <a-button 
        size="large"
        type="primary"
        :loading="loading"
        :disabled="!canCheckout"
        @click="handleCheckout"
      >
        <template #icon>
          <icon-font iconClass="icon-pay" />
        </template>
        立即结账
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { message } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'
import { AmountFormatter } from '../../utils/formatter'

// 定义组件名称
defineOptions({
  name: 'CartSummary'
})

// 定义Props
const props = defineProps({
  // 购物车汇总信息
  summary: {
    type: Object,
    required: true
  },
  // 总计信息
  total: {
    type: Object,
    required: true
  },
  // 是否可以结账
  canCheckout: {
    type: Boolean,
    default: false
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'checkout'
])

// ==================== 方法 ====================

/**
 * 格式化价格显示
 * @param {number} price - 价格
 * @returns {string} 格式化后的价格
 */
const formatPrice = (price) => {
  return AmountFormatter.formatCurrency(price || 0)
}

/**
 * 处理结账
 */
const handleCheckout = () => {
  if (!props.canCheckout) {
    message.warning('购物车为空或商品信息有误，无法结账')
    return
  }
  
  emit('checkout')
}
</script>

<style scoped>
.cart-summary {
  padding: 16px;
  background: #fff;
}

/* 金额明细 */
.amount-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.amount-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
}

.amount-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #595959;
}

.amount-value {
  color: #262626;
  font-weight: 500;
}

.discount-row .amount-label {
  color: #52c41a;
}

.discount-value {
  color: #52c41a !important;
}

/* 应付总额 */
.total-amount {
  margin-top: 8px;
}

.total-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
}

.total-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #262626;
}

.total-value {
  color: #ff4d4f;
  font-size: 18px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.action-buttons .ant-btn {
  flex: 1;
  height: 44px;
  font-size: 14px;
  font-weight: 500;
}
</style>