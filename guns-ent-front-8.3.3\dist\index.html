<!doctype html>
<html lang="en">
  <head>
    <script type="module" crossorigin src="/assets/polyfills-389a44ba.js"></script>

    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Guns Tech.</title>
    <style>
      .global-loading {
        width: 36px;
        font-size: 0;
        display: inline-block;
        transform: rotate(45deg);
        animation: loadingRotate 1.2s infinite linear;
        position: relative;
        top: calc(50% - 18px);
        left: calc(50% - 18px);
      }

      .global-loading span {
        width: 10px;
        height: 10px;
        margin: 4px;
        border-radius: 50%;
        background: #1890ff;
        display: inline-block;
        opacity: 0.9;
      }

      .global-loading span:nth-child(2) {
        opacity: 0.7;
      }

      .global-loading span:nth-child(3) {
        opacity: 0.5;
      }

      .global-loading span:nth-child(4) {
        opacity: 0.3;
      }

      @keyframes loadingRotate {
        to {
          transform: rotate(405deg);
        }
      }

      #app > .global-loading {
        position: fixed;
      }
      #app {
        height: 100%;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-18a1ea24.js"></script>
    <link rel="stylesheet" href="/assets/index-747cb573.css">
    <script type="module">import.meta.url;import("_").catch(()=>1);async function* g(){};if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
    <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
  </head>
  <body>
    <div id="app">
      <div class="global-loading">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    
    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: '50eeadb2b43161cf0b7fb21accbe084e'
      };
    </script>
    <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
    <script nomodule crossorigin id="vite-legacy-polyfill" src="/assets/polyfills-legacy-7d6f2429.js"></script>
    <script nomodule crossorigin id="vite-legacy-entry" data-src="/assets/index-legacy-ee1db0c7.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
  </body>
</html>
