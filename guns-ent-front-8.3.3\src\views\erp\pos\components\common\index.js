/**
 * 通用组件统一导出
 * 
 * 使用方式：
 * import { FunctionButton, OrderSummary } from './common'
 * 或
 * import CommonComponents from './common'
 * const { FunctionButton } = CommonComponents
 */

// 导入所有通用组件
import FunctionButton from './FunctionButton.vue'
import OrderSummary from './OrderSummary.vue'
import SuspendedOrderItem from './SuspendedOrderItem.vue'
import ErrorBoundary from './ErrorBoundary.vue'
import PerformanceMonitor from './PerformanceMonitor.vue'

// 统一导出
export {
  FunctionButton,
  OrderSummary,
  SuspendedOrderItem,
  ErrorBoundary,
  PerformanceMonitor
}

// 默认导出（用于按需导入）
export default {
  FunctionButton,
  OrderSummary,
  SuspendedOrderItem,
  ErrorBoundary,
  PerformanceMonitor
}