/**
 * 订单组合式函数单元测试
 * 
 * 测试订单业务逻辑的正确性
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useOrder } from '../useOrder'
import { usePosStore } from '@/stores/pos'
import { OrderApi } from '../../api/order'
import { ORDER_STATUS } from '../../utils/constants'
import { message } from 'ant-design-vue'

// Mock dependencies
vi.mock('../../api/order')
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

describe('useOrder', () => {
  let pinia
  let store
  
  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    store = usePosStore()
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('基础状态', () => {
    it('应该正确初始化订单状态', () => {
      const { 
        currentOrder, 
        orderStatus, 
        canCreateOrder, 
        canCancelOrder 
      } = useOrder()
      
      expect(currentOrder.value).toBe(null)
      expect(orderStatus.value).toBe('')
      expect(canCreateOrder.value).toBe(false)
      expect(canCancelOrder.value).toBe(false)
    })
    
    it('应该正确判断是否可以创建订单', () => {
      const { canCreateOrder } = useOrder()
      
      // 设置购物车数据
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
      store.finalAmount = 11
      
      expect(canCreateOrder.value).toBe(true)
    })
    
    it('应该正确判断是否可以取消订单', () => {
      const { canCancelOrder } = useOrder()
      
      store.currentOrder = {
        id: 'ORDER001',
        status: ORDER_STATUS.PENDING
      }
      
      expect(canCancelOrder.value).toBe(true)
    })
  })
  
  describe('createOrder', () => {
    it('应该成功创建订单', async () => {
      const { createOrder } = useOrder()
      
      // 设置购物车数据
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
      store.totalAmount = 11
      store.discountAmount = 1
      store.finalAmount = 10
      store.currentMember = { id: 'M001', cardNo: 'VIP123456' }
      
      const mockResponse = {
        success: true,
        orderId: 'ORDER001',
        orderNo: 'POS20250102001',
        status: ORDER_STATUS.PENDING,
        createdAt: '2025-01-02T10:00:00Z'
      }
      
      OrderApi.createOrder.mockResolvedValue(mockResponse)
      
      const result = await createOrder({ remark: '测试订单' })
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.createOrder).toHaveBeenCalledWith({
        items: store.cartItems,
        member: store.currentMember,
        totalAmount: 11,
        discountAmount: 1,
        finalAmount: 10,
        cashierId: expect.any(String),
        remark: '测试订单'
      })
      expect(message.success).toHaveBeenCalledWith('订单创建成功，订单号: POS20250102001')
    })
    
    it('应该处理空购物车的情况', async () => {
      const { createOrder } = useOrder()
      
      store.cartItems = []
      
      const result = await createOrder()
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('购物车为空，无法创建订单')
    })
    
    it('应该处理订单创建失败的情况', async () => {
      const { createOrder } = useOrder()
      
      store.cartItems = [
        { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
      ]
      store.finalAmount = 10
      
      const mockError = new Error('库存不足')
      OrderApi.createOrder.mockRejectedValue(mockError)
      
      const result = await createOrder()
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('订单创建失败: 库存不足')
    })
  })
  
  describe('getOrderDetail', () => {
    it('应该成功获取订单详情', async () => {
      const { getOrderDetail } = useOrder()
      
      const mockOrder = {
        orderId: 'ORDER001',
        orderNo: 'POS20250102001',
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
        ],
        totalAmount: 11,
        finalAmount: 10,
        status: ORDER_STATUS.PAID,
        createdAt: '2025-01-02T10:00:00Z'
      }
      
      OrderApi.getOrderDetail.mockResolvedValue(mockOrder)
      
      const result = await getOrderDetail('ORDER001')
      
      expect(result).toEqual(mockOrder)
      expect(OrderApi.getOrderDetail).toHaveBeenCalledWith('ORDER001')
    })
    
    it('应该处理订单不存在的情况', async () => {
      const { getOrderDetail } = useOrder()
      
      const mockError = new Error('订单不存在')
      OrderApi.getOrderDetail.mockRejectedValue(mockError)
      
      const result = await getOrderDetail('INVALID')
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('获取订单详情失败: 订单不存在')
    })
  })
  
  describe('updateOrderStatus', () => {
    it('应该成功更新订单状态', async () => {
      const { updateOrderStatus } = useOrder()
      
      const updateParams = {
        orderId: 'ORDER001',
        status: ORDER_STATUS.PAID,
        remark: '支付完成'
      }
      
      const mockResponse = {
        success: true,
        orderId: 'ORDER001',
        status: ORDER_STATUS.PAID,
        updatedAt: '2025-01-02T10:30:00Z'
      }
      
      OrderApi.updateOrderStatus.mockResolvedValue(mockResponse)
      
      const result = await updateOrderStatus(updateParams)
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.updateOrderStatus).toHaveBeenCalledWith({
        ...updateParams,
        updatedBy: expect.any(String)
      })
      expect(message.success).toHaveBeenCalledWith('订单状态更新成功')
    })
  })
  
  describe('cancelOrder', () => {
    it('应该成功取消订单', async () => {
      const { cancelOrder } = useOrder()
      
      const cancelParams = {
        orderId: 'ORDER001',
        cancelReason: '用户取消'
      }
      
      const mockResponse = {
        success: true,
        orderId: 'ORDER001',
        status: ORDER_STATUS.CANCELLED,
        cancelledAt: '2025-01-02T10:45:00Z'
      }
      
      OrderApi.cancelOrder.mockResolvedValue(mockResponse)
      
      const result = await cancelOrder(cancelParams)
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.cancelOrder).toHaveBeenCalledWith({
        ...cancelParams,
        cancelBy: expect.any(String)
      })
      expect(message.success).toHaveBeenCalledWith('订单取消成功')
    })
    
    it('应该处理已支付订单取消失败', async () => {
      const { cancelOrder } = useOrder()
      
      const mockError = new Error('已支付订单无法取消')
      OrderApi.cancelOrder.mockRejectedValue(mockError)
      
      const result = await cancelOrder({
        orderId: 'ORDER001',
        cancelReason: '测试'
      })
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('订单取消失败: 已支付订单无法取消')
    })
  })
  
  describe('searchOrders', () => {
    it('应该成功搜索订单', async () => {
      const { searchOrders } = useOrder()
      
      const searchParams = {
        keyword: 'POS20250102001',
        searchType: 'orderNo'
      }
      
      const mockOrders = [
        {
          orderId: 'ORDER001',
          orderNo: 'POS20250102001',
          totalAmount: 100,
          status: ORDER_STATUS.PAID,
          createdAt: '2025-01-02T10:00:00Z'
        }
      ]
      
      OrderApi.searchOrders.mockResolvedValue(mockOrders)
      
      const result = await searchOrders(searchParams)
      
      expect(result).toEqual(mockOrders)
      expect(OrderApi.searchOrders).toHaveBeenCalledWith(searchParams)
    })
    
    it('应该处理空搜索结果', async () => {
      const { searchOrders } = useOrder()
      
      OrderApi.searchOrders.mockResolvedValue([])
      
      const result = await searchOrders({ keyword: '不存在的订单' })
      
      expect(result).toEqual([])
      expect(message.info).toHaveBeenCalledWith('未找到匹配的订单')
    })
  })
  
  describe('generateOrderNo', () => {
    it('应该成功生成订单号', async () => {
      const { generateOrderNo } = useOrder()
      
      const mockResponse = {
        orderNo: 'POS20250102001',
        prefix: 'POS',
        sequence: 1
      }
      
      OrderApi.generateOrderNo.mockResolvedValue(mockResponse)
      
      const result = await generateOrderNo()
      
      expect(result).toBe('POS20250102001')
      expect(OrderApi.generateOrderNo).toHaveBeenCalledWith({
        storeId: expect.any(String)
      })
    })
  })
  
  describe('printOrder', () => {
    it('应该成功打印订单', async () => {
      const { printOrder } = useOrder()
      
      const printParams = {
        orderId: 'ORDER001',
        printType: 'receipt'
      }
      
      const mockResponse = {
        success: true,
        printJobId: 'PRINT001',
        status: 'QUEUED'
      }
      
      OrderApi.printOrder.mockResolvedValue(mockResponse)
      
      const result = await printOrder(printParams)
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.printOrder).toHaveBeenCalledWith({
        ...printParams,
        printerName: expect.any(String)
      })
      expect(message.success).toHaveBeenCalledWith('订单已发送到打印机')
    })
    
    it('应该处理打印失败的情况', async () => {
      const { printOrder } = useOrder()
      
      const mockError = new Error('打印机离线')
      OrderApi.printOrder.mockRejectedValue(mockError)
      
      const result = await printOrder({ orderId: 'ORDER001' })
      
      expect(result).toBe(null)
      expect(message.error).toHaveBeenCalledWith('订单打印失败: 打印机离线')
    })
  })
  
  describe('getOrderStatistics', () => {
    it('应该成功获取订单统计', async () => {
      const { getOrderStatistics } = useOrder()
      
      const queryParams = {
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z'
      }
      
      const mockStats = {
        totalOrders: 10,
        totalAmount: 1000,
        averageAmount: 100,
        statusStats: {
          [ORDER_STATUS.PAID]: 8,
          [ORDER_STATUS.CANCELLED]: 2
        }
      }
      
      OrderApi.getOrderStatistics.mockResolvedValue(mockStats)
      
      const result = await getOrderStatistics(queryParams)
      
      expect(result).toEqual(mockStats)
      expect(OrderApi.getOrderStatistics).toHaveBeenCalledWith({
        ...queryParams,
        cashierId: expect.any(String)
      })
    })
  })
  
  describe('validateOrderData', () => {
    it('应该成功验证订单数据', async () => {
      const { validateOrderData } = useOrder()
      
      const orderData = {
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 }
        ],
        totalAmount: 11,
        finalAmount: 10
      }
      
      const mockResponse = {
        valid: true,
        errors: []
      }
      
      OrderApi.validateOrderData.mockResolvedValue(mockResponse)
      
      const result = await validateOrderData(orderData)
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.validateOrderData).toHaveBeenCalledWith(orderData)
    })
    
    it('应该处理验证失败的情况', async () => {
      const { validateOrderData } = useOrder()
      
      const invalidOrderData = {
        items: [],
        totalAmount: 0,
        finalAmount: 0
      }
      
      const mockResponse = {
        valid: false,
        errors: ['订单商品不能为空', '订单金额不能为0']
      }
      
      OrderApi.validateOrderData.mockResolvedValue(mockResponse)
      
      const result = await validateOrderData(invalidOrderData)
      
      expect(result).toEqual(mockResponse)
      expect(message.warning).toHaveBeenCalledWith('订单数据验证失败')
    })
  })
  
  describe('getOrderReceipt', () => {
    it('应该成功获取订单小票', async () => {
      const { getOrderReceipt } = useOrder()
      
      const receiptParams = {
        orderId: 'ORDER001',
        template: 'default'
      }
      
      const mockReceipt = {
        receiptData: {
          orderNo: 'POS20250102001',
          items: [
            { name: '苹果', quantity: 2, price: 5.5, subtotal: 11 }
          ],
          totalAmount: 11,
          finalAmount: 10,
          printTime: '2025-01-02 10:30:00'
        },
        template: 'default'
      }
      
      OrderApi.getOrderReceipt.mockResolvedValue(mockReceipt)
      
      const result = await getOrderReceipt(receiptParams)
      
      expect(result).toEqual(mockReceipt)
      expect(OrderApi.getOrderReceipt).toHaveBeenCalledWith(receiptParams)
    })
  })
  
  describe('exportOrders', () => {
    it('应该成功导出订单', async () => {
      const { exportOrders } = useOrder()
      
      const exportParams = {
        startTime: '2025-01-02T00:00:00Z',
        endTime: '2025-01-02T23:59:59Z',
        format: 'excel'
      }
      
      const mockResponse = {
        success: true,
        downloadUrl: '/api/download/orders_20250102.xlsx',
        fileName: 'orders_20250102.xlsx'
      }
      
      OrderApi.exportOrders.mockResolvedValue(mockResponse)
      
      const result = await exportOrders(exportParams)
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.exportOrders).toHaveBeenCalledWith({
        ...exportParams,
        fields: ['orderNo', 'totalAmount', 'status', 'createdAt']
      })
      expect(message.success).toHaveBeenCalledWith('订单导出成功，文件名: orders_20250102.xlsx')
    })
  })
  
  describe('getOrderTimeline', () => {
    it('应该成功获取订单时间线', async () => {
      const { getOrderTimeline } = useOrder()
      
      const mockTimeline = [
        {
          action: 'CREATE',
          status: ORDER_STATUS.PENDING,
          timestamp: '2025-01-02T10:00:00Z',
          operator: 'CASHIER001',
          remark: '订单创建'
        },
        {
          action: 'PAY',
          status: ORDER_STATUS.PAID,
          timestamp: '2025-01-02T10:30:00Z',
          operator: 'CASHIER001',
          remark: '支付完成'
        }
      ]
      
      OrderApi.getOrderTimeline.mockResolvedValue(mockTimeline)
      
      const result = await getOrderTimeline('ORDER001')
      
      expect(result).toEqual(mockTimeline)
      expect(OrderApi.getOrderTimeline).toHaveBeenCalledWith('ORDER001')
    })
  })
  
  describe('batchUpdateOrders', () => {
    it('应该成功批量更新订单', async () => {
      const { batchUpdateOrders } = useOrder()
      
      const batchParams = {
        orderIds: ['ORDER001', 'ORDER002'],
        updates: {
          status: ORDER_STATUS.COMPLETED
        }
      }
      
      const mockResponse = {
        success: true,
        updatedCount: 2,
        results: [
          { orderId: 'ORDER001', success: true },
          { orderId: 'ORDER002', success: true }
        ]
      }
      
      OrderApi.batchUpdateOrders.mockResolvedValue(mockResponse)
      
      const result = await batchUpdateOrders(batchParams)
      
      expect(result).toEqual(mockResponse)
      expect(OrderApi.batchUpdateOrders).toHaveBeenCalledWith({
        ...batchParams,
        updates: {
          ...batchParams.updates,
          updatedBy: expect.any(String)
        }
      })
      expect(message.success).toHaveBeenCalledWith('批量更新成功，共更新2个订单')
    })
  })
  
  describe('calculateOrderSummary', () => {
    it('应该正确计算订单汇总', () => {
      const { calculateOrderSummary } = useOrder()
      
      const orderData = {
        items: [
          { id: 'P001', name: '苹果', price: 5.5, quantity: 2, subtotal: 11 },
          { id: 'P002', name: '香蕉', price: 3.0, quantity: 3, subtotal: 9 }
        ],
        discountAmount: 2,
        member: { id: 'M001', discountRate: 0.1 }
      }
      
      const result = calculateOrderSummary(orderData)
      
      expect(result).toEqual({
        itemCount: 2,
        totalQuantity: 5,
        subtotal: 20,
        discountAmount: 2,
        finalAmount: 18,
        averageItemPrice: 4,
        hasMember: true,
        memberDiscount: 0.1
      })
    })
  })
  
  describe('resetOrderState', () => {
    it('应该重置订单状态', () => {
      const { resetOrderState } = useOrder()
      
      // 设置一些状态
      store.currentOrder = { id: 'ORDER001' }
      store.orderStatus = ORDER_STATUS.PAID
      
      // 重置状态
      resetOrderState()
      
      expect(store.currentOrder).toBe(null)
      expect(store.orderStatus).toBe('')
    })
  })
})