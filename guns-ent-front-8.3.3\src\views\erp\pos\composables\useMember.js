/**
 * 会员业务逻辑组合式函数
 * 
 * 提供会员相关的业务逻辑处理
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { usePosStore } from '@/stores/pos'

export function useMember() {
  const posStore = usePosStore()
  
  /**
   * 选择会员
   */
  const selectMember = (member) => {
    posStore.setCurrentMember(member)
  }
  
  /**
   * 清除会员
   */
  const clearMember = () => {
    posStore.clearCurrentMember()
  }
  
  /**
   * 应用会员折扣
   */
  const applyMemberDiscount = (memberId) => {
    posStore.applyMemberDiscount(memberId)
  }
  
  /**
   * 清除会员折扣
   */
  const clearMemberDiscount = () => {
    posStore.clearMemberDiscount()
  }
  
  return {
    selectMember,
    clearMember,
    applyMemberDiscount,
    clearMemberDiscount
  }
}