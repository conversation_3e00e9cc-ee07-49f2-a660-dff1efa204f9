<template>
  <div class="pos-main" :class="{ 'fullscreen': isFullscreen }">
    <!-- 顶部工具栏 -->
    <div class="pos-toolbar">
      <div class="toolbar-left">
        <div class="pos-logo">
          <icon-font iconClass="icon-pos" />
          <span class="logo-text">POS收银系统</span>
        </div>
        <div class="cashier-info">
          <span class="cashier-name">收银员: {{ currentUser.realName }}</span>
          <span class="work-time">{{ currentTime }}</span>
        </div>
      </div>
      
      <div class="toolbar-right">
        <a-space>
          <a-tooltip title="快捷键: F1">
            <a-button @click="showHelp" class="toolbar-btn">
              <template #icon>
                <question-circle-outlined />
              </template>
              帮助
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="快捷键: F11">
            <a-button @click="toggleFullscreen" class="toolbar-btn">
              <template #icon>
                <fullscreen-outlined v-if="!isFullscreen" />
                <fullscreen-exit-outlined v-else />
              </template>
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </a-button>
          </a-tooltip>
          
          <a-tooltip title="快捷键: Ctrl+R">
            <a-button @click="resetAll" class="toolbar-btn">
              <template #icon>
                <reload-outlined />
              </template>
              重置
            </a-button>
          </a-tooltip>
          
          <a-button @click="logout" type="primary" danger class="logout-btn">
            <template #icon>
              <logout-outlined />
            </template>
            退出登录
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="pos-content">
      <!-- 左侧区域：商品展示区域 -->
      <div class="pos-left pos-card">
        <ProductDisplayArea
          :categories="categories"
          :products="filteredProducts"
          :loading="productsLoading"
          @productAdd="handleProductAdd"
          @productSelect="handleProductSelect"
          @categoryChange="handleCategoryChange"
          @search="handleSearch"
          @filterChange="handlePriceFilter"
        />
      </div>

      <!-- 中间区域：购物车区域 -->
      <div class="pos-center">
        <!-- 购物车组件 -->
        <div class="cart-section pos-card">
          <ShoppingCart
            :current-member="currentMember"
            @item-change="handleCartChange"
            @checkout="handleCheckout"
            @suspend-order="handleSuspendOrder"
            @memberChange="handleMemberChange"
            @memberSelect="handleMemberSelect"
            @memberClear="handleMemberClear"
          />
        </div>
      </div>

      <!-- 右侧区域：功能操作区域 -->
      <div class="pos-right">
        <ToolbarPanel
          :suspended-orders-count="suspendedOrdersCount"
          @showSuspendedOrders="handleShowSuspendedOrders"
          @memberManagement="handleMemberManagement"
        />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="pos-statusbar">
      <div class="status-left">
        <span class="status-item">
          <icon-font iconClass="icon-cart" />
          购物车: {{ cartSummary.itemCount }}件
        </span>
        <span class="status-item">
          <icon-font iconClass="icon-money" />
          金额: {{ formatAmount(cartSummary.finalAmount) }}
        </span>
        <span class="status-item" v-if="hasMember">
          <icon-font iconClass="icon-member" />
          会员: {{ currentMember.memberName }}
        </span>
      </div>
      
      <div class="status-right">
        <span class="status-item">
          <icon-font iconClass="icon-suspend" />
          挂单: {{ suspendedOrdersCount }}个
        </span>
        <span class="status-item">
          <icon-font iconClass="icon-time" />
          {{ currentDate }}
        </span>
      </div>
    </div>

    <!-- 挂单管理抽屉 -->
    <OrderSuspend
      v-model:visible="showSuspendedOrdersDrawer"
      @orderSuspended="handleOrderSuspended"
      @orderResumed="handleOrderResumed"
      @orderDeleted="handleOrderDeleted"
    />

    <!-- 支付面板 -->
    <PaymentPanel
      v-model:visible="showPaymentPanel"
      :order-info="orderInfo"
      :member-info="currentMember"
      @paymentSuccess="handlePaymentSuccess"
      @paymentCancel="handlePaymentCancel"
    />

    <!-- 会员选择对话框 -->
    <MemberSelectDialog
      v-model:open="showMemberSelectDialog"
      @select="handleMemberSelected"
    />

    <!-- 快捷键帮助对话框 -->
    <a-modal
      v-model:open="showHelpModal"
      title="快捷键帮助"
      :footer="null"
      width="600px"
      class="help-modal"
    >
      <div class="help-content">
        <div class="help-section">
          <h4>基本操作</h4>
          <div class="help-item">
            <span class="key">F1</span>
            <span class="desc">显示帮助</span>
          </div>
          <div class="help-item">
            <span class="key">F11</span>
            <span class="desc">切换全屏</span>
          </div>
          <div class="help-item">
            <span class="key">Ctrl+R</span>
            <span class="desc">重置所有状态</span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { 
  QuestionCircleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ReloadOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'
import IconFont from '@/components/common/IconFont/index.vue'

// 导入组件
import ProductDisplayArea from './components/product/ProductDisplayArea.vue'
import ShoppingCart from './components/cart/ShoppingCart.vue'
import ToolbarPanel from './components/ToolbarPanel.vue'
import OrderSuspend from './components/OrderSuspend.vue'
import PaymentPanel from './components/payment/PaymentPanel.vue'
import MemberSelectDialog from './components/member/MemberSelectDialog.vue'

// 导入Composables
import { usePos } from './composables/usePos'
import { useKeyboard } from './composables/useKeyboard'

// 导入样式文件
import './styles/common.css'

// 定义组件名称
defineOptions({
  name: 'PosIndex'
})

// 使用POS业务逻辑
const {
  // 状态
  currentUser,
  currentTime,
  currentDate,
  isFullscreen,
  showHelpModal,
  showPaymentPanel,
  showSuspendedOrdersDrawer,
  showMemberSelectDialog,
  cartSummary,
  hasMember,
  currentMember,
  suspendedOrdersCount,
  orderInfo,
  
  // 商品相关状态
  productsLoading,
  categories,
  products,
  filteredProducts,
  
  // 方法
  toggleFullscreen,
  showHelp,
  resetAll,
  logout,
  formatAmount,
  
  // 商品相关方法
  handleCategoryChange,
  handleSearch,
  handlePriceFilter,
  
  // 事件处理
  handleProductAdd,
  handleProductSelect,
  handleCartChange,
  handleCartClear,
  handleCheckout,
  handleSuspendOrder,
  handleShowSuspendedOrders,
  handleMemberManagement,
  handleMemberChange,
  handleMemberSelect,
  handleMemberSelected,
  handleMemberClear,
  handleOrderSuspended,
  handleOrderResumed,
  handleOrderDeleted,
  handlePaymentSuccess,
  handlePaymentCancel
} = usePos()

// 使用键盘快捷键
useKeyboard()
</script>

<style scoped>
.pos-main {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.pos-main.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

.pos-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.pos-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.logo-text {
  font-size: 16px;
}

.cashier-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.pos-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
  min-height: 0;
}

.pos-left {
  flex: 3;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.pos-center {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.cart-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.pos-right {
  width: 120px;
  flex-shrink: 0;
}

.pos-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pos-statusbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 20px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.help-content {
  padding: 16px 0;
}

.help-section {
  margin-bottom: 24px;
}

.help-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.help-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.help-item .key {
  display: inline-block;
  padding: 2px 8px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  min-width: 60px;
  text-align: center;
}

.help-item .desc {
  font-size: 13px;
  color: #595959;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pos-content {
    gap: 12px;
    padding: 12px;
  }
  
  .pos-right {
    width: 100px;
  }
}

@media (max-width: 768px) {
  .pos-content {
    flex-direction: column;
  }
  
  .pos-left,
  .pos-center,
  .pos-right {
    flex: none;
    width: 100%;
  }
  
  .pos-right {
    height: 80px;
  }
}
</style>