/**
 * 增强版性能监控器
 * 
 * 监控组件渲染、API调用性能，提供内存使用监控和优化建议
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

/**
 * 增强版性能监控器类
 */
export class EnhancedPerformanceMonitor {
  
  // 性能数据存储
  static performanceData = {
    componentRender: new Map(),
    apiCalls: new Map(),
    memoryUsage: [],
    userInteractions: new Map(),
    routeChanges: [],
    bundleSize: null,
    vitals: {
      FCP: null, // First Contentful Paint
      LCP: null, // Largest Contentful Paint
      FID: null, // First Input Delay
      CLS: null  // Cumulative Layout Shift
    }
  }
  
  // 性能阈值配置
  static thresholds = {
    componentRender: 16, // 16ms (60fps)
    apiCall: 3000,       // 3秒
    memoryUsage: 100,    // 100MB
    bundleSize: 2,       // 2MB
    FCP: 1800,          // 1.8秒
    LCP: 2500,          // 2.5秒
    FID: 100,           // 100ms
    CLS: 0.1            // 0.1
  }
  
  // 监控配置
  static config = {
    enableComponentMonitoring: true,
    enableApiMonitoring: true,
    enableMemoryMonitoring: true,
    enableUserInteractionMonitoring: true,
    enableVitalsMonitoring: true,
    sampleRate: 1.0, // 采样率
    reportInterval: 30000, // 30秒报告间隔
    maxDataPoints: 1000 // 最大数据点数量
  }
  
  // 报告定时器
  static reportTimer = null
  
  /**
   * 初始化性能监控器
   * @param {Object} options - 配置选项
   */
  static initialize(options = {}) {
    // 合并配置
    this.config = { ...this.config, ...options }
    
    // 初始化Web Vitals监控
    if (this.config.enableVitalsMonitoring) {
      this.initializeWebVitals()
    }
    
    // 初始化内存监控
    if (this.config.enableMemoryMonitoring) {
      this.initializeMemoryMonitoring()
    }
    
    // 初始化用户交互监控
    if (this.config.enableUserInteractionMonitoring) {
      this.initializeUserInteractionMonitoring()
    }
    
    // 启动定期报告
    this.startPeriodicReporting()
    
    console.log('[POS] 增强版性能监控器已初始化')
  }
  
  /**
   * 初始化Web Vitals监控
   */
  static initializeWebVitals() {
    // 监控First Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.performanceData.vitals.FCP = entry.startTime
              this.checkThreshold('FCP', entry.startTime)
            }
          }
        })
        observer.observe({ entryTypes: ['paint'] })
      } catch (error) {
        console.warn('FCP监控初始化失败:', error)
      }
    }
    
    // 监控Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          this.performanceData.vitals.LCP = lastEntry.startTime
          this.checkThreshold('LCP', lastEntry.startTime)
        })
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (error) {
        console.warn('LCP监控初始化失败:', error)
      }
    }
    
    // 监控First Input Delay
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.performanceData.vitals.FID = entry.processingStart - entry.startTime
            this.checkThreshold('FID', this.performanceData.vitals.FID)
          }
        })
        observer.observe({ entryTypes: ['first-input'] })
      } catch (error) {
        console.warn('FID监控初始化失败:', error)
      }
    }
    
    // 监控Cumulative Layout Shift
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          }
          this.performanceData.vitals.CLS = clsValue
          this.checkThreshold('CLS', clsValue)
        })
        observer.observe({ entryTypes: ['layout-shift'] })
      } catch (error) {
        console.warn('CLS监控初始化失败:', error)
      }
    }
  }
  
  /**
   * 初始化内存监控
   */
  static initializeMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        const memoryInfo = {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit,
          timestamp: Date.now()
        }
        
        this.performanceData.memoryUsage.push(memoryInfo)
        
        // 保持数据点数量在限制内
        if (this.performanceData.memoryUsage.length > this.config.maxDataPoints) {
          this.performanceData.memoryUsage.shift()
        }
        
        // 检查内存使用阈值
        const usedMB = memoryInfo.used / (1024 * 1024)
        this.checkThreshold('memoryUsage', usedMB)
        
      }, 5000) // 每5秒检查一次
    }
  }
  
  /**
   * 初始化用户交互监控
   */
  static initializeUserInteractionMonitoring() {
    const interactionTypes = ['click', 'keydown', 'scroll', 'touchstart']
    
    interactionTypes.forEach(type => {
      document.addEventListener(type, (event) => {
        const interactionData = this.performanceData.userInteractions.get(type) || []
        interactionData.push({
          timestamp: Date.now(),
          target: event.target.tagName,
          detail: this.getInteractionDetail(event)
        })
        
        // 保持数据点数量在限制内
        if (interactionData.length > this.config.maxDataPoints) {
          interactionData.shift()
        }
        
        this.performanceData.userInteractions.set(type, interactionData)
      }, { passive: true })
    })
  }
  
  /**
   * 获取交互详情
   * @param {Event} event - 事件对象
   * @returns {Object} 交互详情
   */
  static getInteractionDetail(event) {
    switch (event.type) {
      case 'click':
        return {
          x: event.clientX,
          y: event.clientY,
          button: event.button
        }
      case 'keydown':
        return {
          key: event.key,
          code: event.code
        }
      case 'scroll':
        return {
          scrollX: window.scrollX,
          scrollY: window.scrollY
        }
      case 'touchstart':
        return {
          touches: event.touches.length
        }
      default:
        return {}
    }
  }
  
  /**
   * 监控组件渲染性能
   * @param {string} componentName - 组件名称
   * @param {Function} renderFunction - 渲染函数
   * @returns {Function} 包装后的渲染函数
   */
  static measureComponentRender(componentName, renderFunction) {
    if (!this.config.enableComponentMonitoring || Math.random() > this.config.sampleRate) {
      return renderFunction
    }
    
    return (...args) => {
      const startTime = performance.now()
      const startMemory = this.getCurrentMemoryUsage()
      
      try {
        const result = renderFunction(...args)
        
        // 如果是Promise，等待完成后测量
        if (result && typeof result.then === 'function') {
          return result.then(
            (value) => {
              this.recordComponentRender(componentName, startTime, startMemory, true)
              return value
            },
            (error) => {
              this.recordComponentRender(componentName, startTime, startMemory, false, error)
              throw error
            }
          )
        } else {
          this.recordComponentRender(componentName, startTime, startMemory, true)
          return result
        }
      } catch (error) {
        this.recordComponentRender(componentName, startTime, startMemory, false, error)
        throw error
      }
    }
  }
  
  /**
   * 记录组件渲染性能
   * @param {string} componentName - 组件名称
   * @param {number} startTime - 开始时间
   * @param {number} startMemory - 开始内存
   * @param {boolean} success - 是否成功
   * @param {Error} error - 错误对象
   */
  static recordComponentRender(componentName, startTime, startMemory, success, error = null) {
    const endTime = performance.now()
    const endMemory = this.getCurrentMemoryUsage()
    const duration = endTime - startTime
    const memoryDelta = endMemory - startMemory
    
    const renderData = this.performanceData.componentRender.get(componentName) || []
    renderData.push({
      duration,
      memoryDelta,
      success,
      error: error ? error.message : null,
      timestamp: Date.now()
    })
    
    // 保持数据点数量在限制内
    if (renderData.length > this.config.maxDataPoints) {
      renderData.shift()
    }
    
    this.performanceData.componentRender.set(componentName, renderData)
    
    // 检查性能阈值
    this.checkThreshold('componentRender', duration, componentName)
    
    // 记录慢渲染
    if (duration > this.thresholds.componentRender) {
      console.warn(`[POS] 慢组件渲染: ${componentName} 耗时 ${duration.toFixed(2)}ms`)
    }
  }
  
  /**
   * 监控API调用性能
   * @param {string} apiName - API名称
   * @param {Function} apiFunction - API函数
   * @returns {Function} 包装后的API函数
   */
  static measureApiCall(apiName, apiFunction) {
    if (!this.config.enableApiMonitoring || Math.random() > this.config.sampleRate) {
      return apiFunction
    }
    
    return async (...args) => {
      const startTime = performance.now()
      const startMemory = this.getCurrentMemoryUsage()
      
      try {
        const result = await apiFunction(...args)
        this.recordApiCall(apiName, startTime, startMemory, true, null, result)
        return result
      } catch (error) {
        this.recordApiCall(apiName, startTime, startMemory, false, error)
        throw error
      }
    }
  }
  
  /**
   * 记录API调用性能
   * @param {string} apiName - API名称
   * @param {number} startTime - 开始时间
   * @param {number} startMemory - 开始内存
   * @param {boolean} success - 是否成功
   * @param {Error} error - 错误对象
   * @param {*} result - 结果
   */
  static recordApiCall(apiName, startTime, startMemory, success, error = null, result = null) {
    const endTime = performance.now()
    const endMemory = this.getCurrentMemoryUsage()
    const duration = endTime - startTime
    const memoryDelta = endMemory - startMemory
    
    const apiData = this.performanceData.apiCalls.get(apiName) || []
    apiData.push({
      duration,
      memoryDelta,
      success,
      error: error ? error.message : null,
      responseSize: this.estimateResponseSize(result),
      timestamp: Date.now()
    })
    
    // 保持数据点数量在限制内
    if (apiData.length > this.config.maxDataPoints) {
      apiData.shift()
    }
    
    this.performanceData.apiCalls.set(apiName, apiData)
    
    // 检查性能阈值
    this.checkThreshold('apiCall', duration, apiName)
    
    // 记录慢API调用
    if (duration > this.thresholds.apiCall) {
      console.warn(`[POS] 慢API调用: ${apiName} 耗时 ${duration.toFixed(2)}ms`)
    }
  }
  
  /**
   * 估算响应大小
   * @param {*} response - 响应数据
   * @returns {number} 估算大小（字节）
   */
  static estimateResponseSize(response) {
    if (!response) return 0
    
    try {
      return JSON.stringify(response).length * 2 // 粗略估算
    } catch (error) {
      return 0
    }
  }
  
  /**
   * 获取当前内存使用量
   * @returns {number} 内存使用量（字节）
   */
  static getCurrentMemoryUsage() {
    if ('memory' in performance) {
      return performance.memory.usedJSHeapSize
    }
    return 0
  }
  
  /**
   * 检查性能阈值
   * @param {string} metric - 指标名称
   * @param {number} value - 指标值
   * @param {string} context - 上下文
   */
  static checkThreshold(metric, value, context = '') {
    const threshold = this.thresholds[metric]
    if (threshold && value > threshold) {
      const message = context ? 
        `${metric} 超过阈值: ${context} (${value.toFixed(2)} > ${threshold})` :
        `${metric} 超过阈值: ${value.toFixed(2)} > ${threshold}`
      
      console.warn(`[POS] 性能警告: ${message}`)
      
      // 可以在这里触发性能优化建议
      this.generateOptimizationSuggestion(metric, value, context)
    }
  }
  
  /**
   * 生成优化建议
   * @param {string} metric - 指标名称
   * @param {number} value - 指标值
   * @param {string} context - 上下文
   */
  static generateOptimizationSuggestion(metric, value, context) {
    const suggestions = {
      componentRender: [
        '考虑使用 React.memo 或 Vue 的 shallowRef 优化组件渲染',
        '检查是否有不必要的重新渲染',
        '使用虚拟滚动处理大列表',
        '延迟加载非关键组件'
      ],
      apiCall: [
        '考虑添加请求缓存',
        '使用分页减少数据量',
        '优化后端查询性能',
        '考虑使用CDN加速'
      ],
      memoryUsage: [
        '检查是否有内存泄漏',
        '清理不再使用的事件监听器',
        '使用对象池减少GC压力',
        '优化大对象的使用'
      ],
      FCP: [
        '优化关键渲染路径',
        '减少阻塞渲染的资源',
        '使用资源预加载',
        '优化CSS和JavaScript'
      ],
      LCP: [
        '优化最大内容元素的加载',
        '使用图片懒加载',
        '优化服务器响应时间',
        '减少渲染阻塞资源'
      ]
    }
    
    const metricSuggestions = suggestions[metric] || []
    if (metricSuggestions.length > 0) {
      const randomSuggestion = metricSuggestions[Math.floor(Math.random() * metricSuggestions.length)]
      console.info(`[POS] 优化建议: ${randomSuggestion}`)
    }
  }
  
  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  static getPerformanceReport() {
    return {
      timestamp: new Date().toISOString(),
      vitals: this.performanceData.vitals,
      componentRender: this.getComponentRenderSummary(),
      apiCalls: this.getApiCallSummary(),
      memoryUsage: this.getMemoryUsageSummary(),
      userInteractions: this.getUserInteractionSummary(),
      recommendations: this.generateRecommendations()
    }
  }
  
  /**
   * 获取组件渲染摘要
   * @returns {Object} 组件渲染摘要
   */
  static getComponentRenderSummary() {
    const summary = {}
    
    for (const [componentName, data] of this.performanceData.componentRender) {
      const durations = data.map(d => d.duration)
      const successRate = data.filter(d => d.success).length / data.length
      
      summary[componentName] = {
        totalRenders: data.length,
        averageDuration: this.calculateAverage(durations),
        maxDuration: Math.max(...durations),
        minDuration: Math.min(...durations),
        successRate: successRate,
        slowRenders: data.filter(d => d.duration > this.thresholds.componentRender).length
      }
    }
    
    return summary
  }
  
  /**
   * 获取API调用摘要
   * @returns {Object} API调用摘要
   */
  static getApiCallSummary() {
    const summary = {}
    
    for (const [apiName, data] of this.performanceData.apiCalls) {
      const durations = data.map(d => d.duration)
      const successRate = data.filter(d => d.success).length / data.length
      
      summary[apiName] = {
        totalCalls: data.length,
        averageDuration: this.calculateAverage(durations),
        maxDuration: Math.max(...durations),
        minDuration: Math.min(...durations),
        successRate: successRate,
        slowCalls: data.filter(d => d.duration > this.thresholds.apiCall).length
      }
    }
    
    return summary
  }
  
  /**
   * 获取内存使用摘要
   * @returns {Object} 内存使用摘要
   */
  static getMemoryUsageSummary() {
    const data = this.performanceData.memoryUsage
    if (data.length === 0) return null
    
    const usedValues = data.map(d => d.used / (1024 * 1024)) // 转换为MB
    
    return {
      current: usedValues[usedValues.length - 1],
      average: this.calculateAverage(usedValues),
      max: Math.max(...usedValues),
      min: Math.min(...usedValues),
      trend: this.calculateTrend(usedValues)
    }
  }
  
  /**
   * 获取用户交互摘要
   * @returns {Object} 用户交互摘要
   */
  static getUserInteractionSummary() {
    const summary = {}
    
    for (const [interactionType, data] of this.performanceData.userInteractions) {
      summary[interactionType] = {
        totalInteractions: data.length,
        recentInteractions: data.filter(d => Date.now() - d.timestamp < 60000).length // 最近1分钟
      }
    }
    
    return summary
  }
  
  /**
   * 生成性能优化建议
   * @returns {Array} 建议列表
   */
  static generateRecommendations() {
    const recommendations = []
    
    // 检查Web Vitals
    if (this.performanceData.vitals.FCP > this.thresholds.FCP) {
      recommendations.push({
        type: 'vitals',
        metric: 'FCP',
        message: 'First Contentful Paint 过慢，建议优化关键渲染路径'
      })
    }
    
    if (this.performanceData.vitals.LCP > this.thresholds.LCP) {
      recommendations.push({
        type: 'vitals',
        metric: 'LCP',
        message: 'Largest Contentful Paint 过慢，建议优化最大内容元素的加载'
      })
    }
    
    // 检查内存使用
    const memoryData = this.performanceData.memoryUsage
    if (memoryData.length > 0) {
      const currentMemory = memoryData[memoryData.length - 1].used / (1024 * 1024)
      if (currentMemory > this.thresholds.memoryUsage) {
        recommendations.push({
          type: 'memory',
          message: `内存使用过高 (${currentMemory.toFixed(2)}MB)，建议检查内存泄漏`
        })
      }
    }
    
    // 检查慢组件
    for (const [componentName, data] of this.performanceData.componentRender) {
      const slowRenders = data.filter(d => d.duration > this.thresholds.componentRender).length
      if (slowRenders > data.length * 0.1) { // 超过10%的渲染较慢
        recommendations.push({
          type: 'component',
          component: componentName,
          message: `组件 ${componentName} 渲染较慢，建议优化渲染逻辑`
        })
      }
    }
    
    return recommendations
  }
  
  /**
   * 计算平均值
   * @param {Array} values - 数值数组
   * @returns {number} 平均值
   */
  static calculateAverage(values) {
    if (values.length === 0) return 0
    return values.reduce((sum, value) => sum + value, 0) / values.length
  }
  
  /**
   * 计算趋势
   * @param {Array} values - 数值数组
   * @returns {string} 趋势描述
   */
  static calculateTrend(values) {
    if (values.length < 2) return 'stable'
    
    const recent = values.slice(-5) // 最近5个值
    const older = values.slice(-10, -5) // 之前5个值
    
    if (recent.length === 0 || older.length === 0) return 'stable'
    
    const recentAvg = this.calculateAverage(recent)
    const olderAvg = this.calculateAverage(older)
    
    const change = (recentAvg - olderAvg) / olderAvg
    
    if (change > 0.1) return 'increasing'
    if (change < -0.1) return 'decreasing'
    return 'stable'
  }
  
  /**
   * 启动定期报告
   */
  static startPeriodicReporting() {
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
    }
    
    this.reportTimer = setInterval(() => {
      const report = this.getPerformanceReport()
      
      // 只在有性能问题时输出报告
      if (report.recommendations.length > 0) {
        console.group('[POS] 性能监控报告')
        console.log('时间:', report.timestamp)
        console.log('Web Vitals:', report.vitals)
        console.log('优化建议:', report.recommendations)
        console.groupEnd()
      }
    }, this.config.reportInterval)
  }
  
  /**
   * 停止监控
   */
  static stop() {
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
      this.reportTimer = null
    }
    
    console.log('[POS] 性能监控器已停止')
  }
  
  /**
   * 清理性能数据
   */
  static clearData() {
    this.performanceData = {
      componentRender: new Map(),
      apiCalls: new Map(),
      memoryUsage: [],
      userInteractions: new Map(),
      routeChanges: [],
      bundleSize: null,
      vitals: {
        FCP: null,
        LCP: null,
        FID: null,
        CLS: null
      }
    }
  }
  
  /**
   * 导出性能数据
   * @returns {Object} 性能数据
   */
  static exportData() {
    return {
      timestamp: new Date().toISOString(),
      config: this.config,
      thresholds: this.thresholds,
      data: {
        componentRender: Object.fromEntries(this.performanceData.componentRender),
        apiCalls: Object.fromEntries(this.performanceData.apiCalls),
        memoryUsage: this.performanceData.memoryUsage,
        userInteractions: Object.fromEntries(this.performanceData.userInteractions),
        vitals: this.performanceData.vitals
      },
      report: this.getPerformanceReport()
    }
  }
}