<!--
  购物车商品项组件
  
  显示单个商品的信息和操作控件
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cart-item" :class="{ 'loading': loading }">
    <!-- 第一行：商品名称 + 删除按钮 -->
    <div class="item-row item-row-1">
      <div class="item-name" :title="item.name">
        {{ item.name }}
        <span v-if="item.specifications" class="item-specs">
          ({{ item.specifications }})
        </span>
      </div>
      <a-button
        type="primary"
        size="small"
        danger
        @click="handleRemoveItem"
        class="remove-btn"
        title="删除商品"
        :loading="loading"
      >
        <template #icon>
          <delete-outlined />
        </template>
      </a-button>
    </div>

    <!-- 第二行：单价 + 数量控制 -->
    <div class="item-row item-row-2">
      <div class="item-unit-price">
        ¥{{ formatPrice(item.price) }}/{{ item.unit || '件' }}
      </div>
      <div class="quantity-control">
        <a-button
          size="small"
          @click="handleDecreaseQuantity"
          :disabled="item.quantity <= 1 || loading"
          class="quantity-btn"
        >
          <template #icon>
            <minus-outlined />
          </template>
        </a-button>

        <a-input-number
          :value="item.quantity"
          :min="1"
          :max="9999"
          :precision="getPrecision()"
          :step="getStep()"
          size="small"
          class="quantity-input"
          :disabled="loading"
          @change="handleQuantityChange"
          @pressEnter="handleQuantityConfirm"
        />

        <a-button
          size="small"
          @click="handleIncreaseQuantity"
          :disabled="loading"
          class="quantity-btn"
        >
          <template #icon>
            <plus-outlined />
          </template>
        </a-button>
      </div>
    </div>

    <!-- 第三行：商品编码/条码 + 小计金额 -->
    <div class="item-row item-row-3">
      <div class="item-code" v-if="item.barcode">
        条码: {{ item.barcode }}
      </div>
      <div class="item-total">
        ¥{{ formatPrice(item.subtotal) }}
      </div>
    </div>

    <!-- 商品图片（可选） -->
    <div v-if="item.image" class="item-image">
      <img :src="item.image" :alt="item.name" />
    </div>
  </div>
</template>

<script setup>
import { DeleteOutlined, MinusOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { AmountFormatter } from '../../utils/formatter'
import { PRICING_TYPES } from '../../utils/constants'

// 定义组件名称
defineOptions({
  name: 'CartItem'
})

// 定义props
const props = defineProps({
  // 商品项数据
  item: {
    type: Object,
    required: true,
    validator: (item) => {
      return item && 
             typeof item.id === 'string' && 
             typeof item.name === 'string' && 
             typeof item.price === 'number' && 
             typeof item.quantity === 'number' &&
             typeof item.subtotal === 'number'
    }
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义emits
const emit = defineEmits([
  'update-quantity',
  'remove-item'
])

// ==================== 工具方法 ====================

/**
 * 格式化价格
 */
const formatPrice = (price) => {
  return AmountFormatter.formatCurrency(price, { showSymbol: false })
}

/**
 * 获取数量输入精度
 */
const getPrecision = () => {
  // 计重商品支持3位小数，其他商品为整数
  return props.item.pricingType === PRICING_TYPES.WEIGHT ? 3 : 0
}

/**
 * 获取数量步长
 */
const getStep = () => {
  // 计重商品步长为0.001，其他商品为1
  return props.item.pricingType === PRICING_TYPES.WEIGHT ? 0.001 : 1
}

// ==================== 事件处理 ====================

/**
 * 处理减少数量
 */
const handleDecreaseQuantity = () => {
  if (props.item.quantity <= 1) {
    return
  }
  
  const step = getStep()
  const newQuantity = Math.max(step, props.item.quantity - step)
  emit('update-quantity', props.item.id, newQuantity)
}

/**
 * 处理增加数量
 */
const handleIncreaseQuantity = () => {
  const step = getStep()
  const newQuantity = props.item.quantity + step
  emit('update-quantity', props.item.id, newQuantity)
}

/**
 * 处理数量变化
 */
const handleQuantityChange = (value) => {
  if (value && value > 0) {
    emit('update-quantity', props.item.id, value)
  }
}

/**
 * 处理数量确认（回车键）
 */
const handleQuantityConfirm = (e) => {
  const value = parseFloat(e.target.value)
  if (value && value > 0) {
    emit('update-quantity', props.item.id, value)
  }
}

/**
 * 处理移除商品
 */
const handleRemoveItem = () => {
  emit('remove-item', props.item.id)
}
</script>

<style scoped>
.cart-item {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fff;
  transition: all 0.2s ease;
  position: relative;
}

.cart-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cart-item.loading {
  opacity: 0.7;
  pointer-events: none;
}

.item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-row:last-child {
  margin-bottom: 0;
}

/* 第一行：商品名称 + 删除按钮 */
.item-row-1 {
  align-items: flex-start;
}

.item-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  line-height: 1.4;
  margin-right: 8px;
  word-break: break-word;
}

.item-specs {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: normal;
}

.remove-btn {
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 第二行：单价 + 数量控制 */
.item-row-2 {
  align-items: center;
}

.item-unit-price {
  font-size: 13px;
  color: #595959;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 4px;
}

.quantity-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.quantity-input {
  width: 60px;
  text-align: center;
}

.quantity-input :deep(.ant-input-number-input) {
  text-align: center;
  padding: 0 4px;
}

/* 第三行：商品编码 + 小计 */
.item-row-3 {
  align-items: center;
}

.item-code {
  font-size: 12px;
  color: #8c8c8c;
  font-family: 'Courier New', monospace;
}

.item-total {
  font-size: 15px;
  font-weight: 600;
  color: #f5222d;
}

/* 商品图片 */
.item-image {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f5f5;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .cart-item {
    padding: 8px;
  }
  
  .item-name {
    font-size: 13px;
  }
  
  .quantity-input {
    width: 50px;
  }
  
  .quantity-btn {
    width: 20px;
    height: 20px;
  }
}
</style>