/**
 * POS模块组件统一导出
 * 
 * 按功能域分组管理组件，支持懒加载优化
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineAsyncComponent } from 'vue'

// 加载中组件
const LoadingComponent = {
  template: '<div class="loading">加载中...</div>'
}

// 错误组件
const ErrorComponent = {
  template: '<div class="error">组件加载失败</div>'
}

// 购物车组件组
export const ShoppingCart = defineAsyncComponent({
  loader: () => import('./cart/ShoppingCart.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const CartItem = defineAsyncComponent({
  loader: () => import('./cart/CartItem.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const CartSummary = defineAsyncComponent({
  loader: () => import('./cart/CartSummary.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 支付组件组
export const PaymentPanel = defineAsyncComponent({
  loader: () => import('./payment/PaymentPanel.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const PaymentMethod = defineAsyncComponent({
  loader: () => import('./payment/PaymentMethod.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const PaymentResult = defineAsyncComponent({
  loader: () => import('./payment/PaymentResult.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 商品组件组
export const ProductDisplayArea = defineAsyncComponent({
  loader: () => import('./product/ProductDisplayArea.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const ProductGrid = defineAsyncComponent({
  loader: () => import('./product/ProductGrid.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const ProductSearch = defineAsyncComponent({
  loader: () => import('./product/ProductSearch.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 会员组件组
export const MemberPanel = defineAsyncComponent({
  loader: () => import('./MemberPanel.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const MemberSearch = defineAsyncComponent({
  loader: () => import('./member/MemberSearch.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const MemberSelector = defineAsyncComponent({
  loader: () => import('./member/MemberSelector.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const MemberInfo = defineAsyncComponent({
  loader: () => import('./member/MemberInfo.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const MemberDiscount = defineAsyncComponent({
  loader: () => import('./member/MemberDiscount.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 通用组件
export const ToolbarPanel = defineAsyncComponent({
  loader: () => import('./ToolbarPanel.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const OrderSuspend = defineAsyncComponent({
  loader: () => import('./OrderSuspend.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const FunctionButton = defineAsyncComponent({
  loader: () => import('./common/FunctionButton.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const OrderSummary = defineAsyncComponent({
  loader: () => import('./common/OrderSummary.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

export const SuspendedOrderItem = defineAsyncComponent({
  loader: () => import('./common/SuspendedOrderItem.vue'),
  loadingComponent: LoadingComponent,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 同步导出（用于非懒加载场景）
export { default as ShoppingCartSync } from './cart/ShoppingCart.vue'
export { default as PaymentPanelSync } from './payment/PaymentPanel.vue'
export { default as ProductDisplayAreaSync } from './product/ProductDisplayArea.vue'
export { default as MemberPanelSync } from './MemberPanel.vue'
export { default as ToolbarPanelSync } from './ToolbarPanel.vue'
export { default as OrderSuspendSync } from './OrderSuspend.vue'

// 会员子组件同步导出
export { default as MemberSearchSync } from './member/MemberSearch.vue'
export { default as MemberSelectorSync } from './member/MemberSelector.vue'
export { default as MemberInfoSync } from './member/MemberInfo.vue'
export { default as MemberDiscountSync } from './member/MemberDiscount.vue'

// 通用子组件同步导出
export { default as FunctionButtonSync } from './common/FunctionButton.vue'
export { default as OrderSummarySync } from './common/OrderSummary.vue'
export { default as SuspendedOrderItemSync } from './common/SuspendedOrderItem.vue'

// 分组导出
export * as CartComponents from './cart'
export * as PaymentComponents from './payment'
export * as ProductComponents from './product'
export * as MemberComponents from './member'
export * as CommonComponents from './common'