<template>
  <div class="product-display-area">
    <!-- 商品网格 -->
    <div class="product-grid-section" v-loading="loading" ref="scrollContainer">
      <div class="product-grid">
        <div
          v-for="product in displayProducts"
          :key="product.productId"
          class="product-card"
          :class="{ 
            'out-of-stock': product.stockStatus === 'OUT_OF_STOCK',
            'low-stock': product.stockStatus === 'WARNING',
            'disabled': product.status !== 'ACTIVE'
          }"
          @click="handleProductClick(product)"
        >
          <!-- 库存状态标识 -->
          <div v-if="product.stockStatus === 'OUT_OF_STOCK'" class="stock-badge out-of-stock">
            缺货
          </div>
          <div v-else-if="product.stockStatus === 'WARNING'" class="stock-badge low-stock">
            库存不足
          </div>

          <!-- 商品状态标识 -->
          <div v-if="product.status !== 'ACTIVE'" class="status-badge disabled">
            已停用
          </div>

          <!-- 商品信息 -->
          <div class="product-info">
            <div class="product-name" :title="product.productName">
              <span v-html="highlightSearchText(product.productName)"></span>
            </div>
            <div class="product-code" v-if="product.productCode">
              编码: <span v-html="highlightSearchText(product.productCode)"></span>
            </div>
            <div class="product-price">
              <span class="price-symbol">￥</span>
              <span class="price-value">{{ formatPrice(getProductPrice(product)) }}</span>
              <span class="price-unit">/{{ product.unit || '件' }}</span>
            </div>
            <div class="product-stock" v-if="showStock">
              库存: {{ product.stockQuantity || 0 }}{{ product.unit || '件' }}
            </div>
          </div>

          <!-- 添加按钮 -->
          <div class="add-button" v-if="canAddToCart(product)">
            <a-button 
              type="primary" 
              shape="circle" 
              size="small"
              @click.stop="addToCart(product)"
            >
              <template #icon>
                <plus-outlined />
              </template>
            </a-button>
          </div>
        </div>
      </div>

      <!-- 瀑布流加载指示器 -->
      <div v-if="loadingMore" class="loading-indicator">
        <a-spin size="small" />
        <span class="loading-text">正在加载...</span>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!hasMore && products.length > 0" class="no-more-data">
        <span>已加载全部商品</span>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && displayProducts.length === 0" class="empty-state">
      <a-empty 
        :description="searchKeyword ? '未找到相关商品' : '暂无商品'"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <a-button v-if="searchKeyword" @click="clearSearch">
          清除搜索
        </a-button>
        <a-button v-else type="primary" @click="refreshProducts">
          重新加载
        </a-button>
      </a-empty>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-state">
      <a-result
        status="error"
        title="加载失败"
        :sub-title="error"
      >
        <template #extra>
          <a-button type="primary" @click="refreshProducts">
            重试
          </a-button>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { SearchOutlined, PlusOutlined, DeleteOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { PosApi } from '@/api/erp/pos'
import { usePosStore } from '@/stores/pos'

// 导入样式文件
import '../styles/common.css'
import '../styles/product-display.css'

// 定义组件名称
defineOptions({
  name: 'ProductGrid'
})

// 定义Props
const props = defineProps({
  showStock: {
    type: Boolean,
    default: true
  },
  gridColumns: {
    type: Number,
    default: 4
  },
  categoryId: {
    type: [String, Number],
    default: null
  },
  searchKeyword: {
    type: String,
    default: ''
  },
  priceFilter: {
    type: String,
    default: ''
  },
  stockFilter: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['productSelect', 'productAdd'])

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const error = ref('')
const products = ref([])
const currentPage = ref(1)
const pageSize = ref(30)
const hasMore = ref(true)
const scrollContainer = ref(null)
const isScrollListening = ref(false)

// 使用POS状态管理
const posStore = usePosStore()

// 显示的商品列表（应用筛选）
const displayProducts = computed(() => {
  let filtered = products.value

  // 库存筛选
  if (props.stockFilter) {
    switch (props.stockFilter) {
      case 'in-stock':
        filtered = filtered.filter(product =>
          product.stockStatus !== 'OUT_OF_STOCK' &&
          (product.stockQuantity == null || product.stockQuantity > 0)
        )
        break
      case 'low-stock':
        filtered = filtered.filter(product =>
          product.stockStatus === 'WARNING'
        )
        break
      case 'out-of-stock':
        filtered = filtered.filter(product =>
          product.stockStatus === 'OUT_OF_STOCK'
        )
        break
    }
  }

  // 价格筛选
  if (props.priceFilter) {
    const [min, max] = props.priceFilter.includes('-')
      ? props.priceFilter.split('-').map(Number)
      : props.priceFilter === '100+'
        ? [100, Infinity]
        : [0, 0]

    if (min !== 0 || max !== 0) {
      filtered = filtered.filter(product => {
        const price = getProductPrice(product)
        return price >= min && (max === Infinity || price <= max)
      })
    }
  }

  return filtered
})

/**
 * 加载商品列表
 */
const loadProducts = async (isLoadMore = false) => {
  try {
    if (isLoadMore) {
      loadingMore.value = true
    } else {
      loading.value = true
      currentPage.value = 1
      products.value = []
    }

    error.value = ''

    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value
    }

    // 根据搜索关键词或分类加载商品
    let result
    // console.log('加载商品参数:', {
    //   searchKeyword: props.searchKeyword,
    //   categoryId: props.categoryId,
    //   params
    // })

    if (props.searchKeyword && props.searchKeyword.trim()) {
      // console.log('使用搜索API')
      result = await PosApi.searchProducts({
        ...params,
        keyword: props.searchKeyword.trim()
      })
    } else if (props.categoryId) {
      // console.log('使用分类API，分类ID:', props.categoryId)
      result = await PosApi.getProductsByCategory({
        ...params,
        categoryId: props.categoryId
      })
    } else {
      // console.log('使用默认商品API')
      result = await PosApi.getProductsByCategory(params)
    }

    // console.log('API返回结果:', result)

    // 处理API响应数据
    let newProducts = []
    if (result && Array.isArray(result)) {
      // 直接是数组格式
      newProducts = result
    } else if (result?.data && Array.isArray(result.data)) {
      // 包装在data字段中
      newProducts = result.data
    } else if (result?.rows && Array.isArray(result.rows)) {
      // 分页格式
      newProducts = result.rows
    } else {
      newProducts = []
    }

    if (isLoadMore) {
      products.value.push(...newProducts)
    } else {
      products.value = newProducts
    }

    // 检查是否还有更多数据
    hasMore.value = newProducts.length === pageSize.value

    // console.log('商品加载成功:', newProducts.length, '个商品')
    // console.log('商品数据:', newProducts)

  } catch (err) {
    console.error('加载商品失败:', err)
    error.value = err.message || '加载商品失败，请重试'
    message.error('加载商品失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

/**
 * 刷新商品列表
 */
const refreshProducts = () => {
  loadProducts()
}

/**
 * 清除搜索
 */
const clearSearch = () => {
  emit('clearSearch')
}

/**
 * 防抖函数
 */
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 检查是否需要加载更多
 */
const checkLoadMore = () => {
  if (!hasMore.value || loadingMore.value || loading.value) {
    return
  }

  const container = scrollContainer.value
  if (!container) return

  const { scrollTop, scrollHeight, clientHeight } = container
  const threshold = 100 // 距离底部100px时开始加载

  if (scrollTop + clientHeight >= scrollHeight - threshold) {
    loadMore()
  }
}

/**
 * 防抖的滚动检查函数
 */
const debouncedCheckLoadMore = debounce(checkLoadMore, 200)

/**
 * 滚动事件处理
 */
const handleScroll = () => {
  debouncedCheckLoadMore()
}

/**
 * 加载更多商品
 */
const loadMore = () => {
  if (hasMore.value && !loadingMore.value) {
    currentPage.value++
    loadProducts(true)
  }
}

/**
 * 设置滚动监听
 */
const setupScrollListener = () => {
  if (scrollContainer.value && !isScrollListening.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll, { passive: true })
    isScrollListening.value = true
  }
}

/**
 * 移除滚动监听
 */
const removeScrollListener = () => {
  if (scrollContainer.value && isScrollListening.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll)
    isScrollListening.value = false
  }
}

// 监听props变化，重新加载商品
watch(
  () => [props.categoryId, props.searchKeyword, props.priceFilter, props.stockFilter],
  () => {
    loadProducts()
  },
  { deep: true }
)

/**
 * 高亮搜索文本
 */
const highlightSearchText = (text) => {
  if (!props.searchKeyword || !text) {
    return text
  }

  const keyword = props.searchKeyword.trim()
  if (!keyword) {
    return text
  }

  // 使用正则表达式进行不区分大小写的替换
  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

/**
 * 处理商品点击
 */
const handleProductClick = (product) => {
  if (canAddToCart(product)) {
    addToCart(product)
  }
  emit('productSelect', product)
}

/**
 * 检查商品是否可以添加到购物车
 */
const canAddToCart = (product) => {
  return product.status === 'ACTIVE' && 
         product.stockStatus !== 'OUT_OF_STOCK' &&
         (product.stockQuantity == null || product.stockQuantity > 0)
}

/**
 * 添加商品到购物车
 */
const addToCart = async (product) => {
  try {
    const success = await posStore.addToCart(product, 1)
    if (success) {
      emit('productAdd', product)
    }
  } catch (err) {
    console.error('添加商品到购物车失败:', err)
    message.error('添加商品失败')
  }
}

/**
 * 获取商品价格
 */
const getProductPrice = (product) => {
  if (product.pricingType === 'NORMAL') {
    return product.retailPrice
  } else if (product.pricingType === 'WEIGHT') {
    return product.unitPrice
  } else if (product.pricingType === 'PIECE') {
    return product.piecePrice
  } else {
    return product.referencePrice
  }
}

/**
 * 格式化价格显示
 */
const formatPrice = (price) => {
  if (price == null || isNaN(price)) return '0.00'
  return Number(price).toFixed(2)
}

/**
 * 处理图片加载错误
 */
const handleImageError = (event) => {
  event.target.style.display = 'none'
  event.target.parentNode.querySelector('.no-image').style.display = 'flex'
}

// 组件挂载时加载数据
onMounted(() => {
  loadProducts()
  // 延迟设置滚动监听，确保DOM已渲染
  nextTick(() => {
    setupScrollListener()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  removeScrollListener()
})

// 暴露方法给父组件
defineExpose({
  refreshProducts,
  loadProducts
})
</script>

<style scoped>
.product-display-area {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 搜索结果高亮 */
:deep(.search-highlight) {
  background: #fff566;
  color: #262626;
  font-weight: 600;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 使用product-display.css中的样式 */

/* 产品卡片特定状态样式 */
.product-card.out-of-stock {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card.out-of-stock:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-color: #e8e8e8;
}

.product-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(50%);
}



.stock-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  color: #fff;
  z-index: 2;
}

.stock-badge.out-of-stock {
  background: #ff4d4f;
}

.stock-badge.low-stock {
  background: #faad14;
}

.status-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  color: #fff;
  background: #8c8c8c;
  z-index: 2;
}

/* 产品信息样式使用product-display.css中的样式 */

.add-button {
  position: absolute;
  bottom: 12px;
  right: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .add-button {
  opacity: 1;
}

/* 瀑布流加载指示器 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.loading-text {
  color: #666;
}

/* 没有更多数据提示 */
.no-more-data {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
  border-top: 1px solid #f0f0f0;
}

.empty-state,
.error-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

/* 响应式样式使用product-display.css中的样式 */

/* 移动设备上的添加按钮特殊处理 */
@media (max-width: 480px) {
  .add-button {
    opacity: 1; /* 在移动设备上始终显示 */
  }
}

/* 加载状态 */
.product-grid-section[v-loading] {
  position: relative;
  min-height: 300px;
}

/* 产品卡片特定动画 */
.product-card {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 触屏设备特定优化 */
@media (hover: none) {
  .product-card:active {
    transform: scale(0.98);
  }

  .add-button {
    opacity: 1;
  }
}
</style>
