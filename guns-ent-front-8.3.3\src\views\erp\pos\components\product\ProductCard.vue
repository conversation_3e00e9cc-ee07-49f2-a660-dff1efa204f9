<!--\n  商品卡片组件\n  \n  显示单个商品的信息，支持网格和列表两种显示模式\n  \n  <AUTHOR>  @since 2025/01/02\n-->\n<template>\n  <div \n    class=\"product-card\" \n    :class=\"{\n      'grid-mode': viewMode === 'grid',\n      'list-mode': viewMode === 'list',\n      'selected': selected,\n      'out-of-stock': isOutOfStock,\n      'low-stock': isLowStock\n    }\"\n    :style=\"cardStyle\"\n    @click=\"handleClick\"\n  >\n    <!-- 商品图片 -->\n    <div class=\"product-image\">\n      <img \n        v-if=\"product.image\" \n        :src=\"product.image\" \n        :alt=\"product.productName\"\n        @error=\"handleImageError\"\n        @load=\"handleImageLoad\"\n      />\n      <div v-else class=\"image-placeholder\">\n        <icon-font iconClass=\"icon-product\" />\n      </div>\n      \n      <!-- 商品状态标签 -->\n      <div class=\"product-badges\">\n        <a-tag v-if=\"product.isNew\" color=\"green\" size=\"small\" class=\"badge\">\n          新品\n        </a-tag>\n        <a-tag v-if=\"product.isHot\" color=\"red\" size=\"small\" class=\"badge\">\n          热销\n        </a-tag>\n        <a-tag v-if=\"product.isDiscount\" color=\"orange\" size=\"small\" class=\"badge\">\n          促销\n        </a-tag>\n        <a-tag v-if=\"isOutOfStock\" color=\"default\" size=\"small\" class=\"badge\">\n          缺货\n        </a-tag>\n        <a-tag v-else-if=\"isLowStock\" color=\"warning\" size=\"small\" class=\"badge\">\n          库存不足\n        </a-tag>\n      </div>\n      \n      <!-- 选中状态指示器 -->\n      <div class=\"selection-indicator\" v-if=\"selected\">\n        <check-circle-filled />\n      </div>\n      \n      <!-- 快速操作按钮 -->\n      <div class=\"quick-actions\" v-if=\"showQuickActions\">\n        <a-button \n          type=\"text\" \n          size=\"small\"\n          @click.stop=\"handleQuickView\"\n          class=\"quick-btn\"\n        >\n          <template #icon>\n            <eye-outlined />\n          </template>\n        </a-button>\n        \n        <a-button \n          type=\"text\" \n          size=\"small\"\n          @click.stop=\"handleFavorite\"\n          class=\"quick-btn\"\n          :class=\"{ 'favorited': product.isFavorite }\"\n        >\n          <template #icon>\n            <heart-outlined v-if=\"!product.isFavorite\" />\n            <heart-filled v-else />\n          </template>\n        </a-button>\n      </div>\n    </div>\n    \n    <!-- 商品信息 -->\n    <div class=\"product-info\">\n      <!-- 商品名称 -->\n      <div class=\"product-name\" :title=\"product.productName\">\n        {{ product.productName }}\n      </div>\n      \n      <!-- 商品规格 -->\n      <div class=\"product-specs\" v-if=\"product.specifications\">\n        {{ product.specifications }}\n      </div>\n      \n      <!-- 商品编码 -->\n      <div class=\"product-code\" v-if=\"showProductCode\">\n        编码: {{ product.productCode }}\n      </div>\n      \n      <!-- 价格信息 -->\n      <div class=\"price-info\">\n        <div class=\"current-price\">\n          {{ formatPrice(product.price) }}\n        </div>\n        <div class=\"original-price\" v-if=\"product.originalPrice && product.originalPrice > product.price\">\n          {{ formatPrice(product.originalPrice) }}\n        </div>\n        <div class=\"unit\" v-if=\"product.unit\">\n          / {{ product.unit }}\n        </div>\n      </div>\n      \n      <!-- 库存信息 -->\n      <div class=\"stock-info\" v-if=\"showStockInfo\">\n        <div class=\"stock-text\">\n          库存: \n          <span class=\"stock-count\" :class=\"getStockClass()\">\n            {{ formatStock(product.stock) }}\n          </span>\n        </div>\n        \n        <!-- 库存进度条 -->\n        <div class=\"stock-progress\" v-if=\"product.maxStock > 0\">\n          <a-progress \n            :percent=\"stockPercent\" \n            :show-info=\"false\" \n            size=\"small\"\n            :stroke-color=\"getStockProgressColor()\"\n          />\n        </div>\n      </div>\n      \n      <!-- 分类信息 -->\n      <div class=\"category-info\" v-if=\"product.categoryName && showCategoryInfo\">\n        <a-tag size=\"small\" class=\"category-tag\">\n          {{ product.categoryName }}\n        </a-tag>\n      </div>\n    </div>\n    \n    <!-- 操作按钮 -->\n    <div class=\"product-actions\">\n      <a-button \n        type=\"primary\"\n        size=\"small\"\n        :disabled=\"isOutOfStock || loading\"\n        :loading=\"loading\"\n        @click.stop=\"handleAddToCart\"\n        class=\"add-btn\"\n      >\n        <template #icon>\n          <shopping-cart-outlined />\n        </template>\n        <span v-if=\"viewMode === 'list' || cardSize > 140\">加入购物车</span>\n      </a-button>\n      \n      <!-- 数量选择器（列表模式） -->\n      <div class=\"quantity-selector\" v-if=\"viewMode === 'list' && showQuantitySelector\">\n        <a-input-number\n          v-model:value=\"quantity\"\n          :min=\"0.001\"\n          :max=\"product.stock\"\n          :precision=\"getPrecision()\"\n          :step=\"getStep()\"\n          size=\"small\"\n          @change=\"handleQuantityChange\"\n          class=\"quantity-input\"\n        />\n      </div>\n    </div>\n    \n    <!-- 加载遮罩 -->\n    <div class=\"loading-overlay\" v-if=\"loading\">\n      <a-spin size=\"small\" />\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { message } from 'ant-design-vue'\nimport {\n  CheckCircleFilled,\n  EyeOutlined,\n  HeartOutlined,\n  HeartFilled,\n  ShoppingCartOutlined\n} from '@ant-design/icons-vue'\nimport IconFont from '@/components/common/IconFont/index.vue'\nimport { AmountFormatter } from '../../utils/formatter'\nimport { PRICING_TYPES, STOCK_LEVELS } from '../../utils/constants'\n\n// 定义组件名称\ndefineOptions({\n  name: 'ProductCard'\n})\n\n// 定义Props\nconst props = defineProps({\n  // 商品信息\n  product: {\n    type: Object,\n    required: true\n  },\n  // 显示模式\n  viewMode: {\n    type: String,\n    default: 'grid',\n    validator: (value) => ['grid', 'list'].includes(value)\n  },\n  // 卡片大小（网格模式）\n  cardSize: {\n    type: Number,\n    default: 160\n  },\n  // 是否选中\n  selected: {\n    type: Boolean,\n    default: false\n  },\n  // 加载状态\n  loading: {\n    type: Boolean,\n    default: false\n  },\n  // 是否显示快速操作\n  showQuickActions: {\n    type: Boolean,\n    default: true\n  },\n  // 是否显示商品编码\n  showProductCode: {\n    type: Boolean,\n    default: false\n  },\n  // 是否显示库存信息\n  showStockInfo: {\n    type: Boolean,\n    default: true\n  },\n  // 是否显示分类信息\n  showCategoryInfo: {\n    type: Boolean,\n    default: false\n  },\n  // 是否显示数量选择器\n  showQuantitySelector: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// 定义Emits\nconst emit = defineEmits([\n  'click',\n  'add-to-cart',\n  'quick-view',\n  'favorite',\n  'quantity-change'\n])\n\n// 响应式状态\nconst imageError = ref(false)\nconst imageLoaded = ref(false)\nconst quantity = ref(1)\n\n// ==================== 计算属性 ====================\n\n/**\n * 卡片样式\n */\nconst cardStyle = computed(() => {\n  if (props.viewMode === 'grid') {\n    return {\n      width: `${props.cardSize}px`,\n      height: `${props.cardSize + 60}px` // 额外高度用于显示信息\n    }\n  }\n  return {}\n})\n\n/**\n * 是否缺货\n */\nconst isOutOfStock = computed(() => {\n  return props.product.stock <= 0\n})\n\n/**\n * 是否库存不足\n */\nconst isLowStock = computed(() => {\n  const stock = props.product.stock || 0\n  const lowStockThreshold = props.product.lowStockThreshold || 10\n  return stock > 0 && stock <= lowStockThreshold\n})\n\n/**\n * 库存百分比\n */\nconst stockPercent = computed(() => {\n  if (!props.product.maxStock || props.product.maxStock <= 0) return 0\n  return Math.round((props.product.stock / props.product.maxStock) * 100)\n})\n\n// ==================== 方法 ====================\n\n/**\n * 格式化价格\n * @param {number} price - 价格\n * @returns {string} 格式化后的价格\n */\nconst formatPrice = (price) => {\n  return AmountFormatter.formatCurrency(price || 0)\n}\n\n/**\n * 格式化库存\n * @param {number} stock - 库存数量\n * @returns {string} 格式化后的库存\n */\nconst formatStock = (stock) => {\n  if (stock === null || stock === undefined) return '未知'\n  if (stock <= 0) return '缺货'\n  \n  // 根据商品类型决定显示精度\n  if (props.product.pricingType === PRICING_TYPES.WEIGHT) {\n    return `${stock.toFixed(2)}${props.product.unit || 'kg'}`\n  }\n  \n  return `${Math.floor(stock)}${props.product.unit || '件'}`\n}\n\n/**\n * 获取库存样式类\n * @returns {string} 样式类名\n */\nconst getStockClass = () => {\n  if (isOutOfStock.value) return 'out-of-stock'\n  if (isLowStock.value) return 'low-stock'\n  return 'normal-stock'\n}\n\n/**\n * 获取库存进度条颜色\n * @returns {string} 颜色值\n */\nconst getStockProgressColor = () => {\n  if (stockPercent.value <= 20) return '#ff4d4f'\n  if (stockPercent.value <= 50) return '#faad14'\n  return '#52c41a'\n}\n\n/**\n * 获取数量输入精度\n * @returns {number} 精度\n */\nconst getPrecision = () => {\n  return props.product.pricingType === PRICING_TYPES.WEIGHT ? 3 : 0\n}\n\n/**\n * 获取数量步长\n * @returns {number} 步长\n */\nconst getStep = () => {\n  if (props.product.pricingType === PRICING_TYPES.WEIGHT) return 0.1\n  return 1\n}\n\n/**\n * 处理点击事件\n */\nconst handleClick = () => {\n  emit('click', props.product)\n}\n\n/**\n * 处理添加到购物车\n */\nconst handleAddToCart = () => {\n  if (isOutOfStock.value) {\n    message.warning('商品已缺货，无法添加到购物车')\n    return\n  }\n  \n  const addQuantity = props.showQuantitySelector ? quantity.value : 1\n  \n  emit('add-to-cart', {\n    ...props.product,\n    quantity: addQuantity\n  })\n}\n\n/**\n * 处理快速预览\n */\nconst handleQuickView = () => {\n  emit('quick-view', props.product)\n}\n\n/**\n * 处理收藏\n */\nconst handleFavorite = () => {\n  emit('favorite', {\n    product: props.product,\n    isFavorite: !props.product.isFavorite\n  })\n}\n\n/**\n * 处理数量变化\n * @param {number} value - 新数量\n */\nconst handleQuantityChange = (value) => {\n  if (value > props.product.stock) {\n    quantity.value = props.product.stock\n    message.warning('数量不能超过库存')\n    return\n  }\n  \n  emit('quantity-change', {\n    product: props.product,\n    quantity: value\n  })\n}\n\n/**\n * 处理图片加载错误\n */\nconst handleImageError = () => {\n  imageError.value = true\n}\n\n/**\n * 处理图片加载完成\n */\nconst handleImageLoad = () => {\n  imageLoaded.value = true\n}\n</script>\n\n<style scoped>\n.product-card {\n  position: relative;\n  background: #fff;\n  border: 1px solid #f0f0f0;\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.product-card:hover {\n  border-color: #1890ff;\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.product-card.selected {\n  border-color: #1890ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n}\n\n.product-card.out-of-stock {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.product-card.out-of-stock:hover {\n  transform: none;\n  box-shadow: none;\n}\n\n/* 网格模式 */\n.product-card.grid-mode {\n  display: flex;\n  flex-direction: column;\n}\n\n.product-card.grid-mode .product-image {\n  height: 60%;\n  min-height: 100px;\n}\n\n.product-card.grid-mode .product-info {\n  flex: 1;\n  padding: 8px;\n}\n\n.product-card.grid-mode .product-actions {\n  padding: 8px;\n  border-top: 1px solid #f0f0f0;\n}\n\n/* 列表模式 */\n.product-card.list-mode {\n  display: flex;\n  align-items: center;\n  height: 80px;\n  padding: 8px;\n}\n\n.product-card.list-mode .product-image {\n  width: 64px;\n  height: 64px;\n  flex-shrink: 0;\n  margin-right: 12px;\n}\n\n.product-card.list-mode .product-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.product-card.list-mode .product-actions {\n  flex-shrink: 0;\n  margin-left: 12px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 商品图片 */\n.product-image {\n  position: relative;\n  background: #f5f5f5;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.product-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.product-card:hover .product-image img {\n  transform: scale(1.05);\n}\n\n.image-placeholder {\n  color: #bfbfbf;\n  font-size: 32px;\n}\n\n.list-mode .image-placeholder {\n  font-size: 24px;\n}\n\n/* 商品标签 */\n.product-badges {\n  position: absolute;\n  top: 4px;\n  left: 4px;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.badge {\n  font-size: 10px;\n  padding: 0 4px;\n  height: 16px;\n  line-height: 16px;\n}\n\n/* 选中指示器 */\n.selection-indicator {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #1890ff;\n  font-size: 20px;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 快速操作 */\n.quick-actions {\n  position: absolute;\n  bottom: 4px;\n  right: 4px;\n  display: flex;\n  gap: 2px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.product-card:hover .quick-actions {\n  opacity: 1;\n}\n\n.quick-btn {\n  width: 24px;\n  height: 24px;\n  padding: 0;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.quick-btn.favorited {\n  color: #ff4d4f;\n}\n\n/* 商品信息 */\n.product-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.product-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.list-mode .product-name {\n  font-size: 15px;\n  -webkit-line-clamp: 1;\n}\n\n.product-specs {\n  font-size: 12px;\n  color: #8c8c8c;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.product-code {\n  font-size: 11px;\n  color: #bfbfbf;\n  font-family: 'Courier New', monospace;\n}\n\n/* 价格信息 */\n.price-info {\n  display: flex;\n  align-items: baseline;\n  gap: 4px;\n  flex-wrap: wrap;\n}\n\n.current-price {\n  font-size: 16px;\n  font-weight: 600;\n  color: #ff4d4f;\n}\n\n.list-mode .current-price {\n  font-size: 18px;\n}\n\n.original-price {\n  font-size: 12px;\n  color: #8c8c8c;\n  text-decoration: line-through;\n}\n\n.unit {\n  font-size: 11px;\n  color: #8c8c8c;\n}\n\n/* 库存信息 */\n.stock-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.stock-text {\n  font-size: 11px;\n  color: #595959;\n}\n\n.stock-count.normal-stock {\n  color: #52c41a;\n}\n\n.stock-count.low-stock {\n  color: #faad14;\n}\n\n.stock-count.out-of-stock {\n  color: #ff4d4f;\n}\n\n.stock-progress {\n  width: 100%;\n}\n\n/* 分类信息 */\n.category-info {\n  margin-top: 4px;\n}\n\n.category-tag {\n  font-size: 10px;\n  padding: 0 4px;\n  height: 16px;\n  line-height: 16px;\n  background: #f0f9ff;\n  border-color: #91d5ff;\n  color: #1890ff;\n}\n\n/* 操作按钮 */\n.product-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.add-btn {\n  flex: 1;\n  height: 32px;\n  font-size: 12px;\n}\n\n.list-mode .add-btn {\n  height: 36px;\n  font-size: 13px;\n  min-width: 100px;\n}\n\n.quantity-selector {\n  flex-shrink: 0;\n}\n\n.quantity-input {\n  width: 80px;\n}\n\n/* 加载遮罩 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .product-card.grid-mode {\n    min-height: 200px;\n  }\n  \n  .product-name {\n    font-size: 13px;\n  }\n  \n  .current-price {\n    font-size: 15px;\n  }\n  \n  .add-btn {\n    height: 28px;\n    font-size: 11px;\n  }\n  \n  .list-mode .add-btn {\n    height: 32px;\n    font-size: 12px;\n    min-width: 80px;\n  }\n}\n\n@media (max-width: 480px) {\n  .product-card.list-mode {\n    height: 72px;\n    padding: 6px;\n  }\n  \n  .product-card.list-mode .product-image {\n    width: 56px;\n    height: 56px;\n    margin-right: 8px;\n  }\n  \n  .product-card.list-mode .product-actions {\n    margin-left: 8px;\n    gap: 4px;\n  }\n  \n  .quantity-input {\n    width: 60px;\n  }\n}\n\n/* 动画效果 */\n.product-card {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .product-card {\n    border-width: 2px;\n  }\n  \n  .product-card.selected {\n    border-width: 3px;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .product-card {\n    animation: none;\n    transition: none;\n  }\n  \n  .product-card:hover {\n    transform: none;\n  }\n  \n  .product-image img {\n    transition: none;\n  }\n  \n  .product-card:hover .product-image img {\n    transform: none;\n  }\n}\n\n/* 触屏设备优化 */\n@media (hover: none) {\n  .product-card:hover {\n    transform: none;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  }\n  \n  .quick-actions {\n    opacity: 1;\n  }\n  \n  .product-card:active {\n    transform: scale(0.98);\n  }\n}\n</style>"