/**
 * 商品管理组合式函数
 * 
 * 封装商品和分类数据的加载、搜索、过滤等业务逻辑
 * 
 * <AUTHOR>
 * @since 2025/01/03
 */

import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ProductApi } from '../api/product'
import { usePosStore } from '@/stores/pos'
import { PosErrorHandler } from '../utils/error-handler'

export function useProduct() {
  // 状态管理
  const posStore = usePosStore()
  
  // 响应式状态
  const loading = ref(false)
  const categoriesLoading = ref(false)
  const productsLoading = ref(false)
  const selectedCategoryId = ref('all')
  const searchKeyword = ref('')
  const priceFilter = ref('')
  
  // 计算属性
  const categories = computed(() => posStore.categories)
  const products = computed(() => posStore.products)
  const hasCategories = computed(() => categories.value.length > 0)
  const hasProducts = computed(() => products.value.length > 0)
  
  // 过滤后的商品列表
  const filteredProducts = computed(() => {
    let result = products.value
    
    // 分类过滤
    if (selectedCategoryId.value !== 'all') {
      result = result.filter(p => p.categoryId === selectedCategoryId.value)
    }
    
    // 搜索过滤
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      result = result.filter(p => 
        p.productName?.toLowerCase().includes(keyword) ||
        p.productCode?.toLowerCase().includes(keyword) ||
        p.barcode?.toLowerCase().includes(keyword)
      )
    }
    
    // 价格过滤
    if (priceFilter.value) {
      result = result.filter(p => {
        const price = p.price || p.retailPrice || 0
        switch (priceFilter.value) {
          case '0-50':
            return price >= 0 && price <= 50
          case '50-100':
            return price > 50 && price <= 100
          case '100-200':
            return price > 100 && price <= 200
          case '200+':
            return price > 200
          default:
            return true
        }
      })
    }
    
    return result
  })
  
  // ==================== 数据加载方法 ====================
  
  /**
   * 加载商品分类列表
   */
  const loadCategories = async () => {
    try {
      categoriesLoading.value = true
      
      const response = await ProductApi.getCategories()
      
      if (response && response.data) {
        posStore.setCategories(response.data)
        console.log('商品分类加载成功:', response.data.length, '个分类')
      } else {
        posStore.setCategories([])
        console.warn('商品分类数据为空')
      }
      
    } catch (error) {
      console.error('加载商品分类失败:', error)
      message.error('加载商品分类失败: ' + error.message)
      posStore.setCategories([])
    } finally {
      categoriesLoading.value = false
    }
  }
  
  /**
   * 根据分类加载商品列表
   * @param {string} categoryId - 分类ID，'all'表示所有商品
   */
  const loadProductsByCategory = async (categoryId = 'all') => {
    try {
      productsLoading.value = true
      
      const params = {
        onlyInStock: true // 只显示有库存的商品
      }
      
      // 如果不是全部商品，添加分类过滤
      if (categoryId !== 'all') {
        params.categoryId = categoryId
      }
      
      const response = await ProductApi.getProductsByCategory(params)
      
      if (response && response.data) {
        posStore.setProducts(response.data)
        console.log('商品列表加载成功:', response.data.length, '个商品')
      } else {
        posStore.setProducts([])
        console.warn('商品数据为空')
      }
      
    } catch (error) {
      console.error('加载商品列表失败:', error)
      message.error('加载商品列表失败: ' + error.message)
      posStore.setProducts([])
    } finally {
      productsLoading.value = false
    }
  }
  
  /**
   * 搜索商品
   * @param {string} keyword - 搜索关键词
   */
  const searchProducts = async (keyword = '') => {
    try {
      if (!keyword.trim()) {
        // 如果搜索关键词为空，重新加载当前分类的商品
        await loadProductsByCategory(selectedCategoryId.value)
        return
      }
      
      productsLoading.value = true
      
      const params = {
        keyword: keyword.trim(),
        onlyInStock: true
      }
      
      const response = await ProductApi.searchProducts(params)
      
      if (response && response.data) {
        posStore.setProducts(response.data)
        console.log('商品搜索完成:', response.data.length, '个结果')
      } else {
        posStore.setProducts([])
      }
      
    } catch (error) {
      console.error('搜索商品失败:', error)
      message.error('搜索商品失败: ' + error.message)
    } finally {
      productsLoading.value = false
    }
  }
  
  /**
   * 初始化商品数据
   */
  const initializeProductData = async () => {
    try {
      loading.value = true
      
      // 并行加载分类和商品数据
      await Promise.all([
        loadCategories(),
        loadProductsByCategory('all')
      ])
      
      console.log('商品数据初始化完成')
      
    } catch (error) {
      console.error('初始化商品数据失败:', error)
      message.error('初始化商品数据失败')
    } finally {
      loading.value = false
    }
  }
  
  // ==================== 事件处理方法 ====================
  
  /**
   * 处理分类切换
   * @param {string} categoryId - 分类ID
   */
  const handleCategoryChange = async (categoryId) => {
    selectedCategoryId.value = categoryId
    posStore.setSelectedCategory(
      categories.value.find(c => c.categoryId === categoryId) || null
    )
    
    // 清空搜索关键词
    searchKeyword.value = ''
    posStore.setSearchKeyword('')
    
    // 重新加载商品
    await loadProductsByCategory(categoryId)
  }
  
  /**
   * 处理搜索
   * @param {string} keyword - 搜索关键词
   */
  const handleSearch = async (keyword) => {
    searchKeyword.value = keyword
    posStore.setSearchKeyword(keyword)
    
    await searchProducts(keyword)
  }
  
  /**
   * 处理价格过滤
   * @param {string} filterValue - 过滤值
   */
  const handlePriceFilter = (filterValue) => {
    priceFilter.value = filterValue
  }
  
  /**
   * 刷新商品数据
   */
  const refreshProductData = async () => {
    if (searchKeyword.value) {
      await searchProducts(searchKeyword.value)
    } else {
      await loadProductsByCategory(selectedCategoryId.value)
    }
  }
  
  /**
   * 重置所有过滤条件
   */
  const resetFilters = async () => {
    selectedCategoryId.value = 'all'
    searchKeyword.value = ''
    priceFilter.value = ''
    posStore.setSearchKeyword('')
    posStore.setSelectedCategory(null)
    
    await loadProductsByCategory('all')
  }
  
  // ==================== 工具方法 ====================
  
  /**
   * 根据商品ID获取商品详情
   * @param {number} productId - 商品ID
   */
  const getProductById = (productId) => {
    return products.value.find(p => p.productId === productId)
  }
  
  /**
   * 根据条形码获取商品
   * @param {string} barcode - 条形码
   */
  const getProductByBarcode = async (barcode) => {
    try {
      const response = await ProductApi.getProductByBarcode(barcode)
      return response?.data || null
    } catch (error) {
      console.error('根据条形码获取商品失败:', error)
      return null
    }
  }
  
  /**
   * 检查商品库存
   * @param {number} productId - 商品ID
   */
  const checkProductStock = async (productId) => {
    try {
      const response = await ProductApi.checkProductStock(productId)
      return response?.data || null
    } catch (error) {
      console.error('检查商品库存失败:', error)
      return null
    }
  }
  
  // 返回所有需要的状态和方法
  return {
    // 状态
    loading,
    categoriesLoading,
    productsLoading,
    selectedCategoryId,
    searchKeyword,
    priceFilter,
    
    // 计算属性
    categories,
    products,
    filteredProducts,
    hasCategories,
    hasProducts,
    
    // 数据加载方法
    loadCategories,
    loadProductsByCategory,
    searchProducts,
    initializeProductData,
    
    // 事件处理方法
    handleCategoryChange,
    handleSearch,
    handlePriceFilter,
    refreshProductData,
    resetFilters,
    
    // 工具方法
    getProductById,
    getProductByBarcode,
    checkProductStock
  }
}