/**
 * POS模块重试处理器
 * 
 * 提供API调用重试机制，支持指数退避、条件重试等策略
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { isRetryableError } from './error-types'

/**
 * 重试处理器类
 */
export class RetryHandler {
  
  /**
   * 带重试的API调用
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 重试选项
   * @param {number} options.maxRetries - 最大重试次数，默认3
   * @param {number} options.retryDelay - 重试延迟（毫秒），默认1000
   * @param {number} options.maxDelay - 最大延迟（毫秒），默认30000
   * @param {number} options.backoffFactor - 退避因子，默认2
   * @param {Function} options.retryCondition - 重试条件函数
   * @param {Function} options.onRetry - 重试回调函数
   * @returns {Promise} API调用结果
   */
  static async withRetry(apiFunction, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      maxDelay = 30000,
      backoffFactor = 2,
      retryCondition = (error) => this.shouldRetry(error),
      onRetry
    } = options
    
    let lastError
    let currentDelay = retryDelay
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction()
      } catch (error) {
        lastError = error
        
        // 如果是最后一次尝试，或者不满足重试条件，直接抛出错误
        if (attempt === maxRetries || !retryCondition(error)) {
          throw error
        }
        
        // 执行重试回调
        if (typeof onRetry === 'function') {
          onRetry(error, attempt + 1, maxRetries)
        }
        
        // 等待指定时间后重试
        await this.delay(currentDelay)
        
        // 计算下次重试的延迟时间（指数退避）
        currentDelay = Math.min(currentDelay * backoffFactor, maxDelay)
      }
    }
    
    throw lastError
  }
  
  /**
   * 判断是否应该重试
   * @param {Error|Object} error - 错误对象
   * @returns {boolean} 是否应该重试
   */
  static shouldRetry(error) {
    // 如果是标准化错误对象，检查retryable属性
    if (error && typeof error.retryable === 'boolean') {
      return error.retryable
    }
    
    // 如果有错误类型，使用错误类型判断
    if (error && error.type) {
      return isRetryableError(error.type)
    }
    
    // 根据HTTP状态码判断
    if (error && error.response && error.response.status) {
      const status = error.response.status
      
      // 5xx服务器错误可以重试
      if (status >= 500) {
        return true
      }
      
      // 408请求超时可以重试
      if (status === 408) {
        return true
      }
      
      // 429请求过多可以重试
      if (status === 429) {
        return true
      }
      
      // 其他4xx客户端错误不重试
      if (status >= 400 && status < 500) {
        return false
      }
    }
    
    // 根据错误消息判断
    if (error && error.message) {
      const message = error.message.toLowerCase()
      
      // 网络相关错误可以重试
      if (message.includes('network') || 
          message.includes('timeout') || 
          message.includes('connection') ||
          message.includes('网络') ||
          message.includes('超时') ||
          message.includes('连接')) {
        return true
      }
      
      // 服务不可用可以重试
      if (message.includes('service unavailable') ||
          message.includes('服务不可用')) {
        return true
      }
    }
    
    // 默认不重试
    return false
  }
  
  /**
   * 延迟函数
   * @param {number} ms - 延迟时间（毫秒）
   * @returns {Promise} 延迟Promise
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 带抖动的延迟（避免惊群效应）
   * @param {number} baseDelay - 基础延迟时间（毫秒）
   * @param {number} jitterFactor - 抖动因子，默认0.1（10%的随机抖动）
   * @returns {Promise} 延迟Promise
   */
  static delayWithJitter(baseDelay, jitterFactor = 0.1) {
    const jitter = baseDelay * jitterFactor * Math.random()
    const actualDelay = baseDelay + jitter
    return this.delay(actualDelay)
  }
  
  /**
   * 线性退避重试
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 重试选项
   * @returns {Promise} API调用结果
   */
  static async withLinearBackoff(apiFunction, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      retryCondition = (error) => this.shouldRetry(error),
      onRetry
    } = options
    
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries || !retryCondition(error)) {
          throw error
        }
        
        if (typeof onRetry === 'function') {
          onRetry(error, attempt + 1, maxRetries)
        }
        
        // 线性增加延迟时间
        const currentDelay = retryDelay * (attempt + 1)
        await this.delay(currentDelay)
      }
    }
    
    throw lastError
  }
  
  /**
   * 固定间隔重试
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 重试选项
   * @returns {Promise} API调用结果
   */
  static async withFixedInterval(apiFunction, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      retryCondition = (error) => this.shouldRetry(error),
      onRetry
    } = options
    
    let lastError
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiFunction()
      } catch (error) {
        lastError = error
        
        if (attempt === maxRetries || !retryCondition(error)) {
          throw error
        }
        
        if (typeof onRetry === 'function') {
          onRetry(error, attempt + 1, maxRetries)
        }
        
        // 固定间隔延迟
        await this.delay(retryDelay)
      }
    }
    
    throw lastError
  }
  
  /**
   * 带熔断器的重试
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 重试选项
   * @param {number} options.failureThreshold - 失败阈值，默认5
   * @param {number} options.recoveryTimeout - 恢复超时时间（毫秒），默认60000
   * @returns {Promise} API调用结果
   */
  static async withCircuitBreaker(apiFunction, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      failureThreshold = 5,
      recoveryTimeout = 60000,
      retryCondition = (error) => this.shouldRetry(error),
      onRetry
    } = options
    
    // 获取或创建熔断器状态
    const circuitKey = apiFunction.name || 'anonymous'
    const circuitState = this.getCircuitState(circuitKey)
    
    // 检查熔断器状态
    if (circuitState.state === 'OPEN') {
      const now = Date.now()
      if (now - circuitState.lastFailureTime < recoveryTimeout) {
        throw new Error('熔断器开启，服务暂时不可用')
      } else {
        // 尝试半开状态
        circuitState.state = 'HALF_OPEN'
      }
    }
    
    try {
      const result = await this.withRetry(apiFunction, {
        maxRetries,
        retryDelay,
        retryCondition,
        onRetry
      })
      
      // 成功时重置熔断器
      this.resetCircuit(circuitKey)
      return result
    } catch (error) {
      // 失败时更新熔断器状态
      this.recordFailure(circuitKey, failureThreshold)
      throw error
    }
  }
  
  /**
   * 获取熔断器状态
   * @param {string} circuitKey - 熔断器键名
   * @returns {Object} 熔断器状态
   */
  static getCircuitState(circuitKey) {
    if (!this.circuitStates) {
      this.circuitStates = new Map()
    }
    
    if (!this.circuitStates.has(circuitKey)) {
      this.circuitStates.set(circuitKey, {
        state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
        failureCount: 0,
        lastFailureTime: 0
      })
    }
    
    return this.circuitStates.get(circuitKey)
  }
  
  /**
   * 记录失败
   * @param {string} circuitKey - 熔断器键名
   * @param {number} failureThreshold - 失败阈值
   */
  static recordFailure(circuitKey, failureThreshold) {
    const circuitState = this.getCircuitState(circuitKey)
    circuitState.failureCount++
    circuitState.lastFailureTime = Date.now()
    
    if (circuitState.failureCount >= failureThreshold) {
      circuitState.state = 'OPEN'
    }
  }
  
  /**
   * 重置熔断器
   * @param {string} circuitKey - 熔断器键名
   */
  static resetCircuit(circuitKey) {
    const circuitState = this.getCircuitState(circuitKey)
    circuitState.state = 'CLOSED'
    circuitState.failureCount = 0
    circuitState.lastFailureTime = 0
  }
  
  /**
   * 批量重试
   * @param {Array<Function>} apiFunctions - API函数数组
   * @param {Object} options - 重试选项
   * @returns {Promise<Array>} 批量调用结果
   */
  static async batchRetry(apiFunctions, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      retryCondition = (error) => this.shouldRetry(error),
      onRetry,
      failFast = false // 是否快速失败
    } = options
    
    const promises = apiFunctions.map(async (apiFunction, index) => {
      try {
        return await this.withRetry(apiFunction, {
          maxRetries,
          retryDelay,
          retryCondition,
          onRetry: onRetry ? (error, attempt, maxAttempts) => onRetry(error, attempt, maxAttempts, index) : undefined
        })
      } catch (error) {
        if (failFast) {
          throw error
        }
        return { error, index }
      }
    })
    
    if (failFast) {
      return await Promise.all(promises)
    } else {
      return await Promise.allSettled(promises)
    }
  }
  
  /**
   * 条件重试（只有满足特定条件时才重试）
   * @param {Function} apiFunction - API函数
   * @param {Function} condition - 重试条件函数
   * @param {Object} options - 重试选项
   * @returns {Promise} API调用结果
   */
  static async retryIf(apiFunction, condition, options = {}) {
    return this.withRetry(apiFunction, {
      ...options,
      retryCondition: condition
    })
  }
  
  /**
   * 重试直到成功或超时
   * @param {Function} apiFunction - API函数
   * @param {Object} options - 重试选项
   * @param {number} options.timeout - 超时时间（毫秒），默认30000
   * @returns {Promise} API调用结果
   */
  static async retryUntilTimeout(apiFunction, options = {}) {
    const {
      timeout = 30000,
      retryDelay = 1000,
      retryCondition = (error) => this.shouldRetry(error),
      onRetry
    } = options
    
    const startTime = Date.now()
    let attempt = 0
    let lastError
    
    while (Date.now() - startTime < timeout) {
      try {
        return await apiFunction()
      } catch (error) {
        lastError = error
        
        if (!retryCondition(error)) {
          throw error
        }
        
        if (typeof onRetry === 'function') {
          onRetry(error, ++attempt, Infinity)
        }
        
        await this.delay(retryDelay)
      }
    }
    
    throw new Error(`操作超时（${timeout}ms），最后一次错误: ${lastError?.message || '未知错误'}`)
  }
}