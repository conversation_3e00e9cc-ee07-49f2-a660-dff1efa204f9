package cn.stylefeng.roses.kernel.erp.modular.pos.controller;

import cn.stylefeng.roses.kernel.db.api.pojo.page.PageResult;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrder;
import cn.stylefeng.roses.kernel.erp.api.pojo.entity.PosOrderItem;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductCategoryRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PosOrderItemRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PosOrderRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.request.PosProductSearchRequest;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductCategoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.ErpProductResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PosCategoryResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PosOrderItemResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PosOrderResponse;
import cn.stylefeng.roses.kernel.erp.api.pojo.response.PosProductResponse;
import cn.stylefeng.roses.kernel.erp.modular.pos.service.PosOrderService;
import cn.stylefeng.roses.kernel.erp.modular.product.service.ErpProductService;
import cn.stylefeng.roses.kernel.erp.modular.productcategory.service.ErpProductCategoryService;
import cn.stylefeng.roses.kernel.rule.annotation.BizLog;
import cn.stylefeng.roses.kernel.rule.pojo.response.ResponseData;
import cn.stylefeng.roses.kernel.rule.pojo.response.SuccessResponseData;
import cn.stylefeng.roses.kernel.scanner.api.annotation.ApiResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.GetResource;
import cn.stylefeng.roses.kernel.scanner.api.annotation.PostResource;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * POS主控制器
 * 
 * 提供POS系统所需的商品分类查询、商品查询和搜索功能
 *
 * <AUTHOR>
 * @since 2025/08/01 15:00
 */
@RestController
@ApiResource(name = "POS主控制器", requiredPermission = true, requirePermissionCode = "ERP_POS")
public class PosController {

    @Resource
    private ErpProductService erpProductService;

    @Resource
    private ErpProductCategoryService erpProductCategoryService;

    @Resource
    private PosOrderService posOrderService;

    /**
     * 获取商品分类列表
     * 
     * 获取所有启用状态的商品分类，用于POS界面的分类导航
     */
    @GetResource(name = "获取商品分类列表", path = "/erp/pos/categories")
    public ResponseData<List<PosCategoryResponse>> getCategories() {
        // 创建查询请求，只查询启用状态的分类
        ErpProductCategoryRequest request = new ErpProductCategoryRequest();
        request.setStatus("Y"); // 只查询启用状态的分类
        
        // 查询分类列表
        List<ErpProductCategoryResponse> categoryList = erpProductCategoryService.findList(request);
        
        // 转换为POS专用的响应格式
        List<PosCategoryResponse> posCategoryList = categoryList.stream()
                .map(this::convertToPosCategoryResponse)
                .collect(Collectors.toList());
        
        return new SuccessResponseData<>(posCategoryList);
    }

    /**
     * 根据分类ID获取商品列表
     * 
     * 获取指定分类下的所有可销售商品
     */
    @GetResource(name = "根据分类获取商品列表", path = "/erp/pos/products")
    public ResponseData<List<PosProductResponse>> getProductsByCategory(PosProductSearchRequest request) {
        // 查询指定分类下的商品
        List<ErpProductResponse> productList;
        
        if (request.getCategoryId() != null) {
            // 根据分类ID查询商品
            productList = erpProductService.findListByCategoryId(request.getCategoryId());
        } else {
            // 查询所有商品
            cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest productRequest = 
                new cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest();
            productRequest.setStatus("ACTIVE"); // 只查询正常状态的商品
            productList = erpProductService.findList(productRequest);
        }
        
        // 过滤商品（根据库存状态等条件）
        List<PosProductResponse> posProductList = productList.stream()
                .filter(product -> filterProduct(product, request))
                .map(this::convertToPosProductResponse)
                .collect(Collectors.toList());
        
        return new SuccessResponseData<>(posProductList);
    }

    /**
     * 搜索商品
     * 
     * 根据关键词搜索商品，支持商品名称、编码、条形码模糊搜索
     */
    @GetResource(name = "搜索商品", path = "/erp/pos/products/search")
    public ResponseData<List<PosProductResponse>> searchProducts(PosProductSearchRequest request) {
        // 创建商品查询请求
        cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest productRequest = 
            new cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest();
        
        // 设置搜索条件
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            productRequest.setProductName(request.getKeyword());
            productRequest.setProductCode(request.getKeyword());
            productRequest.setBarcode(request.getKeyword());
        }
        
        productRequest.setStatus("ACTIVE"); // 只查询正常状态的商品
        
        // 查询商品列表
        List<ErpProductResponse> productList = erpProductService.findList(productRequest);
        
        // 过滤和转换商品数据
        List<PosProductResponse> posProductList = productList.stream()
                .filter(product -> filterProduct(product, request))
                .map(this::convertToPosProductResponse)
                .collect(Collectors.toList());
        
        return new SuccessResponseData<>(posProductList);
    }

    /**
     * 根据商品ID获取商品详情
     * 
     * 获取单个商品的详细信息，用于POS界面显示商品详情
     */
    @GetResource(name = "获取商品详情", path = "/erp/pos/product/detail")
    public ResponseData<PosProductResponse> getProductDetail(PosProductSearchRequest request) {
        if (request.getProductId() == null) {
            throw new IllegalArgumentException("商品ID不能为空");
        }
        
        // 创建商品查询请求
        cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest productRequest = 
            new cn.stylefeng.roses.kernel.erp.api.pojo.request.ErpProductRequest();
        productRequest.setProductId(request.getProductId());
        
        // 查询商品详情
        ErpProductResponse product = erpProductService.detail(productRequest);
        
        // 转换为POS专用的响应格式
        PosProductResponse posProduct = convertToPosProductResponse(product);
        
        return new SuccessResponseData<>(posProduct);
    }

    /**
     * 转换商品分类响应格式
     */
    private PosCategoryResponse convertToPosCategoryResponse(ErpProductCategoryResponse category) {
        PosCategoryResponse posCategory = new PosCategoryResponse();
        BeanUtils.copyProperties(category, posCategory);
        return posCategory;
    }

    /**
     * 转换商品响应格式
     */
    private PosProductResponse convertToPosProductResponse(ErpProductResponse product) {
        PosProductResponse posProduct = new PosProductResponse();
        BeanUtils.copyProperties(product, posProduct);
        
        // 设置商品图片URL（如果需要的话，这里可以添加图片处理逻辑）
        // posProduct.setImageUrl(generateImageUrl(product.getProductId()));
        
        return posProduct;
    }

    /**
     * 过滤商品
     */
    private boolean filterProduct(ErpProductResponse product, PosProductSearchRequest request) {
        // 只显示正常状态的商品
        if (!"ACTIVE".equals(product.getStatus())) {
            return false;
        }
        
        // 如果设置了只显示有库存的商品
        if (Boolean.TRUE.equals(request.getOnlyInStock())) {
            if (product.getStockQuantity() == null || 
                product.getStockQuantity().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return false;
            }
        }
        
        // 如果设置了计价类型过滤
        if (request.getPricingType() != null && !request.getPricingType().isEmpty()) {
            if (!request.getPricingType().equals(product.getPricingType())) {
                return false;
            }
        }
        
        // 关键词搜索过滤
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            String keyword = request.getKeyword().toLowerCase();
            String productName = product.getProductName() != null ? product.getProductName().toLowerCase() : "";
            String productCode = product.getProductCode() != null ? product.getProductCode().toLowerCase() : "";
            String barcode = product.getBarcode() != null ? product.getBarcode().toLowerCase() : "";
            
            if (!productName.contains(keyword) && 
                !productCode.contains(keyword) && 
                !barcode.contains(keyword)) {
                return false;
            }
        }
        
        return true;
    }

    // ==================== 订单管理API ====================

    /**
     * 创建POS订单
     * 
     * 创建新的POS订单，包含订单基本信息和订单项列表
     */
    @PostResource(name = "创建POS订单", path = "/erp/pos/order/create")
    @BizLog(logTypeCode = "ERP_POS_ORDER_CREATE")
    public ResponseData<PosOrderResponse> createOrder(@RequestBody @Validated(PosOrderRequest.add.class) PosOrderRequest request) {
        // 转换请求参数为实体对象
        PosOrder posOrder = convertToPosOrder(request);
        List<PosOrderItem> orderItems = convertToPosOrderItems(request.getOrderItems());
        
        // 创建订单
        Long orderId = posOrderService.createOrder(posOrder, orderItems);
        
        // 查询创建的订单详情
        PosOrder createdOrder = posOrderService.getOrderById(orderId);
        List<PosOrderItem> createdOrderItems = posOrderService.getOrderItems(orderId);
        
        // 转换为响应格式
        PosOrderResponse response = convertToPosOrderResponse(createdOrder, createdOrderItems);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 根据订单ID查询订单详情
     * 
     * 获取订单的详细信息，包括订单项列表
     */
    @GetResource(name = "查询订单详情", path = "/erp/pos/order/detail")
    public ResponseData<PosOrderResponse> getOrderDetail(@Validated(PosOrderRequest.detail.class) PosOrderRequest request) {
        // 查询订单基本信息
        PosOrder order = posOrderService.getOrderById(request.getOrderId());
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 查询订单项列表
        List<PosOrderItem> orderItems = posOrderService.getOrderItems(request.getOrderId());
        
        // 转换为响应格式
        PosOrderResponse response = convertToPosOrderResponse(order, orderItems);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 根据订单号查询订单详情
     * 
     * 通过订单号获取订单详细信息
     */
    @GetResource(name = "根据订单号查询订单", path = "/erp/pos/order/detailByNo")
    public ResponseData<PosOrderResponse> getOrderDetailByNo(PosOrderRequest request) {
        if (request.getOrderNo() == null || request.getOrderNo().trim().isEmpty()) {
            throw new IllegalArgumentException("订单号不能为空");
        }
        
        // 查询订单基本信息
        PosOrder order = posOrderService.getOrderByNo(request.getOrderNo());
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }
        
        // 查询订单项列表
        List<PosOrderItem> orderItems = posOrderService.getOrderItems(order.getOrderId());
        
        // 转换为响应格式
        PosOrderResponse response = convertToPosOrderResponse(order, orderItems);
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 分页查询订单列表
     * 
     * 支持按订单状态、支付状态、收银员等条件查询
     */
    @GetResource(name = "分页查询订单列表", path = "/erp/pos/order/page")
    public ResponseData<PageResult<PosOrderResponse>> getOrderPage(PosOrderRequest request) {
        // 设置默认分页参数
        if (request.getPageNo() == null) {
            request.setPageNo(1);
        }
        if (request.getPageSize() == null) {
            request.setPageSize(20);
        }
        
        // 查询订单分页数据
        PageResult<PosOrder> pageResult = posOrderService.findOrderPage(
            request.getPageNo(), 
            request.getPageSize(),
            request.getOrderStatus(),
            request.getPaymentStatus(),
            request.getCashierId()
        );
        
        // 转换为响应格式
        List<PosOrderResponse> responseList = pageResult.getRows().stream()
                .map(order -> convertToPosOrderResponse(order, null))
                .collect(Collectors.toList());
        
        PageResult<PosOrderResponse> response = new PageResult<>();
        response.setRows(responseList);
        response.setTotalRows(pageResult.getTotalRows());
        response.setTotalPage(pageResult.getTotalPage());
        
        return new SuccessResponseData<>(response);
    }

    /**
     * 更新订单状态
     * 
     * 更新订单的状态信息
     */
    @PostResource(name = "更新订单状态", path = "/erp/pos/order/updateStatus")
    @BizLog(logTypeCode = "ERP_POS_ORDER_UPDATE_STATUS")
    public ResponseData<?> updateOrderStatus(@RequestBody @Validated(PosOrderRequest.updateStatus.class) PosOrderRequest request) {
        posOrderService.updateOrderStatus(request.getOrderId(), request.getOrderStatus());
        return new SuccessResponseData<>();
    }

    /**
     * 取消订单
     * 
     * 取消指定的订单
     */
    @PostResource(name = "取消订单", path = "/erp/pos/order/cancel")
    @BizLog(logTypeCode = "ERP_POS_ORDER_CANCEL")
    public ResponseData<?> cancelOrder(@RequestBody PosOrderRequest request) {
        if (request.getOrderId() == null) {
            throw new IllegalArgumentException("订单ID不能为空");
        }
        
        posOrderService.cancelOrder(request.getOrderId(), request.getRemark());
        return new SuccessResponseData<>();
    }

    /**
     * 添加订单项
     * 
     * 向现有订单添加新的商品项
     */
    @PostResource(name = "添加订单项", path = "/erp/pos/order/addItem")
    @BizLog(logTypeCode = "ERP_POS_ORDER_ADD_ITEM")
    public ResponseData<?> addOrderItem(@RequestBody PosOrderItemRequest request) {
        if (request.getOrderId() == null) {
            throw new IllegalArgumentException("订单ID不能为空");
        }
        
        PosOrderItem orderItem = convertToPosOrderItem(request);
        posOrderService.addOrderItem(request.getOrderId(), orderItem);
        
        // 重新计算订单金额
        posOrderService.recalculateOrderAmount(request.getOrderId());
        
        return new SuccessResponseData<>();
    }

    /**
     * 更新订单项数量
     * 
     * 修改订单项的数量
     */
    @PostResource(name = "更新订单项数量", path = "/erp/pos/order/updateItemQuantity")
    @BizLog(logTypeCode = "ERP_POS_ORDER_UPDATE_ITEM")
    public ResponseData<?> updateOrderItemQuantity(@RequestBody PosOrderItemRequest request) {
        if (request.getItemId() == null) {
            throw new IllegalArgumentException("订单项ID不能为空");
        }
        if (request.getQuantity() == null) {
            throw new IllegalArgumentException("数量不能为空");
        }
        
        posOrderService.updateOrderItemQuantity(request.getItemId(), request.getQuantity());
        
        // 重新计算订单金额
        if (request.getOrderId() != null) {
            posOrderService.recalculateOrderAmount(request.getOrderId());
        }
        
        return new SuccessResponseData<>();
    }

    /**
     * 删除订单项
     * 
     * 从订单中删除指定的商品项
     */
    @PostResource(name = "删除订单项", path = "/erp/pos/order/removeItem")
    @BizLog(logTypeCode = "ERP_POS_ORDER_REMOVE_ITEM")
    public ResponseData<?> removeOrderItem(@RequestBody PosOrderItemRequest request) {
        if (request.getItemId() == null) {
            throw new IllegalArgumentException("订单项ID不能为空");
        }
        
        posOrderService.removeOrderItem(request.getItemId());
        
        // 重新计算订单金额
        if (request.getOrderId() != null) {
            posOrderService.recalculateOrderAmount(request.getOrderId());
        }
        
        return new SuccessResponseData<>();
    }

    /**
     * 查询收银员今日订单
     * 
     * 获取指定收银员今天的所有订单
     */
    @GetResource(name = "查询收银员今日订单", path = "/erp/pos/order/todayOrders")
    public ResponseData<List<PosOrderResponse>> getTodayOrdersByCashier(PosOrderRequest request) {
        if (request.getCashierId() == null) {
            throw new IllegalArgumentException("收银员ID不能为空");
        }
        
        List<PosOrder> orders = posOrderService.getTodayOrdersByCashier(request.getCashierId());
        
        List<PosOrderResponse> responseList = orders.stream()
                .map(order -> convertToPosOrderResponse(order, null))
                .collect(Collectors.toList());
        
        return new SuccessResponseData<>(responseList);
    }

    // ==================== 私有转换方法 ====================

    /**
     * 转换订单请求为实体对象
     */
    private PosOrder convertToPosOrder(PosOrderRequest request) {
        PosOrder posOrder = new PosOrder();
        BeanUtils.copyProperties(request, posOrder);
        
        // 设置默认值
        if (posOrder.getOrderStatus() == null) {
            posOrder.setOrderStatus("PENDING");
        }
        if (posOrder.getPaymentStatus() == null) {
            posOrder.setPaymentStatus("UNPAID");
        }
        if (posOrder.getDiscountAmount() == null) {
            posOrder.setDiscountAmount(java.math.BigDecimal.ZERO);
        }
        
        return posOrder;
    }

    /**
     * 转换订单项请求列表为实体对象列表
     */
    private List<PosOrderItem> convertToPosOrderItems(List<PosOrderItemRequest> requests) {
        return requests.stream()
                .map(this::convertToPosOrderItem)
                .collect(Collectors.toList());
    }

    /**
     * 转换订单项请求为实体对象
     */
    private PosOrderItem convertToPosOrderItem(PosOrderItemRequest request) {
        PosOrderItem orderItem = new PosOrderItem();
        BeanUtils.copyProperties(request, orderItem);
        
        // 计算小计金额
        if (orderItem.getTotalPrice() == null && 
            orderItem.getUnitPrice() != null && 
            orderItem.getQuantity() != null) {
            orderItem.setTotalPrice(orderItem.getUnitPrice().multiply(orderItem.getQuantity()));
        }
        
        return orderItem;
    }

    /**
     * 转换订单实体为响应对象
     */
    private PosOrderResponse convertToPosOrderResponse(PosOrder order, List<PosOrderItem> orderItems) {
        PosOrderResponse response = new PosOrderResponse();
        BeanUtils.copyProperties(order, response);
        
        // 设置状态名称
        response.setOrderStatusName(getOrderStatusName(order.getOrderStatus()));
        response.setPaymentStatusName(getPaymentStatusName(order.getPaymentStatus()));
        response.setPaymentMethodName(getPaymentMethodName(order.getPaymentMethod()));
        
        // 转换订单项
        if (orderItems != null) {
            List<PosOrderItemResponse> itemResponses = orderItems.stream()
                    .map(this::convertToPosOrderItemResponse)
                    .collect(Collectors.toList());
            response.setOrderItems(itemResponses);
        }
        
        return response;
    }

    /**
     * 转换订单项实体为响应对象
     */
    private PosOrderItemResponse convertToPosOrderItemResponse(PosOrderItem orderItem) {
        PosOrderItemResponse response = new PosOrderItemResponse();
        BeanUtils.copyProperties(orderItem, response);
        
        // 设置计价类型名称
        response.setPricingTypeName(getPricingTypeName(orderItem.getPricingType()));
        
        return response;
    }

    /**
     * 获取订单状态名称
     */
    private String getOrderStatusName(String orderStatus) {
        if (orderStatus == null) return "";
        switch (orderStatus) {
            case "PENDING": return "待支付";
            case "PAID": return "已支付";
            case "CANCELLED": return "已取消";
            case "REFUNDED": return "已退款";
            default: return orderStatus;
        }
    }

    /**
     * 获取支付状态名称
     */
    private String getPaymentStatusName(String paymentStatus) {
        if (paymentStatus == null) return "";
        switch (paymentStatus) {
            case "UNPAID": return "未支付";
            case "PAID": return "已支付";
            case "PARTIAL": return "部分支付";
            case "REFUNDED": return "已退款";
            default: return paymentStatus;
        }
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentMethodName(String paymentMethod) {
        if (paymentMethod == null) return "";
        switch (paymentMethod) {
            case "CASH": return "现金";
            case "WECHAT": return "微信支付";
            case "ALIPAY": return "支付宝";
            case "MEMBER": return "会员卡";
            case "CARD": return "银行卡";
            default: return paymentMethod;
        }
    }

    /**
     * 获取计价类型名称
     */
    private String getPricingTypeName(String pricingType) {
        if (pricingType == null) return "";
        switch (pricingType) {
            case "NORMAL": return "普通";
            case "WEIGHT": return "计重";
            case "PIECE": return "计件";
            case "VARIABLE": return "不定价";
            default: return pricingType;
        }
    }

}