<template>
  <div class="member-selector">
    <!-- 快捷选择最近会员 -->
    <div class="recent-members" v-if="recentMembers.length > 0">
      <div class="recent-title">最近使用</div>
      <div class="recent-list">
        <div
          v-for="member in recentMembers"
          :key="member.memberId"
          class="recent-member-item"
          @click="selectMember(member)"
        >
          <div class="member-avatar">
            <img v-if="member.avatar" :src="member.avatar" :alt="member.memberName" />
            <icon-font v-else iconClass="icon-user-default" />
          </div>
          <div class="member-info">
            <div class="member-name">{{ member.memberName }}</div>
            <div class="member-phone">{{ formatPhone(member.phone) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 定义组件名称
defineOptions({
  name: 'MemberSelector'
})

// 定义事件
const emit = defineEmits(['memberSelect'])

// 响应式数据
const recentMembers = ref([])

/**
 * 格式化手机号显示
 */
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 选择会员
 */
const selectMember = (member) => {
  emit('memberSelect', member)
}

/**
 * 添加到最近使用会员
 */
const addToRecentMembers = (member) => {
  // 移除已存在的相同会员
  recentMembers.value = recentMembers.value.filter(m => m.memberId !== member.memberId)
  
  // 添加到开头
  recentMembers.value.unshift(member)
  
  // 最多保留5个
  if (recentMembers.value.length > 5) {
    recentMembers.value = recentMembers.value.slice(0, 5)
  }
  
  // 保存到本地存储
  saveRecentMembers()
}

/**
 * 保存最近使用的会员到本地存储
 */
const saveRecentMembers = () => {
  try {
    localStorage.setItem('pos_recent_members', JSON.stringify(recentMembers.value))
  } catch (error) {
    console.error('保存最近会员失败:', error)
  }
}

/**
 * 加载最近使用的会员
 */
const loadRecentMembers = () => {
  try {
    const stored = localStorage.getItem('pos_recent_members')
    if (stored) {
      recentMembers.value = JSON.parse(stored)
    }
  } catch (error) {
    console.error('加载最近会员失败:', error)
  }
}

// 组件挂载时加载最近会员
onMounted(() => {
  loadRecentMembers()
})

// 暴露方法给父组件
defineExpose({
  addToRecentMembers,
  loadRecentMembers
})
</script>

<style scoped>
.member-selector {
  padding: 16px 0;
}

.recent-members {
  margin-top: 16px;
}

.recent-title {
  font-size: 13px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recent-member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.recent-member-item:hover {
  background: #f0f9ff;
  border-color: #e6f7ff;
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-info .member-name {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.member-info .member-phone {
  font-size: 11px;
  color: #8c8c8c;
}
</style>