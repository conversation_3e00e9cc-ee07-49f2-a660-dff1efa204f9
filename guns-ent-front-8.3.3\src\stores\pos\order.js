/**
 * POS订单状态管理
 * 
 * 管理挂单、订单历史、订单状态等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineStore } from 'pinia'
import { ref, computed, shallowRef } from 'vue'
import { message } from 'ant-design-vue'

export const useOrderStore = defineStore('pos-order', () => {
  // ==================== 状态定义 ====================
  
  // 使用shallowRef优化大数组的响应性能
  const suspendedOrders = shallowRef([])
  const orderHistory = shallowRef([])
  
  // 挂单计数器（用于生成挂单编号）
  const suspendCounter = ref(1)
  
  // 当前订单信息
  const currentOrder = ref(null)
  
  // 订单配置
  const orderConfig = ref({
    maxSuspendedOrders: 50, // 最大挂单数量
    maxOrderHistory: 100, // 最大历史订单数量
    suspendExpireHours: 24, // 挂单过期时间（小时）
    autoCleanup: true // 是否自动清理过期数据
  })
  
  // ==================== 计算属性 ====================
  
  // 是否有挂起的订单
  const hasSuspendedOrders = computed(() => {
    return suspendedOrders.value.length > 0
  })
  
  // 挂单数量
  const suspendedOrderCount = computed(() => {
    return suspendedOrders.value.length
  })
  
  // 历史订单数量
  const orderHistoryCount = computed(() => {
    return orderHistory.value.length
  })
  
  // 今日订单数量
  const todayOrderCount = computed(() => {
    const today = new Date().toDateString()
    return orderHistory.value.filter(order => {
      const orderDate = new Date(order.createTime).toDateString()
      return orderDate === today
    }).length
  })
  
  // 今日销售额
  const todaySalesAmount = computed(() => {
    const today = new Date().toDateString()
    return orderHistory.value
      .filter(order => {
        const orderDate = new Date(order.createTime).toDateString()
        return orderDate === today && order.status === 'completed'
      })
      .reduce((total, order) => total + (order.finalAmount || 0), 0)
  })
  
  // 过期的挂单
  const expiredSuspendedOrders = computed(() => {
    const expireTime = Date.now() - (orderConfig.value.suspendExpireHours * 60 * 60 * 1000)
    return suspendedOrders.value.filter(order => {
      const suspendTime = new Date(order.suspendTime).getTime()
      return suspendTime < expireTime
    })
  })
  
  // ==================== 私有方法 ====================
  
  /**
   * 生成订单编号
   * @param {string} prefix - 前缀
   * @returns {string} 订单编号
   */
  const generateOrderNo = (prefix = 'ORDER') => {
    const now = new Date()
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
    const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '')
    const randomStr = Math.random().toString(36).substr(2, 4).toUpperCase()
    return `${prefix}-${dateStr}-${timeStr}-${randomStr}`
  }
  
  /**
   * 生成挂单编号
   * @returns {string} 挂单编号
   */
  const generateSuspendNo = () => {
    const counterStr = suspendCounter.value.toString().padStart(3, '0')
    suspendCounter.value++
    return `SUSPEND-${Date.now()}-${counterStr}`
  }
  
  /**
   * 验证订单数据
   * @param {Object} orderData - 订单数据
   * @returns {boolean} 是否有效
   */
  const validateOrderData = (orderData) => {
    if (!orderData) {
      message.error('订单数据无效')
      return false
    }
    
    if (!orderData.cartItems || !Array.isArray(orderData.cartItems) || orderData.cartItems.length === 0) {
      message.error('订单商品不能为空')
      return false
    }
    
    if (typeof orderData.totalAmount !== 'number' || orderData.totalAmount <= 0) {
      message.error('订单金额无效')
      return false
    }
    
    return true
  }
  
  /**
   * 触发响应式更新
   */
  const triggerSuspendedOrdersUpdate = () => {
    suspendedOrders.value = [...suspendedOrders.value]
  }
  
  const triggerOrderHistoryUpdate = () => {
    orderHistory.value = [...orderHistory.value]
  }
  
  /**
   * 保存挂单数据到本地存储
   */
  const saveSuspendedOrdersToLocal = () => {
    try {
      const data = {
        suspendedOrders: suspendedOrders.value,
        suspendCounter: suspendCounter.value,
        timestamp: Date.now()
      }
      localStorage.setItem('pos_suspended_orders', JSON.stringify(data))
    } catch (error) {
      console.error('保存挂单数据到本地存储失败:', error)
    }
  }
  
  /**
   * 保存订单历史到本地存储
   */
  const saveOrderHistoryToLocal = () => {
    try {
      const data = {
        orderHistory: orderHistory.value.slice(-orderConfig.value.maxOrderHistory),
        timestamp: Date.now()
      }
      localStorage.setItem('pos_order_history', JSON.stringify(data))
    } catch (error) {
      console.error('保存订单历史到本地存储失败:', error)
    }
  }
  
  // ==================== 公共方法 ====================
  
  /**
   * 挂起当前订单
   * @param {Object} orderData - 订单数据
   * @param {string} remark - 挂单备注
   * @returns {boolean} 是否挂单成功
   */
  const suspendCurrentOrder = (orderData, remark = '') => {
    try {
      if (!validateOrderData(orderData)) {
        return false
      }
      
      // 检查挂单数量限制
      if (suspendedOrders.value.length >= orderConfig.value.maxSuspendedOrders) {
        message.warning(`挂单数量已达上限（${orderConfig.value.maxSuspendedOrders}个）`)
        return false
      }
      
      // 生成挂单编号
      const suspendNo = generateSuspendNo()
      
      // 创建挂单数据
      const suspendedOrder = {
        suspendId: Date.now(),
        suspendNo: suspendNo,
        suspendTime: new Date().toISOString(),
        remark: remark,
        orderData: {
          ...JSON.parse(JSON.stringify(orderData)),
          suspendTime: Date.now()
        },
        status: 'suspended',
        createUser: 'current_user', // 这里应该从用户状态获取
        updateTime: new Date().toISOString()
      }
      
      // 添加到挂单列表
      suspendedOrders.value.push(suspendedOrder)
      triggerSuspendedOrdersUpdate()
      
      // 保存到本地存储
      saveSuspendedOrdersToLocal()
      
      message.success(`订单已挂起，挂单号：${suspendNo}`)
      return true
      
    } catch (error) {
      console.error('挂单失败:', error)
      message.error('挂单失败，请重试')
      return false
    }
  }
  
  /**
   * 恢复挂起的订单
   * @param {number} suspendId - 挂单ID
   * @returns {Object|null} 恢复的订单数据
   */
  const resumeSuspendedOrder = (suspendId) => {
    try {
      const suspendedOrder = suspendedOrders.value.find(order => order.suspendId === suspendId)
      if (!suspendedOrder) {
        message.error('挂单不存在')
        return null
      }
      
      // 检查挂单是否过期
      const expireTime = Date.now() - (orderConfig.value.suspendExpireHours * 60 * 60 * 1000)
      const suspendTime = new Date(suspendedOrder.suspendTime).getTime()
      
      if (suspendTime < expireTime) {
        message.warning('挂单已过期')
        // 可以选择是否自动删除过期挂单
        if (orderConfig.value.autoCleanup) {
          deleteSuspendedOrder(suspendId)
        }
        return null
      }
      
      // 获取订单数据
      const orderData = suspendedOrder.orderData
      
      // 从挂单列表中移除
      const index = suspendedOrders.value.findIndex(order => order.suspendId === suspendId)
      if (index > -1) {
        suspendedOrders.value.splice(index, 1)
        triggerSuspendedOrdersUpdate()
        saveSuspendedOrdersToLocal()
      }
      
      message.success(`已恢复挂单：${suspendedOrder.suspendNo}`)
      return orderData
      
    } catch (error) {
      console.error('恢复挂单失败:', error)
      message.error('恢复挂单失败，请重试')
      return null
    }
  }
  
  /**
   * 删除挂起的订单
   * @param {number} suspendId - 挂单ID
   * @returns {boolean} 是否删除成功
   */
  const deleteSuspendedOrder = (suspendId) => {
    try {
      const index = suspendedOrders.value.findIndex(order => order.suspendId === suspendId)
      if (index > -1) {
        const deletedOrder = suspendedOrders.value.splice(index, 1)[0]
        triggerSuspendedOrdersUpdate()
        saveSuspendedOrdersToLocal()
        message.success(`已删除挂单：${deletedOrder.suspendNo}`)
        return true
      }
      return false
    } catch (error) {
      console.error('删除挂单失败:', error)
      message.error('删除挂单失败')
      return false
    }
  }
  
  /**
   * 批量删除挂单
   * @param {Array} suspendIds - 挂单ID数组
   * @returns {number} 删除成功的数量
   */
  const batchDeleteSuspendedOrders = (suspendIds) => {
    try {
      let deletedCount = 0
      
      suspendIds.forEach(suspendId => {
        const index = suspendedOrders.value.findIndex(order => order.suspendId === suspendId)
        if (index > -1) {
          suspendedOrders.value.splice(index, 1)
          deletedCount++
        }
      })
      
      if (deletedCount > 0) {
        triggerSuspendedOrdersUpdate()
        saveSuspendedOrdersToLocal()
        message.success(`已删除${deletedCount}个挂单`)
      }
      
      return deletedCount
    } catch (error) {
      console.error('批量删除挂单失败:', error)
      message.error('批量删除挂单失败')
      return 0
    }
  }
  
  /**
   * 清理过期的挂单
   * @param {number} expireHours - 过期时间（小时），默认使用配置值
   * @returns {number} 清理的数量
   */
  const clearExpiredSuspendedOrders = (expireHours = null) => {
    try {
      const expireTime = Date.now() - ((expireHours || orderConfig.value.suspendExpireHours) * 60 * 60 * 1000)
      const beforeCount = suspendedOrders.value.length
      
      suspendedOrders.value = suspendedOrders.value.filter(order => {
        const suspendTime = new Date(order.suspendTime).getTime()
        return suspendTime > expireTime
      })
      
      const clearedCount = beforeCount - suspendedOrders.value.length
      
      if (clearedCount > 0) {
        triggerSuspendedOrdersUpdate()
        saveSuspendedOrdersToLocal()
        message.info(`已清理 ${clearedCount} 个过期挂单`)
      }
      
      return clearedCount
    } catch (error) {
      console.error('清理过期挂单失败:', error)
      return 0
    }
  }
  
  /**
   * 创建新订单
   * @param {Object} orderData - 订单数据
   * @returns {Object|null} 创建的订单
   */
  const createOrder = (orderData) => {
    try {
      if (!validateOrderData(orderData)) {
        return null
      }
      
      const orderNo = generateOrderNo()
      const order = {
        orderId: Date.now(),
        orderNo: orderNo,
        ...orderData,
        status: 'pending',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        createUser: 'current_user' // 这里应该从用户状态获取
      }
      
      currentOrder.value = order
      
      return order
    } catch (error) {
      console.error('创建订单失败:', error)
      message.error('创建订单失败')
      return null
    }
  }
  
  /**
   * 完成订单
   * @param {Object} paymentResult - 支付结果
   * @returns {boolean} 是否完成成功
   */
  const completeOrder = (paymentResult = {}) => {
    try {
      if (!currentOrder.value) {
        message.error('没有待完成的订单')
        return false
      }
      
      // 更新订单状态
      const completedOrder = {
        ...currentOrder.value,
        status: 'completed',
        paymentResult: paymentResult,
        completeTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      
      // 添加到历史记录
      orderHistory.value.unshift(completedOrder)
      
      // 限制历史记录数量
      if (orderHistory.value.length > orderConfig.value.maxOrderHistory) {
        orderHistory.value = orderHistory.value.slice(0, orderConfig.value.maxOrderHistory)
      }
      
      triggerOrderHistoryUpdate()
      saveOrderHistoryToLocal()
      
      // 清除当前订单
      currentOrder.value = null
      
      message.success(`订单${completedOrder.orderNo}已完成`)
      return true
      
    } catch (error) {
      console.error('完成订单失败:', error)
      message.error('完成订单失败')
      return false
    }
  }
  
  /**
   * 取消订单
   * @param {string} reason - 取消原因
   * @returns {boolean} 是否取消成功
   */
  const cancelOrder = (reason = '') => {
    try {
      if (!currentOrder.value) {
        message.error('没有待取消的订单')
        return false
      }
      
      // 更新订单状态
      const cancelledOrder = {
        ...currentOrder.value,
        status: 'cancelled',
        cancelReason: reason,
        cancelTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
      
      // 添加到历史记录
      orderHistory.value.unshift(cancelledOrder)
      triggerOrderHistoryUpdate()
      saveOrderHistoryToLocal()
      
      // 清除当前订单
      currentOrder.value = null
      
      message.success(`订单${cancelledOrder.orderNo}已取消`)
      return true
      
    } catch (error) {
      console.error('取消订单失败:', error)
      message.error('取消订单失败')
      return false
    }
  }
  
  /**
   * 获取订单详情
   * @param {string} orderNo - 订单号
   * @returns {Object|null} 订单详情
   */
  const getOrderByNo = (orderNo) => {
    // 先从当前订单查找
    if (currentOrder.value && currentOrder.value.orderNo === orderNo) {
      return currentOrder.value
    }
    
    // 从历史订单查找
    return orderHistory.value.find(order => order.orderNo === orderNo) || null
  }
  
  /**
   * 搜索订单
   * @param {Object} criteria - 搜索条件
   * @returns {Array} 搜索结果
   */
  const searchOrders = (criteria = {}) => {
    try {
      let results = [...orderHistory.value]
      
      // 按订单号搜索
      if (criteria.orderNo) {
        results = results.filter(order => 
          order.orderNo.toLowerCase().includes(criteria.orderNo.toLowerCase())
        )
      }
      
      // 按状态搜索
      if (criteria.status) {
        results = results.filter(order => order.status === criteria.status)
      }
      
      // 按日期范围搜索
      if (criteria.startDate && criteria.endDate) {
        const startTime = new Date(criteria.startDate).getTime()
        const endTime = new Date(criteria.endDate).getTime()
        
        results = results.filter(order => {
          const orderTime = new Date(order.createTime).getTime()
          return orderTime >= startTime && orderTime <= endTime
        })
      }
      
      // 按金额范围搜索
      if (criteria.minAmount !== undefined || criteria.maxAmount !== undefined) {
        results = results.filter(order => {
          const amount = order.finalAmount || 0
          const minOk = criteria.minAmount === undefined || amount >= criteria.minAmount
          const maxOk = criteria.maxAmount === undefined || amount <= criteria.maxAmount
          return minOk && maxOk
        })
      }
      
      return results
    } catch (error) {
      console.error('搜索订单失败:', error)
      return []
    }
  }
  
  /**
   * 从本地存储加载挂单数据
   */
  const loadSuspendedOrdersFromLocal = () => {
    try {
      const dataStr = localStorage.getItem('pos_suspended_orders')
      if (dataStr) {
        const data = JSON.parse(dataStr)
        suspendedOrders.value = data.suspendedOrders || []
        suspendCounter.value = data.suspendCounter || 1
        
        // 自动清理过期挂单
        if (orderConfig.value.autoCleanup) {
          clearExpiredSuspendedOrders()
        }
      }
    } catch (error) {
      console.error('从本地存储加载挂单数据失败:', error)
      suspendedOrders.value = []
      suspendCounter.value = 1
    }
  }
  
  /**
   * 从本地存储加载订单历史
   */
  const loadOrderHistoryFromLocal = () => {
    try {
      const dataStr = localStorage.getItem('pos_order_history')
      if (dataStr) {
        const data = JSON.parse(dataStr)
        orderHistory.value = data.orderHistory || []
      }
    } catch (error) {
      console.error('从本地存储加载订单历史失败:', error)
      orderHistory.value = []
    }
  }
  
  /**
   * 获取订单统计信息
   * @param {Object} options - 统计选项
   * @returns {Object} 统计信息
   */
  const getOrderStatistics = (options = {}) => {
    const { dateRange = 'today' } = options
    
    let targetOrders = orderHistory.value
    
    // 按日期范围过滤
    if (dateRange === 'today') {
      const today = new Date().toDateString()
      targetOrders = orderHistory.value.filter(order => {
        const orderDate = new Date(order.createTime).toDateString()
        return orderDate === today
      })
    } else if (dateRange === 'week') {
      const weekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000)
      targetOrders = orderHistory.value.filter(order => {
        const orderTime = new Date(order.createTime).getTime()
        return orderTime >= weekAgo
      })
    }
    
    const completedOrders = targetOrders.filter(order => order.status === 'completed')
    const cancelledOrders = targetOrders.filter(order => order.status === 'cancelled')
    
    return {
      totalOrders: targetOrders.length,
      completedOrders: completedOrders.length,
      cancelledOrders: cancelledOrders.length,
      totalSales: completedOrders.reduce((sum, order) => sum + (order.finalAmount || 0), 0),
      averageOrderValue: completedOrders.length > 0 
        ? completedOrders.reduce((sum, order) => sum + (order.finalAmount || 0), 0) / completedOrders.length 
        : 0,
      suspendedOrders: suspendedOrders.value.length,
      expiredSuspendedOrders: expiredSuspendedOrders.value.length
    }
  }
  
  /**
   * 设置订单配置
   * @param {Object} config - 配置对象
   */
  const setOrderConfig = (config) => {
    try {
      orderConfig.value = { ...orderConfig.value, ...config }
    } catch (error) {
      console.error('设置订单配置失败:', error)
    }
  }
  
  /**
   * 重置订单状态
   */
  const resetOrderState = () => {
    try {
      currentOrder.value = null
      suspendedOrders.value = []
      suspendCounter.value = 1
      
      // 不清除历史记录，只清除当前状态
    } catch (error) {
      console.error('重置订单状态失败:', error)
    }
  }
  
  /**
   * 初始化订单存储
   */
  const initializeOrderStore = () => {
    try {
      loadSuspendedOrdersFromLocal()
      loadOrderHistoryFromLocal()
      console.log('订单存储初始化完成')
    } catch (error) {
      console.error('订单存储初始化失败:', error)
    }
  }
  
  // ==================== 返回接口 ====================
  
  return {
    // 状态
    suspendedOrders,
    orderHistory,
    suspendCounter,
    currentOrder,
    orderConfig,
    
    // 计算属性
    hasSuspendedOrders,
    suspendedOrderCount,
    orderHistoryCount,
    todayOrderCount,
    todaySalesAmount,
    expiredSuspendedOrders,
    
    // 方法
    suspendCurrentOrder,
    resumeSuspendedOrder,
    deleteSuspendedOrder,
    batchDeleteSuspendedOrders,
    clearExpiredSuspendedOrders,
    createOrder,
    completeOrder,
    cancelOrder,
    getOrderByNo,
    searchOrders,
    loadSuspendedOrdersFromLocal,
    loadOrderHistoryFromLocal,
    getOrderStatistics,
    setOrderConfig,
    resetOrderState,
    initializeOrderStore
  }
})