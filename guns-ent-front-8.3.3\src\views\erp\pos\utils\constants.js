/**
 * POS模块业务常量定义
 * 
 * 定义支付方式、订单状态、错误类型等业务常量
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

/**
 * 支付方式常量
 */
export const PAYMENT_METHODS = {
  CASH: 'CASH',           // 现金支付
  WECHAT: 'WECHAT',       // 微信支付
  ALIPAY: 'ALIPAY',       // 支付宝支付
  MEMBER: 'MEMBER',       // 会员卡支付
  CARD: 'CARD',           // 银行卡支付
  POINTS: 'POINTS'        // 积分支付
}

/**
 * 支付方式选项列表
 */
export const PAYMENT_METHOD_OPTIONS = [
  { label: '现金', value: PAYMENT_METHODS.CASH, icon: 'money-collect' },
  { label: '微信支付', value: PAYMENT_METHODS.WECHAT, icon: 'wechat' },
  { label: '支付宝', value: PAYMENT_METHODS.ALIPAY, icon: 'alipay' },
  { label: '会员卡', value: PAYMENT_METHODS.MEMBER, icon: 'credit-card' },
  { label: '银行卡', value: PAYMENT_METHODS.CARD, icon: 'bank' },
  { label: '积分支付', value: PAYMENT_METHODS.POINTS, icon: 'gift' }
]

/**
 * 订单状态常量
 */
export const ORDER_STATUS = {
  PENDING: 'PENDING',     // 待支付
  PAID: 'PAID',           // 已支付
  CANCELLED: 'CANCELLED', // 已取消
  REFUNDED: 'REFUNDED'    // 已退款
}

/**
 * 订单状态选项列表
 */
export const ORDER_STATUS_OPTIONS = [
  { label: '待支付', value: ORDER_STATUS.PENDING, color: 'orange' },
  { label: '已支付', value: ORDER_STATUS.PAID, color: 'green' },
  { label: '已取消', value: ORDER_STATUS.CANCELLED, color: 'red' },
  { label: '已退款', value: ORDER_STATUS.REFUNDED, color: 'purple' }
]

/**
 * 支付状态常量
 */
export const PAYMENT_STATUS = {
  UNPAID: 'UNPAID',       // 未支付
  PAID: 'PAID',           // 已支付
  PARTIAL: 'PARTIAL',     // 部分支付
  REFUNDED: 'REFUNDED',   // 已退款
  PROCESSING: 'PROCESSING' // 处理中
}

/**
 * 支付状态选项列表
 */
export const PAYMENT_STATUS_OPTIONS = [
  { label: '未支付', value: PAYMENT_STATUS.UNPAID, color: 'orange' },
  { label: '已支付', value: PAYMENT_STATUS.PAID, color: 'green' },
  { label: '部分支付', value: PAYMENT_STATUS.PARTIAL, color: 'blue' },
  { label: '已退款', value: PAYMENT_STATUS.REFUNDED, color: 'purple' },
  { label: '处理中', value: PAYMENT_STATUS.PROCESSING, color: 'cyan' }
]

/**
 * 计价类型常量
 */
export const PRICING_TYPES = {
  NORMAL: 'NORMAL',       // 普通计价
  WEIGHT: 'WEIGHT',       // 计重商品
  PIECE: 'PIECE',         // 计件商品
  VARIABLE: 'VARIABLE'    // 不定价商品
}

/**
 * 计价类型选项列表
 */
export const PRICING_TYPE_OPTIONS = [
  { label: '普通', value: PRICING_TYPES.NORMAL },
  { label: '计重', value: PRICING_TYPES.WEIGHT },
  { label: '计件', value: PRICING_TYPES.PIECE },
  { label: '不定价', value: PRICING_TYPES.VARIABLE }
]

/**
 * 库存状态常量
 */
export const STOCK_STATUS = {
  NORMAL: 'NORMAL',           // 正常
  WARNING: 'WARNING',         // 预警
  OUT_OF_STOCK: 'OUT_OF_STOCK' // 缺货
}

/**
 * 库存状态选项列表
 */
export const STOCK_STATUS_OPTIONS = [
  { label: '正常', value: STOCK_STATUS.NORMAL, color: 'green' },
  { label: '预警', value: STOCK_STATUS.WARNING, color: 'orange' },
  { label: '缺货', value: STOCK_STATUS.OUT_OF_STOCK, color: 'red' }
]

/**
 * 会员等级常量
 */
export const MEMBER_LEVELS = {
  BRONZE: 'BRONZE',       // 铜牌会员
  SILVER: 'SILVER',       // 银牌会员
  GOLD: 'GOLD',           // 金牌会员
  PLATINUM: 'PLATINUM',   // 白金会员
  DIAMOND: 'DIAMOND'      // 钻石会员
}

/**
 * 会员等级选项列表
 */
export const MEMBER_LEVEL_OPTIONS = [
  { label: '铜牌会员', value: MEMBER_LEVELS.BRONZE, color: '#CD7F32' },
  { label: '银牌会员', value: MEMBER_LEVELS.SILVER, color: '#C0C0C0' },
  { label: '金牌会员', value: MEMBER_LEVELS.GOLD, color: '#FFD700' },
  { label: '白金会员', value: MEMBER_LEVELS.PLATINUM, color: '#E5E4E2' },
  { label: '钻石会员', value: MEMBER_LEVELS.DIAMOND, color: '#B9F2FF' }
]

/**
 * 快捷键常量
 */
export const KEYBOARD_SHORTCUTS = {
  HELP: 'F1',                 // 帮助
  SUSPEND_ORDER: 'F2',        // 挂单
  RESTORE_ORDER: 'F3',        // 恢复挂单
  CHECKOUT: 'F4',             // 结账
  MEMBER_SCAN: 'F5',          // 会员卡扫描
  CLEAR_MEMBER: 'F6',         // 清除会员
  FULLSCREEN: 'F11',          // 全屏切换
  RESET_ALL: 'Ctrl+R',        // 重置所有
  ADD_PRODUCT: 'Ctrl+A',      // 添加商品
  DELETE_ITEM: 'Ctrl+D',      // 删除商品
  CLEAR_CART: 'Ctrl+C',       // 清空购物车
  CANCEL: 'Escape'            // 取消操作
}

/**
 * 数值常量
 */
export const NUMERIC_CONSTANTS = {
  // 金额精度
  AMOUNT_PRECISION: 2,
  
  // 数量精度
  QUANTITY_PRECISION: 3,
  
  // 折扣率精度
  DISCOUNT_PRECISION: 2,
  
  // 积分兑换比例（多少积分兑换1元）
  POINTS_EXCHANGE_RATE: 100,
  
  // 最大购物车商品数量
  MAX_CART_ITEMS: 100,
  
  // 最大挂单数量
  MAX_SUSPENDED_ORDERS: 50,
  
  // 挂单过期时间（小时）
  SUSPEND_ORDER_EXPIRE_HOURS: 24,
  
  // 购物车备份过期时间（小时）
  CART_BACKUP_EXPIRE_HOURS: 1,
  
  // 性能监控阈值
  PERFORMANCE_THRESHOLDS: {
    COMPONENT_RENDER_TIME: 100,  // 组件渲染时间阈值（毫秒）
    API_CALL_TIME: 5000,         // API调用时间阈值（毫秒）
    MEMORY_USAGE_PERCENT: 80     // 内存使用率阈值（百分比）
  }
}

/**
 * 本地存储键名常量
 */
export const STORAGE_KEYS = {
  CART_BACKUP: 'pos_cart_backup',
  SUSPENDED_ORDERS: 'pos_suspended_orders',
  USER_PREFERENCES: 'pos_user_preferences',
  PERFORMANCE_METRICS: 'pos_performance_metrics'
}

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  // 商品相关
  CATEGORIES: '/erp/pos/categories',
  PRODUCTS: '/erp/pos/products',
  PRODUCT_SEARCH: '/erp/pos/products/search',
  PRODUCT_DETAIL: '/erp/pos/product/detail',
  PRODUCT_BARCODE: '/erp/pos/product/barcode',
  
  // 订单相关
  ORDER_CREATE: '/erp/pos/order/create',
  ORDER_DETAIL: '/erp/pos/order/detail',
  ORDER_UPDATE_STATUS: '/erp/pos/order/updateStatus',
  ORDER_CANCEL: '/erp/pos/order/cancel',
  
  // 支付相关
  PAYMENT_CASH: '/erp/pos/payment/cash',
  PAYMENT_QRCODE: '/erp/pos/payment/qrcode',
  PAYMENT_MEMBER: '/erp/pos/payment/member',
  PAYMENT_CARD: '/erp/pos/payment/bankcard',
  
  // 会员相关
  MEMBER_BY_CARD: '/erp/member/getByCardNo',
  MEMBER_BY_PHONE: '/erp/member/getByPhone',
  MEMBER_DETAIL: '/erp/member/detail',
  MEMBER_DISCOUNT: '/erp/member/calculateDiscount',
  MEMBER_POINTS: '/erp/member/calculatePointsDeduction'
}