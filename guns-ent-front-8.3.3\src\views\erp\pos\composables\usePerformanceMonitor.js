/**
 * 性能监控组合式函数
 * 
 * 提供组件级别的性能监控功能，包括渲染性能、内存使用、用户交互等
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { PerformanceMonitor } from '../utils/performance-monitor'

export function usePerformanceMonitor(componentName = 'Unknown') {
  // 性能状态
  const isMonitoring = ref(false)
  const performanceData = ref({
    renderTimes: [],
    memoryUsage: [],
    userInteractions: [],
    apiCalls: []
  })
  
  // 性能统计
  const performanceStats = ref({
    averageRenderTime: 0,
    maxRenderTime: 0,
    minRenderTime: 0,
    totalRenders: 0,
    memoryTrend: 'stable', // stable, increasing, decreasing
    interactionCount: 0,
    slowOperations: 0
  })
  
  // 性能警告
  const performanceWarnings = ref([])
  const hasPerformanceIssues = computed(() => performanceWarnings.value.length > 0)
  
  // 监控配置
  const monitorConfig = ref({
    enableRenderMonitoring: true,
    enableMemoryMonitoring: true,
    enableInteractionMonitoring: true,
    enableApiMonitoring: true,
    renderTimeThreshold: 16, // 16ms (60fps)
    memoryThreshold: 50, // 50MB
    slowOperationThreshold: 100 // 100ms
  })
  
  // 内部状态
  let renderStartTime = 0
  let memoryCheckInterval = null
  let performanceObserver = null
  
  /**
   * 开始性能监控
   */
  const startMonitoring = () => {
    if (isMonitoring.value) return
    
    isMonitoring.value = true
    
    // 初始化性能监控器
    PerformanceMonitor.init()
    
    // 开始各种监控
    if (monitorConfig.value.enableRenderMonitoring) {
      startRenderMonitoring()
    }
    
    if (monitorConfig.value.enableMemoryMonitoring) {
      startMemoryMonitoring()
    }
    
    if (monitorConfig.value.enableInteractionMonitoring) {
      startInteractionMonitoring()
    }
    
    if (monitorConfig.value.enableApiMonitoring) {
      startApiMonitoring()
    }
    
    console.log(`🔍 开始监控组件 ${componentName} 的性能`)
  }
  
  /**
   * 停止性能监控
   */
  const stopMonitoring = () => {
    if (!isMonitoring.value) return
    
    isMonitoring.value = false
    
    // 清理定时器
    if (memoryCheckInterval) {
      clearInterval(memoryCheckInterval)
      memoryCheckInterval = null
    }
    
    // 清理性能观察器
    if (performanceObserver) {
      performanceObserver.disconnect()
      performanceObserver = null
    }
    
    console.log(`⏹️ 停止监控组件 ${componentName} 的性能`)
  }
  
  /**
   * 开始渲染性能监控
   */
  const startRenderMonitoring = () => {
    // 监控组件渲染时间
    const originalNextTick = nextTick
    
    // 包装nextTick以监控渲染完成时间
    window.nextTick = (...args) => {
      const startTime = performance.now()
      
      return originalNextTick(...args).then(result => {
        const endTime = performance.now()
        const renderTime = endTime - startTime
        
        recordRenderTime(renderTime)
        return result
      })
    }
  }
  
  /**
   * 开始内存监控
   */
  const startMemoryMonitoring = () => {
    memoryCheckInterval = setInterval(() => {
      if (performance.memory) {
        const memoryInfo = {\n          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB\n          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024), // MB\n          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024), // MB\n          timestamp: Date.now()\n        }\n        \n        recordMemoryUsage(memoryInfo)\n        \n        // 检查内存使用是否超过阈值\n        if (memoryInfo.used > monitorConfig.value.memoryThreshold) {\n          addPerformanceWarning({\n            type: 'high_memory_usage',\n            message: `内存使用过高: ${memoryInfo.used}MB`,\n            component: componentName,\n            timestamp: Date.now(),\n            data: memoryInfo\n          })\n        }\n      }\n    }, 5000) // 每5秒检查一次\n  }\n  \n  /**\n   * 开始用户交互监控\n   */\n  const startInteractionMonitoring = () => {\n    const interactionEvents = ['click', 'keydown', 'scroll', 'touchstart']\n    \n    interactionEvents.forEach(eventType => {\n      document.addEventListener(eventType, (event) => {\n        recordUserInteraction({\n          type: eventType,\n          target: event.target.tagName,\n          timestamp: Date.now(),\n          component: componentName\n        })\n      }, { passive: true })\n    })\n  }\n  \n  /**\n   * 开始API监控\n   */\n  const startApiMonitoring = () => {\n    // 这个会在API调用时被外部调用\n    // 主要是提供记录API性能的接口\n  }\n  \n  /**\n   * 记录渲染时间\n   * @param {number} renderTime - 渲染时间（毫秒）\n   */\n  const recordRenderTime = (renderTime) => {\n    performanceData.value.renderTimes.push({\n      time: renderTime,\n      timestamp: Date.now(),\n      component: componentName\n    })\n    \n    // 限制数据量\n    if (performanceData.value.renderTimes.length > 100) {\n      performanceData.value.renderTimes = performanceData.value.renderTimes.slice(-50)\n    }\n    \n    // 更新统计\n    updateRenderStats()\n    \n    // 检查是否超过阈值\n    if (renderTime > monitorConfig.value.renderTimeThreshold) {\n      addPerformanceWarning({\n        type: 'slow_render',\n        message: `渲染时间过长: ${renderTime.toFixed(2)}ms`,\n        component: componentName,\n        timestamp: Date.now(),\n        data: { renderTime }\n      })\n    }\n    \n    // 记录到全局监控器\n    PerformanceMonitor.recordMetric('component_render', {\n      component: componentName,\n      renderTime,\n      timestamp: Date.now()\n    })\n  }\n  \n  /**\n   * 记录内存使用\n   * @param {Object} memoryInfo - 内存信息\n   */\n  const recordMemoryUsage = (memoryInfo) => {\n    performanceData.value.memoryUsage.push(memoryInfo)\n    \n    // 限制数据量\n    if (performanceData.value.memoryUsage.length > 100) {\n      performanceData.value.memoryUsage = performanceData.value.memoryUsage.slice(-50)\n    }\n    \n    // 更新内存趋势\n    updateMemoryTrend()\n  }\n  \n  /**\n   * 记录用户交互\n   * @param {Object} interaction - 交互信息\n   */\n  const recordUserInteraction = (interaction) => {\n    performanceData.value.userInteractions.push(interaction)\n    \n    // 限制数据量\n    if (performanceData.value.userInteractions.length > 200) {\n      performanceData.value.userInteractions = performanceData.value.userInteractions.slice(-100)\n    }\n    \n    // 更新交互统计\n    performanceStats.value.interactionCount = performanceData.value.userInteractions.length\n  }\n  \n  /**\n   * 记录API调用性能\n   * @param {Object} apiCall - API调用信息\n   */\n  const recordApiCall = (apiCall) => {\n    performanceData.value.apiCalls.push({\n      ...apiCall,\n      component: componentName,\n      timestamp: Date.now()\n    })\n    \n    // 限制数据量\n    if (performanceData.value.apiCalls.length > 100) {\n      performanceData.value.apiCalls = performanceData.value.apiCalls.slice(-50)\n    }\n    \n    // 检查是否为慢操作\n    if (apiCall.duration > monitorConfig.value.slowOperationThreshold) {\n      performanceStats.value.slowOperations++\n      \n      addPerformanceWarning({\n        type: 'slow_api',\n        message: `API调用缓慢: ${apiCall.name} (${apiCall.duration}ms)`,\n        component: componentName,\n        timestamp: Date.now(),\n        data: apiCall\n      })\n    }\n  }\n  \n  /**\n   * 更新渲染统计\n   */\n  const updateRenderStats = () => {\n    const renderTimes = performanceData.value.renderTimes.map(r => r.time)\n    \n    if (renderTimes.length === 0) return\n    \n    performanceStats.value.totalRenders = renderTimes.length\n    performanceStats.value.averageRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length\n    performanceStats.value.maxRenderTime = Math.max(...renderTimes)\n    performanceStats.value.minRenderTime = Math.min(...renderTimes)\n  }\n  \n  /**\n   * 更新内存趋势\n   */\n  const updateMemoryTrend = () => {\n    const memoryData = performanceData.value.memoryUsage\n    \n    if (memoryData.length < 3) return\n    \n    const recent = memoryData.slice(-3)\n    const trend = recent[2].used - recent[0].used\n    \n    if (trend > 5) { // 增长超过5MB\n      performanceStats.value.memoryTrend = 'increasing'\n    } else if (trend < -5) { // 减少超过5MB\n      performanceStats.value.memoryTrend = 'decreasing'\n    } else {\n      performanceStats.value.memoryTrend = 'stable'\n    }\n  }\n  \n  /**\n   * 添加性能警告\n   * @param {Object} warning - 警告信息\n   */\n  const addPerformanceWarning = (warning) => {\n    performanceWarnings.value.push({\n      id: `warning_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      ...warning\n    })\n    \n    // 限制警告数量\n    if (performanceWarnings.value.length > 50) {\n      performanceWarnings.value = performanceWarnings.value.slice(-25)\n    }\n    \n    // 记录到全局监控器\n    PerformanceMonitor.recordMetric('performance_warning', warning)\n    \n    // 在开发模式下输出警告\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(`⚠️ 性能警告 [${componentName}]:`, warning.message, warning.data)\n    }\n  }\n  \n  /**\n   * 清除性能警告\n   * @param {string} warningId - 警告ID（可选）\n   */\n  const clearPerformanceWarnings = (warningId = null) => {\n    if (warningId) {\n      performanceWarnings.value = performanceWarnings.value.filter(w => w.id !== warningId)\n    } else {\n      performanceWarnings.value = []\n    }\n  }\n  \n  /**\n   * 测量函数执行时间\n   * @param {string} operationName - 操作名称\n   * @param {Function} operation - 要测量的操作\n   * @returns {Promise|any} 操作结果\n   */\n  const measureOperation = async (operationName, operation) => {\n    const startTime = performance.now()\n    \n    try {\n      const result = await operation()\n      const endTime = performance.now()\n      const duration = endTime - startTime\n      \n      // 记录操作性能\n      recordApiCall({\n        name: operationName,\n        duration,\n        status: 'success'\n      })\n      \n      return result\n    } catch (error) {\n      const endTime = performance.now()\n      const duration = endTime - startTime\n      \n      // 记录失败的操作\n      recordApiCall({\n        name: operationName,\n        duration,\n        status: 'error',\n        error: error.message\n      })\n      \n      throw error\n    }\n  }\n  \n  /**\n   * 获取性能报告\n   * @returns {Object} 性能报告\n   */\n  const getPerformanceReport = () => {\n    return {\n      component: componentName,\n      timestamp: Date.now(),\n      isMonitoring: isMonitoring.value,\n      stats: { ...performanceStats.value },\n      warnings: [...performanceWarnings.value],\n      data: {\n        renderTimes: performanceData.value.renderTimes.slice(-10), // 最近10次渲染\n        memoryUsage: performanceData.value.memoryUsage.slice(-10), // 最近10次内存检查\n        apiCalls: performanceData.value.apiCalls.slice(-10), // 最近10次API调用\n        interactions: performanceData.value.userInteractions.slice(-20) // 最近20次交互\n      },\n      config: { ...monitorConfig.value }\n    }\n  }\n  \n  /**\n   * 导出性能数据\n   * @param {string} format - 导出格式 ('json' | 'csv')\n   * @returns {string} 导出的数据\n   */\n  const exportPerformanceData = (format = 'json') => {\n    const report = getPerformanceReport()\n    \n    if (format === 'csv') {\n      const csvData = []\n      \n      // 渲染时间数据\n      csvData.push('Render Times')\n      csvData.push('Timestamp,Render Time (ms)')\n      report.data.renderTimes.forEach(render => {\n        csvData.push(`${new Date(render.timestamp).toISOString()},${render.time}`)\n      })\n      \n      csvData.push('')\n      \n      // 内存使用数据\n      csvData.push('Memory Usage')\n      csvData.push('Timestamp,Used (MB),Total (MB)')\n      report.data.memoryUsage.forEach(memory => {\n        csvData.push(`${new Date(memory.timestamp).toISOString()},${memory.used},${memory.total}`)\n      })\n      \n      return csvData.join('\\n')\n    }\n    \n    return JSON.stringify(report, null, 2)\n  }\n  \n  /**\n   * 更新监控配置\n   * @param {Object} newConfig - 新配置\n   */\n  const updateMonitorConfig = (newConfig) => {\n    monitorConfig.value = { ...monitorConfig.value, ...newConfig }\n    \n    // 如果正在监控，重新启动以应用新配置\n    if (isMonitoring.value) {\n      stopMonitoring()\n      startMonitoring()\n    }\n  }\n  \n  /**\n   * 获取性能建议\n   * @returns {Array} 性能优化建议\n   */\n  const getPerformanceSuggestions = () => {\n    const suggestions = []\n    \n    // 渲染性能建议\n    if (performanceStats.value.averageRenderTime > monitorConfig.value.renderTimeThreshold) {\n      suggestions.push({\n        type: 'render',\n        priority: 'high',\n        message: '组件渲染时间过长，考虑使用虚拟滚动或分页',\n        details: `平均渲染时间: ${performanceStats.value.averageRenderTime.toFixed(2)}ms`\n      })\n    }\n    \n    // 内存使用建议\n    if (performanceStats.value.memoryTrend === 'increasing') {\n      suggestions.push({\n        type: 'memory',\n        priority: 'medium',\n        message: '内存使用呈上升趋势，检查是否存在内存泄漏',\n        details: '建议检查事件监听器和定时器的清理'\n      })\n    }\n    \n    // API性能建议\n    if (performanceStats.value.slowOperations > 5) {\n      suggestions.push({\n        type: 'api',\n        priority: 'medium',\n        message: '存在多个慢API调用，考虑优化或缓存',\n        details: `慢操作数量: ${performanceStats.value.slowOperations}`\n      })\n    }\n    \n    return suggestions\n  }\n  \n  // 生命周期钩子\n  onMounted(() => {\n    if (process.env.NODE_ENV === 'development') {\n      startMonitoring()\n    }\n  })\n  \n  onUnmounted(() => {\n    stopMonitoring()\n  })\n  \n  return {\n    // 状态\n    isMonitoring,\n    performanceData,\n    performanceStats,\n    performanceWarnings,\n    hasPerformanceIssues,\n    monitorConfig,\n    \n    // 方法\n    startMonitoring,\n    stopMonitoring,\n    recordRenderTime,\n    recordApiCall,\n    measureOperation,\n    clearPerformanceWarnings,\n    getPerformanceReport,\n    exportPerformanceData,\n    updateMonitorConfig,\n    getPerformanceSuggestions\n  }\n}