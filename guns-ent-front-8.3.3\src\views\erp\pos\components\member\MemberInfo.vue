<template>
  <div class="member-info-display">
    <div class="member-card">
      <!-- 会员基本信息 -->
      <div class="member-basic">
        <div class="member-avatar-large">
          <img v-if="member.avatar" :src="member.avatar" :alt="member.memberName" />
          <icon-font v-else iconClass="icon-user-default" />
        </div>
        <div class="member-details">
          <div class="member-name-large">{{ member.memberName }}</div>
          <div class="member-level">
            <a-tag :color="getLevelColor(member.levelName)">
              {{ member.levelName || 'VIP会员' }}
            </a-tag>
          </div>
          <div class="member-phone">{{ formatPhone(member.phone) }}</div>
          <div class="member-card-no">卡号: {{ member.cardNo }}</div>
        </div>
      </div>

      <!-- 会员余额和积分 -->
      <div class="member-balance-points">
        <div class="balance-item">
          <div class="balance-label">账户余额</div>
          <div class="balance-value">￥{{ formatAmount(member.balance || 0) }}</div>
        </div>
        <div class="points-item">
          <div class="points-label">可用积分</div>
          <div class="points-value">{{ member.points || 0 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { AmountFormatter } from '../../utils/formatter'

// 定义组件名称
defineOptions({
  name: 'MemberInfo'
})

// 定义属性
const props = defineProps({
  member: {
    type: Object,
    required: true
  }
})

/**
 * 格式化金额显示
 */
const formatAmount = (amount) => {
  return AmountFormatter.formatCurrency(amount, { showSymbol: false })
}

/**
 * 格式化手机号显示
 */
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 获取会员等级颜色
 */
const getLevelColor = (levelName) => {
  const colorMap = {
    '普通会员': 'default',
    '银卡会员': '#c0c0c0',
    '金卡会员': '#ffd700',
    '钻石会员': '#b9f2ff',
    'VIP会员': '#f50'
  }
  return colorMap[levelName] || 'blue'
}
</script>

<style scoped>
.member-info-display {
  padding: 16px 0;
}

.member-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: #fff;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.member-basic {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.member-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.member-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-details {
  flex: 1;
}

.member-name-large {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 6px;
}

.member-level {
  margin-bottom: 6px;
}

.member-phone,
.member-card-no {
  font-size: 13px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.member-balance-points {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.balance-item,
.points-item {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.balance-label,
.points-label {
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.balance-value {
  font-size: 18px;
  font-weight: 600;
  color: #52c41a;
}

.points-value {
  font-size: 18px;
  font-weight: 600;
  color: #faad14;
}
</style>