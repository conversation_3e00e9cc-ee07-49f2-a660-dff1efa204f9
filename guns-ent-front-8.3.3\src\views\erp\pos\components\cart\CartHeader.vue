<!--
  购物车头部组件
  
  显示购物车标题、商品数量和操作按钮
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cart-header">
    <!-- 购物车标题 -->
    <div class="cart-title">
      <icon-font iconClass="icon-cart" />
      <span>购物车</span>
      <a-badge 
        :count="itemCount" 
        :number-style="{ backgroundColor: '#52c41a' }"
      />
    </div>
    
    <!-- 操作按钮组 -->
    <div class="cart-actions">
      <!-- 清空购物车 -->
      <a-button 
        type="text" 
        size="small" 
        :disabled="isEmpty || loading"
        @click="handleClearCart"
        class="clear-cart-btn"
      >
        <template #icon>
          <icon-font iconClass="icon-delete" />
        </template>
        清空购物车
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { Modal } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'

// 定义组件名称
defineOptions({
  name: 'CartHeader'
})

// 定义Props
const props = defineProps({
  // 商品数量
  itemCount: {
    type: Number,
    default: 0
  },
  // 是否为空购物车
  isEmpty: {
    type: Boolean,
    default: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'clear-cart'
])

// ==================== 事件处理方法 ====================

/**
 * 处理清空购物车
 */
const handleClearCart = () => {
  Modal.confirm({
    title: '确认清空购物车？',
    content: '此操作将清空购物车中的所有商品，是否继续？',
    okText: '确认清空',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      emit('clear-cart')
    }
  })
}


</script>

<style scoped>
.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 购物车标题 */
.cart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 操作按钮组 */
.cart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 清空购物车按钮样式 */
.clear-cart-btn {
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.clear-cart-btn:hover {
  color: #fff;
  background-color: #ff4d4f;
  border-color: #ff4d4f;
}

.clear-cart-btn:disabled {
  color: #d9d9d9;
  border-color: #d9d9d9;
  background-color: #f5f5f5;
}

/* 危险菜单项 */
:deep(.danger-item) {
  color: #ff4d4f !important;
}

:deep(.danger-item:hover) {
  background-color: #fff2f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cart-header {
    padding: 12px;
  }
  
  .cart-title {
    font-size: 14px;
  }
}

/* 动画效果 */
.cart-header {
  transition: all 0.2s ease;
}

.cart-actions .ant-btn {
  transition: all 0.2s ease;
}

.cart-actions .ant-btn:hover {
  transform: translateY(-1px);
}
</style>