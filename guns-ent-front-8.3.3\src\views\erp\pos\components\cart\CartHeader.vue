<!--
  购物车头部组件
  
  显示购物车标题、商品数量和操作按钮
  
  <AUTHOR>
  @since 2025/01/02
-->
<template>
  <div class="cart-header">
    <!-- 购物车标题 -->
    <div class="cart-title">
      <icon-font iconClass="icon-cart" />
      <span>购物车</span>
      <a-badge 
        :count="itemCount" 
        :number-style="{ backgroundColor: '#52c41a' }"
      />
    </div>
    
    <!-- 操作按钮组 -->
    <div class="cart-actions">
      <!-- 清空购物车 -->
      <a-tooltip title="清空购物车">
        <a-button 
          type="text" 
          size="small" 
          :disabled="isEmpty || loading"
          @click="handleClearCart"
        >
          <template #icon>
            <icon-font iconClass="icon-delete" />
          </template>
        </a-button>
      </a-tooltip>
      
      <!-- 扫码添加商品 -->
      <a-tooltip title="扫码添加商品">
        <a-button 
          type="text" 
          size="small"
          :loading="loading"
          @click="handleScanBarcode"
        >
          <template #icon>
            <icon-font iconClass="icon-scan" />
          </template>
        </a-button>
      </a-tooltip>
      
      <!-- 更多操作 -->
      <a-dropdown 
        :trigger="['click']"
        placement="bottomRight"
      >
        <a-button type="text" size="small">
          <template #icon>
            <icon-font iconClass="icon-more" />
          </template>
        </a-button>
        
        <template #overlay>
          <a-menu @click="handleMenuClick">
            <a-menu-item key="import" :disabled="loading">
              <icon-font iconClass="icon-import" />
              导入购物清单
            </a-menu-item>
            <a-menu-item key="export" :disabled="isEmpty || loading">
              <icon-font iconClass="icon-export" />
              导出购物清单
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="save-template" :disabled="isEmpty || loading">
              <icon-font iconClass="icon-save" />
              保存为模板
            </a-menu-item>
            <a-menu-item key="load-template" :disabled="loading">
              <icon-font iconClass="icon-template" />
              加载模板
            </a-menu-item>
            <a-menu-divider />
            <a-menu-item key="clear" class="danger-item" :disabled="isEmpty || loading">
              <icon-font iconClass="icon-delete" />
              清空购物车
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
  </div>
</template>

<script setup>
import { Modal } from 'ant-design-vue'
import IconFont from '@/components/common/IconFont/index.vue'

// 定义组件名称
defineOptions({
  name: 'CartHeader'
})

// 定义Props
const props = defineProps({
  // 商品数量
  itemCount: {
    type: Number,
    default: 0
  },
  // 是否为空购物车
  isEmpty: {
    type: Boolean,
    default: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义Emits
const emit = defineEmits([
  'clear-cart',
  'scan-barcode',
  'import-list',
  'export-list',
  'save-template',
  'load-template'
])

// ==================== 事件处理方法 ====================

/**
 * 处理清空购物车
 */
const handleClearCart = () => {
  Modal.confirm({
    title: '确认清空购物车？',
    content: '此操作将清空购物车中的所有商品，是否继续？',
    okText: '确认清空',
    okType: 'danger',
    cancelText: '取消',
    onOk: () => {
      emit('clear-cart')
    }
  })
}

/**
 * 处理扫码添加商品
 */
const handleScanBarcode = () => {
  emit('scan-barcode')
}

/**
 * 处理菜单点击
 * @param {Object} menuInfo - 菜单信息
 */
const handleMenuClick = ({ key }) => {
  switch (key) {
    case 'import':
      emit('import-list')
      break
    case 'export':
      emit('export-list')
      break
    case 'save-template':
      emit('save-template')
      break
    case 'load-template':
      emit('load-template')
      break
    case 'clear':
      handleClearCart()
      break
  }
}
</script>

<style scoped>
.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 购物车标题 */
.cart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 操作按钮组 */
.cart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 危险菜单项 */
:deep(.danger-item) {
  color: #ff4d4f !important;
}

:deep(.danger-item:hover) {
  background-color: #fff2f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cart-header {
    padding: 12px;
  }
  
  .cart-title {
    font-size: 14px;
  }
}

/* 动画效果 */
.cart-header {
  transition: all 0.2s ease;
}

.cart-actions .ant-btn {
  transition: all 0.2s ease;
}

.cart-actions .ant-btn:hover {
  transform: translateY(-1px);
}
</style>