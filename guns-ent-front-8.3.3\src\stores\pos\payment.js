/**
 * POS支付状态管理
 * 
 * 管理支付方式、支付状态、支付结果等功能
 * 
 * <AUTHOR>
 * @since 2025/01/02
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

export const usePaymentStore = defineStore('pos-payment', () => {
  // ==================== 状态定义 ====================
  
  // 支付状态: idle(空闲), processing(处理中), success(成功), failed(失败)
  const paymentStatus = ref('idle')
  
  // 当前选择的支付方式
  const selectedPaymentMethod = ref('')
  
  // 实收金额（现金支付时使用）
  const receivedAmount = ref(0)
  
  // 找零金额
  const changeAmount = ref(0)
  
  // 支付结果信息
  const paymentResult = ref(null)
  
  // 支付方式配置
  const paymentMethods = ref([
    { code: 'CASH', name: '现金支付', icon: 'money-collect', enabled: true },
    { code: 'ALIPAY', name: '支付宝', icon: 'alipay', enabled: true },
    { code: 'WECHAT', name: '微信支付', icon: 'wechat', enabled: true },
    { code: 'CARD', name: '银行卡', icon: 'credit-card', enabled: true },
    { code: 'MEMBER_CARD', name: '会员卡', icon: 'idcard', enabled: true }
  ])
  
  // 支付超时时间（秒）
  const paymentTimeout = ref(300) // 5分钟
  
  // 支付开始时间
  const paymentStartTime = ref(null)
  
  // 支付定时器
  let paymentTimer = null
  
  // ==================== 计算属性 ====================
  
  // 是否正在支付
  const isPaymentProcessing = computed(() => {
    return paymentStatus.value === 'processing'
  })
  
  // 支付是否成功
  const isPaymentSuccess = computed(() => {
    return paymentStatus.value === 'success'
  })
  
  // 支付是否失败
  const isPaymentFailed = computed(() => {
    return paymentStatus.value === 'failed'
  })
  
  // 是否可以开始支付
  const canStartPayment = computed(() => {
    return paymentStatus.value === 'idle' && selectedPaymentMethod.value !== ''
  })
  
  // 当前支付方式信息
  const currentPaymentMethod = computed(() => {
    return paymentMethods.value.find(method => method.code === selectedPaymentMethod.value) || null
  })
  
  // 是否为现金支付
  const isCashPayment = computed(() => {
    return selectedPaymentMethod.value === 'CASH'
  })
  
  // 现金支付是否有效（实收金额大于等于应付金额）
  const isCashPaymentValid = computed(() => {
    if (!isCashPayment.value) return true
    const finalAmount = 100 // 这里应该从购物车获取最终金额
    return receivedAmount.value >= finalAmount
  })
  
  // 支付剩余时间
  const paymentRemainingTime = computed(() => {
    if (!paymentStartTime.value || paymentStatus.value !== 'processing') {
      return paymentTimeout.value
    }
    
    const elapsed = Math.floor((Date.now() - paymentStartTime.value) / 1000)
    return Math.max(0, paymentTimeout.value - elapsed)
  })
  
  // 支付进度百分比
  const paymentProgress = computed(() => {
    if (!paymentStartTime.value || paymentStatus.value !== 'processing') {
      return 0
    }
    
    const elapsed = Math.floor((Date.now() - paymentStartTime.value) / 1000)
    return Math.min(100, (elapsed / paymentTimeout.value) * 100)
  })
  
  // ==================== 私有方法 ====================
  
  /**
   * 验证支付方式
   * @param {string} methodCode - 支付方式代码
   * @returns {boolean} 是否有效
   */
  const validatePaymentMethod = (methodCode) => {
    const method = paymentMethods.value.find(m => m.code === methodCode)
    if (!method) {
      message.error('不支持的支付方式')
      return false
    }
    
    if (!method.enabled) {
      message.error(`${method.name}暂不可用`)
      return false
    }
    
    return true
  }
  
  /**
   * 启动支付超时定时器
   */
  const startPaymentTimer = () => {
    if (paymentTimer) {
      clearTimeout(paymentTimer)
    }
    
    paymentTimer = setTimeout(() => {
      if (paymentStatus.value === 'processing') {
        paymentFailed('支付超时，请重试')
      }
    }, paymentTimeout.value * 1000)
  }
  
  /**
   * 清除支付定时器
   */
  const clearPaymentTimer = () => {
    if (paymentTimer) {
      clearTimeout(paymentTimer)
      paymentTimer = null
    }
  }
  
  // ==================== 公共方法 ====================
  
  /**
   * 设置支付方式
   * @param {string} methodCode - 支付方式代码
   * @returns {boolean} 是否设置成功
   */
  const setPaymentMethod = (methodCode) => {
    try {
      if (!validatePaymentMethod(methodCode)) {
        return false
      }
      
      selectedPaymentMethod.value = methodCode
      
      // 如果不是现金支付，清除现金相关数据
      if (methodCode !== 'CASH') {
        receivedAmount.value = 0
        changeAmount.value = 0
      }
      
      const method = currentPaymentMethod.value
      message.success(`已选择${method.name}`)
      return true
      
    } catch (error) {
      console.error('设置支付方式失败:', error)
      message.error('设置支付方式失败')
      return false
    }
  }
  
  /**
   * 设置实收金额（现金支付）
   * @param {number} amount - 实收金额
   * @param {number} finalAmount - 应付金额
   * @returns {boolean} 是否设置成功
   */
  const setReceivedAmount = (amount, finalAmount) => {
    try {
      if (!isCashPayment.value) {
        message.warning('当前支付方式不需要设置实收金额')
        return false
      }
      
      if (amount < 0) {
        message.error('实收金额不能为负数')
        return false
      }
      
      receivedAmount.value = amount
      
      // 计算找零
      changeAmount.value = Math.max(0, amount - finalAmount)
      
      if (amount < finalAmount) {
        message.warning(`实收金额不足，还需${(finalAmount - amount).toFixed(2)}元`)
      }
      
      return true
      
    } catch (error) {
      console.error('设置实收金额失败:', error)
      message.error('设置实收金额失败')
      return false
    }
  }
  
  /**
   * 开始支付处理
   * @param {Object} paymentData - 支付数据
   * @returns {boolean} 是否开始成功
   */
  const startPayment = (paymentData = {}) => {
    try {
      if (!canStartPayment.value) {
        message.warning('请先选择支付方式')
        return false
      }
      
      // 现金支付验证
      if (isCashPayment.value && !isCashPaymentValid.value) {
        message.warning('现金支付金额不足')
        return false
      }
      
      paymentStatus.value = 'processing'
      paymentStartTime.value = Date.now()
      paymentResult.value = null
      
      // 启动超时定时器
      startPaymentTimer()
      
      // 保存支付数据
      const method = currentPaymentMethod.value
      message.loading(`正在使用${method.name}支付...`, 0)
      
      console.log('开始支付:', {
        method: selectedPaymentMethod.value,
        amount: paymentData.amount,
        receivedAmount: receivedAmount.value,
        changeAmount: changeAmount.value,
        ...paymentData
      })
      
      return true
      
    } catch (error) {
      console.error('开始支付失败:', error)
      message.error('开始支付失败')
      return false
    }
  }
  
  /**
   * 支付成功
   * @param {Object} result - 支付结果
   */
  const paymentSuccess = (result = {}) => {
    try {
      clearPaymentTimer()
      message.destroy() // 清除loading消息
      
      paymentStatus.value = 'success'
      paymentResult.value = {
        success: true,
        method: selectedPaymentMethod.value,
        methodName: currentPaymentMethod.value?.name || '',
        amount: result.amount || 0,
        transactionId: result.transactionId || '',
        timestamp: Date.now(),
        receivedAmount: receivedAmount.value,
        changeAmount: changeAmount.value,
        ...result
      }
      
      const method = currentPaymentMethod.value
      message.success(`${method.name}支付成功`)
      
    } catch (error) {
      console.error('处理支付成功失败:', error)
    }
  }
  
  /**
   * 支付失败
   * @param {string} errorMessage - 错误信息
   * @param {Object} errorData - 错误数据
   */
  const paymentFailed = (errorMessage = '支付失败', errorData = {}) => {
    try {
      clearPaymentTimer()
      message.destroy() // 清除loading消息
      
      paymentStatus.value = 'failed'
      paymentResult.value = {
        success: false,
        method: selectedPaymentMethod.value,
        methodName: currentPaymentMethod.value?.name || '',
        errorMessage: errorMessage,
        timestamp: Date.now(),
        ...errorData
      }
      
      message.error(errorMessage)
      
    } catch (error) {
      console.error('处理支付失败失败:', error)
    }
  }
  
  /**
   * 取消支付
   */
  const cancelPayment = () => {
    try {
      if (paymentStatus.value !== 'processing') {
        return
      }
      
      clearPaymentTimer()
      message.destroy() // 清除loading消息
      
      paymentStatus.value = 'idle'
      paymentStartTime.value = null
      paymentResult.value = null
      
      message.info('支付已取消')
      
    } catch (error) {
      console.error('取消支付失败:', error)
    }
  }
  
  /**
   * 重置支付状态
   */
  const resetPaymentStatus = () => {
    try {
      clearPaymentTimer()
      message.destroy() // 清除可能的loading消息
      
      paymentStatus.value = 'idle'
      selectedPaymentMethod.value = ''
      receivedAmount.value = 0
      changeAmount.value = 0
      paymentResult.value = null
      paymentStartTime.value = null
      
    } catch (error) {
      console.error('重置支付状态失败:', error)
    }
  }
  
  /**
   * 重试支付
   * @param {Object} paymentData - 支付数据
   * @returns {boolean} 是否重试成功
   */
  const retryPayment = (paymentData = {}) => {
    try {
      if (paymentStatus.value === 'processing') {
        message.warning('支付正在进行中，请稍候')
        return false
      }
      
      // 重置状态但保持支付方式
      const currentMethod = selectedPaymentMethod.value
      resetPaymentStatus()
      selectedPaymentMethod.value = currentMethod
      
      // 重新开始支付
      return startPayment(paymentData)
      
    } catch (error) {
      console.error('重试支付失败:', error)
      message.error('重试支付失败')
      return false
    }
  }
  
  /**
   * 设置支付方式配置
   * @param {Array} methods - 支付方式列表
   */
  const setPaymentMethods = (methods) => {
    try {
      if (!Array.isArray(methods)) {
        console.error('支付方式配置必须是数组')
        return
      }
      
      paymentMethods.value = methods
      
      // 如果当前选择的支付方式不在新配置中，清除选择
      if (selectedPaymentMethod.value) {
        const exists = methods.some(m => m.code === selectedPaymentMethod.value)
        if (!exists) {
          selectedPaymentMethod.value = ''
          message.info('当前支付方式已不可用，请重新选择')
        }
      }
      
    } catch (error) {
      console.error('设置支付方式配置失败:', error)
    }
  }
  
  /**
   * 启用/禁用支付方式
   * @param {string} methodCode - 支付方式代码
   * @param {boolean} enabled - 是否启用
   */
  const togglePaymentMethod = (methodCode, enabled) => {
    try {
      const method = paymentMethods.value.find(m => m.code === methodCode)
      if (method) {
        method.enabled = enabled
        
        // 如果禁用的是当前选择的支付方式，清除选择
        if (!enabled && selectedPaymentMethod.value === methodCode) {
          selectedPaymentMethod.value = ''
          message.info(`${method.name}已禁用，请重新选择支付方式`)
        }
      }
    } catch (error) {
      console.error('切换支付方式状态失败:', error)
    }
  }
  
  /**
   * 设置支付超时时间
   * @param {number} timeout - 超时时间（秒）
   */
  const setPaymentTimeout = (timeout) => {
    try {
      if (timeout <= 0) {
        message.error('支付超时时间必须大于0')
        return false
      }
      
      paymentTimeout.value = timeout
      return true
      
    } catch (error) {
      console.error('设置支付超时时间失败:', error)
      return false
    }
  }
  
  /**
   * 获取支付数据快照（用于备份）
   * @returns {Object} 支付数据快照
   */
  const getPaymentSnapshot = () => {
    return {
      paymentStatus: paymentStatus.value,
      selectedPaymentMethod: selectedPaymentMethod.value,
      receivedAmount: receivedAmount.value,
      changeAmount: changeAmount.value,
      paymentResult: paymentResult.value ? JSON.parse(JSON.stringify(paymentResult.value)) : null,
      paymentStartTime: paymentStartTime.value,
      timestamp: Date.now()
    }
  }
  
  /**
   * 从快照恢复支付数据
   * @param {Object} snapshot - 支付数据快照
   * @returns {boolean} 是否恢复成功
   */
  const restoreFromSnapshot = (snapshot) => {
    try {
      if (!snapshot) return false
      
      // 只恢复非处理中的状态
      if (snapshot.paymentStatus !== 'processing') {
        paymentStatus.value = snapshot.paymentStatus || 'idle'
        selectedPaymentMethod.value = snapshot.selectedPaymentMethod || ''
        receivedAmount.value = snapshot.receivedAmount || 0
        changeAmount.value = snapshot.changeAmount || 0
        paymentResult.value = snapshot.paymentResult
        paymentStartTime.value = snapshot.paymentStartTime
      }
      
      return true
    } catch (error) {
      console.error('从快照恢复支付数据失败:', error)
      return false
    }
  }
  
  /**
   * 获取支付统计信息
   * @returns {Object} 统计信息
   */
  const getPaymentStatistics = () => {
    return {
      currentStatus: paymentStatus.value,
      selectedMethod: selectedPaymentMethod.value,
      isProcessing: isPaymentProcessing.value,
      remainingTime: paymentRemainingTime.value,
      progress: paymentProgress.value,
      availableMethods: paymentMethods.value.filter(m => m.enabled).length,
      totalMethods: paymentMethods.value.length
    }
  }
  
  // ==================== 生命周期 ====================
  
  // 组件卸载时清理定时器
  const cleanup = () => {
    clearPaymentTimer()
  }
  
  // ==================== 返回接口 ====================
  
  return {
    // 状态
    paymentStatus,
    selectedPaymentMethod,
    receivedAmount,
    changeAmount,
    paymentResult,
    paymentMethods,
    paymentTimeout,
    paymentStartTime,
    
    // 计算属性
    isPaymentProcessing,
    isPaymentSuccess,
    isPaymentFailed,
    canStartPayment,
    currentPaymentMethod,
    isCashPayment,
    isCashPaymentValid,
    paymentRemainingTime,
    paymentProgress,
    
    // 方法
    setPaymentMethod,
    setReceivedAmount,
    startPayment,
    paymentSuccess,
    paymentFailed,
    cancelPayment,
    resetPaymentStatus,
    retryPayment,
    setPaymentMethods,
    togglePaymentMethod,
    setPaymentTimeout,
    getPaymentSnapshot,
    restoreFromSnapshot,
    getPaymentStatistics,
    cleanup
  }
})